# SCRM-Next

## 项目概述

SCRM-Next 是一套面向企业的全栈 Web 数据分析与用户管理平台，专为福昕公司 15+ 产品线提供统一的数据洞察和商业决策支持。系统采用**Maven多模块DDD架构**，实现了技术与业务关注点的完全分离，后端基于 Java 21 + Spring Boot 3.4.6，前端基于 Vue 3 + TypeScript + Ant Design Vue，是一个功能完整的企业级数据分析平台。

### 🎯 项目特色

- **Maven多模块架构**：13个模块的清晰架构体系，技术与业务关注点完全分离
- **企业级DDD设计**：完整的四层架构实现，支持大规模业务扩展和微服务化
- **模块化开发**：shared、system、analytics、gateway、starter模块各司其职
- **技术先进性**：Java 21 + Spring Boot 3.4.6 + Vue 3 现代化技术栈
- **数据架构**：MySQL + Redis + ClickHouse 三重数据库架构，支持海量数据分析
- **可扩展性强**：新增业务模块无需修改现有代码，便于团队并行开发

---

## 技术栈

### 后端技术栈

- **Java 21** - 最新 LTS 版本
- **Spring Boot 3.4.6** - 企业级应用框架
- **MyBatis-Plus 3.5.12** - 数据访问层（已移除 XML 映射）
- **MySQL 8.0.33** - 主数据存储
- **ClickHouse 0.4.6** - 行为分析大数据存储
- **Redisson 3.35.0** - Redis 分布式客户端
- **Spring Security 6.x** - 安全框架
- **JWT 4.5.0** - 认证与授权（Auth0 实现）
- **Hutool 5.8.36** - Java 工具库
- **Jasypt 3.0.5** - 配置加密
- **Smart-Doc 3.0.5** - API 文档自动生成

### 前端技术栈

- **Vue 3.4.x** - 渐进式 JavaScript 框架
- **TypeScript 5.3.0** - 静态类型检查
- **Ant Design Vue 4.2.6** - 企业级 UI 组件库
- **Pinia 2.1.7** - 状态管理
- **Vue Router 4.2.5** - 路由管理
- **Axios 1.6.2** - HTTP 客户端
- **ECharts 5.6.0** - 数据可视化
- **Vite 5.4.19** - 现代化构建工具
- **ESLint + Prettier** - 代码质量保障

---

## 🎉 重构成果

### ✅ 架构重构完成

本项目已完成从单体架构到**Maven多模块DDD架构**的重大重构：

- **✅ 完全清理了旧代码**：删除了原有的单体架构代码
- **✅ 建立了13个模块**：shared(3) + system(4) + analytics(4) + gateway(1) + starter(1)
- **✅ 实现了技术与业务分离**：shared模块处理技术关注点，业务模块专注业务逻辑
- **✅ 编译构建成功**：新架构已通过Maven编译验证
- **✅ 为微服务化做好准备**：模块化设计便于后续拆分

### 🏗️ 新架构价值

- **开发效率提升**：清晰的模块结构便于团队协作
- **维护成本降低**：模块化设计便于问题定位和修复
- **扩展能力增强**：新功能可以独立模块开发
- **技术债务减少**：现代化的技术栈和架构设计

---

## 系统架构与组件结构

### 后端架构设计

#### 🏗️ Maven多模块DDD架构

```
backend/
├── shared/                 # 共享模块（技术关注点）
│   ├── common/            # 通用工具和基础类
│   ├── domain/            # 共享领域对象
│   └── infrastructure/    # 基础设施配置
├── system/                # 系统管理模块（业务关注点）
│   ├── domain/           # 用户、权限领域模型
│   ├── application/      # 应用服务层
│   ├── infrastructure/   # 数据访问实现
│   └── api/             # REST API接口
├── analytics/             # 数据分析模块（业务关注点）
│   └── user/            # 用户分析子模块
│       ├── domain/      # 用户分析领域模型
│       ├── application/ # 分析应用服务
│       ├── infrastructure/ # 数据访问实现
│       └── api/        # 分析API接口
├── gateway/              # API网关模块
└── starter/              # 应用启动模块
```

#### 📦 模块架构优势
- **模块独立性**：每个模块都有独立的领域、应用、基础设施和API层
- **技术分离**：shared模块统一管理技术关注点，业务模块专注业务逻辑
- **可扩展性**：新增业务模块无需修改现有代码
- **团队协作**：不同团队可以并行开发不同的业务模块
- **微服务就绪**：模块化设计便于后续拆分为微服务

#### 🔒 安全与权限体系
- **认证机制**：Spring Security + JWT（Auth0 实现）
- **权限控制**：RBAC 模型 + 细粒度权限注解
- **数据权限**：`@DataPermission` 注解实现行级数据控制
- **操作审计**：完整的操作日志记录和追踪

#### 🗄️ 数据架构
- **MySQL 8.0.33**：主业务数据存储，支持事务和复杂查询
- **Redis 7.x**：多层缓存架构，会话管理和实时数据
- **ClickHouse**：行为分析大数据存储，支持海量数据查询

### 前端架构设计

#### 🎨 技术架构
- **组件化设计**：基于 Ant Design Vue 的企业级组件库
- **状态管理**：Pinia 管理全局状态（用户、产品线、权限等）
- **路由管理**：Vue Router 4.x，支持路由守卫和权限控制
- **类型安全**：TypeScript 5.3.0 提供完整的类型检查

#### 📱 功能模块
- **数据看板**：总览仪表盘、产品线仪表盘、实时统计
- **用户分析**：活跃用户、用户增长、登录行为、用户结构
- **行为分析**：事件分析、功能使用、漏斗分析、用户路径
- **系统管理**：用户管理、角色权限、产品管理、系统配置

#### 🎯 用户体验优化
- **响应式设计**：支持桌面端和移动端适配
- **实时数据**：支持数据实时刷新和自动更新
- **交互优化**：路由切换自动滚动重置、防抖节流优化
- **错误处理**：友好的错误提示和数据降级机制

---

## 核心功能模块

### 🔐 用户与权限管理体系（69 个接口）

#### 用户认证与管理
- **用户认证**：登录、登出、用户信息查询、健康检查（4 个接口）
- **用户管理**：用户 CRUD、状态管理、密码重置、批量操作（8 个接口）

#### 权限控制体系
- **角色管理**：角色 CRUD、权限分配、状态管理、关联管理（11 个接口）
- **权限管理**：权限 CRUD、权限树、类型管理、动态权限（15 个接口）
- **产品线管理**：产品线 CRUD、数据源配置、权限隔离（16 个接口）
- **产品版本**：版本管理、发布历史、下载统计（12 个接口）

#### 系统管理
- **操作审计**：日志查询、统计分析、报告导出、敏感操作监控（15 个接口）
- **系统配置**：配置 CRUD、缓存管理、批量操作、分组管理（18 个接口）

### 📊 数据看板与监控（8 个接口）

- **总览仪表盘**：核心指标、用户增长、收入统计等综合数据展示
- **产品线仪表盘**：产品线级别的数据分析和对比
- **实时统计**：实时在线用户、操作统计、系统状态监控
- **核心指标**：多维度指标数据支持，包含趋势、分布、对比等

### 🎯 用户分析模块（46 个接口）

#### 用户活跃分析（8 个接口）
- DAU/WAU/MAU 统计、活跃趋势分析、用户活跃频次分析
- 支持多产品线对比和时间维度分析

#### 用户增长分析（8 个接口）
- 新增用户统计、用户留存分析、增长趋势预测
- 用户生命周期分析和价值分层

#### 其他用户分析
- 登录行为分析、用户结构分析、流失预警等高级分析功能

### 🔍 行为分析模块（33 个接口）

#### 事件分析（8 个接口）
- 事件统计、事件趋势、事件属性分析
- 支持自定义事件和多维度筛选

#### 功能使用分析（8 个接口）
- 功能使用统计、热力图分析、功能覆盖率
- 实时功能使用监控和趋势预测

#### 漏斗分析（8 个接口）
- 转化漏斗分析、流失点识别、优化建议
- 支持多步骤漏斗和实时转化监控

#### 用户路径分析（8 个接口）
- 用户行为路径追踪、路径流向分析
- 异常路径检测和用户旅程优化

#### 公共接口（1 个接口）
- 无需认证的漏斗分析接口，支持外部系统集成

---

## 环境搭建与部署指南

### 🔧 环境要求

#### 后端环境
- **JDK 21** - 必需，推荐使用 Oracle JDK 或 OpenJDK
- **Maven 3.8+** - 项目构建工具
- **MySQL 8.0.33+** - 主数据库
- **Redis 7.x** - 缓存和会话存储
- **ClickHouse 8.x** - 行为分析数据库（可选）

#### 前端环境
- **Node.js >= 18.0.0** - 推荐使用 18.17.0+
- **npm >= 8.0.0** - 包管理器

### 🚀 快速启动

#### 后端服务启动

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd scrm-next/backend
   ```

2. **配置数据库**
   ```bash
   # 修改配置文件
   vim src/main/resources/application-private.yml
   # 配置 MySQL、Redis、ClickHouse 连接信息
   ```

3. **编译运行**
   ```bash
   # 编译项目（Maven多模块）
   mvn clean compile

   # 打包项目
   mvn clean package -DskipTests

   # 启动服务（默认端口 9090）
   cd starter
   mvn spring-boot:run

   # 或直接运行打包后的jar
   java -jar starter/target/scrm-next-starter-1.0.0.jar
   ```

4. **验证启动**
   ```bash
   # 健康检查
   curl http://localhost:9090/api/auth/health

   # API 文档
   open http://localhost:9090/api/static/doc/index.html
   ```

#### 前端应用启动

1. **安装依赖**
   ```bash
   cd frontend
   npm install
   ```

2. **本地开发**
   ```bash
   # 启动开发服务器（默认端口 3000）
   npm run dev

   # 访问应用
   open http://localhost:3000
   ```

3. **生产构建**
   ```bash
   # 构建生产版本
   npm run build

   # 预览构建结果
   npm run preview
   ```

### 🗄️ 数据库配置

#### MySQL 配置
```sql
-- 创建数据库
CREATE DATABASE scrm_next CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'scrm_user'@'%' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON scrm_next.* TO 'scrm_user'@'%';
FLUSH PRIVILEGES;
```

#### Redis 配置
```bash
# 启动 Redis 服务
redis-server

# 验证连接
redis-cli ping
```

#### ClickHouse 配置（可选）
```sql
-- 创建数据库
CREATE DATABASE scrm_behavior;

-- 创建行为事件表
CREATE TABLE scrm_behavior.user_events (
    event_id String,
    user_id String,
    event_name String,
    event_time DateTime,
    properties String
) ENGINE = MergeTree()
ORDER BY (event_time, user_id);
```

---

## 📚 API 文档与接口规范

### API 接口统计
- **总接口数量**：156 个 RESTful API
- **控制器数量**：16 个业务控制器
- **接口分布**：认证(4) + 用户管理(65) + 数据分析(87)

### 接口模块划分
```
/api
├── /auth                    # 用户认证（4个接口）
├── /admin                   # 管理员接口
│   ├── /users              # 用户管理（8个接口）
│   ├── /roles              # 角色管理（11个接口）
│   ├── /permissions        # 权限管理（15个接口）
│   ├── /product-lines      # 产品线管理（16个接口）
│   ├── /product-versions   # 产品版本（12个接口）
│   ├── /operation-logs     # 操作日志（15个接口）
│   └── /system-configs     # 系统配置（18个接口）
├── /dashboard              # 数据看板（8个接口）
├── /user                   # 用户分析
│   ├── /active            # 活跃用户（8个接口）
│   ├── /growth            # 用户增长（8个接口）
│   └── /...               # 其他用户分析
├── /behavior               # 行为分析
│   ├── /event             # 事件分析（8个接口）
│   ├── /feature           # 功能使用（8个接口）
│   ├── /funnel            # 漏斗分析（8个接口）
│   └── /path              # 路径分析（8个接口）
└── /public                 # 公共接口（1个接口）
```

### 文档生成与访问
```bash
# 生成 API 文档
cd backend
mvn smart-doc:html

# 访问文档
open http://localhost:9090/api/static/doc/index.html
```

---

## 🛠️ 开发规范与质量保障

### 代码规范
- **后端规范**：遵循阿里巴巴 Java 开发手册 + Spring Boot 最佳实践
- **前端规范**：ESLint + Prettier + TypeScript 严格模式
- **命名规范**：统一的包名、类名、方法名、变量名规范
- **注释规范**：完整的 JavaDoc 和 JSDoc 注释

### 架构规范
- **DDD 架构**：严格按照四层架构进行开发
- **模块化设计**：高内聚低耦合的模块划分
- **接口设计**：RESTful API 设计规范
- **数据库设计**：规范化的表结构和索引设计

### 安全规范
- **认证授权**：JWT + Spring Security 双重保障
- **数据权限**：基于注解的细粒度权限控制
- **敏感数据**：配置加密存储，密码 BCrypt 加密
- **接口安全**：请求限流、参数校验、SQL 注入防护

### 测试规范
- **单元测试**：JUnit 5 + Mockito（后端）
- **集成测试**：Spring Boot Test 集成测试
- **API 测试**：基于 Smart-Doc 的接口测试
- **前端测试**：Vitest + Vue Test Utils（预留）

### 版本控制
- **分支策略**：Git Flow 工作流
- **提交规范**：Conventional Commits 规范
- **代码审查**：Pull Request 必须经过 Code Review
- **发布管理**：语义化版本控制（SemVer）

---

## 📁 项目目录结构

```
scrm-next/
├── backend/                          # Spring Boot 后端服务
│   ├── src/main/java/com/foxit/crm/
│   │   ├── ScrmNextApplication.java  # 应用启动类
│   │   ├── common/                   # 公共组件层
│   │   │   ├── annotation/           # 自定义注解（数据权限、限流等）
│   │   │   ├── aspect/               # AOP 切面（日志、权限、限流）
│   │   │   ├── config/               # 配置类（安全、缓存、数据库）
│   │   │   ├── exception/            # 异常处理
│   │   │   └── util/                 # 工具类
│   │   ├── modules/                  # 业务模块
│   │   │   ├── system/               # 系统管理模块（69个接口）
│   │   │   │   ├── api/controller/   # 控制器层
│   │   │   │   ├── application/      # 应用服务层
│   │   │   │   ├── domain/           # 领域模型层
│   │   │   │   └── infrastructure/   # 基础设施层
│   │   │   ├── dashboard/            # 数据看板模块（8个接口）
│   │   │   ├── behavioranalysis/     # 行为分析模块（33个接口）
│   │   │   └── useranalysis/         # 用户分析模块（46个接口）
│   │   ├── shared/                   # 共享基础设施
│   │   │   ├── domain/               # 共享领域对象
│   │   │   └── infrastructure/       # 共享基础设施
│   │   └── tool/                     # 工具类
│   ├── src/main/resources/
│   │   ├── application.yml           # 主配置文件
│   │   ├── application-common.yml    # 公共配置
│   │   ├── application-private.yml   # 私有配置（数据库连接等）
│   │   └── smart-doc.json           # API 文档配置
│   ├── src/test/java/                # 测试代码
│   └── pom.xml                       # Maven 配置
├── frontend/                         # Vue 3 前端应用
│   ├── src/
│   │   ├── api/                      # API 接口封装
│   │   │   ├── dashboard.ts          # 数据看板 API
│   │   │   ├── system/               # 系统管理 API
│   │   │   ├── user/                 # 用户分析 API
│   │   │   └── behavior/             # 行为分析 API
│   │   ├── components/               # 公共组件
│   │   │   ├── common/               # 基础组件
│   │   │   ├── charts/               # 图表组件
│   │   │   └── system/               # 系统组件
│   │   ├── layouts/                  # 布局组件
│   │   │   └── MainLayout.vue        # 主布局
│   │   ├── views/                    # 页面组件
│   │   │   ├── dashboard/            # 数据看板页面
│   │   │   ├── user/                 # 用户分析页面
│   │   │   ├── behavior/             # 行为分析页面
│   │   │   ├── system/               # 系统管理页面
│   │   │   └── Login.vue             # 登录页面
│   │   ├── stores/                   # 状态管理
│   │   │   ├── app.ts                # 应用状态
│   │   │   ├── user.ts               # 用户状态
│   │   │   └── dashboard.ts          # 看板状态
│   │   ├── router/                   # 路由配置
│   │   ├── utils/                    # 工具函数
│   │   ├── types/                    # TypeScript 类型定义
│   │   └── styles/                   # 样式文件
│   ├── package.json                  # 前端依赖配置
│   ├── vite.config.ts               # Vite 构建配置
│   ├── tsconfig.json                # TypeScript 配置
│   └── .eslintrc.cjs                # ESLint 配置
└── README.md                         # 项目说明文档
```

---

## 📊 项目统计与成果

### 代码规模
- **后端架构**：13个Maven模块，完整的DDD四层架构
- **模块化设计**：shared(3) + system(4) + analytics(4) + gateway(1) + starter(1)
- **前端代码**：Vue 3 + TypeScript，完整的企业级前端应用
- **数据库**：MySQL + Redis + ClickHouse 三重数据架构
- **文档**：自动生成的 API 文档，完善的项目文档

### 技术特色
- **架构先进**：Maven多模块DDD架构，技术与业务完全分离
- **微服务就绪**：模块化设计便于后续拆分为微服务
- **技术栈新**：Java 21 + Spring Boot 3.4.6 + Vue 3 最新技术栈
- **可扩展性强**：新增业务模块无需修改现有代码
- **质量保障**：完善的代码规范、测试框架、安全机制

### 适用场景
- **企业级应用**：适合中大型企业的数据分析平台
- **多产品线**：支持多产品线统一管理和数据分析
- **数据驱动**：为数据驱动的业务决策提供支持
- **二次开发**：良好的架构设计，便于功能扩展和定制

---

## 🤝 贡献与支持

### 参与贡献
- 欢迎提交 Issue 反馈问题和建议
- 欢迎提交 Pull Request 贡献代码
- 请遵循项目的代码规范和提交规范
- 建议先阅读 [贡献指南](./CONTRIBUTING.md)

### 技术支持
- 项目文档：查看各模块的 README.md 文件
- API 文档：http://localhost:9090/api/static/doc/index.html
- 问题反馈：通过 GitHub Issues 提交问题

---

## 📄 许可证

本项目采用 [MIT License](./LICENSE) 开源协议。

---

**注意：本文档基于项目实际代码状态编写，所有功能描述均为已实现功能，不包含任何未实现或计划中的功能。**
