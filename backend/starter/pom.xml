<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.foxit.crm</groupId>
        <artifactId>scrm-next-parent</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>starter</artifactId>
    <name>SCRM-Next Starter</name>
    <description>SCRM-Next应用启动模块</description>

    <dependencies>
        <!-- 依赖所有业务模块 -->
        <dependency>
            <groupId>com.foxit.crm</groupId>
            <artifactId>system-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.foxit.crm</groupId>
            <artifactId>system-application</artifactId>
        </dependency>
        <dependency>
            <groupId>com.foxit.crm</groupId>
            <artifactId>system-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.foxit.crm</groupId>
            <artifactId>system-infrastructure</artifactId>
        </dependency>

        <dependency>
            <groupId>com.foxit.crm</groupId>
            <artifactId>analytics-user-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.foxit.crm</groupId>
            <artifactId>analytics-user-application</artifactId>
        </dependency>
        <dependency>
            <groupId>com.foxit.crm</groupId>
            <artifactId>analytics-user-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.foxit.crm</groupId>
            <artifactId>analytics-user-infrastructure</artifactId>
        </dependency>

        <!-- 依赖网关模块 -->
        <dependency>
            <groupId>com.foxit.crm</groupId>
            <artifactId>gateway</artifactId>
        </dependency>

        <!-- 依赖共享模块 -->
        <dependency>
            <groupId>com.foxit.crm</groupId>
            <artifactId>shared-infrastructure</artifactId>
        </dependency>

        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- Spring Boot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Spring Boot Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.foxit.crm.ScrmnextApplication</mainClass>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
