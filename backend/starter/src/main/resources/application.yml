# ============================
# SCRM-Next 主配置文件
# 说明: 负责环境激活、配置导入、环境特定配置
# 架构: 三层配置结构（主配置 + 公共配置 + 私有配置）
# ============================
spring:
  application:
    name: scrm-next-backend # 应用名称
  profiles:
    active: dev # 默认激活开发环境
  config:
    import:
      - optional:classpath:application-common.yml # 导入公共配置（与环境无关的通用配置）
      - optional:classpath:application-private.yml # 导入私有配置（含连接信息和加密数据）

server:
  port: 9090
  servlet:
    context-path: /api # API 根路径
    encoding:
      charset: UTF-8 # 字符编码
      enabled: true # 启用编码过滤器
      force: true # 强制使用指定编码
  # 错误页面配置
  error:
    include-message: always # 错误响应中包含异常信息
    include-binding-errors: always # 错误响应中包含绑定错误


# ============================
# Spring Boot Actuator 配置
# ============================
management:
  # 端点配置
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus # 暴露的端点
      base-path: /actuator # 端点基础路径
  # 健康检查配置
  endpoint:
    health:
      show-details: when-authorized # 授权时显示详细信息
      show-components: always # 总是显示组件信息
  # 应用信息配置
  info:
    env:
      enabled: true # 启用环境信息
    java:
      enabled: true # 启用Java信息
    os:
      enabled: true # 启用操作系统信息

---
# ============================
# 开发环境配置 (dev)
# ============================
spring:
  config:
    activate:
      on-profile: dev

# MyBatis Plus 开发环境配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # 开启 SQL 日志

# 日志配置 - 开发环境
logging:
  level:
    com.foxit.crm: debug # 业务日志级别
    org.springframework.security: debug # Security 日志级别
    org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping: debug # 控制器映射日志
    root: info # 根日志级别

---
# ============================
# 测试环境配置 (test)
# ============================
spring:
  config:
    activate:
      on-profile: test


# 日志配置 - 测试环境
logging:
  level:
    com.foxit.crm: info
    org.springframework.security: info
    root: info

---
# ============================
# 生产环境配置 (prod)
# ============================
spring:
  config:
    activate:
      on-profile: prod


# 日志配置 - 生产环境（更严格的日志级别）
logging:
  level:
    com.foxit.crm: info
    org.springframework.security: warn
    root: warn
