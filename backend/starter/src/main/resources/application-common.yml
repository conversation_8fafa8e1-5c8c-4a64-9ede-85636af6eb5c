# SCRM-Next 公共配置文件
# 说明: 包含与环境无关的框架配置和业务配置，所有环境共享

# ============================
# Spring Boot 框架公共配置
# ============================
spring:
  # JSON 序列化配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss # 日期格式
    time-zone: GMT+8 # 时区设置（中国标准时间）
    serialization:
      write-dates-as-timestamps: false # 日期不使用时间戳
    deserialization:
      fail-on-unknown-properties: false # 忽略未知属性
  # 数据源公共配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver # MySQL 8.0+ 驱动
    hikari:
      connection-timeout: 30000 # 连接超时时间(ms)
      idle-timeout: 600000 # 空闲超时时间(ms) - 10分钟
      max-lifetime: 1800000 # 连接最大生命周期(ms) - 30分钟
      minimum-idle: 5 # 最小空闲连接数  # Redis 公共配置
  data:
    redis:
      port: 6379 # Redis 默认端口
      lettuce:
        pool:
          max-wait: -1ms # 最大等待时间（不限制）
          max-active: 8 # 默认最大连接数
          max-idle: 8 # 默认最大空闲连接数
          min-idle: 0 # 默认最小空闲连接数
  # 静态资源配置
  web:
    resources:
      static-locations: classpath:/static/ # 静态资源位置
      cache:
        period: 31536000 # 静态资源缓存时间(秒) - 1年
  # 国际化配置
  messages:
    basename: messages # 国际化文件基础名
    encoding: UTF-8 # 国际化文件编码
    cache-duration: 3600 # 国际化消息缓存时间(秒) - 1小时
  # 任务执行配置
  task:
    execution:
      pool:
        core-size: 8 # 核心线程数
        max-size: 16 # 最大线程数
        queue-capacity: 100 # 队列容量
        keep-alive: 60s # 线程空闲时间
    scheduling:
      pool:
        size: 4 # 调度线程池大小


# ============================
# MyBatis Plus 框架配置
# ============================
mybatis-plus:
  # 实体类包路径
  type-aliases-package: com.foxit.crm.modules.*.infrastructure.persistence.entity
  # 核心配置
  configuration:
    map-underscore-to-camel-case: true # 下划线自动转驼峰命名
    cache-enabled: false # 禁用二级缓存（提高性能）
    call-setters-on-nulls: true # NULL 值时调用 setter 方法
    jdbc-type-for-null: null # NULL 值对应的 JDBC 类型
    auto-mapping-behavior: partial # 自动映射策略：部分映射
    auto-mapping-unknown-column-behavior: none # 未知列的自动映射行为
    lazy-loading-enabled: false # 禁用懒加载
    multiple-result-sets-enabled: true # 允许多结果集
    use-column-label: true # 使用列标签代替列名
    use-generated-keys: false # 不使用自动生成的键
    default-executor-type: simple # 默认执行器类型
    default-statement-timeout: 25 # 默认语句超时时间(秒)
  # 全局配置
  global-config:
    # 数据库相关配置
    db-config:
      id-type: ASSIGN_ID # 主键生成策略：雪花算法
      table-underline: true # 表名下划线命名
      capital-mode: false # 关闭大写命名
      logic-delete-field: deleted # 逻辑删除字段名
      logic-delete-value: 1 # 逻辑删除值（已删除）
      logic-not-delete-value: 0 # 逻辑未删除值（未删除）
      update-strategy: NOT_NULL # 更新策略：只更新非空字段
      insert-strategy: NOT_NULL # 插入策略：只插入非空字段
      select-strategy: NOT_EMPTY # 查询策略：忽略空字符串

# ============================
# 日志框架配置
# ============================
logging:
  # 日志格式配置
  pattern:
    # 控制台日志格式：时间 [线程] 级别 类名 - 消息
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
    # 文件日志格式：与控制台格式相同
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
  # 日志文件配置
  file:
    name: /opt/logs/scrm-next.log
    # 异步日志配置
  logback:
    rolling-policy:
      clean-history-on-start: true # 启动时清理历史日志
      total-size-cap: 1GB
      max-file-size: 100MB
      max-history: 30

# ============================
# SCRM 业务公共配置
# ============================
scrm:
  # JWT 配置
  jwt:
    expire: 86400 # JWT 过期时间（秒）- 24小时
    header: Authorization # JWT 请求头名称
    prefix: "Bearer " # JWT 前缀（注意空格）
    issuer: scrm-next # JWT 签发者
    audience: scrm-users # JWT 受众
  # 跨域配置（通用设置）
  cors:
    allowed-methods: # 允许的HTTP方法
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
      - PATCH
    allowed-headers: "*" # 允许的请求头（开发时可用*，生产环境建议具体指定）
    allow-credentials: true # 允许携带认证信息（Cookie等）
    max-age: 3600 # 预检请求缓存时间(秒) - 1小时






