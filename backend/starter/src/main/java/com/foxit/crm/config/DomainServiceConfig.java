package com.foxit.crm.config;

import com.foxit.crm.system.domain.model.valueobject.Password;
import com.foxit.crm.system.domain.repository.UserRepository;
import com.foxit.crm.system.domain.repository.RoleRepository;
import com.foxit.crm.system.domain.service.UserDomainService;
import com.foxit.crm.system.domain.service.RoleDomainService;
import com.foxit.crm.system.infrastructure.persistence.repository.RoleRepositoryImpl;
import com.foxit.crm.system.infrastructure.persistence.mapper.RoleMapper;
import com.foxit.crm.system.infrastructure.persistence.converter.RolePOConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 领域服务配置类
 * 显式配置领域服务Bean，确保Spring能够正确创建和注入
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Configuration
public class DomainServiceConfig {

    /**
     * 用户领域服务Bean
     */
    @Bean
    public UserDomainService userDomainService(
            UserRepository userRepository,
            Password.PasswordEncoder passwordEncoder) {
        return new UserDomainService(userRepository, passwordEncoder);
    }

    /**
     * 角色仓储Bean
     */
    @Bean
    public RoleRepository roleRepository(RoleMapper roleMapper, RolePOConverter rolePOConverter) {
        return new RoleRepositoryImpl(roleMapper, rolePOConverter);
    }

    /**
     * 角色领域服务Bean
     */
    @Bean
    public RoleDomainService roleDomainService(RoleRepository roleRepository) {
        return new RoleDomainService(roleRepository);
    }
}
