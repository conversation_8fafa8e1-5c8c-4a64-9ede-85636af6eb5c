package com.foxit.crm.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.web.firewall.HttpFirewall;
import org.springframework.security.web.firewall.StrictHttpFirewall;

/**
 * Web安全配置
 * 配置HTTP防火墙等Web层安全设置
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Configuration
public class WebSecurityConfig {

    /**
     * 自定义Web安全配置
     */
    @Bean
    public WebSecurityCustomizer webSecurityCustomizer() {
        return (web) -> web.httpFirewall(httpFirewall());
    }

    /**
     * 配置HTTP防火墙
     * 允许分号等字符，解决URL被拒绝的问题
     */
    @Bean
    public HttpFirewall httpFirewall() {
        StrictHttpFirewall firewall = new StrictHttpFirewall();
        
        // 允许URL中包含分号
        firewall.setAllowSemicolon(true);
        
        // 允许URL中包含反斜杠
        firewall.setAllowBackSlash(true);
        
        // 允许URL中包含百分号编码
        firewall.setAllowUrlEncodedPercent(true);
        
        // 允许URL中包含双斜杠
        firewall.setAllowUrlEncodedSlash(true);
        
        // 允许URL中包含点号
        firewall.setAllowUrlEncodedPeriod(true);
        
        return firewall;
    }
}
