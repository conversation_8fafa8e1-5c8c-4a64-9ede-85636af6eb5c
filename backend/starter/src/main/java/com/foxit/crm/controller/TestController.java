package com.foxit.crm.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 测试控制器
 * 用于验证安全配置和API功能
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@RestController
public class TestController {

    /**
     * 公开的健康检查端点
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        return Map.of(
            "status", "UP",
            "timestamp", System.currentTimeMillis(),
            "message", "应用运行正常"
        );
    }

    /**
     * 公开的登录端点
     */
    @PostMapping("/auth/login")
    public Map<String, Object> login(@RequestBody Map<String, String> request) {
        String username = request.get("username");
        String password = request.get("password");
        
        // 简单的测试登录逻辑
        if ("admin".equals(username) && "123456".equals(password)) {
            return Map.of(
                "code", 200,
                "message", "登录成功",
                "data", Map.of(
                    "token", "test-jwt-token-" + System.currentTimeMillis(),
                    "username", username,
                    "roles", new String[]{"ADMIN"}
                ),
                "timestamp", System.currentTimeMillis()
            );
        } else {
            return Map.of(
                "code", 401,
                "message", "用户名或密码错误",
                "timestamp", System.currentTimeMillis()
            );
        }
    }

    /**
     * 需要认证的端点
     */
    @GetMapping("/user/profile")
    public Map<String, Object> profile() {
        return Map.of(
            "code", 200,
            "message", "获取用户信息成功",
            "data", Map.of(
                "username", "admin",
                "nickname", "管理员",
                "roles", new String[]{"ADMIN"}
            ),
            "timestamp", System.currentTimeMillis()
        );
    }
}
