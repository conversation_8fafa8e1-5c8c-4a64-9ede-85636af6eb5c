# SCRM-Next 后端服务

> 基于 Spring Boot 3 + DDD 架构的企业级 CRM 数据分析平台后端服务

## 📋 项目概述

SCRM-Next 后端是一个现代化的企业级 CRM 数据分析平台后端服务，采用领域驱动设计（DDD）架构，为福昕公司 15+ 产品线提供统一的数据处理和业务逻辑支持。

## ✨ 最新更新 (v6.0.0 - 2025 年 7 月 7 日)

### 🎉 阶段五完成 + 系统优化

#### 🎯 行为分析模块全面完成

- **事件分析**：完整的事件分析架构和 8 个 API 接口
- **功能使用分析**：功能使用统计和趋势分析（8 个接口）
- **用户路径分析**：用户行为路径追踪和分析（8 个接口）
- **漏斗分析**：转化漏斗分析和优化建议（8 个接口）
- **公共接口**：新增无需认证的漏斗分析接口
- **ClickHouse 集成**：支持大数据量行为分析查询
- **双重数据源**：真实数据 + 模拟数据自动降级机制

#### 🔧 系统优化与修复

- **漏斗分析优化**：修复 FunnelAnalysisRepositoryRealImpl 表名映射问题
- **数据库集成**：完善 ClickHouse 数据源配置和查询逻辑
- **前端滚动重置**：实现路由切换自动滚动到顶部功能
- **API 响应优化**：统一漏斗分析数据格式和错误处理
- **文档更新**：完善项目结构文档和 README 文档

#### 📊 项目里程碑

- **总接口数量**：156 个（超出原计划 96 个）
- **控制器数量**：16 个（涵盖所有业务模块）
- **架构完整性**：完整的 DDD 四层架构实现
- **功能覆盖**：5 个核心业务模块全部完成
- **数据库支持**：MySQL + Redis + ClickHouse 三重数据架构

### 🎉 阶段四：用户分析模块 - 全面完成

#### 📊 用户分析核心功能

- **活跃用户分析**：完整的 DDD 架构 + 8 个核心 API 接口
- **用户增长分析**：新增用户、留存分析、增长趋势等核心功能
- **登录行为分析**：登录趋势、时间分布、设备地域分析
- **用户结构分析**：用户转化漏斗、会员类型构成、价值分层分析
- **用户分群功能**：分群管理、创建分群、批量操作等完整功能
- **技术亮点**：企业级 SCRM 系统专业水准，前端组件库完善，后端架构稳定

### 🎉 阶段三：数据看板核心功能 - 已完成

#### 📊 数据看板模块

- **总览仪表盘**：核心指标、用户增长、收入统计等综合数据展示
- **产品线仪表盘**：产品线级别的数据分析和对比
- **实时统计**：实时在线用户、操作统计、系统状态监控
- **图表数据**：多维度图表数据支持，包含趋势、分布、对比等
- **缓存优化**：多层缓存架构，提升数据查询性能
- **权限控制**：产品线级别的数据权限隔离（8 个核心接口）

#### 🔧 技术架构优化

- **数据模拟器**：完整的业务数据模拟，支持开发和测试
- **缓存管理**：Spring Cache + Redisson 双重缓存策略
- **权限拦截**：数据权限动态拦截和过滤
- **DDD 完善**：Dashboard 聚合根和值对象设计

### 🎉 阶段二：系统管理与权限体系 - 已完成

#### 🔐 权限管理体系

- **RBAC 权限模型**：完整的用户-角色-权限三层关联体系
- **角色管理模块**：角色 CRUD、权限分配、状态管理（15 个接口）
- **权限管理模块**：权限树结构、类型管理、动态权限（12 个接口）
- **关联管理**：用户角色关联、角色权限关联的完整服务

#### 🏢 产品线管理

- **产品线配置**：支持 5 种产品线类型（阅读器、编辑器、云服务、工具类、内容平台）
- **数据源管理**：产品线级别的数据源配置和管理
- **权限隔离**：产品线级别的数据权限隔离（16 个接口）

#### 📊 系统操作审计

- **操作日志**：完整的用户操作记录和追踪
- **多维度查询**：按用户、操作类型、时间范围、IP 地址查询
- **智能分析**：敏感操作识别、慢操作检测、失败操作统计
- **审计报告**：操作统计报告和数据导出（15 个接口）

#### ⚙️ 基础配置管理

- **系统参数**：支持 4 种配置类型（字符串、数字、布尔值、JSON）
- **配置分组**：按业务模块分组管理配置项
- **缓存支持**：Redis 缓存提升配置读取性能
- **批量操作**：配置的批量设置和管理（18 个接口）

#### 🔒 数据权限控制

- **行级权限**：基于用户、角色、部门的数据行级控制
- **列级权限**：敏感字段的列级访问控制
- **查询权限**：动态 SQL 条件注入和权限过滤
- **权限范围**：全部数据、本人数据、部门数据、自定义条件

### 🏗️ 架构优化 (v1.0.0)

- **DDD 架构完善**：完整的领域驱动设计分层实现
- **Lombok 集成**：大幅简化实体类和 DTO 代码
- **MyBatis-Plus 全面应用**：移除 XML 映射，使用注解和 QueryWrapper
- **乐观锁支持**：数据更新并发控制
- **代码质量提升**：统一代码风格，提高可维护性

## 🛠 技术栈

### 核心框架

- **Spring Boot 3.4.6** - 企业级 Java 应用框架
- **JDK 21** - Java 开发工具包
- **Maven 3.9+** - 项目构建和依赖管理

### 数据库

- **MySQL 8.0.33** - 关系型数据库，存储业务数据
- **Redis 7.x** - 内存数据库，用于缓存和会话管理
- **ClickHouse 8.x** - 列式数据库，用于行为分析和大数据查询

### 数据访问

- **MyBatis Plus 3.5.12** - MyBatis 增强工具（已移除 XML 映射）
- **HikariCP** - 高性能数据库连接池

### 安全认证

- **Spring Security 6.x** - 安全框架
- **JWT (Auth0)** - JSON Web Token 认证
- **BCrypt** - 密码加密

### 开发工具

- **Lombok** - Java 代码简化工具
- **Smart-doc 3.x** - API 文档生成工具

### 配置加密

- **Jasypt** - 配置文件加密工具

### 监控工具

- **Spring Boot Actuator** - 应用监控和管理
- **Micrometer** - 应用指标收集

## 🏛️ DDD 架构设计

### 架构原则

本项目采用领域驱动设计（DDD）的分层架构，以更好地应对复杂业务需求和未来扩展性要求。

#### 分层职责

- **API 层（api）**：处理 HTTP 请求，数据传输对象（DTO）
- **应用层（application）**：编排业务流程，事务管理
- **领域层（domain）**：核心业务逻辑，领域规则
- **基础设施层（infrastructure）**：技术实现，外部依赖

#### 模块化设计

- 按业务领域划分模块，每个模块内部完整的 DDD 分层
- 模块间通过明确的接口进行交互，降低耦合度
- 支持未来微服务拆分的演进路径

### 📁 项目结构

```
backend/
├── src/main/java/com/foxit/crm/
│   ├── ScrmNextApplication.java           # 应用启动类
│   ├── common/                           # 公共组件层
│   │   ├── annotation/                   # 自定义注解
│   │   │   ├── ApiVersion.java          # API版本控制注解
│   │   │   ├── DataPermission.java      # 数据权限控制注解
│   │   │   └── RateLimit.java           # 接口限流注解
│   │   ├── api/                         # API响应封装
│   │   │   └── ApiResponse.java         # 统一API响应格式
│   │   ├── aspect/                      # AOP切面
│   │   │   ├── DataPermissionAspect.java # 数据权限切面
│   │   │   ├── LoggingAspect.java       # 操作日志切面
│   │   │   └── RateLimitAspect.java     # 限流切面
│   │   ├── cache/                       # 缓存配置
│   │   │   ├── CacheConfig.java         # 缓存配置
│   │   │   └── CacheService.java        # 缓存服务
│   │   ├── config/                      # 全局配置
│   │   │   ├── SecurityConfig.java      # Spring Security配置
│   │   │   ├── MyBatisPlusConfig.java   # MyBatis-Plus配置（含乐观锁）
│   │   │   ├── RedissonConfig.java      # Redisson分布式锁配置
│   │   │   ├── JwtConfig.java           # JWT认证配置
│   │   │   ├── JasyptConfig.java        # 配置文件加密
│   │   │   ├── WebConfig.java           # Web相关配置
│   │   │   └── ApiVersionConfig.java    # API版本配置
│   │   ├── constant/                    # 常量定义
│   │   │   ├── CommonConstant.java      # 通用常量
│   │   │   └── RedisKeyConstant.java    # Redis键常量
│   │   ├── controller/                  # 公共控制器
│   │   │   └── HealthController.java    # 健康检查控制器
│   │   ├── converter/                   # DTO转换器
│   │   │   ├── AbstractDtoConverter.java # 抽象转换器
│   │   │   ├── DtoConverter.java        # 转换器接口
│   │   │   └── ConverterFactory.java    # 转换器工厂
│   │   ├── datapermission/              # 数据权限
│   │   │   ├── DataPermissionHandler.java    # 数据权限处理器
│   │   │   └── DataPermissionInterceptor.java # 数据权限拦截器
│   │   ├── enums/                       # 枚举类
│   │   │   ├── OperationType.java       # 操作类型枚举
│   │   │   └── UserStatus.java          # 用户状态枚举
│   │   ├── exception/                   # 异常处理
│   │   │   ├── GlobalExceptionHandler.java # 全局异常处理器
│   │   │   ├── BusinessException.java   # 业务异常
│   │   │   ├── SystemException.java     # 系统异常
│   │   │   ├── ErrorCode.java           # 错误码枚举
│   │   │   └── Result.java              # 统一响应结果封装
│   │   ├── util/                        # 工具类
│   │   │   ├── JwtUtil.java             # JWT工具（含权限）
│   │   │   ├── IpUtil.java              # IP地址工具
│   │   │   ├── IpUtils.java             # IP工具类
│   │   │   ├── JsonUtils.java           # JSON序列化工具
│   │   │   ├── DateUtils.java           # 日期时间工具
│   │   │   ├── SecurityUtils.java       # 安全工具
│   │   │   ├── PageResult.java          # 分页结果封装
│   │   │   └── JasyptUtil.java          # 配置加密工具
│   │   └── version/                     # API版本控制
│   │       ├── ApiVersionInterceptor.java # API版本拦截器
│   │       └── ApiVersionRequestMappingHandlerMapping.java # 版本映射处理
│   ├── modules/                         # 业务模块
│   │   ├── system/                      # 系统管理模块
│   │   │   ├── api/                     # API层
│   │   │   │   └── controller/          # 控制器
│   │   │   │       ├── UserController.java         # 用户管理接口
│   │   │   │       ├── RoleController.java         # 角色管理接口
│   │   │   │       ├── PermissionController.java   # 权限管理接口
│   │   │   │       ├── ProductLineController.java  # 产品线管理接口
│   │   │   │       ├── OperationLogController.java # 操作日志接口
│   │   │   │       ├── SystemConfigController.java # 系统配置接口
│   │   │   │       └── ProductVersionController.java # 产品版本管理接口
│   │   │   ├── application/             # 应用层
│   │   │   │   ├── service/             # 应用服务接口
│   │   │   │   ├── impl/                # 应用服务实现
│   │   │   │   └── dto/                 # 数据传输对象
│   │   │   │       ├── request/         # 请求DTO
│   │   │   │       └── response/        # 响应DTO
│   │   │   ├── domain/                  # 领域层
│   │   │   │   ├── model/               # 领域模型
│   │   │   │   │   ├── aggregate/       # 聚合根
│   │   │   │   │   │   ├── User.java                # 用户聚合根
│   │   │   │   │   │   ├── Role.java                # 角色聚合根
│   │   │   │   │   │   ├── Permission.java          # 权限聚合根
│   │   │   │   │   │   ├── ProductLine.java         # 产品线聚合根
│   │   │   │   │   │   ├── OperationLog.java        # 操作日志聚合根
│   │   │   │   │   │   └── SystemConfig.java        # 系统配置聚合根
│   │   │   │   │   └── valueobject/     # 值对象
│   │   │   │   │       ├── UserId.java              # 用户ID值对象
│   │   │   │   │       ├── Username.java            # 用户名值对象
│   │   │   │   │       ├── Password.java            # 密码值对象
│   │   │   │   │       └── Email.java               # 邮箱值对象
│   │   │   │   └── repository/          # 仓储接口
│   │   │   └── infrastructure/          # 基础设施层
│   │   │       └── persistence/         # 持久化
│   │   │           ├── entity/          # 数据库实体（PO）
│   │   │           ├── mapper/          # MyBatis Mapper接口
│   │   │           ├── repository/      # 仓储实现
│   │   │           └── converter/       # 转换器
│   │   ├── dashboard/                   # 数据看板模块
│   │   │   ├── api/                     # API层
│   │   │   │   └── controller/          # 控制器
│   │   │   │       ├── DashboardController.java     # 数据看板控制器
│   │   │   │       └── TestController.java          # 测试控制器
│   │   │   ├── application/             # 应用层
│   │   │   │   ├── service/             # 应用服务接口
│   │   │   │   │   ├── DashboardService.java        # 数据看板服务
│   │   │   │   │   └── DashboardPermissionService.java # 权限服务
│   │   │   │   ├── impl/                # 应用服务实现
│   │   │   │   │   └── DashboardServiceImpl.java    # 数据看板服务实现
│   │   │   │   └── dto/                 # 数据传输对象
│   │   │   │       ├── request/         # 请求DTO
│   │   │   │       └── response/        # 响应DTO
│   │   │   ├── domain/                  # 领域层
│   │   │   │   ├── entity/              # 聚合根
│   │   │   │   │   └── DashboardAggregate.java      # 数据看板聚合根
│   │   │   │   ├── valueobject/         # 值对象
│   │   │   │   │   ├── MetricValue.java             # 指标值对象
│   │   │   │   │   └── TimeRange.java               # 时间范围值对象
│   │   │   │   └── repository/          # 仓储接口
│   │   │   │       └── DashboardRepository.java     # 数据看板仓储接口
│   │   │   └── infrastructure/          # 基础设施层
│   │   │       ├── cache/               # 缓存管理
│   │   │       │   └── DashboardCacheManager.java   # 缓存管理器
│   │   │       ├── persistence/         # 持久化
│   │   │       │   └── repository/      # 仓储实现
│   │   │       │       └── DashboardRepositoryImpl.java # 仓储实现
│   │   │       ├── security/            # 安全控制
│   │   │       │   ├── DashboardDataPermissionInterceptor.java # 权限拦截器
│   │   │       │   └── DashboardPermission.java     # 权限定义
│   │   │       └── simulator/           # 数据模拟
│   │   │           └── DashboardDataSimulator.java  # 数据模拟器
│   │   ├── useranalysis/                # 用户分析模块
│   │   │   ├── domain/                  # 领域层
│   │   │   │   └── entity/              # 聚合根
│   │   │   │       ├── ActiveUserAggregate.java     # 活跃用户分析聚合根
│   │   │   │       └── UserGrowthAggregate.java     # 用户增长分析聚合根
│   │   │   └── application/             # 应用层
│   │   │       └── service/             # 应用服务接口
│   │   │           ├── ActiveUserAnalysisService.java    # 活跃用户分析服务
│   │   │           ├── UserGrowthAnalysisService.java    # 用户增长分析服务
│   │   │           ├── LoginBehaviorAnalysisService.java # 登录行为分析服务
│   │   │           ├── UserStructureAnalysisService.java # 用户结构分析服务
│   │   │           └── UserSegmentationService.java     # 用户分群服务
│   │   └── behavioranalysis/            # 行为分析模块
│   │       ├── api/                     # API层
│   │       │   └── controller/          # 控制器
│   │       │       ├── EventAnalysisController.java     # 事件分析控制器
│   │       │       ├── FeatureUsageController.java      # 功能使用分析控制器
│   │       │       ├── FunnelAnalysisController.java    # 漏斗分析控制器
│   │       │       └── UserPathAnalysisController.java  # 用户路径分析控制器
│   │       ├── application/             # 应用层
│   │       │   ├── service/             # 应用服务接口
│   │       │   │   ├── EventAnalysisService.java        # 事件分析服务
│   │       │   │   ├── FeatureUsageService.java         # 功能使用分析服务
│   │       │   │   ├── FunnelAnalysisService.java       # 漏斗分析服务
│   │       │   │   └── UserPathAnalysisService.java     # 用户路径分析服务
│   │       │   └── dto/                 # 数据传输对象
│   │       │       ├── EventAnalysisRequest.java        # 事件分析请求DTO
│   │       │       ├── EventAnalysisResponse.java       # 事件分析响应DTO
│   │       │       └── ...              # 其他DTO
│   │       ├── domain/                  # 领域层
│   │       │   ├── entity/              # 聚合根
│   │       │   │   ├── EventAnalysisAggregate.java      # 事件分析聚合根
│   │       │   │   ├── FeatureUsageAggregate.java       # 功能使用分析聚合根
│   │       │   │   ├── FunnelAnalysisAggregate.java     # 漏斗分析聚合根
│   │       │   │   └── UserPathAnalysisAggregate.java   # 用户路径分析聚合根
│   │       │   └── repository/          # 仓储接口
│   │       │       ├── EventAnalysisRepository.java     # 事件分析仓储接口
│   │       │       ├── FeatureUsageRepository.java      # 功能使用分析仓储接口
│   │       │       ├── FunnelAnalysisRepository.java    # 漏斗分析仓储接口
│   │       │       └── UserPathAnalysisRepository.java  # 用户路径分析仓储接口
│   │       └── infrastructure/          # 基础设施层
│   │           ├── repository/          # 仓储实现
│   │           │   ├── EventAnalysisRepositoryImpl.java     # 事件分析仓储实现
│   │           │   ├── FeatureUsageRepositoryImpl.java      # 功能使用分析仓储实现
│   │           │   ├── FunnelAnalysisRepositoryImpl.java    # 漏斗分析仓储实现
│   │           │   └── UserPathAnalysisRepositoryImpl.java  # 用户路径分析仓储实现
│   │           └── simulator/           # 数据模拟
│   │               └── BehaviorAnalysisDataSimulator.java   # 行为分析数据模拟器
│   ├── shared/                          # 共享内核
│   │   ├── domain/                      # 共享领域对象
│   │   │   └── event/                   # 领域事件
│   │   └── infrastructure/              # 共享基础设施
│   │       ├── cache/                   # 缓存服务
│   │       └── security/                # 安全组件
│   │           ├── JwtAuthenticationFilter.java  # JWT过滤器
│   │           └── CustomUserDetailsService.java # 用户详情服务
│   └── tool/                            # 工具类
│       └── ConfigEncryptTool.java       # 配置加密工具
├── src/main/resources/
│   ├── sql/                             # SQL脚本
│   │   └── init.sql                     # 初始化脚本（含admin用户）
│   ├── application.yml                  # 主配置文件
│   ├── application-common.yml           # 通用配置
│   ├── application-private.yml          # 私有配置（加密）
│   ├── application-dev.yml              # 开发环境配置
│   ├── application-test.yml             # 测试环境配置
│   ├── application-prod.yml             # 生产环境配置
│   └── smart-doc.json                   # API文档配置
├── config-encrypt-tool.ps1             # PowerShell加密工具
├── login.json                           # 登录测试数据
├── JASYPT-GUIDE.md                      # 配置加密指南
└── pom.xml                              # Maven配置
```

## ⚡ 快速开始

### 🔧 环境要求

- **JDK** >= 21
- **Maven** >= 3.9.0
- **MySQL** >= 8.0.30 (主数据库)
- **Redis** >= 7.0.0 (缓存)
- **ClickHouse** >= 8.0.0 (行为分析数据库，可选)

### 📦 构建与运行

#### 1. 数据库初始化

```sql
-- 创建数据库
CREATE DATABASE scrm_next CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 导入初始化脚本
USE scrm_next;
SOURCE src/main/resources/sql/init.sql;
```

初始化脚本会创建：

- 系统表结构
- 默认管理员账号：`admin` / `admin123`

#### 2. 配置环境变量

```bash
# 设置主密钥（用于配置文件加密）
export FX_MASTER_KEY=your-master-key

# 设置数据库连接（可选，如需覆盖配置文件）
export DB_HOST=localhost
export DB_PORT=3306
export DB_NAME=scrm_next
export DB_USERNAME=root
export DB_PASSWORD=your-password

# 设置Redis连接（可选）
export REDIS_HOST=localhost
export REDIS_PORT=6379
export REDIS_PASSWORD=your-password

# 设置ClickHouse连接（可选，用于行为分析）
export CLICKHOUSE_HOST=localhost
export CLICKHOUSE_PORT=8123
export CLICKHOUSE_DATABASE=scrm_next
```

#### 3. 编译项目

```bash
cd backend
mvn clean compile
```

#### 4. 运行应用

```bash
# 开发环境运行
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 或者打包后运行
mvn package -DskipTests
java -jar target/scrm-next-backend-1.0.0.jar --spring.profiles.active=dev
```

#### 5. 验证运行

```bash
# 健康检查
curl http://localhost:9090/api/auth/health

# 管理员登录测试
curl -X POST http://localhost:9090/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 测试漏斗分析接口（无需认证）
curl -X GET "http://localhost:9090/api/public/behavior/funnel/conversion-analysis?startDate=2025-06-07&endDate=2025-07-07"
```

## 🏃‍♂️ 快速开始指南

### 首次运行步骤

1. **克隆项目**

```bash
git clone <repository-url>
cd scrm-next/backend
```

2. **环境检查**

```bash
# 检查Java版本
java -version  # 需要 >= 21

# 检查Maven版本
mvn -version   # 需要 >= 3.9

# 检查MySQL连接
mysql -u root -p -e "SELECT VERSION();"

# 检查Redis连接
redis-cli ping
```

3. **配置数据库**

```sql
-- 创建数据库
CREATE DATABASE scrm_next CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 切换到数据库
USE scrm_next;

-- 导入初始化脚本
SOURCE src/main/resources/sql/init.sql;

-- 验证初始化
SELECT username, real_name FROM sys_user WHERE username = 'admin';
```

4. **配置环境变量**

```bash
# 在 ~/.bashrc 或 ~/.zshrc 中添加
export FX_MASTER_KEY=your-secure-master-key-here
export DB_PASSWORD=your-mysql-password
export REDIS_PASSWORD=your-redis-password

# 重新加载配置
source ~/.bashrc
```

5. **编译运行**

```bash
# 编译项目
mvn clean compile

# 运行应用
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 或打包后运行
mvn package -DskipTests
java -jar target/scrm-next-backend-1.0.0.jar --spring.profiles.active=dev
```

6. **验证部署**

```bash
# 健康检查
curl http://localhost:8080/auth/health

# 登录测试
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 应该返回包含token的JSON响应
```

### 开发环境配置

#### IDE 配置 (IntelliJ IDEA)

1. **安装 Lombok 插件**

   - File → Settings → Plugins → 搜索 "Lombok" → 安装
   - File → Settings → Build → Compiler → Annotation Processors → 启用

2. **代码格式化**

   - 导入项目根目录的 `.editorconfig` 配置
   - File → Settings → Code Style → 选择对应的代码风格

3. **Maven 配置**
   - File → Settings → Maven → 设置正确的 Maven 路径和 settings.xml

#### 数据库客户端配置

推荐使用以下数据库客户端：

- **DBeaver**（免费）
- **Navicat**
- **DataGrip**（IntelliJ 系列）

连接参数：

```
Host: localhost
Port: 3306
Database: scrm_next
Username: root
Password: your-password
```

### 常见问题排查

#### 1. 编译错误

**问题**：Lombok 注解不生效

```bash
# 解决方案
mvn clean compile -DskipTests
# 确保IDE安装了Lombok插件并启用注解处理
```

**问题**：MyBatis-Plus 乐观锁报错

```bash
# 检查是否配置了乐观锁插件
# 确保实体类有@Version注解
# 更新时要传递原始version值
```

#### 2. 运行时错误

**问题**：JWT Token 无效

```bash
# 检查Redis是否正常运行
redis-cli ping

# 检查token是否过期
# 检查请求头格式：Authorization: Bearer <token>
```

**问题**：权限不足

```bash
# 检查用户类型：1-管理员，2-普通用户
# 检查接口权限配置
# 确保token中包含正确的权限信息
```

#### 3. 数据库问题

**问题**：连接失败

```bash
# 检查数据库服务状态
sudo systemctl status mysql

# 检查配置文件中的数据库连接参数
# 检查用户权限和密码
```

**问题**：初始化失败

```bash
# 确保数据库编码为utf8mb4
# 检查SQL脚本是否完整执行
# 验证管理员用户是否创建成功
```

## 🔒 配置加密工具

SCRM-Next 后端使用 Jasypt 加密敏感配置信息，项目提供了便捷工具脚本帮助开发人员加密配置值。

### 配置加密工具使用方法

#### PowerShell 脚本 (encrypt-config.ps1)

```powershell
# 加密配置值
./encrypt-config.ps1 -action enc -key 你的主密钥 -value 需要加密的值

# 解密配置值
./encrypt-config.ps1 -action dec -key 你的主密钥 -value 加密后的值
```

#### Bash 脚本 (encrypt-config.sh)

```bash
# 加密配置值
./encrypt-config.sh enc 你的主密钥 需要加密的值

# 解密配置值
./encrypt-config.sh dec 你的主密钥 加密后的值
```

### 加密流程

1. 使用加密工具加密敏感信息
2. 将加密后的值放入配置文件，格式为：`ENC(加密后的值)`
3. 启动应用时通过环境变量或 JVM 参数提供主密钥 `FX_MASTER_KEY`

### 示例

#### 1. 加密数据库密码

```powershell
# 使用PowerShell加密
./encrypt-config.ps1 -action enc -key yourMasterKey -value complexDbPassword

# 输出示例
# ==== 加密操作 ====
# 原始值: complexDbPassword
# 加密后: 9vzD5Ri7lyMBzySyG9CbQT==
#
# 配置示例:
# password: ENC(9vzD5Ri7lyMBzySyG9CbQT==)
# ==== 操作成功 ====
```

#### 2. 在配置文件中使用

```yaml
spring:
  datasource:
    password: ENC(9vzD5Ri7lyMBzySyG9CbQT==)
```

#### 3. 启动应用

```bash
# 设置环境变量方式
export FX_MASTER_KEY=yourMasterKey
java -jar target/scrm-next-backend-1.0.0.jar

# 或使用JVM参数方式
java -DFX_MASTER_KEY=yourMasterKey -jar target/scrm-next-backend-1.0.0.jar
```

> 📖 详细说明请参考 [Jasypt 加密指南](./JASYPT-GUIDE.md)

## ⚙️ 配置说明

### 多环境配置

项目支持多环境配置：

- `application.yml` - 基础配置
- `application-dev.yml` - 开发环境
- `application-test.yml` - 测试环境
- `application-prod.yml` - 生产环境

### 数据库配置

```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
```

### Redis 配置

```yaml
spring:
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      database: ${REDIS_DATABASE:0}
      password: ${REDIS_PASSWORD:}
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
```

### JWT 配置

```yaml
scrm:
  jwt:
    secret: ${JWT_SECRET:your-secret-key}
    expire: ${JWT_EXPIRE:86400}
    header: ${JWT_HEADER:Authorization}
    prefix: ${JWT_PREFIX:Bearer }
```

## 🎯 API 接口说明

### 📊 接口统计

阶段五启动后，系统提供了完整的管理、数据分析和行为分析接口：

| 模块       | 接口数量   | 主要功能                       | 完成状态 | 实际控制器                 |
| ---------- | ---------- | ------------------------------ | -------- | -------------------------- |
| 用户认证   | 4 个       | 登录、登出、用户信息、健康检查 | ✅ 完成  | UserAccountController      |
| 用户管理   | 8 个       | 用户 CRUD、状态管理、密码重置  | ✅ 完成  | UserManagementController   |
| 角色管理   | 11 个      | 角色 CRUD、权限分配、状态管理  | ✅ 完成  | RoleController             |
| 权限管理   | 15 个      | 权限 CRUD、权限树、类型管理    | ✅ 完成  | PermissionController       |
| 产品线管理 | 16 个      | 产品线 CRUD、数据源配置、统计  | ✅ 完成  | ProductLineController      |
| 产品版本   | 12 个      | 版本管理、发布历史、下载统计   | ✅ 完成  | ProductVersionController   |
| 操作审计   | 15 个      | 日志查询、统计分析、报告导出   | ✅ 完成  | OperationLogController     |
| 系统配置   | 18 个      | 配置 CRUD、缓存管理、批量操作  | ✅ 完成  | SystemConfigController     |
| 数据看板   | 8 个       | 总览、产品线、实时统计等       | ✅ 完成  | DashboardController        |
| 活跃用户   | 8 个       | DAU/WAU/MAU、趋势、频次分析    | ✅ 完成  | ActiveUserController       |
| 用户增长   | 8 个       | 新增用户、留存、增长趋势分析   | ✅ 完成  | UserGrowthController       |
| 事件分析   | 8 个       | 事件统计、趋势、属性分析       | ✅ 完成  | EventAnalysisController    |
| 功能使用   | 8 个       | 功能使用统计、热力图分析       | ✅ 完成  | FeatureUsageController     |
| 漏斗分析   | 8 个       | 转化漏斗、流失点、优化分析     | ✅ 完成  | FunnelAnalysisController   |
| 用户路径   | 8 个       | 路径流向、统计、异常检测       | ✅ 完成  | UserPathAnalysisController |
| 公共接口   | 1 个       | 无需认证的漏斗分析接口         | ✅ 完成  | PublicFunnelController     |
| **已完成** | **156 个** | **完整的数据分析平台功能**     | **完成** | **16 个控制器**            |

### 规划中接口 (后续阶段)

| 模块         | 预计接口数量 | 主要功能                     | 计划阶段   |
| ------------ | ------------ | ---------------------------- | ---------- |
| 商业分析     | 15 个        | 订单、收入、转化分析         | 阶段六     |
| 社群分析     | 12 个        | 粉丝、活跃度分析             | 阶段六     |
| 活动分析     | 10 个        | 活动参与、效果分析           | 阶段六     |
| 跨产品分析   | 18 个        | 产品对比、用户重叠、协同分析 | 阶段七     |
| 智能分析     | 15 个        | 自定义分析、异常检测         | 阶段七     |
| **预计总计** | **70 个**    | **商业分析和智能分析功能**   | **规划中** |

### 认证接口

#### 用户登录

```http
POST /auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

响应：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expire": 86400,
    "userInfo": {
      "id": 1,
      "username": "admin",
      "realName": "系统管理员",
      "email": "<EMAIL>",
      "userType": 1,
      "status": 1
    }
  }
}
```

#### 用户登出

```http
POST /auth/logout
Authorization: Bearer <token>
```

#### 获取用户信息

```http
GET /auth/user/info
Authorization: Bearer <token>
```

### 用户管理接口（管理员权限）

#### 创建用户

```http
POST /admin/users/create
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "username": "newuser",
  "password": "password123",
  "realName": "新用户",
  "email": "<EMAIL>",
  "phone": "13800138000"
}
```

#### 禁用用户

```http
PUT /admin/users/{userId}/disable
Authorization: Bearer <admin-token>
```

#### 启用用户

```http
PUT /admin/users/{userId}/enable
Authorization: Bearer <admin-token>
```

#### 重置密码

```http
PUT /admin/users/{userId}/reset-password
Authorization: Bearer <admin-token>
```

#### 获取用户列表

```http
GET /admin/users?page=1&size=10&keyword=admin
Authorization: Bearer <admin-token>
```

### 角色管理接口（管理员权限）

#### 创建角色

```http
POST /admin/roles
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "roleName": "销售经理",
  "roleCode": "SALES_MANAGER",
  "description": "销售部门经理角色",
  "status": 1,
  "sortOrder": 10
}
```

#### 为角色分配权限

```http
POST /admin/roles/{roleId}/permissions
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "permissionIds": [1, 2, 3, 4, 5]
}
```

### 产品线管理接口（管理员权限）

#### 创建产品线

```http
POST /admin/product-lines
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "code": "PDF_READER",
  "name": "PDF阅读器",
  "description": "福昕PDF阅读器产品线",
  "type": 1,
  "status": 1,
  "ownerName": "张三",
  "sortOrder": 10
}
```

### 系统配置接口（管理员权限）

#### 获取配置值

```http
GET /admin/system-configs/value/system.max.upload.size
Authorization: Bearer <admin-token>
```

#### 批量设置配置

```http
PUT /admin/system-configs/values/batch
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "system.max.upload.size": "100MB",
  "system.session.timeout": "3600",
  "system.enable.audit": "true"
}
```

### 数据看板接口

#### 获取总览仪表盘

```http
GET /dashboard/overview
Authorization: Bearer <token>
```

响应：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "coreMetrics": {
      "totalUsers": 15420,
      "activeUsers": 8650,
      "totalRevenue": 2580000,
      "growthRate": 12.5
    },
    "userGrowthChart": {
      "chartType": "line",
      "data": [...],
      "labels": [...]
    },
    "revenueChart": {
      "chartType": "bar",
      "data": [...],
      "labels": [...]
    },
    "updateTime": "2025-07-02T10:30:00"
  }
}
```

#### 获取产品线仪表盘

```http
GET /dashboard/product-line?productLineIds=1,2,3
Authorization: Bearer <token>
```

#### 获取实时统计

```http
GET /dashboard/real-time-stats
Authorization: Bearer <token>
```

#### 获取图表数据

```http
GET /dashboard/chart-data?chartType=userGrowth&timeRange=30d
Authorization: Bearer <token>
```

### 用户分析接口

#### 活跃用户分析接口 (ActiveUserController)

```http
# 获取活跃用户总览
GET /user-analysis/active-users/overview?startDate=2025-07-01&endDate=2025-07-02
Authorization: Bearer <token>

# 获取活跃用户趋势
GET /user-analysis/active-users/trends?startDate=2025-07-01&endDate=2025-07-02

# 获取活跃用户分布
GET /user-analysis/active-users/distribution?startDate=2025-07-01&endDate=2025-07-02

# 获取活跃频次分析
GET /user-analysis/active-users/frequency?startDate=2025-07-01&endDate=2025-07-02

# 获取活跃用户对比
GET /user-analysis/active-users/comparison?startDate=2025-07-01&endDate=2025-07-02

# 获取留存分析
GET /user-analysis/active-users/retention?startDate=2025-07-01&endDate=2025-07-02

# 获取队列分析
GET /user-analysis/active-users/cohort?startDate=2025-07-01&endDate=2025-07-02

# 导出活跃用户数据
POST /user-analysis/active-users/export
```

响应示例：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "dau": 8650,
    "wau": 45200,
    "mau": 156800,
    "dauGrowthRate": 12.5,
    "wauGrowthRate": 8.3,
    "mauGrowthRate": 15.2
  }
}
```

#### 用户增长分析接口 (UserGrowthController)

```http
# 获取用户增长总览
GET /user-analysis/growth/overview?startDate=2025-07-01&endDate=2025-07-02
Authorization: Bearer <token>

# 获取新增用户分析
GET /user-analysis/growth/new-users?startDate=2025-07-01&endDate=2025-07-02

# 获取留存分析
GET /user-analysis/growth/retention?startDate=2025-07-01&endDate=2025-07-02

# 获取增长趋势分析
GET /user-analysis/growth/trends?startDate=2025-07-01&endDate=2025-07-02

# 获取渠道分析
GET /user-analysis/growth/channels?startDate=2025-07-01&endDate=2025-07-02

# 获取预测分析
GET /user-analysis/growth/prediction?startDate=2025-07-01&endDate=2025-07-02

# 导出增长数据
POST /user-analysis/growth/export

# 获取增长统计摘要
GET /user-analysis/growth/summary?startDate=2025-07-01&endDate=2025-07-02
```

### 行为分析接口

#### 事件分析接口 (EventAnalysisController)

```http
# 获取事件总览分析
GET /behavior-analysis/events/overview?startDate=2025-07-01&endDate=2025-07-02
Authorization: Bearer <token>

# 获取事件趋势分析
GET /behavior-analysis/events/trends?startDate=2025-07-01&endDate=2025-07-02

# 获取事件属性分析
GET /behavior-analysis/events/properties?startDate=2025-07-01&endDate=2025-07-02

# 获取事件分布分析
GET /behavior-analysis/events/distribution?startDate=2025-07-01&endDate=2025-07-02

# 获取事件对比分析
GET /behavior-analysis/events/comparison?startDate=2025-07-01&endDate=2025-07-02

# 导出事件数据
POST /behavior-analysis/events/export

# 获取事件列表
GET /behavior-analysis/events/list

# 获取实时事件统计
GET /behavior-analysis/events/realtime-stats

# 获取事件分析统计摘要
GET /behavior-analysis/events/summary?startDate=2025-07-01&endDate=2025-07-02
```

响应示例：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalEvents": 125600,
    "uniqueUsers": 8650,
    "avgEventsPerUser": 14.5,
    "trendData": [
      { "date": "2025-07-01", "count": 18500 },
      { "date": "2025-07-02", "count": 19200 }
    ]
  }
}
```

#### 功能使用分析接口 (FeatureUsageController)

```http
# 获取功能使用总览
GET /behavior-analysis/features/overview?startDate=2025-07-01&endDate=2025-07-02
Authorization: Bearer <token>

# 获取功能使用趋势
GET /behavior-analysis/features/trends?startDate=2025-07-01&endDate=2025-07-02

# 获取功能热力图
GET /behavior-analysis/features/heatmap?startDate=2025-07-01&endDate=2025-07-02

# 获取功能使用分布
GET /behavior-analysis/features/distribution?startDate=2025-07-01&endDate=2025-07-02

# 获取功能对比分析
GET /behavior-analysis/features/comparison?startDate=2025-07-01&endDate=2025-07-02

# 导出功能使用数据
POST /behavior-analysis/features/export

# 获取功能列表
GET /behavior-analysis/features/list

# 获取实时功能统计
GET /behavior-analysis/features/realtime-stats
```

#### 漏斗分析接口 (FunnelAnalysisController)

```http
# 获取漏斗转化分析
GET /behavior-analysis/funnels/conversion-analysis?startDate=2025-07-01&endDate=2025-07-02
Authorization: Bearer <token>

# 获取流失点分析
GET /behavior-analysis/funnels/dropout-analysis?startDate=2025-07-01&endDate=2025-07-02

# 获取漏斗趋势分析
GET /behavior-analysis/funnels/trends?startDate=2025-07-01&endDate=2025-07-02

# 获取漏斗对比分析
GET /behavior-analysis/funnels/comparison?startDate=2025-07-01&endDate=2025-07-02

# 获取漏斗细分分析
GET /behavior-analysis/funnels/segmentation?startDate=2025-07-01&endDate=2025-07-02

# 导出漏斗数据
POST /behavior-analysis/funnels/export

# 获取实时漏斗统计
GET /behavior-analysis/funnels/realtime-stats

# 获取漏斗分析统计摘要
GET /behavior-analysis/funnels/summary?startDate=2025-07-01&endDate=2025-07-02
```

#### 用户路径分析接口 (UserPathAnalysisController)

```http
# 获取路径流向分析
GET /behavior-analysis/user-paths/flow-analysis?startDate=2025-07-01&endDate=2025-07-02
Authorization: Bearer <token>

# 获取路径统计分析
GET /behavior-analysis/user-paths/statistics-analysis?startDate=2025-07-01&endDate=2025-07-02

# 获取异常路径检测
GET /behavior-analysis/user-paths/anomaly-detection?startDate=2025-07-01&endDate=2025-07-02

# 获取路径效率分析
GET /behavior-analysis/user-paths/efficiency-analysis?startDate=2025-07-01&endDate=2025-07-02

# 获取路径对比分析
POST /behavior-analysis/user-paths/comparison-analysis

# 导出路径数据
POST /behavior-analysis/user-paths/export

# 获取路径节点列表
GET /behavior-analysis/user-paths/nodes

# 获取实时路径统计
GET /behavior-analysis/user-paths/realtime-stats
```

### 错误响应格式

```json
{
  "code": 400,
  "message": "参数验证失败",
  "data": null,
  "timestamp": "2025-06-20T23:30:00"
}
```

常见错误码：

- `400` - 请求参数错误
- `401` - 未认证或 Token 无效
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器内部错误

## 🔧 开发指南

### DDD 领域模型设计

本项目严格遵循 DDD 设计原则，以下是核心概念的实现：

#### 聚合根 (Aggregate Root)

```java
@Data
@NoArgsConstructor
@ToString(exclude = {"password"})  // Lombok注解
public class User {
    private UserId userId;           // 值对象
    private Username username;       // 值对象
    private Password password;       // 值对象
    private Email email;            // 值对象

    // 业务方法
    public boolean validatePassword(String rawPassword) {
        return this.password.matches(rawPassword);
    }

    public void updateLastLogin(String loginIp) {
        this.lastLoginTime = LocalDateTime.now();
        this.lastLoginIp = loginIp;
        this.updateTime = LocalDateTime.now();
    }
}
```

#### 值对象 (Value Object)

```java
@Value  // Lombok注解，不可变对象
public class Username {
    String value;

    public Username(String value) {
        if (!StringUtils.hasText(value) || value.length() < 3) {
            throw new IllegalArgumentException("用户名不能为空且长度不能少于3位");
        }
        this.value = value;
    }
}
```

#### 仓储模式 (Repository Pattern)

```java
// 领域层接口
public interface UserRepository {
    User save(User user);                      // 领域对象
    Optional<User> findById(UserId userId);
    Optional<User> findByUsername(Username username);
}

// 基础设施层实现
@Repository
@RequiredArgsConstructor
public class UserRepositoryImpl implements UserRepository {
    private final UserMapper userMapper;      // MyBatis-Plus
    private final UserConverter userConverter; // 转换器

    public User save(User user) {
        UserPO userPO = userConverter.toPO(user);  // 领域对象转持久化对象

        if (userPO.getId() == null) {
            userMapper.insert(userPO);             // MyBatis-Plus自动填充
        } else {
            // 乐观锁更新
            int updateResult = userMapper.updateById(userPO);
            if (updateResult == 0) {
                throw new RuntimeException("数据已被其他用户修改，请刷新后重试");
            }
        }

        return userConverter.toDomain(userPO);     // 持久化对象转领域对象
    }
}
```

#### 持久化对象 (PO)

```java
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString(exclude = {"password"})
@TableName("sys_user")
public class UserPO {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("username")
    private String username;

    @Version                    // 乐观锁
    @TableField("version")
    private Integer version;

    @TableLogic                 // 逻辑删除
    @TableField(value = "deleted", fill = FieldFill.INSERT)
    private Integer deleted;
}
```

### MyBatis-Plus 最佳实践

#### 1. 配置乐观锁插件

```java
@Configuration
public class MyBatisPlusConfig {
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        return interceptor;
    }
}
```

#### 2. 使用 LambdaQueryWrapper 替代 XML

```java
// 替代复杂的XML查询
public Optional<User> findByUsername(Username username) {
    LambdaQueryWrapper<UserPO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(UserPO::getUsername, username.getValue());
    // @TableLogic 自动处理逻辑删除，无需手动添加 deleted=0

    UserPO userPO = userMapper.selectOne(queryWrapper);
    return userPO != null ? Optional.of(userConverter.toDomain(userPO)) : Optional.empty();
}
```

### Lombok 使用规范

#### 实体类注解

```java
@Data                                    // 生成getter/setter/toString/equals/hashCode
@NoArgsConstructor                       // 生成无参构造器
@AllArgsConstructor                      // 生成全参构造器
@ToString(exclude = {"password"})        // toString排除敏感字段
@EqualsAndHashCode(callSuper = false)    // equals/hashCode不调用父类
@Accessors(chain = true)                 // 链式调用
```

#### 服务类注解

```java
@Service
@RequiredArgsConstructor    // 生成final字段的构造器，替代@Autowired
public class UserAccountServiceImpl implements UserAccountService {
    private final UserRepository userRepository;  // final字段
    private final RedisService redisService;
}
```

### 代码规范

#### 包命名规范

- **API 层**：`com.foxit.crm.modules.{module}.api.{controller|dto}`
- **应用层**：`com.foxit.crm.modules.{module}.application.{service|impl}`
- **领域层**：`com.foxit.crm.modules.{module}.domain.{model|repository}`
- **基础设施层**：`com.foxit.crm.modules.{module}.infrastructure.{persistence}`

#### 类命名规范

| 类型       | 命名规则                  | 示例                               |
| ---------- | ------------------------- | ---------------------------------- |
| 聚合根     | 业务名词                  | `User`                             |
| 值对象     | 业务名词                  | `UserId`, `Username`               |
| 持久化对象 | 业务名词+PO               | `UserPO`                           |
| 传输对象   | 业务名词+用途+DTO         | `LoginRequest`, `UserInfoResponse` |
| 控制器     | 业务名词+Controller       | `UserAccountController`            |
| 服务       | 业务名词+Service(Impl)    | `UserAccountService`               |
| 仓储       | 业务名词+Repository(Impl) | `UserRepository`                   |

#### 方法命名规范

- **查询**：`find`, `get`, `query`
- **创建**：`create`, `save`, `add`
- **更新**：`update`, `modify`, `change`
- **删除**：`delete`, `remove`
- **验证**：`validate`, `check`, `verify`
- **业务操作**：动词开头，如 `updateLastLogin`, `resetPassword`

### API 设计规范

#### RESTful API 设计

```java
@RestController
@RequestMapping("/admin/users")
@RequiredArgsConstructor
@PreAuthorize("hasAuthority('ADMIN')")    // 类级别权限控制
public class UserManagementController {

    @PostMapping("/create")
    @OperationLog(value = "创建用户", operation = "CREATE_USER")
    public Result<String> createUser(@Valid @RequestBody RegisterRequest request) {
        // 创建用户逻辑
        return Result.success("用户创建成功");
    }

    @GetMapping
    @OperationLog(value = "查询用户列表", operation = "LIST_USERS")
    public Result<PageResult<UserInfoResponse>> getUsers(
        @RequestParam(defaultValue = "1") Integer page,
        @RequestParam(defaultValue = "10") Integer size,
        @RequestParam(required = false) String keyword) {
        // 分页查询逻辑
        return Result.success(pageResult);
    }
}
```

#### 统一响应格式

```java
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Result<T> {
    private Integer code;
    private String message;
    private T data;
    private LocalDateTime timestamp;

    public static <T> Result<T> success() {
        return new Result<>(200, "操作成功", null, LocalDateTime.now());
    }

    public static <T> Result<T> success(T data) {
        return new Result<>(200, "操作成功", data, LocalDateTime.now());
    }

    public static <T> Result<T> error(String message) {
        return new Result<>(500, message, null, LocalDateTime.now());
    }
}
```

#### 请求验证

```java
@Data
@NoArgsConstructor
public class LoginRequest {
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度应在3-50之间")
    private String username;

    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 100, message = "密码长度应在6-100之间")
    private String password;
}
```

### 异常处理

```java
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(BusinessException.class)
    public Result<Void> handleBusinessException(BusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        return Result.error(e.getMessage());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<Void> handleValidationException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors()
            .stream()
            .map(FieldError::getDefaultMessage)
            .collect(Collectors.joining(", "));
        log.warn("参数验证失败: {}", message);
        return Result.error("参数验证失败: " + message);
    }

    @ExceptionHandler(AccessDeniedException.class)
    public Result<Void> handleAccessDeniedException(AccessDeniedException e) {
        log.warn("权限不足: {}", e.getMessage());
        return Result.error("权限不足，无法访问该资源");
    }

    @ExceptionHandler(Exception.class)
    public Result<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return Result.error("系统异常，请稍后重试");
    }
}
```

## 🧪 测试

### 单元测试

```bash
# 运行所有测试
mvn test

# 运行指定测试类
mvn test -Dtest=UserServiceTest

# 生成测试报告
mvn surefire-report:report
```

### 集成测试

```bash
# 运行集成测试
mvn test -Dtest=*IntegrationTest

# 使用测试配置文件
mvn test -Dspring.profiles.active=test
```

### API 测试

使用 Smart-doc 生成的 API 文档进行接口测试：

```bash
# 生成 API 文档
mvn smart-doc:html

# 文档位置：target/smart-doc/
```

## 📊 监控与运维

### 应用监控

Spring Boot Actuator 提供的监控端点：

- `/actuator/health` - 健康检查
- `/actuator/info` - 应用信息
- `/actuator/metrics` - 应用指标
- `/actuator/env` - 环境信息

### 日志配置

```yaml
logging:
  level:
    com.foxit.crm: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/scrm-next.log
```

## 🚀 部署

### Docker 部署

```dockerfile
FROM openjdk:21-jdk-slim

WORKDIR /app

COPY target/scrm-next-backend-*.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]
```

### 构建镜像

```bash
# 构建应用
mvn clean package -DskipTests

# 构建 Docker 镜像
docker build -t scrm-next-backend:latest .

# 运行容器
docker run -d -p 8080:8080 --name scrm-next-backend scrm-next-backend:latest
```

## 📞 支持与贡献

### 🤝 贡献指南

我们欢迎所有形式的贡献！请遵循以下步骤：

1. **Fork 项目**
2. **创建特性分支** (`git checkout -b feature/AmazingFeature`)
3. **遵循代码规范** (Checkstyle + SonarQube)
4. **编写测试** (单元测试覆盖率 > 80%)
5. **提交更改** (`git commit -m 'Add some AmazingFeature'`)
6. **推送到分支** (`git push origin feature/AmazingFeature`)
7. **开启 Pull Request**

### � 提交规范

使用 [Conventional Commits](https://conventionalcommits.org/) 规范：

```bash
# 功能开发
git commit -m "feat(user): add user management APIs"

# 问题修复
git commit -m "fix(auth): resolve JWT token validation issue"

# 文档更新
git commit -m "docs(readme): update API documentation"

# 代码重构
git commit -m "refactor(service): optimize user service logic"

# 测试添加
git commit -m "test(user): add unit tests for user repository"
```

### 🔍 代码审查

Pull Request 需要通过以下检查：

- [ ] **编译通过** - `mvn clean compile`
- [ ] **测试通过** - `mvn test`
- [ ] **代码规范** - Checkstyle 检查
- [ ] **安全扫描** - SonarQube 检查
- [ ] **文档更新** - 相关文档同步更新
- [ ] **功能验证** - 手动测试核心功能

### 🐛 问题反馈

如果你发现了问题，请按以下模板提交 Issue：

```markdown
## 问题描述

简洁清晰地描述问题

## 复现步骤

1. 进入 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 出现错误

## 预期行为

描述你期望发生什么

## 实际行为

描述实际发生了什么

## 环境信息

- OS: [e.g. Ubuntu 20.04]
- Java: [e.g. OpenJDK 21]
- MySQL: [e.g. 8.0.33]
- Redis: [e.g. 7.0.8]

## 附加信息

添加其他有助于解决问题的信息
```

### 📚 相关资源

- **项目文档**：[/docs](../docs/)
- **API 文档**：启动后访问 `/doc.html`
- **代码规范**：[Google Java Style Guide](https://google.github.io/styleguide/javaguide.html)
- **DDD 指南**：[Domain-Driven Design Reference](https://domainlanguage.com/ddd/reference/)
- **Spring Boot 文档**：[Spring Boot Reference](https://docs.spring.io/spring-boot/docs/current/reference/htmlsingle/)

### 📞 联系方式

- **技术支持**：<EMAIL>
- **项目维护者**：[@veikin](mailto:<EMAIL>)
- **问题反馈**：[GitHub Issues](https://github.com/your-org/scrm-next/issues)

### 🏆 贡献者

感谢所有为项目做出贡献的开发者！

<a href="https://github.com/your-org/scrm-next/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=your-org/scrm-next" />
</a>

### 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情。

---

<div align="center">
  <p>
    <strong>SCRM-Next Backend</strong> - 企业级数据分析平台后端服务
  </p>
  <p>
    Made with ❤️ by <a href="https://www.foxit.com">Foxit</a>
  </p>
  <p>
    <img src="https://img.shields.io/badge/Java-21-orange.svg" alt="Java 21">
    <img src="https://img.shields.io/badge/Spring%20Boot-3.4.6-green.svg" alt="Spring Boot">
    <img src="https://img.shields.io/badge/MyBatis%20Plus-3.5.12-blue.svg" alt="MyBatis Plus">
    <img src="https://img.shields.io/badge/License-MIT-yellow.svg" alt="License">
  </p>
</div>

## 🔐 安全架构

### 权限控制体系

SCRM-Next 采用分层的权限控制体系，确保系统安全：

```
┌─────────────────────────────────────────┐
│                前端请求                  │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│          JWT 认证过滤器                 │
│  • 验证 Token 有效性                   │
│  • 检查 Redis 中是否存在               │
│  • 提取用户权限信息                    │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│          Spring Security                │
│  • 路径权限：/auth/login (允许)        │
│  • 管理员权限：/admin/** (ADMIN)       │
│  • 认证权限：其他接口 (USER)            │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│          方法级权限控制                  │
│  • @PreAuthorize("hasAuthority('ADMIN')")│
│  • @PreAuthorize("isAuthenticated()")    │
└─────────────────────────────────────────┘
```

### 接口权限矩阵

| 接口路径                               | 权限要求 | 说明                     |
| -------------------------------------- | -------- | ------------------------ |
| `POST /auth/login`                     | 无       | 唯一允许未认证的业务接口 |
| `POST /auth/logout`                    | 认证     | 需要有效 token           |
| `GET /auth/user/info`                  | 认证     | 获取当前用户信息         |
| `POST /admin/users/create`             | ADMIN    | 管理员创建用户           |
| `PUT /admin/users/{id}/disable`        | ADMIN    | 管理员禁用用户           |
| `PUT /admin/users/{id}/enable`         | ADMIN    | 管理员启用用户           |
| `PUT /admin/users/{id}/reset-password` | ADMIN    | 管理员重置密码           |
| `GET /admin/users`                     | ADMIN    | 管理员查看用户列表       |

### 用户类型权限

| 用户类型   | userType | 权限        | 可访问接口         |
| ---------- | -------- | ----------- | ------------------ |
| 超级管理员 | 1        | ADMIN, USER | 所有接口           |
| 普通用户   | 2        | USER        | 认证接口，业务接口 |

### JWT Token 结构

```json
{
  "sub": "admin",
  "userId": 1,
  "username": "admin",
  "userType": 1,
  "iat": 1640995200,
  "exp": 1641081600
}
```

### 安全特性

- **密码加密**：使用 BCrypt 算法加密存储
- **Token 管理**：Redis 存储 Token，支持登出失效
- **乐观锁**：数据更新并发控制，防止数据冲突
- **配置加密**：Jasypt 加密敏感配置信息
- **权限隔离**：严格的角色权限控制

## 📈 项目状态

### 🎯 开发进度

- [x] **阶段一**：基础架构与核心框架 (100%)
- [x] **阶段二**：系统管理与权限体系 (100%)
- [x] **阶段三**：数据看板核心功能 (100%)
- [x] **阶段四**：用户分析模块 (100%)
- [x] **阶段五**：行为分析模块 (100%)
- [ ] **阶段六**：商业分析模块 (规划中)
- [ ] **阶段七**：优化与上线 (规划中)

### 📊 代码统计

- **Java 类总数**：300+ 个
- **接口总数**：156 个 (全部已完成)
- **控制器数量**：16 个 (涵盖所有业务模块)
- **单元测试**：150+ 个
- **代码行数**：30,000+ 行
- **模块覆盖**：5 个核心业务模块全部完成
- **架构层次**：完整的 DDD 四层架构实现
- **数据库支持**：MySQL + Redis + ClickHouse 三重数据架构

### 🏆 技术亮点

- **DDD 架构**：完整的领域驱动设计实现
- **ClickHouse 集成**：大数据量行为分析支持
- **双重数据源**：真实数据 + 模拟数据自动降级
- **多层缓存**：Spring Cache + Redisson 双重缓存策略
- **数据权限**：行级、列级权限控制
- **配置加密**：Jasypt 敏感配置加密
- **API 文档**：Smart-doc 自动生成文档
- **乐观锁**：MyBatis-Plus 并发控制
- **滚动重置**：前端路由切换自动滚动到顶部
- **公共接口**：支持无需认证的数据分析接口

---

<div align="center">
  <p>
    <strong>SCRM-Next Backend</strong> - 企业级数据分析平台后端服务
  </p>
  <p>
    Made with ❤️ by <a href="https://www.foxit.com">Foxit</a>
  </p>
  <p>
    <img src="https://img.shields.io/badge/Java-21-orange.svg" alt="Java 21">
    <img src="https://img.shields.io/badge/Spring%20Boot-3.4.6-green.svg" alt="Spring Boot">
    <img src="https://img.shields.io/badge/MyBatis%20Plus-3.5.12-blue.svg" alt="MyBatis Plus">
    <img src="https://img.shields.io/badge/License-MIT-yellow.svg" alt="License">
  </p>
</div>
