package com.foxit.crm.modules.dashboard.test;

import com.foxit.crm.modules.dashboard.domain.repository.DashboardRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Dashboard 仓储配置测试
 * 验证 Bean 注入配置是否正确
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("dev")
public class DashboardRepositoryConfigTest {

    @Autowired
    private DashboardRepository dashboardRepository; // 默认注入（应该是 Real 实现）

    @Autowired
    @Qualifier("dashboardRepositoryReal")
    private DashboardRepository realRepository; // 真实数据实现

    @Autowired
    @Qualifier("dashboardRepositoryMock")
    private DashboardRepository mockRepository; // 模拟数据实现

    @Test
    public void testBeanConfiguration() {
        log.info("=== Dashboard Repository Bean 配置测试 ===");

        // 验证 Bean 不为空
        assertNotNull(dashboardRepository, "默认 DashboardRepository 不应为空");
        assertNotNull(realRepository, "Real DashboardRepository 不应为空");
        assertNotNull(mockRepository, "Mock DashboardRepository 不应为空");

        // 验证默认注入是真实数据实现
        assertEquals(realRepository.getClass(), dashboardRepository.getClass(),
                "默认注入应该是真实数据实现");

        // 验证实现类型
        assertTrue(dashboardRepository.getClass().getSimpleName().contains("Real"),
                "默认实现应该包含 'Real'");
        assertTrue(mockRepository.getClass().getSimpleName().contains("Impl") &&
                !mockRepository.getClass().getSimpleName().contains("Real"),
                "Mock实现应该不包含 'Real'");

        log.info("默认注入实现: {}", dashboardRepository.getClass().getSimpleName());
        log.info("真实数据实现: {}", realRepository.getClass().getSimpleName());
        log.info("模拟数据实现: {}", mockRepository.getClass().getSimpleName());
        log.info("=== Bean 配置测试通过 ===");
    }

    @Test
    public void testRepositoryInstancesAreDifferent() {
        log.info("=== Repository 实例独立性测试 ===");

        // 确保真实实现和模拟实现是不同的实例
        assertNotSame(realRepository, mockRepository, "真实实现和模拟实现应该是不同的实例");

        // 确保默认注入和真实实现是同一个实例（由于 @Primary）
        assertSame(dashboardRepository, realRepository, "默认注入和真实实现应该是同一个实例");

        log.info("实例独立性测试通过");
    }
}
