package com.foxit.crm.common.cache;

import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 缓存配置类
 * 配置多级缓存策略和不同业务场景的缓存规则
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Configuration
public class CacheConfig {

        /**
         * 缓存名称常量
         * 注意：主缓存管理器已在RedissonConfig中定义
         */
        public static final String DASHBOARD_CACHE = "dashboard";
        public static final String USER_CACHE = "user";
        public static final String ROLE_CACHE = "role";
        public static final String PERMISSION_CACHE = "permission";
        public static final String PRODUCT_LINE_CACHE = "productLine";
        public static final String CONFIG_CACHE = "config";
        public static final String OPERATION_LOG_CACHE = "operationLog";

        /**
         * 本地缓存管理器（用于热点数据的二级缓存）
         */
        @Bean("localCacheManager")
        public CacheManager localCacheManager() {
                return new org.springframework.cache.concurrent.ConcurrentMapCacheManager(
                                "localUser", "localRole", "localPermission", "localConfig");
        }
}
