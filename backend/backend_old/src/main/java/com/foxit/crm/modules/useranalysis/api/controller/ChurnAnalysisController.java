package com.foxit.crm.modules.useranalysis.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.common.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.*;

/**
 * 用户流失分析控制器
 * 提供用户流失分析相关的REST API接口
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Slf4j
@RestController
@RequestMapping("/user/churn")
@RequiredArgsConstructor
@Tag(name = "用户流失分析", description = "用户流失分析相关接口")
public class ChurnAnalysisController {

    @Operation(summary = "获取用户流失总览", description = "获取用户流失分析的总览数据")
    @GetMapping("/overview")
    @PreAuthorize("hasPermission('user:churn', 'READ')")
    public Result<Map<String, Object>> getChurnOverview(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            // 模拟流失分析总览数据
            Map<String, Object> response = new HashMap<>();
            
            // 核心指标
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("totalUsers", 15680);
            metrics.put("churnedUsers", 1256);
            metrics.put("churnRate", 8.01);
            metrics.put("avgLifetime", 156.5);
            metrics.put("riskUsers", 892);
            metrics.put("preventionRate", 23.5);
            response.put("metrics", metrics);
            
            // 流失趋势数据
            List<Map<String, Object>> trendData = new ArrayList<>();
            for (int i = 0; i < 30; i++) {
                Map<String, Object> trend = new HashMap<>();
                trend.put("date", startDate.plusDays(i).toString());
                trend.put("churnCount", 35 + (int)(Math.random() * 20));
                trend.put("churnRate", 7.5 + Math.random() * 2);
                trend.put("newUsers", 80 + (int)(Math.random() * 30));
                trendData.add(trend);
            }
            response.put("trendData", trendData);
            
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取用户流失总览失败", e);
            return Result.error("获取用户流失总览失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取流失原因分析", description = "分析用户流失的主要原因")
    @GetMapping("/reasons")
    @PreAuthorize("hasPermission('user:churn', 'READ')")
    public Result<Map<String, Object>> getChurnReasons(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            // 模拟流失原因分析数据
            Map<String, Object> response = new HashMap<>();
            
            // 流失原因分布
            List<Map<String, Object>> reasons = new ArrayList<>();
            String[] reasonNames = {"功能不满足需求", "价格因素", "竞品吸引", "使用体验差", "技术问题", "服务不满意", "其他"};
            double[] percentages = {28.5, 22.3, 18.7, 12.4, 8.9, 6.2, 3.0};
            
            for (int i = 0; i < reasonNames.length; i++) {
                Map<String, Object> reason = new HashMap<>();
                reason.put("reason", reasonNames[i]);
                reason.put("percentage", percentages[i]);
                reason.put("count", (int)(percentages[i] * 12.56));
                reason.put("trend", i % 3 == 0 ? "up" : (i % 3 == 1 ? "down" : "stable"));
                reasons.add(reason);
            }
            response.put("reasons", reasons);
            
            // 不同产品线的流失原因对比
            List<Map<String, Object>> productComparison = new ArrayList<>();
            String[] products = {"福昕阅读器GA版", "PDF编辑器专业版", "PDF365在线服务"};
            for (String product : products) {
                Map<String, Object> productData = new HashMap<>();
                productData.put("productName", product);
                productData.put("mainReason", reasonNames[(int)(Math.random() * reasonNames.length)]);
                productData.put("churnRate", 6.5 + Math.random() * 4);
                productComparison.add(productData);
            }
            response.put("productComparison", productComparison);
            
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取流失原因分析失败", e);
            return Result.error("获取流失原因分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取流失预警分析", description = "识别有流失风险的用户")
    @GetMapping("/risk-prediction")
    @PreAuthorize("hasPermission('user:churn', 'READ')")
    public Result<Map<String, Object>> getRiskPrediction(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds,
            @Parameter(description = "风险等级") @RequestParam(required = false) String riskLevel) {

        try {
            // 模拟风险预测数据
            Map<String, Object> response = new HashMap<>();
            
            // 风险用户统计
            Map<String, Object> riskStats = new HashMap<>();
            riskStats.put("highRisk", 156);
            riskStats.put("mediumRisk", 324);
            riskStats.put("lowRisk", 412);
            riskStats.put("totalRisk", 892);
            response.put("riskStats", riskStats);
            
            // 风险用户列表
            List<Map<String, Object>> riskUsers = new ArrayList<>();
            String[] riskLevels = {"高", "中", "低"};
            String[] riskReasons = {"长期未登录", "使用频率下降", "功能使用减少", "反馈负面", "支付逾期"};
            
            for (int i = 0; i < 20; i++) {
                Map<String, Object> user = new HashMap<>();
                user.put("userId", "U" + String.format("%06d", 100000 + i));
                user.put("userName", "用户" + (i + 1));
                user.put("riskLevel", riskLevels[i % 3]);
                user.put("riskScore", 85 - i * 2);
                user.put("lastActiveDate", startDate.minusDays(i + 1).toString());
                user.put("riskReason", riskReasons[i % riskReasons.length]);
                user.put("productLine", "福昕阅读器GA版");
                riskUsers.add(user);
            }
            response.put("riskUsers", riskUsers);
            
            // 预测准确率
            Map<String, Object> accuracy = new HashMap<>();
            accuracy.put("precision", 78.5);
            accuracy.put("recall", 82.3);
            accuracy.put("f1Score", 80.3);
            response.put("accuracy", accuracy);
            
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取流失预警分析失败", e);
            return Result.error("获取流失预警分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取用户生命周期分析", description = "分析用户的生命周期阶段")
    @GetMapping("/lifecycle")
    @PreAuthorize("hasPermission('user:churn', 'READ')")
    public Result<Map<String, Object>> getLifecycleAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            // 模拟生命周期分析数据
            Map<String, Object> response = new HashMap<>();
            
            // 生命周期阶段分布
            List<Map<String, Object>> stages = new ArrayList<>();
            String[] stageNames = {"新用户", "成长期", "成熟期", "衰退期", "流失期"};
            int[] counts = {1250, 3200, 5800, 2100, 890};
            String[] colors = {"#52c41a", "#1890ff", "#faad14", "#ff7a45", "#ff4d4f"};
            
            for (int i = 0; i < stageNames.length; i++) {
                Map<String, Object> stage = new HashMap<>();
                stage.put("stage", stageNames[i]);
                stage.put("count", counts[i]);
                stage.put("percentage", counts[i] / 132.4);
                stage.put("color", colors[i]);
                stages.add(stage);
            }
            response.put("stages", stages);
            
            // 生命周期转化漏斗
            List<Map<String, Object>> funnel = new ArrayList<>();
            for (int i = 0; i < stageNames.length - 1; i++) {
                Map<String, Object> conversion = new HashMap<>();
                conversion.put("from", stageNames[i]);
                conversion.put("to", stageNames[i + 1]);
                conversion.put("rate", 85 - i * 15);
                conversion.put("count", counts[i] * (85 - i * 15) / 100);
                funnel.add(conversion);
            }
            response.put("funnel", funnel);
            
            // 平均生命周期时长
            Map<String, Object> avgLifetime = new HashMap<>();
            avgLifetime.put("overall", 156.5);
            avgLifetime.put("byProduct", Arrays.asList(
                Map.of("product", "福昕阅读器GA版", "lifetime", 180.2),
                Map.of("product", "PDF编辑器专业版", "lifetime", 145.8),
                Map.of("product", "PDF365在线服务", "lifetime", 132.6)
            ));
            response.put("avgLifetime", avgLifetime);
            
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取用户生命周期分析失败", e);
            return Result.error("获取用户生命周期分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取挽回策略建议", description = "基于流失分析提供用户挽回策略建议")
    @GetMapping("/retention-strategies")
    @PreAuthorize("hasPermission('user:churn', 'READ')")
    public Result<List<Map<String, Object>>> getRetentionStrategies(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            // 模拟挽回策略建议
            List<Map<String, Object>> strategies = new ArrayList<>();
            
            Map<String, Object> strategy1 = new HashMap<>();
            strategy1.put("type", "个性化推荐");
            strategy1.put("title", "基于使用行为的功能推荐");
            strategy1.put("description", "向用户推荐他们可能感兴趣但未使用的功能");
            strategy1.put("targetGroup", "成熟期用户");
            strategy1.put("expectedEffect", "提升活跃度15-20%");
            strategy1.put("priority", "高");
            strategies.add(strategy1);
            
            Map<String, Object> strategy2 = new HashMap<>();
            strategy2.put("type", "优惠激励");
            strategy2.put("title", "流失风险用户专属优惠");
            strategy2.put("description", "为高风险用户提供续费优惠或功能升级");
            strategy2.put("targetGroup", "高风险用户");
            strategy2.put("expectedEffect", "挽回率提升25%");
            strategy2.put("priority", "高");
            strategies.add(strategy2);
            
            Map<String, Object> strategy3 = new HashMap<>();
            strategy3.put("type", "用户教育");
            strategy3.put("title", "功能使用指导和培训");
            strategy3.put("description", "提供个性化的功能使用指导，提升用户价值感知");
            strategy3.put("targetGroup", "新用户和成长期用户");
            strategy3.put("expectedEffect", "留存率提升10%");
            strategy3.put("priority", "中");
            strategies.add(strategy3);
            
            Map<String, Object> strategy4 = new HashMap<>();
            strategy4.put("type", "产品优化");
            strategy4.put("title", "基于反馈的产品改进");
            strategy4.put("description", "针对主要流失原因进行产品功能优化");
            strategy4.put("targetGroup", "所有用户");
            strategy4.put("expectedEffect", "整体流失率降低5%");
            strategy4.put("priority", "中");
            strategies.add(strategy4);
            
            return Result.success(strategies);

        } catch (Exception e) {
            log.error("获取挽回策略建议失败", e);
            return Result.error("获取挽回策略建议失败: " + e.getMessage());
        }
    }
}
