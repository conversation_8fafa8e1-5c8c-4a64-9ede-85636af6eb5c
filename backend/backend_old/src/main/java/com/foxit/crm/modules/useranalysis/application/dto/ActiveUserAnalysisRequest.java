package com.foxit.crm.modules.useranalysis.application.dto;

import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 活跃用户分析请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Builder
@EqualsAndHashCode
public class ActiveUserAnalysisRequest {

    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @NotNull(message = "结束日期不能为空")
    private LocalDate endDate;

    /**
     * 时间粒度
     */
    private TimeRange.TimeGranularity granularity;

    /**
     * 产品线ID列表
     */
    private List<Long> productLineIds;

    /**
     * 分析类型
     */
    private String analysisType;

    /**
     * 数据权限范围
     */
    @NotNull(message = "数据权限范围不能为空")
    private String dataScope;

    /**
     * 用户ID（用于权限控制）
     */
    private Long userId;

    /**
     * 是否包含历史对比
     */
    private Boolean includeComparison;

    /**
     * 是否包含详细数据
     */
    private Boolean includeDetails;

    /**
     * 导出格式
     */
    private String exportFormat;

    /**
     * 生成缓存键
     */
    public String cacheKey() {
        return String.format("activeUser_%s_%s_%s_%s_%s",
                startDate,
                endDate,
                granularity != null ? granularity.name() : "DAY",
                productLineIds != null ? String.join(",", productLineIds.stream().map(String::valueOf).toList())
                        : "all",
                dataScope);
    }

    /**
     * 创建最近7天的请求
     */
    public static ActiveUserAnalysisRequest ofLast7Days(String dataScope) {
        return ActiveUserAnalysisRequest.builder()
                .startDate(LocalDate.now().minusDays(6))
                .endDate(LocalDate.now())
                .granularity(TimeRange.TimeGranularity.DAY)
                .dataScope(dataScope)
                .includeComparison(true)
                .includeDetails(false)
                .build();
    }

    /**
     * 创建最近30天的请求
     */
    public static ActiveUserAnalysisRequest ofLast30Days(String dataScope) {
        return ActiveUserAnalysisRequest.builder()
                .startDate(LocalDate.now().minusDays(29))
                .endDate(LocalDate.now())
                .granularity(TimeRange.TimeGranularity.DAY)
                .dataScope(dataScope)
                .includeComparison(true)
                .includeDetails(false)
                .build();
    }

    /**
     * 创建当前月的请求
     */
    public static ActiveUserAnalysisRequest ofCurrentMonth(String dataScope) {
        LocalDate now = LocalDate.now();
        return ActiveUserAnalysisRequest.builder()
                .startDate(now.withDayOfMonth(1))
                .endDate(now.withDayOfMonth(now.lengthOfMonth()))
                .granularity(TimeRange.TimeGranularity.DAY)
                .dataScope(dataScope)
                .includeComparison(true)
                .includeDetails(false)
                .build();
    }

    /**
     * 创建产品线对比请求
     */
    public static ActiveUserAnalysisRequest ofProductLineComparison(List<Long> productLineIds, String dataScope) {
        return ActiveUserAnalysisRequest.builder()
                .startDate(LocalDate.now().minusDays(29))
                .endDate(LocalDate.now())
                .granularity(TimeRange.TimeGranularity.DAY)
                .productLineIds(productLineIds)
                .dataScope(dataScope)
                .analysisType("comparison")
                .includeComparison(false)
                .includeDetails(true)
                .build();
    }
}
