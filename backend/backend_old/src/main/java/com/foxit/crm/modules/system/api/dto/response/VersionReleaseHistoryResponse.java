package com.foxit.crm.modules.system.api.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 版本发布历史响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
public class VersionReleaseHistoryResponse {

    /**
     * 历史记录ID
     */
    private Long id;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 操作类型：1-创建，2-更新，3-发布，4-撤回，5-废弃
     */
    private Integer actionType;

    /**
     * 操作类型名称
     */
    private String actionTypeName;

    /**
     * 原状态
     */
    private Integer fromStatus;

    /**
     * 原状态名称
     */
    private String fromStatusName;

    /**
     * 目标状态
     */
    private Integer toStatus;

    /**
     * 目标状态名称
     */
    private String toStatusName;

    /**
     * 操作原因
     */
    private String actionReason;

    /**
     * 操作人ID
     */
    private Long actionBy;

    /**
     * 操作人姓名
     */
    private String actionByName;

    /**
     * 操作时间
     */
    private LocalDateTime actionTime;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 操作描述
     */
    private String actionDescription;

    /**
     * 风险级别
     */
    private String riskLevel;

    /**
     * 是否重要操作
     */
    private Boolean isImportantAction;

    /**
     * 备注
     */
    private String remark;
}
