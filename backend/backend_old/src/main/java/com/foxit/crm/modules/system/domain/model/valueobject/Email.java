package com.foxit.crm.modules.system.domain.model.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.util.regex.Pattern;

/**
 * 邮箱值对象
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Getter
@EqualsAndHashCode
@ToString
public class Email {

    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

    private final String value;

    public Email(String value) {
        if (value != null && !value.trim().isEmpty()) {
            String trimmedValue = value.trim();
            if (!EMAIL_PATTERN.matcher(trimmedValue).matches()) {
                throw new IllegalArgumentException("邮箱格式不正确");
            }
            if (trimmedValue.length() > 100) {
                throw new IllegalArgumentException("邮箱长度不能超过100个字符");
            }
            this.value = trimmedValue;
        } else {
            this.value = null;
        }
    }

    public boolean isEmpty() {
        return value == null || value.trim().isEmpty();
    }
}
