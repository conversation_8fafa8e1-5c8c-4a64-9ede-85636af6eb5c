package com.foxit.crm.modules.behavioranalysis.domain.repository;

import com.foxit.crm.modules.behavioranalysis.domain.entity.EventAnalysisAggregate;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 事件分析仓储接口
 * 定义事件分析数据访问的领域契约
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface EventAnalysisRepository {

    /**
     * 获取事件趋势分析数据
     *
     * @param timeRange 时间范围
     * @param eventIds 事件ID列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 事件分析聚合根
     */
    Optional<EventAnalysisAggregate> getEventTrendAnalysis(TimeRange timeRange, List<String> eventIds, 
                                                          List<Long> productLineIds, String dataScope);

    /**
     * 获取事件漏斗分析数据
     *
     * @param timeRange 时间范围
     * @param eventIds 事件ID列表（按漏斗顺序）
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 事件分析聚合根
     */
    Optional<EventAnalysisAggregate> getEventFunnelAnalysis(TimeRange timeRange, List<String> eventIds, 
                                                           List<Long> productLineIds, String dataScope);

    /**
     * 获取事件路径分析数据
     *
     * @param timeRange 时间范围
     * @param startEventIds 起始事件ID列表
     * @param endEventIds 结束事件ID列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 事件分析聚合根
     */
    Optional<EventAnalysisAggregate> getEventPathAnalysis(TimeRange timeRange, List<String> startEventIds, 
                                                         List<String> endEventIds, List<Long> productLineIds, String dataScope);

    /**
     * 获取事件对比分析数据
     *
     * @param timeRange 时间范围
     * @param eventIds 事件ID列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 事件分析聚合根
     */
    Optional<EventAnalysisAggregate> getEventComparisonAnalysis(TimeRange timeRange, List<String> eventIds, 
                                                               List<Long> productLineIds, String dataScope);

    /**
     * 获取事件属性分析数据
     *
     * @param timeRange 时间范围
     * @param eventId 事件ID
     * @param propertyNames 属性名称列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 事件分析聚合根
     */
    Optional<EventAnalysisAggregate> getEventPropertyAnalysis(TimeRange timeRange, String eventId, 
                                                             List<String> propertyNames, List<Long> productLineIds, String dataScope);

    /**
     * 获取实时事件统计
     *
     * @param eventIds 事件ID列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 实时统计数据
     */
    EventRealTimeStats getRealTimeEventStats(List<String> eventIds, List<Long> productLineIds, String dataScope);

    /**
     * 获取事件列表
     *
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 事件列表
     */
    List<EventInfo> getEventList(List<Long> productLineIds, String dataScope);

    /**
     * 保存事件分析结果
     *
     * @param aggregate 事件分析聚合根
     * @return 是否保存成功
     */
    boolean saveEventAnalysis(EventAnalysisAggregate aggregate);

    /**
     * 删除事件分析数据
     *
     * @param aggregateId 聚合根ID
     * @return 是否删除成功
     */
    boolean deleteEventAnalysis(String aggregateId);

    /**
     * 批量获取事件分析数据
     *
     * @param timeRanges 时间范围列表
     * @param eventIds 事件ID列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 事件分析聚合根列表
     */
    List<EventAnalysisAggregate> batchGetEventAnalysisData(List<TimeRange> timeRanges, List<String> eventIds, 
                                                          List<Long> productLineIds, String dataScope);

    /**
     * 删除过期数据
     *
     * @param beforeTime 过期时间点
     * @return 删除的数据条数
     */
    int deleteExpiredData(LocalDateTime beforeTime);

    /**
     * 事件实时统计数据
     */
    record EventRealTimeStats(
            Long totalEvents,
            Long uniqueUsers,
            Double avgEventsPerUser,
            List<EventStats> topEvents,
            LocalDateTime lastUpdateTime
    ) {}

    /**
     * 事件统计数据
     */
    record EventStats(
            String eventId,
            String eventName,
            Long eventCount,
            Long userCount,
            Double percentage
    ) {}

    /**
     * 事件信息
     */
    record EventInfo(
            String eventId,
            String eventName,
            String eventCategory,
            String description,
            List<String> properties,
            Long productLineId,
            String productLineName,
            Boolean isActive
    ) {}

    /**
     * 获取事件分析统计摘要
     *
     * @param timeRange 时间范围
     * @param dataScope 数据权限范围
     * @return 统计摘要数据
     */
    EventAnalysisSummary getEventAnalysisSummary(TimeRange timeRange, String dataScope);

    /**
     * 事件分析统计摘要
     */
    record EventAnalysisSummary(
            Long totalEvents,
            Long uniqueUsers,
            Long activeEvents,
            Double avgEventsPerUser,
            List<String> topEventCategories,
            LocalDateTime summaryTime
    ) {}
}
