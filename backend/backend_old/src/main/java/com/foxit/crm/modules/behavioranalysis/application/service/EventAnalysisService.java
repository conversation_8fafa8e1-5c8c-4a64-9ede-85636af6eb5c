package com.foxit.crm.modules.behavioranalysis.application.service;

import com.foxit.crm.modules.behavioranalysis.application.dto.EventAnalysisRequest;
import com.foxit.crm.modules.behavioranalysis.application.dto.EventAnalysisResponse;
import com.foxit.crm.modules.behavioranalysis.domain.entity.EventAnalysisAggregate;
import com.foxit.crm.modules.behavioranalysis.domain.repository.EventAnalysisRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 事件分析应用服务
 * 协调事件分析的业务流程
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EventAnalysisService {

    private final EventAnalysisRepository eventAnalysisRepository;

    /**
     * 获取事件趋势分析
     */
    public EventAnalysisResponse getEventTrendAnalysis(EventAnalysisRequest request) {
        log.info("获取事件趋势分析: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            // 验证请求参数
            validateEventAnalysisRequest(request);
            
            // 获取事件趋势分析数据
            Optional<EventAnalysisAggregate> aggregateOpt = eventAnalysisRepository.getEventTrendAnalysis(
                timeRange, request.getEventIds(), request.getProductLineIds(), request.getDataScope());
            
            if (aggregateOpt.isEmpty()) {
                log.warn("未找到事件趋势分析数据: timeRange={}, eventIds={}", timeRange, request.getEventIds());
                return EventAnalysisResponse.empty();
            }
            
            EventAnalysisAggregate aggregate = aggregateOpt.get();
            
            // 转换为响应DTO
            return convertToResponse(aggregate);
            
        } catch (Exception e) {
            log.error("获取事件趋势分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取事件趋势分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取事件漏斗分析
     */
    public EventAnalysisResponse getEventFunnelAnalysis(EventAnalysisRequest request) {
        log.info("获取事件漏斗分析: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            // 验证请求参数
            validateEventAnalysisRequest(request);
            
            // 获取事件漏斗分析数据
            Optional<EventAnalysisAggregate> aggregateOpt = eventAnalysisRepository.getEventFunnelAnalysis(
                timeRange, request.getEventIds(), request.getProductLineIds(), request.getDataScope());
            
            if (aggregateOpt.isEmpty()) {
                log.warn("未找到事件漏斗分析数据: timeRange={}, eventIds={}", timeRange, request.getEventIds());
                return EventAnalysisResponse.empty();
            }
            
            EventAnalysisAggregate aggregate = aggregateOpt.get();
            
            // 转换为响应DTO
            return convertToResponse(aggregate);
            
        } catch (Exception e) {
            log.error("获取事件漏斗分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取事件漏斗分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取事件路径分析
     */
    public EventAnalysisResponse getEventPathAnalysis(EventAnalysisRequest request) {
        log.info("获取事件路径分析: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            // 验证请求参数
            validateEventAnalysisRequest(request);
            
            // 获取事件路径分析数据
            Optional<EventAnalysisAggregate> aggregateOpt = eventAnalysisRepository.getEventPathAnalysis(
                timeRange, request.getStartEventIds(), request.getEndEventIds(), 
                request.getProductLineIds(), request.getDataScope());
            
            if (aggregateOpt.isEmpty()) {
                log.warn("未找到事件路径分析数据: timeRange={}, startEventIds={}, endEventIds={}", 
                    timeRange, request.getStartEventIds(), request.getEndEventIds());
                return EventAnalysisResponse.empty();
            }
            
            EventAnalysisAggregate aggregate = aggregateOpt.get();
            
            // 转换为响应DTO
            return convertToResponse(aggregate);
            
        } catch (Exception e) {
            log.error("获取事件路径分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取事件路径分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取事件对比分析
     */
    public EventAnalysisResponse getEventComparisonAnalysis(EventAnalysisRequest request) {
        log.info("获取事件对比分析: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            // 验证请求参数
            validateEventAnalysisRequest(request);
            
            // 获取事件对比分析数据
            Optional<EventAnalysisAggregate> aggregateOpt = eventAnalysisRepository.getEventComparisonAnalysis(
                timeRange, request.getEventIds(), request.getProductLineIds(), request.getDataScope());
            
            if (aggregateOpt.isEmpty()) {
                log.warn("未找到事件对比分析数据: timeRange={}, eventIds={}", timeRange, request.getEventIds());
                return EventAnalysisResponse.empty();
            }
            
            EventAnalysisAggregate aggregate = aggregateOpt.get();
            
            // 转换为响应DTO
            return convertToResponse(aggregate);
            
        } catch (Exception e) {
            log.error("获取事件对比分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取事件对比分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取事件属性分析
     */
    public EventAnalysisResponse getEventPropertyAnalysis(EventAnalysisRequest request) {
        log.info("获取事件属性分析: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            // 验证请求参数
            validateEventAnalysisRequest(request);
            
            // 获取事件属性分析数据
            Optional<EventAnalysisAggregate> aggregateOpt = eventAnalysisRepository.getEventPropertyAnalysis(
                timeRange, request.getEventId(), request.getPropertyNames(), 
                request.getProductLineIds(), request.getDataScope());
            
            if (aggregateOpt.isEmpty()) {
                log.warn("未找到事件属性分析数据: timeRange={}, eventId={}, propertyNames={}", 
                    timeRange, request.getEventId(), request.getPropertyNames());
                return EventAnalysisResponse.empty();
            }
            
            EventAnalysisAggregate aggregate = aggregateOpt.get();
            
            // 转换为响应DTO
            return convertToResponse(aggregate);
            
        } catch (Exception e) {
            log.error("获取事件属性分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取事件属性分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取事件列表
     */
    public List<EventAnalysisRepository.EventInfo> getEventList(List<Long> productLineIds, String dataScope) {
        log.info("获取事件列表: productLineIds={}, dataScope={}", productLineIds, dataScope);
        
        try {
            return eventAnalysisRepository.getEventList(productLineIds, dataScope);
        } catch (Exception e) {
            log.error("获取事件列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取事件列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取实时事件统计
     */
    public EventAnalysisRepository.EventRealTimeStats getRealTimeEventStats(List<String> eventIds, 
                                                                           List<Long> productLineIds, String dataScope) {
        log.info("获取实时事件统计: eventIds={}, productLineIds={}, dataScope={}", eventIds, productLineIds, dataScope);
        
        try {
            return eventAnalysisRepository.getRealTimeEventStats(eventIds, productLineIds, dataScope);
        } catch (Exception e) {
            log.error("获取实时事件统计失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取实时事件统计失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取事件分析统计摘要
     */
    public EventAnalysisRepository.EventAnalysisSummary getEventAnalysisSummary(EventAnalysisRequest request) {
        log.info("获取事件分析统计摘要: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            return eventAnalysisRepository.getEventAnalysisSummary(timeRange, request.getDataScope());
        } catch (Exception e) {
            log.error("获取事件分析统计摘要失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取事件分析统计摘要失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证事件分析请求参数
     */
    private void validateEventAnalysisRequest(EventAnalysisRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("事件分析请求不能为空");
        }
        
        if (request.getStartDate() == null || request.getEndDate() == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }
        
        if (request.getStartDate().isAfter(request.getEndDate())) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }
        
        if (request.getDataScope() == null || request.getDataScope().trim().isEmpty()) {
            throw new IllegalArgumentException("数据权限范围不能为空");
        }
    }

    /**
     * 转换聚合根为响应DTO
     */
    private EventAnalysisResponse convertToResponse(EventAnalysisAggregate aggregate) {
        // TODO: 实现具体的转换逻辑
        return EventAnalysisResponse.builder()
                .analysisId(aggregate.getId())
                .analysisType(aggregate.getAnalysisType().name())
                .timeRange(aggregate.getTimeRange())
                .coreMetrics(aggregate.getCoreMetrics())
                .trendData(aggregate.getTrendData())
                .funnelData(aggregate.getFunnelData())
                .pathData(aggregate.getPathData())
                .eventPropertyData(aggregate.getEventPropertyData())
                .lastUpdateTime(aggregate.getLastUpdateTime())
                .build();
    }
}
