package com.foxit.crm.modules.dashboard.application.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;
import java.util.Map;

/**
 * Dashboard配置请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DashboardConfigRequest {

    /**
     * 默认时间范围
     */
    private String defaultTimeRange;

    /**
     * 默认时间粒度
     */
    private String defaultTimeGranularity;

    /**
     * 默认选中的产品线列表
     */
    private List<Long> defaultProductLineIds;

    /**
     * 默认显示的指标列表
     */
    private List<String> defaultMetrics;

    /**
     * 图表配置
     */
    private Map<String, ChartConfigUpdate> chartConfigs;

    /**
     * 刷新间隔（分钟）
     */
    private Integer refreshInterval;

    /**
     * 是否启用实时更新
     */
    private Boolean enableRealTimeUpdate;

    /**
     * 图表配置更新内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ChartConfigUpdate {
        /**
         * 是否显示
         */
        private Boolean visible;

        /**
         * 显示顺序
         */
        private Integer order;

        /**
         * 图表高度
         */
        private Integer height;

        /**
         * 自定义配置
         */
        private Map<String, Object> customOptions;
    }
}
