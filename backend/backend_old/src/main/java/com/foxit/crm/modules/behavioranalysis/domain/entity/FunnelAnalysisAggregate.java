package com.foxit.crm.modules.behavioranalysis.domain.entity;

import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 漏斗分析聚合根
 * 封装漏斗分析的核心业务逻辑
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Getter
@Builder
public class FunnelAnalysisAggregate {

    /**
     * 聚合根ID
     */
    private final String id;

    /**
     * 分析类型：FUNNEL_CONVERSION, FUNNEL_COMPARISON, DROPOUT_ANALYSIS, COHORT_FUNNEL
     */
    private final FunnelAnalysisType analysisType;

    /**
     * 时间范围
     */
    private final TimeRange timeRange;

    /**
     * 产品线ID列表
     */
    private final List<Long> productLineIds;

    /**
     * 漏斗步骤列表
     */
    private final List<String> funnelSteps;

    /**
     * 核心指标
     */
    private final Map<String, MetricValue> coreMetrics;

    /**
     * 漏斗转化数据
     */
    private final Map<String, FunnelConversionData> conversionData;

    /**
     * 漏斗对比数据
     */
    private final Map<String, FunnelComparisonData> comparisonData;

    /**
     * 流失点分析数据
     */
    private final Map<String, DropoutAnalysisData> dropoutData;

    /**
     * 队列漏斗数据
     */
    private final Map<String, CohortFunnelData> cohortData;

    /**
     * 数据权限范围
     */
    private final String dataScope;

    /**
     * 最后更新时间
     */
    private final LocalDateTime lastUpdateTime;

    /**
     * 漏斗分析类型枚举
     */
    public enum FunnelAnalysisType {
        FUNNEL_CONVERSION("漏斗转化分析"),
        FUNNEL_COMPARISON("漏斗对比分析"),
        DROPOUT_ANALYSIS("流失点分析"),
        COHORT_FUNNEL("队列漏斗分析");

        private final String description;

        FunnelAnalysisType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 漏斗转化数据
     */
    @Getter
    @Builder
    public static class FunnelConversionData {
        private final String funnelId;
        private final String funnelName;
        private final List<FunnelStep> steps;
        private final Long totalUsers;
        private final Double overallConversionRate;
        private final Double avgTimeToConvert;
        private final List<ConversionTrend> trends;
        private final String funnelType;
    }

    /**
     * 漏斗步骤
     */
    @Getter
    @Builder
    public static class FunnelStep {
        private final String stepId;
        private final String stepName;
        private final Integer stepOrder;
        private final Long userCount;
        private final Double conversionRate;
        private final Double dropoffRate;
        private final Double avgTimeSpent;
        private final String stepType;
        private final Map<String, Object> stepProperties;
    }

    /**
     * 转化趋势
     */
    @Getter
    @Builder
    public static class ConversionTrend {
        private final String timeLabel;
        private final Long userCount;
        private final Double conversionRate;
        private final String trendDirection;
        private final Double changeRate;
    }

    /**
     * 漏斗对比数据
     */
    @Getter
    @Builder
    public static class FunnelComparisonData {
        private final String comparisonId;
        private final String comparisonName;
        private final List<FunnelComparisonItem> items;
        private final Map<String, Double> comparisonMetrics;
        private final String winnerFunnel;
        private final String comparisonSummary;
        private final LocalDateTime comparisonTime;
    }

    /**
     * 漏斗对比项
     */
    @Getter
    @Builder
    public static class FunnelComparisonItem {
        private final String funnelId;
        private final String funnelName;
        private final Long totalUsers;
        private final Double conversionRate;
        private final Double avgTimeToConvert;
        private final List<FunnelStep> steps;
        private final String performance;
    }

    /**
     * 流失点分析数据
     */
    @Getter
    @Builder
    public static class DropoutAnalysisData {
        private final String analysisId;
        private final String analysisName;
        private final List<DropoutPoint> dropoutPoints;
        private final List<DropoutReason> topReasons;
        private final Map<String, Long> dropoutDistribution;
        private final List<String> optimizationSuggestions;
        private final Double criticalDropoutRate;
    }

    /**
     * 流失点
     */
    @Getter
    @Builder
    public static class DropoutPoint {
        private final String stepId;
        private final String stepName;
        private final Long dropoutUsers;
        private final Double dropoutRate;
        private final String severity;
        private final List<String> possibleReasons;
        private final Double impactScore;
    }

    /**
     * 流失原因
     */
    @Getter
    @Builder
    public static class DropoutReason {
        private final String reasonId;
        private final String reasonName;
        private final String reasonCategory;
        private final Long affectedUsers;
        private final Double impactPercentage;
        private final String description;
        private final List<String> actionItems;
    }

    /**
     * 队列漏斗数据
     */
    @Getter
    @Builder
    public static class CohortFunnelData {
        private final String cohortId;
        private final String cohortName;
        private final String cohortPeriod;
        private final List<CohortStep> steps;
        private final Map<String, List<Double>> conversionMatrix;
        private final Double avgCohortConversion;
        private final List<String> insights;
    }

    /**
     * 队列步骤
     */
    @Getter
    @Builder
    public static class CohortStep {
        private final String stepId;
        private final String stepName;
        private final Integer stepOrder;
        private final Map<String, Long> cohortUserCounts;
        private final Map<String, Double> cohortConversionRates;
        private final String stepInsight;
    }

    /**
     * 生成聚合根ID
     */
    private static String generateId(String prefix, TimeRange timeRange) {
        return String.format("%s_%s_%s_%s", 
            prefix, 
            timeRange.getStartDate(), 
            timeRange.getEndDate(),
            UUID.randomUUID().toString().substring(0, 8));
    }

    /**
     * 获取核心指标
     */
    public MetricValue getCoreMetric(String key) {
        return coreMetrics != null ? coreMetrics.get(key) : null;
    }

    /**
     * 创建漏斗转化分析
     */
    public static FunnelAnalysisAggregate createFunnelConversionAnalysis(TimeRange timeRange, List<String> funnelSteps, 
                                                                        String dataScope) {
        return FunnelAnalysisAggregate.builder()
                .id(generateId("funnel_conversion", timeRange))
                .analysisType(FunnelAnalysisType.FUNNEL_CONVERSION)
                .timeRange(timeRange)
                .funnelSteps(funnelSteps)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建漏斗对比分析
     */
    public static FunnelAnalysisAggregate createFunnelComparisonAnalysis(TimeRange timeRange, List<String> funnelSteps, 
                                                                        String dataScope) {
        return FunnelAnalysisAggregate.builder()
                .id(generateId("funnel_comparison", timeRange))
                .analysisType(FunnelAnalysisType.FUNNEL_COMPARISON)
                .timeRange(timeRange)
                .funnelSteps(funnelSteps)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建流失点分析
     */
    public static FunnelAnalysisAggregate createDropoutAnalysis(TimeRange timeRange, List<String> funnelSteps, 
                                                               String dataScope) {
        return FunnelAnalysisAggregate.builder()
                .id(generateId("dropout_analysis", timeRange))
                .analysisType(FunnelAnalysisType.DROPOUT_ANALYSIS)
                .timeRange(timeRange)
                .funnelSteps(funnelSteps)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建队列漏斗分析
     */
    public static FunnelAnalysisAggregate createCohortFunnelAnalysis(TimeRange timeRange, List<String> funnelSteps, 
                                                                    String dataScope) {
        return FunnelAnalysisAggregate.builder()
                .id(generateId("cohort_funnel", timeRange))
                .analysisType(FunnelAnalysisType.COHORT_FUNNEL)
                .timeRange(timeRange)
                .funnelSteps(funnelSteps)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 验证聚合根是否有效
     */
    public boolean isValid() {
        return timeRange != null && 
               timeRange.isValid() && 
               analysisType != null &&
               dataScope != null && 
               !dataScope.trim().isEmpty() &&
               funnelSteps != null &&
               !funnelSteps.isEmpty() &&
               funnelSteps.size() >= 2; // 漏斗至少需要2个步骤
    }

    /**
     * 验证数据完整性
     */
    public boolean isDataComplete() {
        return id != null && 
               analysisType != null && 
               timeRange != null && 
               dataScope != null && 
               lastUpdateTime != null &&
               coreMetrics != null && 
               !coreMetrics.isEmpty();
    }

    /**
     * 获取数据摘要
     */
    public String getDataSummary() {
        if (!isDataComplete()) {
            return "数据不完整";
        }
        
        MetricValue totalUsers = getCoreMetric("totalUsers");
        MetricValue overallConversionRate = getCoreMetric("overallConversionRate");
        
        return String.format("分析类型: %s, 时间范围: %s - %s, 总用户数: %s, 整体转化率: %s%%",
                analysisType.getDescription(),
                timeRange.getStartDate(),
                timeRange.getEndDate(),
                totalUsers != null ? totalUsers.getValue() : "N/A",
                overallConversionRate != null ? String.format("%.2f", overallConversionRate.getValue().doubleValue()) : "N/A");
    }

    /**
     * 获取漏斗步骤数量
     */
    public int getStepCount() {
        return funnelSteps != null ? funnelSteps.size() : 0;
    }

    /**
     * 检查是否为有效漏斗
     */
    public boolean isValidFunnel() {
        return isValid() && getStepCount() >= 2 && getStepCount() <= 10; // 限制步骤数量在2-10之间
    }
}
