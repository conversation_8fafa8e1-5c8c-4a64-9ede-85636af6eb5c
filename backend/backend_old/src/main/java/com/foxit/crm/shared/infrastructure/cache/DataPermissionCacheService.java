package com.foxit.crm.shared.infrastructure.cache;

import com.foxit.crm.common.constant.RedisKeyConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RKeys;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;

/**
 * 数据权限缓存服务
 * 专门处理数据权限相关的缓存操作，使用Redisson客户端
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataPermissionCacheService {

    private final RedissonClient redissonClient;

    /**
     * 缓存用户数据权限规则
     */
    public void cacheUserDataPermission(Long userId, String tableName, Object permissionRules) {
        String key = RedisKeyConstant.buildUserDataPermissionKey(userId, tableName);
        RBucket<Object> bucket = redissonClient.getBucket(key);
        bucket.setAsync(permissionRules, Duration.ofSeconds(RedisKeyConstant.MEDIUM_EXPIRE));
        log.debug("缓存用户数据权限: userId={}, tableName={}", userId, tableName);
    }

    /**
     * 获取用户数据权限规则缓存
     */
    @SuppressWarnings("unchecked")
    public <T> T getUserDataPermission(Long userId, String tableName, Class<T> clazz) {
        String key = RedisKeyConstant.buildUserDataPermissionKey(userId, tableName);
        RBucket<Object> bucket = redissonClient.getBucket(key);
        return (T) bucket.get();
    }

    /**
     * 删除用户数据权限缓存
     */
    public void evictUserDataPermission(Long userId, String tableName) {
        String key = RedisKeyConstant.buildUserDataPermissionKey(userId, tableName);
        RBucket<Object> bucket = redissonClient.getBucket(key);
        bucket.deleteAsync();
        log.debug("清除用户数据权限缓存: userId={}, tableName={}", userId, tableName);
    }

    /**
     * 删除用户所有数据权限缓存
     */
    public void evictUserDataPermissions(Long userId) {
        String pattern = RedisKeyConstant.USER_DATA_PERMISSION_PREFIX + userId + RedisKeyConstant.SEPARATOR + "*";
        deleteByPattern(pattern);
        log.debug("清除用户所有数据权限缓存: userId={}", userId);
    }

    /**
     * 删除指定表的所有数据权限缓存
     */
    public void evictTableDataPermissions(String tableName) {
        String pattern = RedisKeyConstant.USER_DATA_PERMISSION_PREFIX + "*" + RedisKeyConstant.SEPARATOR + tableName;
        deleteByPattern(pattern);
        log.debug("清除表数据权限缓存: tableName={}", tableName);
    }

    /**
     * 清除所有数据权限缓存
     */
    public void evictAllDataPermissions() {
        deleteByPattern(RedisKeyConstant.DATA_PERMISSION_PREFIX + "*");
        deleteByPattern(RedisKeyConstant.USER_DATA_PERMISSION_PREFIX + "*");
        log.info("清除所有数据权限缓存");
    }

    /**
     * 根据模式删除缓存键
     */
    private void deleteByPattern(String pattern) {
        RKeys keys = redissonClient.getKeys();
        keys.deleteByPatternAsync(pattern);
    }

    /**
     * 缓存数据权限规则列表
     */
    public void cacheDataPermissionRules(String cacheKey, List<?> rules) {
        RBucket<Object> bucket = redissonClient.getBucket(cacheKey);
        bucket.setAsync(rules, Duration.ofSeconds(RedisKeyConstant.MEDIUM_EXPIRE));
        log.debug("缓存数据权限规则列表: key={}", cacheKey);
    }

    /**
     * 获取数据权限规则列表缓存
     */
    @SuppressWarnings("unchecked")
    public <T> List<T> getDataPermissionRules(String cacheKey, Class<T> clazz) {
        RBucket<Object> bucket = redissonClient.getBucket(cacheKey);
        return (List<T>) bucket.get();
    }

    /**
     * 构建数据权限规则缓存键
     */
    public String buildDataPermissionRulesKey(Long userId, List<Long> roleIds, String tableName) {
        StringBuilder keyBuilder = new StringBuilder(RedisKeyConstant.DATA_PERMISSION_PREFIX);
        keyBuilder.append("rules").append(RedisKeyConstant.SEPARATOR);
        keyBuilder.append("user").append(RedisKeyConstant.SEPARATOR).append(userId);

        if (roleIds != null && !roleIds.isEmpty()) {
            keyBuilder.append(RedisKeyConstant.SEPARATOR).append("roles").append(RedisKeyConstant.SEPARATOR);
            roleIds.forEach(roleId -> keyBuilder.append(roleId).append(","));
            // 移除最后一个逗号
            keyBuilder.setLength(keyBuilder.length() - 1);
        }

        keyBuilder.append(RedisKeyConstant.SEPARATOR).append("table").append(RedisKeyConstant.SEPARATOR)
                .append(tableName);

        return keyBuilder.toString();
    }

    /**
     * 构建行级权限规则缓存键
     */
    public String buildRowLevelRulesKey(Long userId, List<Long> roleIds, String tableName) {
        return buildDataPermissionRulesKey(userId, roleIds, tableName) + RedisKeyConstant.SEPARATOR + "row";
    }

    /**
     * 构建列级权限规则缓存键
     */
    public String buildColumnLevelRulesKey(Long userId, List<Long> roleIds, String tableName) {
        return buildDataPermissionRulesKey(userId, roleIds, tableName) + RedisKeyConstant.SEPARATOR + "column";
    }

    /**
     * 构建查询级权限规则缓存键
     */
    public String buildQueryLevelRulesKey(Long userId, List<Long> roleIds, String tableName) {
        return buildDataPermissionRulesKey(userId, roleIds, tableName) + RedisKeyConstant.SEPARATOR + "query";
    }

    /**
     * 缓存行级权限规则
     */
    public void cacheRowLevelRules(Long userId, List<Long> roleIds, String tableName, List<?> rules) {
        String key = buildRowLevelRulesKey(userId, roleIds, tableName);
        cacheDataPermissionRules(key, rules);
    }

    /**
     * 获取行级权限规则缓存
     */
    public <T> List<T> getRowLevelRules(Long userId, List<Long> roleIds, String tableName, Class<T> clazz) {
        String key = buildRowLevelRulesKey(userId, roleIds, tableName);
        return getDataPermissionRules(key, clazz);
    }

    /**
     * 缓存列级权限规则
     */
    public void cacheColumnLevelRules(Long userId, List<Long> roleIds, String tableName, List<?> rules) {
        String key = buildColumnLevelRulesKey(userId, roleIds, tableName);
        cacheDataPermissionRules(key, rules);
    }

    /**
     * 获取列级权限规则缓存
     */
    public <T> List<T> getColumnLevelRules(Long userId, List<Long> roleIds, String tableName, Class<T> clazz) {
        String key = buildColumnLevelRulesKey(userId, roleIds, tableName);
        return getDataPermissionRules(key, clazz);
    }

    /**
     * 缓存查询级权限规则
     */
    public void cacheQueryLevelRules(Long userId, List<Long> roleIds, String tableName, List<?> rules) {
        String key = buildQueryLevelRulesKey(userId, roleIds, tableName);
        cacheDataPermissionRules(key, rules);
    }

    /**
     * 获取查询级权限规则缓存
     */
    public <T> List<T> getQueryLevelRules(Long userId, List<Long> roleIds, String tableName, Class<T> clazz) {
        String key = buildQueryLevelRulesKey(userId, roleIds, tableName);
        return getDataPermissionRules(key, clazz);
    }

    /**
     * 当角色权限发生变化时，清除相关缓存
     */
    public void evictRoleRelatedDataPermissions(Long roleId) {
        String pattern = RedisKeyConstant.DATA_PERMISSION_PREFIX + "*roles*" + roleId + "*";
        deleteByPattern(pattern);
        log.debug("清除角色相关数据权限缓存: roleId={}", roleId);
    }

    /**
     * 当用户角色发生变化时，清除相关缓存
     */
    public void evictUserRoleRelatedDataPermissions(Long userId) {
        evictUserDataPermissions(userId);
        log.debug("清除用户角色相关数据权限缓存: userId={}", userId);
    }
}
