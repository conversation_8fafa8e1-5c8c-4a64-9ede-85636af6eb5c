package com.foxit.crm.modules.system.domain.model.aggregate;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色聚合根
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@NoArgsConstructor
public class Role {

    /**
     * 角色ID
     */
    private Long id;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 角色状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 权限ID列表
     */
    private List<Long> permissionIds;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 版本号（乐观锁）
     */
    private Integer version;

    /**
     * 构造函数
     */
    public Role(String roleName, String roleCode, String description, Integer status, Integer sortOrder) {
        this.roleName = roleName;
        this.roleCode = roleCode;
        this.description = description;
        this.status = status;
        this.sortOrder = sortOrder;
    }

    /**
     * 启用角色
     */
    public void enable() {
        this.status = 1;
    }

    /**
     * 禁用角色
     */
    public void disable() {
        this.status = 0;
    }

    /**
     * 检查角色是否启用
     */
    public boolean isEnabled() {
        return this.status != null && this.status == 1;
    }

    /**
     * 检查角色编码是否有效
     */
    public boolean isValidRoleCode() {
        return this.roleCode != null && !this.roleCode.trim().isEmpty();
    }

    /**
     * 检查角色名称是否有效
     */
    public boolean isValidRoleName() {
        return this.roleName != null && !this.roleName.trim().isEmpty();
    }
}
