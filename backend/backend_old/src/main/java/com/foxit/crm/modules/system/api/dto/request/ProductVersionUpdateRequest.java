package com.foxit.crm.modules.system.api.dto.request;

import lombok.Data;

import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 更新产品版本请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
public class ProductVersionUpdateRequest {

    /**
     * 版本名称，例如：福昕阅读器GA版 v1.0.0
     */
    @Size(max = 100, message = "版本名称长度不能超过100个字符")
    private String versionName;

    /**
     * 版本描述
     */
    @Size(max = 500, message = "版本描述长度不能超过500个字符")
    private String description;

    /**
     * 发布说明
     */
    @Size(max = 2000, message = "发布说明长度不能超过2000个字符")
    private String releaseNotes;

    /**
     * 版本类型：1-主版本，2-次版本，3-修订版，4-预发布版
     */
    private Integer versionType;

    /**
     * 计划发布时间
     */
    private LocalDateTime plannedDate;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 下载地址
     */
    @Size(max = 500, message = "下载地址长度不能超过500个字符")
    private String downloadUrl;

    /**
     * 支持平台列表
     */
    private List<String> platforms;

    /**
     * 新功能列表
     */
    private List<String> features;

    /**
     * 修复问题列表
     */
    private List<String> bugFixes;

    /**
     * 破坏性变更列表
     */
    private List<String> breakingChanges;

    /**
     * 依赖信息
     */
    private List<String> dependencies;

    /**
     * 系统要求
     */
    private List<String> systemRequirements;

    /**
     * MD5校验值
     */
    @Pattern(regexp = "^[a-fA-F0-9]{32}$", message = "MD5校验值格式不正确")
    private String checksumMd5;

    /**
     * SHA256校验值
     */
    @Pattern(regexp = "^[a-fA-F0-9]{64}$", message = "SHA256校验值格式不正确")
    private String checksumSha256;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
