package com.foxit.crm.common.util;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * IP工具类
 * 用于获取客户端真实IP地址
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
public class IpUtils {

    private static final String UNKNOWN = "unknown";
    private static final String LOCALHOST_IPV4 = "127.0.0.1";
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";
    private static final int IP_MAX_LENGTH = 15;

    /**
     * 获取客户端IP地址
     */
    public static String getClientIP(HttpServletRequest request) {
        if (request == null) {
            return UNKNOWN;
        }

        String ip = null;
        
        // 1. 检查X-Forwarded-For头（代理服务器传递的原始客户端IP）
        ip = request.getHeader("X-Forwarded-For");
        if (isValidIP(ip)) {
            // X-Forwarded-For可能包含多个IP，取第一个
            if (ip.contains(",")) {
                ip = ip.split(",")[0].trim();
            }
            return ip;
        }

        // 2. 检查X-Real-IP头（Nginx等反向代理设置的真实IP）
        ip = request.getHeader("X-Real-IP");
        if (isValidIP(ip)) {
            return ip;
        }

        // 3. 检查Proxy-Client-IP头（Apache服务器设置）
        ip = request.getHeader("Proxy-Client-IP");
        if (isValidIP(ip)) {
            return ip;
        }

        // 4. 检查WL-Proxy-Client-IP头（WebLogic服务器设置）
        ip = request.getHeader("WL-Proxy-Client-IP");
        if (isValidIP(ip)) {
            return ip;
        }

        // 5. 检查HTTP_CLIENT_IP头
        ip = request.getHeader("HTTP_CLIENT_IP");
        if (isValidIP(ip)) {
            return ip;
        }

        // 6. 检查HTTP_X_FORWARDED_FOR头
        ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (isValidIP(ip)) {
            return ip;
        }

        // 7. 最后使用request.getRemoteAddr()
        ip = request.getRemoteAddr();
        
        // 如果是本地回环地址，尝试获取本机真实IP
        if (LOCALHOST_IPV4.equals(ip) || LOCALHOST_IPV6.equals(ip)) {
            try {
                InetAddress inetAddress = InetAddress.getLocalHost();
                ip = inetAddress.getHostAddress();
            } catch (UnknownHostException e) {
                log.warn("获取本机IP失败", e);
            }
        }

        return ip;
    }

    /**
     * 验证IP地址是否有效
     */
    private static boolean isValidIP(String ip) {
        return ip != null 
            && !ip.isEmpty() 
            && !UNKNOWN.equalsIgnoreCase(ip) 
            && ip.length() <= IP_MAX_LENGTH;
    }

    /**
     * 判断是否为内网IP
     */
    public static boolean isInternalIP(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        try {
            InetAddress inetAddress = InetAddress.getByName(ip);
            return inetAddress.isSiteLocalAddress() 
                || inetAddress.isLoopbackAddress() 
                || inetAddress.isLinkLocalAddress();
        } catch (UnknownHostException e) {
            log.warn("判断内网IP失败: {}", ip, e);
            return false;
        }
    }

    /**
     * 获取本机IP地址
     */
    public static String getLocalIP() {
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            return inetAddress.getHostAddress();
        } catch (UnknownHostException e) {
            log.error("获取本机IP失败", e);
            return LOCALHOST_IPV4;
        }
    }

    /**
     * 获取本机主机名
     */
    public static String getLocalHostName() {
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            return inetAddress.getHostName();
        } catch (UnknownHostException e) {
            log.error("获取本机主机名失败", e);
            return "localhost";
        }
    }

    /**
     * IP地址转换为长整型
     */
    public static long ipToLong(String ip) {
        if (ip == null || ip.isEmpty()) {
            return 0L;
        }

        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return 0L;
        }

        try {
            long result = 0L;
            for (int i = 0; i < 4; i++) {
                int part = Integer.parseInt(parts[i]);
                if (part < 0 || part > 255) {
                    return 0L;
                }
                result = (result << 8) + part;
            }
            return result;
        } catch (NumberFormatException e) {
            log.warn("IP地址格式错误: {}", ip);
            return 0L;
        }
    }

    /**
     * 长整型转换为IP地址
     */
    public static String longToIP(long ip) {
        return ((ip >> 24) & 0xFF) + "." +
               ((ip >> 16) & 0xFF) + "." +
               ((ip >> 8) & 0xFF) + "." +
               (ip & 0xFF);
    }
}
