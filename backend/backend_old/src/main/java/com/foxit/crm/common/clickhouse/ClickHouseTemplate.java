package com.foxit.crm.common.clickhouse;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * ClickHouse数据访问模板类
 * 封装ClickHouse数据库操作的通用方法
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@Component
public class ClickHouseTemplate {

    private final JdbcTemplate clickhouseJdbcTemplate;

    public ClickHouseTemplate(@Qualifier("clickhouseJdbcTemplate") JdbcTemplate clickhouseJdbcTemplate) {
        this.clickhouseJdbcTemplate = clickhouseJdbcTemplate;
        if (clickhouseJdbcTemplate != null && clickhouseJdbcTemplate.getDataSource() != null) {
            log.info("ClickHouseTemplate初始化完成，使用数据源: {}", clickhouseJdbcTemplate.getDataSource().getClass().getSimpleName());
        } else {
            log.warn("ClickHouseTemplate初始化时数据源为空");
        }
    }

    /**
     * 执行查询并返回结果列表
     *
     * @param sql    SQL语句
     * @param mapper 行映射器
     * @param args   参数
     * @param <T>    结果类型
     * @return 查询结果列表
     */
    public <T> List<T> query(String sql, RowMapper<T> mapper, Object... args) {
        try {
            log.debug("执行ClickHouse查询: {}", sql);
            return clickhouseJdbcTemplate.query(sql, mapper, args);
        } catch (Exception e) {
            log.error("ClickHouse查询失败: {}", sql, e);
            throw new RuntimeException("ClickHouse查询失败", e);
        }
    }

    /**
     * 执行查询并返回Map列表
     *
     * @param sql  SQL语句
     * @param args 参数
     * @return 查询结果Map列表
     */
    public List<Map<String, Object>> queryForList(String sql, Object... args) {
        try {
            log.debug("执行ClickHouse查询: {}", sql);
            return clickhouseJdbcTemplate.queryForList(sql, args);
        } catch (Exception e) {
            log.error("ClickHouse查询失败: {}", sql, e);
            throw new RuntimeException("ClickHouse查询失败", e);
        }
    }



    /**
     * 执行查询并返回单个值
     *
     * @param sql   SQL语句
     * @param clazz 结果类型
     * @param args  参数
     * @param <T>   结果类型
     * @return 查询结果值
     */
    public <T> T queryForObject(String sql, Class<T> clazz, Object... args) {
        try {
            log.debug("执行ClickHouse查询: {}", sql);
            return clickhouseJdbcTemplate.queryForObject(sql, clazz, args);
        } catch (Exception e) {
            log.error("ClickHouse查询失败: {}", sql, e);
            throw new RuntimeException("ClickHouse查询失败", e);
        }
    }

    /**
     * 执行更新操作（INSERT、UPDATE、DELETE）
     *
     * @param sql  SQL语句
     * @param args 参数
     * @return 影响的行数
     */
    public int update(String sql, Object... args) {
        try {
            log.debug("执行ClickHouse更新: {}", sql);
            return clickhouseJdbcTemplate.update(sql, args);
        } catch (Exception e) {
            log.error("ClickHouse更新失败: {}", sql, e);
            throw new RuntimeException("ClickHouse更新失败", e);
        }
    }



    /**
     * 执行DDL语句（CREATE、DROP、ALTER等）
     *
     * @param sql DDL语句
     */
    public void execute(String sql) {
        try {
            log.debug("执行ClickHouse DDL: {}", sql);
            clickhouseJdbcTemplate.execute(sql);
        } catch (Exception e) {
            log.error("ClickHouse DDL执行失败: {}", sql, e);
            throw new RuntimeException("ClickHouse DDL执行失败", e);
        }
    }

    /**
     * 测试ClickHouse连接
     *
     * @return 连接是否正常
     */
    public boolean testConnection() {
        try {
            clickhouseJdbcTemplate.queryForObject("SELECT 1", Integer.class);
            log.info("ClickHouse连接测试成功");
            return true;
        } catch (Exception e) {
            log.error("ClickHouse连接测试失败", e);
            return false;
        }
    }

    /**
     * 获取ClickHouse版本信息
     *
     * @return 版本信息
     */
    public String getVersion() {
        try {
            String version = clickhouseJdbcTemplate.queryForObject("SELECT version()", String.class);
            log.info("数据库版本: {}", version);

            // 验证是否为ClickHouse（ClickHouse版本格式：24.4.1.2088，MySQL版本格式：8.0.36）
            if (version != null && (version.contains("mysql") || version.contains("MySQL") || version.matches("^[0-9]+\\.[0-9]+\\.[0-9]+$"))) {
                log.error("警告：连接的不是ClickHouse数据库！版本信息: {}", version);
                log.error("数据源类型: {}", clickhouseJdbcTemplate.getDataSource().getClass().getName());
            } else {
                log.info("ClickHouse数据库连接正常，版本: {}", version);
            }

            return version;
        } catch (Exception e) {
            log.error("获取ClickHouse版本失败", e);
            return "Unknown";
        }
    }

    /**
     * 验证ClickHouse连接
     */
    public boolean validateClickHouseConnection() {
        try {
            // 尝试执行ClickHouse特有的函数
            clickhouseJdbcTemplate.queryForObject("SELECT toDate('2025-01-01')", String.class);
            log.info("ClickHouse连接验证成功");
            return true;
        } catch (Exception e) {
            log.error("ClickHouse连接验证失败，可能连接到了错误的数据库: {}", e.getMessage());
            return false;
        }
    }
}
