package com.foxit.crm.modules.useranalysis.domain.entity;

import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 活跃用户聚合根
 * 封装活跃用户分析的核心业务逻辑
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Builder
public class ActiveUserAggregate {

    /**
     * 聚合根ID
     */
    private final String id;

    /**
     * 分析类型：DAU, WAU, MAU, FREQUENCY, DISTRIBUTION
     */
    private final ActiveUserAnalysisType analysisType;

    /**
     * 时间范围
     */
    private final TimeRange timeRange;

    /**
     * 产品线ID列表
     */
    private final List<Long> productLineIds;

    /**
     * 核心指标
     */
    private final Map<String, MetricValue> coreMetrics;

    /**
     * 趋势数据
     */
    private final Map<String, TrendData> trendData;

    /**
     * 分布数据
     */
    private final Map<String, DistributionData> distributionData;

    /**
     * 频次分析数据
     */
    private final FrequencyAnalysisData frequencyData;

    /**
     * 数据权限范围
     */
    private final String dataScope;

    /**
     * 最后更新时间
     */
    private final LocalDateTime lastUpdateTime;

    /**
     * 活跃用户分析类型枚举
     */
    public enum ActiveUserAnalysisType {
        DAU("日活跃用户"),
        WAU("周活跃用户"),
        MAU("月活跃用户"),
        FREQUENCY("活跃频次"),
        DISTRIBUTION("活跃分布"),
        RETENTION("活跃留存"),
        COHORT("队列分析");

        private final String description;

        ActiveUserAnalysisType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 趋势数据
     */
    @Getter
    @Builder
    public static class TrendData {
        private final String name;
        private final List<String> categories;
        private final List<Number> values;
        private final String unit;
        private final String chartType;
    }

    /**
     * 分布数据
     */
    @Getter
    @Builder
    public static class DistributionData {
        private final String name;
        private final List<DistributionItem> items;
        private final String chartType;
    }

    /**
     * 分布项
     */
    @Getter
    @Builder
    public static class DistributionItem {
        private final String name;
        private final Number value;
        private final Double percentage;
        private final String color;
    }

    /**
     * 频次分析数据
     */
    @Getter
    @Builder
    public static class FrequencyAnalysisData {
        private final Map<String, Integer> frequencyDistribution;
        private final Map<String, Double> retentionByFrequency;
        private final Double averageFrequency;
        private final Integer totalActiveUsers;
    }

    /**
     * 创建总览活跃用户分析
     */
    public static ActiveUserAggregate createOverview(TimeRange timeRange, String dataScope) {
        return ActiveUserAggregate.builder()
                .id(generateId("overview", timeRange))
                .analysisType(ActiveUserAnalysisType.DAU)
                .timeRange(timeRange)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建产品线活跃用户分析
     */
    public static ActiveUserAggregate createProductLine(TimeRange timeRange, List<Long> productLineIds, String dataScope) {
        return ActiveUserAggregate.builder()
                .id(generateId("productline", timeRange))
                .analysisType(ActiveUserAnalysisType.DAU)
                .timeRange(timeRange)
                .productLineIds(productLineIds)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建频次分析
     */
    public static ActiveUserAggregate createFrequencyAnalysis(TimeRange timeRange, List<Long> productLineIds, String dataScope) {
        return ActiveUserAggregate.builder()
                .id(generateId("frequency", timeRange))
                .analysisType(ActiveUserAnalysisType.FREQUENCY)
                .timeRange(timeRange)
                .productLineIds(productLineIds)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 验证聚合根是否有效
     */
    public boolean isValid() {
        return timeRange != null && 
               timeRange.isValid() && 
               analysisType != null &&
               dataScope != null && 
               !dataScope.trim().isEmpty();
    }

    /**
     * 检查是否需要刷新数据
     */
    public boolean needsRefresh() {
        if (lastUpdateTime == null) {
            return true;
        }
        
        // 根据分析类型确定刷新间隔
        int refreshIntervalMinutes = switch (analysisType) {
            case DAU -> 30;  // DAU每30分钟刷新
            case WAU -> 60;  // WAU每小时刷新
            case MAU -> 120; // MAU每2小时刷新
            default -> 60;
        };
        
        return lastUpdateTime.isBefore(LocalDateTime.now().minusMinutes(refreshIntervalMinutes));
    }

    /**
     * 获取核心指标值
     */
    public MetricValue getCoreMetric(String metricName) {
        return coreMetrics != null ? coreMetrics.get(metricName) : null;
    }

    /**
     * 获取趋势数据
     */
    public TrendData getTrendData(String trendName) {
        return trendData != null ? trendData.get(trendName) : null;
    }

    /**
     * 获取分布数据
     */
    public DistributionData getDistributionData(String distributionName) {
        return distributionData != null ? distributionData.get(distributionName) : null;
    }

    /**
     * 生成聚合根ID
     */
    private static String generateId(String prefix, TimeRange timeRange) {
        return String.format("%s_%s_%s", 
                prefix, 
                timeRange.getStartDate().toString(), 
                timeRange.getEndDate().toString());
    }

    /**
     * 计算活跃率
     */
    public Double calculateActiveRate(Integer totalUsers) {
        if (totalUsers == null || totalUsers == 0) {
            return 0.0;
        }
        
        MetricValue activeUsers = getCoreMetric("activeUsers");
        if (activeUsers == null || activeUsers.getValue() == null) {
            return 0.0;
        }
        
        return (activeUsers.getValue().doubleValue() / totalUsers) * 100;
    }

    /**
     * 计算黏性系数 (DAU/MAU)
     */
    public Double calculateStickinessRatio() {
        MetricValue dau = getCoreMetric("dau");
        MetricValue mau = getCoreMetric("mau");
        
        if (dau == null || mau == null || 
            dau.getValue() == null || mau.getValue() == null ||
            mau.getValue().doubleValue() == 0) {
            return 0.0;
        }
        
        return (dau.getValue().doubleValue() / mau.getValue().doubleValue()) * 100;
    }
}
