package com.foxit.crm.shared.infrastructure.cache;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 统计数据缓存管理器
 * 使用Spring Cache注解管理各种统计数据的缓存
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsCacheManager {

    /**
     * 缓存操作日志统计数据
     */
    @Cacheable(value = "operationStats", key = "'daily:' + #date")
    public Map<String, Object> getDailyOperationStats(LocalDate date,
                                                      java.util.function.Supplier<Map<String, Object>> dataLoader) {
        log.debug("加载每日操作统计: date={}", date);
        return dataLoader.get();
    }

    /**
     * 缓存用户操作统计
     */
    @Cacheable(value = "operationStats", key = "'user:' + #userId + ':' + #startDate + ':' + #endDate")
    public Map<String, Object> getUserOperationStats(Long userId, LocalDate startDate, LocalDate endDate,
                                                     java.util.function.Supplier<Map<String, Object>> dataLoader) {
        log.debug("加载用户操作统计: userId={}, startDate={}, endDate={}", userId, startDate, endDate);
        return dataLoader.get();
    }

    /**
     * 缓存操作类型统计
     */
    @Cacheable(value = "operationStats", key = "'type:' + #operationType + ':' + #date")
    public Long getOperationTypeCount(String operationType, LocalDate date,
                                     java.util.function.Supplier<Long> dataLoader) {
        log.debug("加载操作类型统计: operationType={}, date={}", operationType, date);
        return dataLoader.get();
    }

    /**
     * 缓存系统访问统计
     */
    @Cacheable(value = "operationStats", key = "'access:' + #date")
    public Map<String, Object> getSystemAccessStats(LocalDate date,
                                                    java.util.function.Supplier<Map<String, Object>> dataLoader) {
        log.debug("加载系统访问统计: date={}", date);
        return dataLoader.get();
    }

    /**
     * 缓存用户活跃度统计
     */
    @Cacheable(value = "operationStats", key = "'activity:' + #date")
    public Map<String, Object> getUserActivityStats(LocalDate date,
                                                    java.util.function.Supplier<Map<String, Object>> dataLoader) {
        log.debug("加载用户活跃度统计: date={}", date);
        return dataLoader.get();
    }

    /**
     * 缓存模块使用统计
     */
    @Cacheable(value = "operationStats", key = "'module:' + #module + ':' + #date")
    public Map<String, Object> getModuleUsageStats(String module, LocalDate date,
                                                   java.util.function.Supplier<Map<String, Object>> dataLoader) {
        log.debug("加载模块使用统计: module={}, date={}", module, date);
        return dataLoader.get();
    }

    /**
     * 缓存API调用统计
     */
    @Cacheable(value = "operationStats", key = "'api:' + #apiPath + ':' + #date")
    public Map<String, Object> getApiCallStats(String apiPath, LocalDate date,
                                              java.util.function.Supplier<Map<String, Object>> dataLoader) {
        log.debug("加载API调用统计: apiPath={}, date={}", apiPath, date);
        return dataLoader.get();
    }

    /**
     * 缓存错误统计
     */
    @Cacheable(value = "operationStats", key = "'error:' + #date")
    public Map<String, Object> getErrorStats(LocalDate date,
                                            java.util.function.Supplier<Map<String, Object>> dataLoader) {
        log.debug("加载错误统计: date={}", date);
        return dataLoader.get();
    }

    /**
     * 缓存性能统计
     */
    @Cacheable(value = "operationStats", key = "'performance:' + #date")
    public Map<String, Object> getPerformanceStats(LocalDate date,
                                                   java.util.function.Supplier<Map<String, Object>> dataLoader) {
        log.debug("加载性能统计: date={}", date);
        return dataLoader.get();
    }

    /**
     * 缓存实时统计数据
     */
    @Cacheable(value = "operationStats", key = "'realtime:' + #type", condition = "#cacheMinutes > 0")
    public Map<String, Object> getRealtimeStats(String type, int cacheMinutes,
                                               java.util.function.Supplier<Map<String, Object>> dataLoader) {
        log.debug("加载实时统计: type={}, cacheMinutes={}", type, cacheMinutes);
        return dataLoader.get();
    }

    /**
     * 缓存趋势分析数据
     */
    @Cacheable(value = "operationStats", key = "'trend:' + #type + ':' + #startDate + ':' + #endDate")
    public List<Map<String, Object>> getTrendAnalysis(String type, LocalDate startDate, LocalDate endDate,
                                                     java.util.function.Supplier<List<Map<String, Object>>> dataLoader) {
        log.debug("加载趋势分析: type={}, startDate={}, endDate={}", type, startDate, endDate);
        return dataLoader.get();
    }

    /**
     * 缓存排行榜数据
     */
    @Cacheable(value = "operationStats", key = "'ranking:' + #type + ':' + #date + ':' + #limit")
    public List<Map<String, Object>> getRankingData(String type, LocalDate date, int limit,
                                                   java.util.function.Supplier<List<Map<String, Object>>> dataLoader) {
        log.debug("加载排行榜数据: type={}, date={}, limit={}", type, date, limit);
        return dataLoader.get();
    }

    /**
     * 缓存汇总统计数据
     */
    @Cacheable(value = "operationStats", key = "'summary:' + #type + ':' + #period")
    public Map<String, Object> getSummaryStats(String type, String period,
                                              java.util.function.Supplier<Map<String, Object>> dataLoader) {
        log.debug("加载汇总统计: type={}, period={}", type, period);
        return dataLoader.get();
    }

    // ============================
    // 缓存清理方法
    // ============================

    /**
     * 清除指定日期的统计缓存
     */
    @CacheEvict(value = "operationStats", key = "'daily:' + #date")
    public void evictDailyStats(LocalDate date) {
        log.debug("清除每日统计缓存: date={}", date);
    }

    /**
     * 清除用户统计缓存
     */
    @CacheEvict(value = "operationStats", key = "'user:' + #userId + ':*'")
    public void evictUserStats(Long userId) {
        log.debug("清除用户统计缓存: userId={}", userId);
    }

    /**
     * 清除操作类型统计缓存
     */
    @CacheEvict(value = "operationStats", key = "'type:' + #operationType + ':*'")
    public void evictOperationTypeStats(String operationType) {
        log.debug("清除操作类型统计缓存: operationType={}", operationType);
    }

    /**
     * 清除模块统计缓存
     */
    @CacheEvict(value = "operationStats", key = "'module:' + #module + ':*'")
    public void evictModuleStats(String module) {
        log.debug("清除模块统计缓存: module={}", module);
    }

    /**
     * 清除API统计缓存
     */
    @CacheEvict(value = "operationStats", key = "'api:' + #apiPath + ':*'")
    public void evictApiStats(String apiPath) {
        log.debug("清除API统计缓存: apiPath={}", apiPath);
    }

    /**
     * 清除实时统计缓存
     */
    @CacheEvict(value = "operationStats", key = "'realtime:' + #type")
    public void evictRealtimeStats(String type) {
        log.debug("清除实时统计缓存: type={}", type);
    }

    /**
     * 清除趋势分析缓存
     */
    @CacheEvict(value = "operationStats", key = "'trend:' + #type + ':*'")
    public void evictTrendAnalysis(String type) {
        log.debug("清除趋势分析缓存: type={}", type);
    }

    /**
     * 清除排行榜缓存
     */
    @CacheEvict(value = "operationStats", key = "'ranking:' + #type + ':*'")
    public void evictRankingData(String type) {
        log.debug("清除排行榜缓存: type={}", type);
    }

    /**
     * 清除汇总统计缓存
     */
    @CacheEvict(value = "operationStats", key = "'summary:' + #type + ':*'")
    public void evictSummaryStats(String type) {
        log.debug("清除汇总统计缓存: type={}", type);
    }

    /**
     * 清除所有统计缓存
     */
    @CacheEvict(value = "operationStats", allEntries = true)
    public void evictAllStats() {
        log.info("清除所有统计缓存");
    }

    /**
     * 批量清除过期的统计缓存
     */
    public void evictExpiredStats(LocalDate beforeDate) {
        // 清除指定日期之前的统计缓存
        LocalDate current = beforeDate;
        while (current.isBefore(LocalDate.now().minusDays(30))) {
            evictDailyStats(current);
            current = current.plusDays(1);
        }
        log.info("清除过期统计缓存: beforeDate={}", beforeDate);
    }
}
