package com.foxit.crm.modules.useranalysis.domain.valueobject;

import lombok.Builder;
import lombok.Getter;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 指标值对象
 * 用于表示各种分析指标的值和元数据
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Builder
@EqualsAndHashCode
public class MetricValue {

    /**
     * 指标名称
     */
    private final String name;

    /**
     * 指标值
     */
    private final Number value;

    /**
     * 上期值（用于计算环比）
     */
    private final Number previousValue;

    /**
     * 同期值（用于计算同比）
     */
    private final Number yearOverYearValue;

    /**
     * 单位
     */
    private final String unit;

    /**
     * 指标类型
     */
    private final MetricType type;

    /**
     * 格式化模式
     */
    private final String format;

    /**
     * 计算时间
     */
    private final LocalDateTime calculatedAt;

    /**
     * 指标类型枚举
     */
    public enum MetricType {
        COUNT("计数"),
        PERCENTAGE("百分比"),
        RATIO("比率"),
        AMOUNT("金额"),
        DURATION("时长"),
        RATE("速率");

        private final String description;

        MetricType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 计算环比增长率
     */
    public Double getGrowthRate() {
        if (previousValue == null || previousValue.doubleValue() == 0) {
            return null;
        }
        
        double current = value != null ? value.doubleValue() : 0;
        double previous = previousValue.doubleValue();
        
        return ((current - previous) / previous) * 100;
    }

    /**
     * 计算同比增长率
     */
    public Double getYearOverYearGrowthRate() {
        if (yearOverYearValue == null || yearOverYearValue.doubleValue() == 0) {
            return null;
        }
        
        double current = value != null ? value.doubleValue() : 0;
        double yearOverYear = yearOverYearValue.doubleValue();
        
        return ((current - yearOverYear) / yearOverYear) * 100;
    }

    /**
     * 获取格式化的值
     */
    public String getFormattedValue() {
        if (value == null) {
            return "N/A";
        }
        
        String formattedValue = switch (type) {
            case COUNT -> formatCount(value);
            case PERCENTAGE -> formatPercentage(value);
            case RATIO -> formatRatio(value);
            case AMOUNT -> formatAmount(value);
            case DURATION -> formatDuration(value);
            case RATE -> formatRate(value);
        };
        
        return unit != null ? formattedValue + " " + unit : formattedValue;
    }

    /**
     * 获取格式化的环比增长率
     */
    public String getFormattedGrowthRate() {
        Double growthRate = getGrowthRate();
        if (growthRate == null) {
            return "N/A";
        }
        
        String sign = growthRate >= 0 ? "+" : "";
        return String.format("%s%.2f%%", sign, growthRate);
    }

    /**
     * 获取格式化的同比增长率
     */
    public String getFormattedYearOverYearGrowthRate() {
        Double yoyGrowthRate = getYearOverYearGrowthRate();
        if (yoyGrowthRate == null) {
            return "N/A";
        }
        
        String sign = yoyGrowthRate >= 0 ? "+" : "";
        return String.format("%s%.2f%%", sign, yoyGrowthRate);
    }

    /**
     * 判断是否为正增长
     */
    public boolean isPositiveGrowth() {
        Double growthRate = getGrowthRate();
        return growthRate != null && growthRate > 0;
    }

    /**
     * 判断是否为负增长
     */
    public boolean isNegativeGrowth() {
        Double growthRate = getGrowthRate();
        return growthRate != null && growthRate < 0;
    }

    /**
     * 获取增长趋势
     */
    public GrowthTrend getGrowthTrend() {
        Double growthRate = getGrowthRate();
        if (growthRate == null) {
            return GrowthTrend.UNKNOWN;
        }
        
        if (growthRate > 5) {
            return GrowthTrend.STRONG_UP;
        } else if (growthRate > 0) {
            return GrowthTrend.UP;
        } else if (growthRate == 0) {
            return GrowthTrend.FLAT;
        } else if (growthRate > -5) {
            return GrowthTrend.DOWN;
        } else {
            return GrowthTrend.STRONG_DOWN;
        }
    }

    /**
     * 增长趋势枚举
     */
    public enum GrowthTrend {
        STRONG_UP("强劲上升"),
        UP("上升"),
        FLAT("持平"),
        DOWN("下降"),
        STRONG_DOWN("大幅下降"),
        UNKNOWN("未知");

        private final String description;

        GrowthTrend(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 创建计数类型指标
     */
    public static MetricValue ofCount(String name, Number value, Number previousValue) {
        return MetricValue.builder()
                .name(name)
                .value(value)
                .previousValue(previousValue)
                .type(MetricType.COUNT)
                .unit("个")
                .calculatedAt(LocalDateTime.now())
                .build();
    }

    /**
     * 创建百分比类型指标
     */
    public static MetricValue ofPercentage(String name, Number value, Number previousValue) {
        return MetricValue.builder()
                .name(name)
                .value(value)
                .previousValue(previousValue)
                .type(MetricType.PERCENTAGE)
                .unit("%")
                .calculatedAt(LocalDateTime.now())
                .build();
    }

    /**
     * 创建金额类型指标
     */
    public static MetricValue ofAmount(String name, Number value, Number previousValue) {
        return MetricValue.builder()
                .name(name)
                .value(value)
                .previousValue(previousValue)
                .type(MetricType.AMOUNT)
                .unit("元")
                .calculatedAt(LocalDateTime.now())
                .build();
    }

    // 私有格式化方法
    private String formatCount(Number value) {
        long count = value.longValue();
        if (count >= 100000000) {
            return String.format("%.2f亿", count / 100000000.0);
        } else if (count >= 10000) {
            return String.format("%.2f万", count / 10000.0);
        } else {
            return String.valueOf(count);
        }
    }

    private String formatPercentage(Number value) {
        return String.format("%.2f", value.doubleValue());
    }

    private String formatRatio(Number value) {
        return String.format("%.3f", value.doubleValue());
    }

    private String formatAmount(Number value) {
        double amount = value.doubleValue();
        if (amount >= 100000000) {
            return String.format("%.2f亿", amount / 100000000.0);
        } else if (amount >= 10000) {
            return String.format("%.2f万", amount / 10000.0);
        } else {
            return String.format("%.2f", amount);
        }
    }

    private String formatDuration(Number value) {
        long seconds = value.longValue();
        long hours = seconds / 3600;
        long minutes = (seconds % 3600) / 60;
        long secs = seconds % 60;
        
        if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, secs);
        } else {
            return String.format("%d秒", secs);
        }
    }

    private String formatRate(Number value) {
        return String.format("%.2f", value.doubleValue());
    }
}
