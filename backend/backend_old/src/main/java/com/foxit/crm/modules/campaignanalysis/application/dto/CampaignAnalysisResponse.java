package com.foxit.crm.modules.campaignanalysis.application.dto;

import com.foxit.crm.modules.campaignanalysis.domain.entity.CampaignAggregate;
import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 活动分析响应DTO
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Getter
@Builder
public class CampaignAnalysisResponse {

    /**
     * 是否成功
     */
    private final boolean success;

    /**
     * 消息
     */
    private final String message;

    /**
     * 聚合根ID
     */
    private final String aggregateId;

    /**
     * 分析类型
     */
    private final String analysisType;

    /**
     * 时间范围
     */
    private final TimeRange timeRange;

    /**
     * 活动ID列表
     */
    private final List<Long> campaignIds;

    /**
     * 核心指标
     */
    private final Map<String, MetricValue> coreMetrics;

    /**
     * 活动列表数据
     */
    private final List<CampaignAggregate.CampaignData> campaignList;

    /**
     * 趋势数据
     */
    private final Map<String, CampaignAggregate.TrendData> trendData;

    /**
     * 渠道分析数据
     */
    private final Map<String, CampaignAggregate.ChannelData> channelData;

    /**
     * 数据权限范围
     */
    private final String dataScope;

    /**
     * 最后更新时间
     */
    private final LocalDateTime lastUpdateTime;

    /**
     * 数据摘要
     */
    private final String dataSummary;

    /**
     * 创建成功响应
     */
    public static CampaignAnalysisResponse success(CampaignAggregate aggregate) {
        return CampaignAnalysisResponse.builder()
                .success(true)
                .message("获取活动分析数据成功")
                .aggregateId(aggregate.getId())
                .analysisType(aggregate.getAnalysisType().name())
                .timeRange(aggregate.getTimeRange())
                .campaignIds(aggregate.getCampaignIds())
                .coreMetrics(aggregate.getCoreMetrics())
                .campaignList(aggregate.getCampaignList())
                .trendData(aggregate.getTrendData())
                .channelData(aggregate.getChannelData())
                .dataScope(aggregate.getDataScope())
                .lastUpdateTime(aggregate.getLastUpdateTime())
                .dataSummary(aggregate.getDataSummary())
                .build();
    }

    /**
     * 创建空响应
     */
    public static CampaignAnalysisResponse empty(String message) {
        return CampaignAnalysisResponse.builder()
                .success(false)
                .message(message)
                .build();
    }

    /**
     * 创建错误响应
     */
    public static CampaignAnalysisResponse error(String message) {
        return CampaignAnalysisResponse.builder()
                .success(false)
                .message(message)
                .build();
    }
}
