package com.foxit.crm.modules.behavioranalysis.application.dto;

import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 功能使用分析请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@Builder
@Schema(description = "功能使用分析请求")
public class FeatureUsageRequest {

    @Schema(description = "开始日期", example = "2025-01-01")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2025-01-31")
    private LocalDate endDate;

    @Schema(description = "时间粒度", example = "DAY")
    private TimeRange.TimeGranularity granularity;

    @Schema(description = "产品线ID列表")
    private List<Long> productLineIds;

    @Schema(description = "功能ID列表")
    private List<String> featureIds;

    @Schema(description = "起始功能ID列表（用于路径分析）")
    private List<String> startFeatureIds;

    @Schema(description = "结束功能ID列表（用于路径分析）")
    private List<String> endFeatureIds;

    @Schema(description = "单个功能ID（用于详细分析）")
    private String featureId;

    @Schema(description = "功能分类列表")
    private List<String> featureCategories;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "数据权限范围")
    private String dataScope;

    @Schema(description = "是否包含对比数据")
    private Boolean includeComparison;

    @Schema(description = "是否包含详细数据")
    private Boolean includeDetails;

    @Schema(description = "分析类型")
    private String analysisType;

    @Schema(description = "筛选条件")
    private FeatureFilterCondition filterCondition;

    /**
     * 功能筛选条件
     */
    @Data
    @Builder
    @Schema(description = "功能筛选条件")
    public static class FeatureFilterCondition {

        @Schema(description = "功能类型")
        private List<String> featureTypes;

        @Schema(description = "功能优先级")
        private List<Integer> priorities;

        @Schema(description = "功能版本")
        private List<String> versions;

        @Schema(description = "用户群体ID")
        private List<String> userSegmentIds;

        @Schema(description = "设备类型")
        private List<String> deviceTypes;

        @Schema(description = "操作系统")
        private List<String> operatingSystems;

        @Schema(description = "应用版本")
        private List<String> appVersions;

        @Schema(description = "地域")
        private List<String> regions;

        @Schema(description = "渠道")
        private List<String> channels;

        @Schema(description = "最小使用次数")
        private Integer minUsageCount;

        @Schema(description = "最大使用次数")
        private Integer maxUsageCount;

        @Schema(description = "最小用户数")
        private Integer minUserCount;

        @Schema(description = "最大用户数")
        private Integer maxUserCount;

        @Schema(description = "最小使用率")
        private Double minUsageRate;

        @Schema(description = "最大使用率")
        private Double maxUsageRate;

        @Schema(description = "自定义属性筛选")
        private List<PropertyFilter> propertyFilters;
    }

    /**
     * 属性筛选条件
     */
    @Data
    @Builder
    @Schema(description = "属性筛选条件")
    public static class PropertyFilter {

        @Schema(description = "属性名称")
        private String propertyName;

        @Schema(description = "操作符", example = "equals, contains, greater_than, less_than")
        private String operator;

        @Schema(description = "属性值")
        private String propertyValue;

        @Schema(description = "属性值列表（用于in操作）")
        private List<String> propertyValues;
    }

    /**
     * 创建功能使用统计分析请求
     */
    public static FeatureUsageRequest createUsageStatisticsRequest(LocalDate startDate, LocalDate endDate, 
                                                                  List<String> featureIds, String dataScope) {
        return FeatureUsageRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .featureIds(featureIds)
                .dataScope(dataScope)
                .analysisType("USAGE_STATISTICS")
                .includeComparison(false)
                .includeDetails(false)
                .build();
    }

    /**
     * 创建功能热度分析请求
     */
    public static FeatureUsageRequest createHeatAnalysisRequest(LocalDate startDate, LocalDate endDate, 
                                                               List<String> featureIds, String dataScope) {
        return FeatureUsageRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .featureIds(featureIds)
                .dataScope(dataScope)
                .analysisType("HEAT_ANALYSIS")
                .includeComparison(false)
                .includeDetails(true)
                .build();
    }

    /**
     * 创建功能路径分析请求
     */
    public static FeatureUsageRequest createPathAnalysisRequest(LocalDate startDate, LocalDate endDate, 
                                                               List<String> startFeatureIds, List<String> endFeatureIds, 
                                                               String dataScope) {
        return FeatureUsageRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .startFeatureIds(startFeatureIds)
                .endFeatureIds(endFeatureIds)
                .dataScope(dataScope)
                .analysisType("PATH_ANALYSIS")
                .includeComparison(false)
                .includeDetails(true)
                .build();
    }

    /**
     * 创建功能价值贡献分析请求
     */
    public static FeatureUsageRequest createValueContributionRequest(LocalDate startDate, LocalDate endDate, 
                                                                    List<String> featureIds, String dataScope) {
        return FeatureUsageRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .featureIds(featureIds)
                .dataScope(dataScope)
                .analysisType("VALUE_CONTRIBUTION")
                .includeComparison(false)
                .includeDetails(true)
                .build();
    }

    /**
     * 创建功能满意度评估请求
     */
    public static FeatureUsageRequest createSatisfactionEvaluationRequest(LocalDate startDate, LocalDate endDate, 
                                                                          List<String> featureIds, String dataScope) {
        return FeatureUsageRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .featureIds(featureIds)
                .dataScope(dataScope)
                .analysisType("SATISFACTION_EVALUATION")
                .includeComparison(false)
                .includeDetails(true)
                .build();
    }

    /**
     * 验证请求参数
     */
    public boolean isValid() {
        if (startDate == null || endDate == null) {
            return false;
        }
        
        if (startDate.isAfter(endDate)) {
            return false;
        }
        
        if (dataScope == null || dataScope.trim().isEmpty()) {
            return false;
        }
        
        // 根据分析类型验证特定参数
        if (analysisType != null) {
            switch (analysisType) {
                case "USAGE_STATISTICS":
                case "HEAT_ANALYSIS":
                case "VALUE_CONTRIBUTION":
                case "SATISFACTION_EVALUATION":
                    return featureIds != null && !featureIds.isEmpty();
                case "PATH_ANALYSIS":
                    return startFeatureIds != null && !startFeatureIds.isEmpty();
                default:
                    return true;
            }
        }
        
        return true;
    }

    /**
     * 获取请求摘要
     */
    public String getSummary() {
        return String.format("功能使用分析请求: 类型=%s, 时间范围=%s至%s, 功能数=%d", 
                analysisType != null ? analysisType : "未指定",
                startDate != null ? startDate.toString() : "未指定",
                endDate != null ? endDate.toString() : "未指定",
                featureIds != null ? featureIds.size() : 0);
    }
}
