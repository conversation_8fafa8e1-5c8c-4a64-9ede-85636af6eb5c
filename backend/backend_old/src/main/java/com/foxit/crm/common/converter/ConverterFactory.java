package com.foxit.crm.common.converter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.ResolvableType;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 转换器工厂类
 * 
 * 提供统一的转换器获取和管理功能，支持自动发现和缓存转换器实例
 * 
 * <AUTHOR>
 * @since 2025-06-23
 */
@Component
public class ConverterFactory {
    
    private final ApplicationContext applicationContext;
    private final Map<String, DtoConverter<?, ?>> converterCache = new ConcurrentHashMap<>();
    
    @Autowired
    public ConverterFactory(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }
    
    /**
     * 根据实体类和DTO类获取对应的转换器
     * 
     * @param entityClass 实体类
     * @param dtoClass DTO类
     * @param <E> 实体类型
     * @param <D> DTO类型
     * @return 转换器实例
     * @throws IllegalArgumentException 如果找不到对应的转换器
     */
    @SuppressWarnings("unchecked")
    public <E, D> DtoConverter<E, D> getConverter(Class<E> entityClass, Class<D> dtoClass) {
        String key = entityClass.getName() + "->" + dtoClass.getName();
        
        return (DtoConverter<E, D>) converterCache.computeIfAbsent(key, k -> {
            Map<String, DtoConverter> converters = applicationContext.getBeansOfType(DtoConverter.class);
            
            for (DtoConverter<?, ?> converter : converters.values()) {
                ResolvableType resolvableType = ResolvableType.forClass(converter.getClass());
                ResolvableType[] generics = resolvableType.as(DtoConverter.class).getGenerics();
                
                if (generics.length == 2) {
                    Class<?> entityType = generics[0].resolve();
                    Class<?> dtoType = generics[1].resolve();
                    
                    if (entityClass.equals(entityType) && dtoClass.equals(dtoType)) {
                        return converter;
                    }
                }
            }
            
            throw new IllegalArgumentException(
                "No converter found for " + entityClass.getSimpleName() + " -> " + dtoClass.getSimpleName()
            );
        });
    }
    
    /**
     * 根据实体类获取默认的转换器
     * 
     * 注意：此方法假设每个实体类只有一个主要的DTO转换器
     * 
     * @param entityClass 实体类
     * @param <E> 实体类型
     * @return 转换器实例
     * @throws IllegalArgumentException 如果找不到对应的转换器
     */
    @SuppressWarnings("unchecked")
    public <E> DtoConverter<E, ?> getConverter(Class<E> entityClass) {
        String key = entityClass.getName() + "->*";
        
        return (DtoConverter<E, ?>) converterCache.computeIfAbsent(key, k -> {
            Map<String, DtoConverter> converters = applicationContext.getBeansOfType(DtoConverter.class);
            
            for (DtoConverter<?, ?> converter : converters.values()) {
                ResolvableType resolvableType = ResolvableType.forClass(converter.getClass());
                ResolvableType[] generics = resolvableType.as(DtoConverter.class).getGenerics();
                
                if (generics.length == 2) {
                    Class<?> entityType = generics[0].resolve();
                    
                    if (entityClass.equals(entityType)) {
                        return converter;
                    }
                }
            }
            
            throw new IllegalArgumentException(
                "No converter found for entity: " + entityClass.getSimpleName()
            );
        });
    }
    
    /**
     * 清空转换器缓存
     * 
     * 在开发环境或测试环境中可能需要清空缓存以重新加载转换器
     */
    public void clearCache() {
        converterCache.clear();
    }
    
    /**
     * 获取所有已注册的转换器
     * 
     * @return 转换器映射表
     */
    public Map<String, DtoConverter<?, ?>> getAllConverters() {
        return Map.copyOf(converterCache);
    }
}
