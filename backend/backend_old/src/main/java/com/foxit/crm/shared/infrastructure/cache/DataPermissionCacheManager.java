package com.foxit.crm.shared.infrastructure.cache;

import com.foxit.crm.common.constant.RedisKeyConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 数据权限缓存管理器
 * 使用Spring Cache注解管理数据权限相关的缓存
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataPermissionCacheManager {

    /**
     * 缓存用户数据权限规则
     */
    @Cacheable(value = "dataPermission", key = "'user:' + #userId + ':table:' + #tableName")
    public List<String> getUserDataPermissionRules(Long userId, String tableName, 
                                                   java.util.function.Supplier<List<String>> dataLoader) {
        log.debug("加载用户数据权限规则: userId={}, tableName={}", userId, tableName);
        return dataLoader.get();
    }

    /**
     * 缓存角色数据权限规则
     */
    @Cacheable(value = "dataPermission", key = "'role:' + #roleId + ':table:' + #tableName")
    public List<String> getRoleDataPermissionRules(Long roleId, String tableName,
                                                   java.util.function.Supplier<List<String>> dataLoader) {
        log.debug("加载角色数据权限规则: roleId={}, tableName={}", roleId, tableName);
        return dataLoader.get();
    }

    /**
     * 缓存用户可访问的数据范围
     */
    @Cacheable(value = "dataPermission", key = "'user:' + #userId + ':scope:' + #resourceType")
    public List<Long> getUserDataScope(Long userId, String resourceType,
                                      java.util.function.Supplier<List<Long>> dataLoader) {
        log.debug("加载用户数据范围: userId={}, resourceType={}", userId, resourceType);
        return dataLoader.get();
    }

    /**
     * 缓存用户行级权限过滤条件
     */
    @Cacheable(value = "dataPermission", key = "'user:' + #userId + ':filter:' + #tableName")
    public Map<String, Object> getUserRowLevelFilter(Long userId, String tableName,
                                                     java.util.function.Supplier<Map<String, Object>> dataLoader) {
        log.debug("加载用户行级权限过滤条件: userId={}, tableName={}", userId, tableName);
        return dataLoader.get();
    }

    /**
     * 缓存用户列级权限
     */
    @Cacheable(value = "dataPermission", key = "'user:' + #userId + ':columns:' + #tableName")
    public List<String> getUserColumnPermissions(Long userId, String tableName,
                                                 java.util.function.Supplier<List<String>> dataLoader) {
        log.debug("加载用户列级权限: userId={}, tableName={}", userId, tableName);
        return dataLoader.get();
    }

    /**
     * 缓存部门数据权限
     */
    @Cacheable(value = "dataPermission", key = "'dept:' + #deptId + ':scope'")
    public List<Long> getDepartmentDataScope(Long deptId,
                                            java.util.function.Supplier<List<Long>> dataLoader) {
        log.debug("加载部门数据权限: deptId={}", deptId);
        return dataLoader.get();
    }

    /**
     * 缓存数据权限规则模板
     */
    @Cacheable(value = "dataPermission", key = "'template:' + #templateId")
    public Map<String, Object> getDataPermissionTemplate(Long templateId,
                                                         java.util.function.Supplier<Map<String, Object>> dataLoader) {
        log.debug("加载数据权限规则模板: templateId={}", templateId);
        return dataLoader.get();
    }

    /**
     * 清除用户相关的数据权限缓存
     */
    @CacheEvict(value = "dataPermission", key = "'user:' + #userId + ':*'")
    public void evictUserDataPermissions(Long userId) {
        log.debug("清除用户数据权限缓存: userId={}", userId);
    }

    /**
     * 清除角色相关的数据权限缓存
     */
    @CacheEvict(value = "dataPermission", key = "'role:' + #roleId + ':*'")
    public void evictRoleDataPermissions(Long roleId) {
        log.debug("清除角色数据权限缓存: roleId={}", roleId);
    }

    /**
     * 清除指定表的数据权限缓存
     */
    @CacheEvict(value = "dataPermission", key = "'*:table:' + #tableName")
    public void evictTableDataPermissions(String tableName) {
        log.debug("清除表数据权限缓存: tableName={}", tableName);
    }

    /**
     * 清除部门数据权限缓存
     */
    @CacheEvict(value = "dataPermission", key = "'dept:' + #deptId + ':*'")
    public void evictDepartmentDataPermissions(Long deptId) {
        log.debug("清除部门数据权限缓存: deptId={}", deptId);
    }

    /**
     * 清除所有数据权限缓存
     */
    @CacheEvict(value = "dataPermission", allEntries = true)
    public void evictAllDataPermissions() {
        log.info("清除所有数据权限缓存");
    }

    /**
     * 批量清除用户数据权限缓存
     */
    public void evictUsersDataPermissions(List<Long> userIds) {
        userIds.forEach(this::evictUserDataPermissions);
        log.debug("批量清除用户数据权限缓存: userIds={}", userIds);
    }

    /**
     * 批量清除角色数据权限缓存
     */
    public void evictRolesDataPermissions(List<Long> roleIds) {
        roleIds.forEach(this::evictRoleDataPermissions);
        log.debug("批量清除角色数据权限缓存: roleIds={}", roleIds);
    }

    /**
     * 预热用户数据权限缓存
     */
    public void warmUpUserDataPermissions(Long userId, List<String> tableNames,
                                         java.util.function.BiFunction<Long, String, List<String>> dataLoader) {
        for (String tableName : tableNames) {
            getUserDataPermissionRules(userId, tableName, () -> dataLoader.apply(userId, tableName));
        }
        log.debug("预热用户数据权限缓存完成: userId={}, tables={}", userId, tableNames);
    }

    /**
     * 构建复合缓存键
     */
    public String buildCompositeKey(String prefix, Object... parts) {
        StringBuilder keyBuilder = new StringBuilder(prefix);
        for (Object part : parts) {
            keyBuilder.append(":").append(part);
        }
        return keyBuilder.toString();
    }

    /**
     * 检查缓存是否存在
     */
    public boolean isCacheExists(String cacheKey) {
        // 这里可以通过RedissonClient检查键是否存在
        // 暂时返回false，实际实现需要注入RedissonClient
        return false;
    }

    /**
     * 获取缓存过期时间
     */
    public long getCacheExpireTime(String cacheKey) {
        // 这里可以通过RedissonClient获取键的过期时间
        // 暂时返回-1，实际实现需要注入RedissonClient
        return -1;
    }
}
