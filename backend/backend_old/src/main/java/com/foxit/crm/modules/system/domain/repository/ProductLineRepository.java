package com.foxit.crm.modules.system.domain.repository;

import com.foxit.crm.modules.system.domain.model.aggregate.ProductLine;

import java.util.List;
import java.util.Optional;

/**
 * 产品线仓储接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface ProductLineRepository {

    /**
     * 保存产品线
     */
    ProductLine save(ProductLine productLine);

    /**
     * 根据ID查找产品线
     */
    Optional<ProductLine> findById(Long id);

    /**
     * 根据产品线编码查找产品线
     */
    Optional<ProductLine> findByCode(String code);

    /**
     * 查找所有启用的产品线
     */
    List<ProductLine> findAllEnabled();

    /**
     * 根据产品线类型查找产品线
     */
    List<ProductLine> findByType(Integer type);

    /**
     * 根据负责人ID查找产品线
     */
    List<ProductLine> findByOwnerId(Long ownerId);

    /**
     * 分页查询产品线
     */
    List<ProductLine> findByPage(int page, int size, String keyword, Integer type, Integer status);

    /**
     * 统计产品线总数
     */
    long count(String keyword, Integer type, Integer status);

    /**
     * 删除产品线
     */
    void deleteById(Long id);

    /**
     * 检查产品线编码是否存在
     */
    boolean existsByCode(String code);

    /**
     * 检查产品线编码是否存在（排除指定ID）
     */
    boolean existsByCodeAndIdNot(String code, Long id);

    /**
     * 检查产品线名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 检查产品线名称是否存在（排除指定ID）
     */
    boolean existsByNameAndIdNot(String name, Long id);

    /**
     * 根据用户ID查找有权限的产品线列表
     */
    List<ProductLine> findByUserId(Long userId);

    /**
     * 获取所有产品线类型的统计信息
     */
    List<ProductLine> findAllWithTypeCount();

    /**
     * 根据状态查找产品线
     */
    List<ProductLine> findByStatus(Integer status);

    /**
     * 批量更新产品线状态
     */
    void updateStatusBatch(List<Long> ids, Integer status);
}
