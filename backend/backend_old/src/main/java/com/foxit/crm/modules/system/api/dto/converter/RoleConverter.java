package com.foxit.crm.modules.system.api.dto.converter;

import com.foxit.crm.common.converter.AbstractDtoConverter;
import com.foxit.crm.modules.system.api.dto.response.RoleDetailResponse;
import com.foxit.crm.modules.system.domain.model.aggregate.Role;
import org.springframework.stereotype.Component;

/**
 * 角色转换器
 * 
 * 负责Role实体与RoleDetailResponse DTO之间的转换
 * 
 * <AUTHOR>
 * @since 2025-06-23
 */
@Component
public class RoleConverter extends AbstractDtoConverter<Role, RoleDetailResponse> {
    
    @Override
    protected Class<RoleDetailResponse> getDtoClass() {
        return RoleDetailResponse.class;
    }
    
    @Override
    protected void customizeDto(Role entity, RoleDetailResponse dto) {
        // 设置基础字段
        dto.setId(entity.getId());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateTime(entity.getUpdateTime());
        
        // 这里可以添加其他自定义转换逻辑
        // 例如：权限ID列表的获取、状态文本转换等
        // dto.setPermissionIds(rolePermissionService.getPermissionIdsByRoleId(entity.getId()));
    }
}
