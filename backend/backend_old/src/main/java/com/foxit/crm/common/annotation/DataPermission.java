package com.foxit.crm.common.annotation;

import java.lang.annotation.*;

/**
 * 数据权限注解
 * 用于控制用户对数据的访问权限，支持行级和列级权限控制
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataPermission {

    /**
     * 数据权限类型
     */
    DataScope dataScope() default DataScope.ALL;

    /**
     * 用户字段名（用于关联用户数据）
     */
    String userColumn() default "user_id";

    /**
     * 部门字段名（用于关联部门数据）
     */
    String deptColumn() default "dept_id";

    /**
     * 产品线字段名（用于关联产品线数据）
     */
    String productLineColumn() default "product_line_id";

    /**
     * 表别名
     */
    String tableAlias() default "";

    /**
     * 是否启用
     */
    boolean enabled() default true;

    /**
     * 自定义权限表达式
     */
    String customExpression() default "";

    /**
     * 排除的字段（列级权限控制）
     */
    String[] excludeColumns() default {};

    /**
     * 包含的字段（列级权限控制，如果指定则只返回这些字段）
     */
    String[] includeColumns() default {};

    /**
     * 数据权限范围枚举
     */
    enum DataScope {
        /**
         * 全部数据权限
         */
        ALL,

        /**
         * 自定义数据权限
         */
        CUSTOM,

        /**
         * 部门数据权限
         */
        DEPT,

        /**
         * 部门及以下数据权限
         */
        DEPT_AND_CHILD,

        /**
         * 仅本人数据权限
         */
        SELF,

        /**
         * 产品线数据权限
         */
        PRODUCT_LINE,

        /**
         * 产品线及关联数据权限
         */
        PRODUCT_LINE_AND_RELATED
    }
}
