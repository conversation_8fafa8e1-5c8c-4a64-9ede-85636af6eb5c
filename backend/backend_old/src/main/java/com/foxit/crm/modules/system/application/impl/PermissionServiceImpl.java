package com.foxit.crm.modules.system.application.impl;

import com.foxit.crm.common.exception.BusinessException;
import com.foxit.crm.modules.system.api.dto.request.PermissionCreateRequest;
import com.foxit.crm.modules.system.api.dto.request.PermissionUpdateRequest;
import com.foxit.crm.modules.system.api.dto.response.PermissionDetailResponse;
import com.foxit.crm.modules.system.api.dto.response.PermissionTreeResponse;
import com.foxit.crm.modules.system.api.dto.response.PageResponse;
import com.foxit.crm.modules.system.application.service.PermissionService;
import com.foxit.crm.modules.system.domain.model.aggregate.Permission;
import com.foxit.crm.modules.system.domain.repository.PermissionRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 权限应用服务实现
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
@RequiredArgsConstructor
public class PermissionServiceImpl implements PermissionService {

    private final PermissionRepository permissionRepository;

    @Override
    @Transactional
    @CacheEvict(value = { "permissionTree", "menuPermissionTree", "userPermissions",
            "rolePermissions" }, allEntries = true)
    public Long createPermission(PermissionCreateRequest request) {
        // 检查权限编码是否已存在
        if (permissionRepository.existsByPermissionCode(request.getPermissionCode())) {
            throw new BusinessException("权限编码已存在");
        }

        // 如果不是根权限，检查父权限是否存在
        if (request.getParentId() != 0) {
            permissionRepository.findById(request.getParentId())
                    .orElseThrow(() -> new BusinessException("父权限不存在"));
        }

        // 创建权限领域对象
        Permission permission = new Permission(
                request.getParentId(),
                request.getPermissionName(),
                request.getPermissionCode(),
                request.getPermissionType(),
                request.getPath(),
                request.getComponent(),
                request.getIcon(),
                request.getSortOrder(),
                request.getStatus());

        // 保存权限
        Permission savedPermission = permissionRepository.save(permission);

        return savedPermission.getId();
    }

    @Override
    @Transactional
    @CacheEvict(value = { "permissionTree", "menuPermissionTree", "userPermissions",
            "rolePermissions" }, allEntries = true)
    public void updatePermission(Long id, PermissionUpdateRequest request) {
        // 查找权限
        Permission permission = permissionRepository.findById(id)
                .orElseThrow(() -> new BusinessException("权限不存在"));

        // 检查权限编码是否已被其他权限使用
        if (permissionRepository.existsByPermissionCodeAndIdNot(request.getPermissionCode(), id)) {
            throw new BusinessException("权限编码已存在");
        }

        // 如果不是根权限，检查父权限是否存在
        if (request.getParentId() != 0) {
            permissionRepository.findById(request.getParentId())
                    .orElseThrow(() -> new BusinessException("父权限不存在"));
        }

        // 检查是否将权限设置为自己的子权限（防止循环引用）
        if (request.getParentId().equals(id)) {
            throw new BusinessException("不能将权限设置为自己的子权限");
        }

        // 更新权限信息
        permission.setParentId(request.getParentId());
        permission.setPermissionName(request.getPermissionName());
        permission.setPermissionCode(request.getPermissionCode());
        permission.setPermissionType(request.getPermissionType());
        permission.setPath(request.getPath());
        permission.setComponent(request.getComponent());
        permission.setIcon(request.getIcon());
        permission.setSortOrder(request.getSortOrder());
        permission.setStatus(request.getStatus());
        permission.setVersion(request.getVersion());

        // 保存更新
        permissionRepository.save(permission);
    }

    @Override
    @Transactional
    @CacheEvict(value = { "permissionTree", "menuPermissionTree", "userPermissions",
            "rolePermissions" }, allEntries = true)
    public void deletePermission(Long id) {
        // 检查权限是否存在
        Permission permission = permissionRepository.findById(id)
                .orElseThrow(() -> new BusinessException("权限不存在"));

        // 检查是否有子权限
        if (permissionRepository.hasChildren(id)) {
            throw new BusinessException("该权限下存在子权限，无法删除");
        }

        // TODO: 检查是否有角色关联此权限，如果有则不允许删除

        // 删除权限
        permissionRepository.deleteById(id);
    }

    @Override
    public PermissionDetailResponse getPermissionById(Long id) {
        Permission permission = permissionRepository.findById(id)
                .orElseThrow(() -> new BusinessException("权限不存在"));

        return convertToDetailResponse(permission);
    }

    @Override
    public PageResponse<PermissionDetailResponse> getPermissionList(int page, int size, String keyword,
            Integer permissionType) {
        List<Permission> permissions = permissionRepository.findByPage(page, size, keyword, permissionType);
        long total = permissionRepository.count(keyword, permissionType);

        List<PermissionDetailResponse> permissionResponses = permissions.stream()
                .map(this::convertToDetailResponse)
                .collect(Collectors.toList());

        return new PageResponse<>(permissionResponses, total, page, size);
    }

    @Override
    @Cacheable(value = "permissionTree")
    public List<PermissionTreeResponse> getPermissionTree() {
        List<Permission> permissions = permissionRepository.buildPermissionTree();
        return permissions.stream()
                .map(this::convertToTreeResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<PermissionDetailResponse> getPermissionsByParentId(Long parentId) {
        List<Permission> permissions = permissionRepository.findByParentId(parentId);
        return permissions.stream()
                .map(this::convertToDetailResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<PermissionDetailResponse> getPermissionsByType(Integer permissionType) {
        List<Permission> permissions = permissionRepository.findByPermissionType(permissionType);
        return permissions.stream()
                .map(this::convertToDetailResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "userPermissions", key = "#userId")
    public List<PermissionDetailResponse> getPermissionsByUserId(Long userId) {
        List<Permission> permissions = permissionRepository.findByUserId(userId);
        return permissions.stream()
                .map(this::convertToDetailResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "rolePermissions", key = "#roleId")
    public List<PermissionDetailResponse> getPermissionsByRoleId(Long roleId) {
        List<Permission> permissions = permissionRepository.findByRoleId(roleId);
        return permissions.stream()
                .map(this::convertToDetailResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void enablePermission(Long id) {
        Permission permission = permissionRepository.findById(id)
                .orElseThrow(() -> new BusinessException("权限不存在"));

        permission.enable();
        permissionRepository.save(permission);
    }

    @Override
    @Transactional
    public void disablePermission(Long id) {
        Permission permission = permissionRepository.findById(id)
                .orElseThrow(() -> new BusinessException("权限不存在"));

        permission.disable();
        permissionRepository.save(permission);
    }

    @Override
    @Cacheable(value = "menuPermissionTree")
    public List<PermissionTreeResponse> getEnabledMenuPermissions() {
        List<Permission> permissions = permissionRepository
                .findByPermissionType(Permission.PermissionType.MENU.getCode());
        List<Permission> enabledPermissions = permissions.stream()
                .filter(Permission::isEnabled)
                .collect(Collectors.toList());

        return buildPermissionTree(enabledPermissions);
    }

    @Override
    public List<PermissionDetailResponse> getPermissionsByIds(List<Long> ids) {
        List<Permission> permissions = permissionRepository.findByIds(ids);
        return permissions.stream()
                .map(this::convertToDetailResponse)
                .collect(Collectors.toList());
    }

    /**
     * 构建权限树
     */
    private List<PermissionTreeResponse> buildPermissionTree(List<Permission> permissions) {
        return permissions.stream()
                .filter(permission -> permission.getParentId() == null || permission.getParentId() == 0)
                .map(permission -> {
                    PermissionTreeResponse treeResponse = convertToTreeResponse(permission);
                    treeResponse.setChildren(buildChildren(permissions, permission.getId()));
                    return treeResponse;
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建子权限
     */
    private List<PermissionTreeResponse> buildChildren(List<Permission> permissions, Long parentId) {
        return permissions.stream()
                .filter(permission -> parentId.equals(permission.getParentId()))
                .map(permission -> {
                    PermissionTreeResponse treeResponse = convertToTreeResponse(permission);
                    treeResponse.setChildren(buildChildren(permissions, permission.getId()));
                    return treeResponse;
                })
                .collect(Collectors.toList());
    }

    /**
     * 转换为详情响应对象
     */
    private PermissionDetailResponse convertToDetailResponse(Permission permission) {
        PermissionDetailResponse response = new PermissionDetailResponse();
        BeanUtils.copyProperties(permission, response);
        response.setPermissionType(permission.getPermissionType()); // 触发类型文本设置
        response.setStatus(permission.getStatus()); // 触发状态文本设置
        return response;
    }

    /**
     * 转换为树响应对象
     */
    private PermissionTreeResponse convertToTreeResponse(Permission permission) {
        PermissionTreeResponse response = new PermissionTreeResponse();
        BeanUtils.copyProperties(permission, response);
        response.setPermissionType(permission.getPermissionType()); // 触发类型文本设置
        response.setStatus(permission.getStatus()); // 触发状态文本设置

        // 递归转换子权限
        if (permission.getChildren() != null && !permission.getChildren().isEmpty()) {
            List<PermissionTreeResponse> children = permission.getChildren().stream()
                    .map(this::convertToTreeResponse)
                    .collect(Collectors.toList());
            response.setChildren(children);
        }

        return response;
    }
}
