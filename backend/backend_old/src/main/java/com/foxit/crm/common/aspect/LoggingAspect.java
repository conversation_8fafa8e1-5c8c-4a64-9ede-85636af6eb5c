package com.foxit.crm.common.aspect;

import com.foxit.crm.shared.domain.event.OperationLog;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 日志切面
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Aspect
@Component
public class LoggingAspect {

    private static final Logger logger = LoggerFactory.getLogger(LoggingAspect.class);

    /**
     * 处理带有@OperationLog注解的方法
     */
    @Before("@annotation(operationLog)")
    public void logBefore(JoinPoint joinPoint, OperationLog operationLog) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        
        logger.info("开始执行操作: {} - {}.{}", 
                operationLog.value(), className, methodName);
    }

    /**
     * 方法正常返回后记录日志
     */
    @AfterReturning(pointcut = "@annotation(operationLog)", returning = "result")
    public void logAfterReturning(JoinPoint joinPoint, OperationLog operationLog, Object result) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        
        logger.info("操作执行成功: {} - {}.{}", 
                operationLog.value(), className, methodName);
    }

    /**
     * 方法抛出异常后记录日志
     */
    @AfterThrowing(pointcut = "@annotation(operationLog)", throwing = "exception")
    public void logAfterThrowing(JoinPoint joinPoint, OperationLog operationLog, Throwable exception) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        
        logger.error("操作执行失败: {} - {}.{}, 异常: {}", 
                operationLog.value(), className, methodName, exception.getMessage());
    }
}
