package com.foxit.crm.modules.behavioranalysis.application.dto;

import com.foxit.crm.modules.behavioranalysis.domain.repository.UserPathAnalysisRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 用户路径分析请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@Builder
@Schema(description = "用户路径分析请求")
public class UserPathAnalysisRequest {

    @Schema(description = "开始日期", example = "2025-01-01")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2025-01-31")
    private LocalDate endDate;

    @Schema(description = "时间粒度", example = "DAY")
    private TimeRange.TimeGranularity granularity;

    @Schema(description = "产品线ID列表")
    private List<Long> productLineIds;

    @Schema(description = "起始节点列表")
    private List<String> startNodes;

    @Schema(description = "结束节点列表")
    private List<String> endNodes;

    @Schema(description = "路径组列表（用于对比分析）")
    private List<UserPathAnalysisRepository.PathGroup> pathGroups;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "数据权限范围")
    private String dataScope;

    @Schema(description = "是否包含对比数据")
    private Boolean includeComparison;

    @Schema(description = "是否包含详细数据")
    private Boolean includeDetails;

    @Schema(description = "分析类型")
    private String analysisType;

    @Schema(description = "筛选条件")
    private PathFilterCondition filterCondition;

    /**
     * 路径筛选条件
     */
    @Data
    @Builder
    @Schema(description = "路径筛选条件")
    public static class PathFilterCondition {

        @Schema(description = "节点类型")
        private List<String> nodeTypes;

        @Schema(description = "节点分类")
        private List<String> nodeCategories;

        @Schema(description = "用户群体ID")
        private List<String> userSegmentIds;

        @Schema(description = "设备类型")
        private List<String> deviceTypes;

        @Schema(description = "操作系统")
        private List<String> operatingSystems;

        @Schema(description = "应用版本")
        private List<String> appVersions;

        @Schema(description = "地域")
        private List<String> regions;

        @Schema(description = "渠道")
        private List<String> channels;

        @Schema(description = "最小路径长度")
        private Integer minPathLength;

        @Schema(description = "最大路径长度")
        private Integer maxPathLength;

        @Schema(description = "最小用户数")
        private Integer minUserCount;

        @Schema(description = "最大用户数")
        private Integer maxUserCount;

        @Schema(description = "最小转化率")
        private Double minConversionRate;

        @Schema(description = "最大转化率")
        private Double maxConversionRate;

        @Schema(description = "最小路径时间（分钟）")
        private Integer minPathTime;

        @Schema(description = "最大路径时间（分钟）")
        private Integer maxPathTime;

        @Schema(description = "自定义属性筛选")
        private List<PropertyFilter> propertyFilters;
    }

    /**
     * 属性筛选条件
     */
    @Data
    @Builder
    @Schema(description = "属性筛选条件")
    public static class PropertyFilter {

        @Schema(description = "属性名称")
        private String propertyName;

        @Schema(description = "操作符", example = "equals, contains, greater_than, less_than")
        private String operator;

        @Schema(description = "属性值")
        private String propertyValue;

        @Schema(description = "属性值列表（用于in操作）")
        private List<String> propertyValues;
    }

    /**
     * 创建路径流向分析请求
     */
    public static UserPathAnalysisRequest createPathFlowRequest(LocalDate startDate, LocalDate endDate, 
                                                               List<String> startNodes, List<String> endNodes, String dataScope) {
        return UserPathAnalysisRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .startNodes(startNodes)
                .endNodes(endNodes)
                .dataScope(dataScope)
                .analysisType("PATH_FLOW")
                .includeComparison(false)
                .includeDetails(true)
                .build();
    }

    /**
     * 创建路径统计分析请求
     */
    public static UserPathAnalysisRequest createPathStatisticsRequest(LocalDate startDate, LocalDate endDate, 
                                                                     List<String> startNodes, List<String> endNodes, String dataScope) {
        return UserPathAnalysisRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .startNodes(startNodes)
                .endNodes(endNodes)
                .dataScope(dataScope)
                .analysisType("PATH_STATISTICS")
                .includeComparison(false)
                .includeDetails(true)
                .build();
    }

    /**
     * 创建异常路径检测请求
     */
    public static UserPathAnalysisRequest createAnomalyDetectionRequest(LocalDate startDate, LocalDate endDate, 
                                                                       List<String> startNodes, List<String> endNodes, String dataScope) {
        return UserPathAnalysisRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .startNodes(startNodes)
                .endNodes(endNodes)
                .dataScope(dataScope)
                .analysisType("ANOMALY_DETECTION")
                .includeComparison(false)
                .includeDetails(true)
                .build();
    }

    /**
     * 创建路径效率分析请求
     */
    public static UserPathAnalysisRequest createEfficiencyAnalysisRequest(LocalDate startDate, LocalDate endDate, 
                                                                          List<String> startNodes, List<String> endNodes, String dataScope) {
        return UserPathAnalysisRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .startNodes(startNodes)
                .endNodes(endNodes)
                .dataScope(dataScope)
                .analysisType("EFFICIENCY_ANALYSIS")
                .includeComparison(false)
                .includeDetails(true)
                .build();
    }

    /**
     * 创建路径对比分析请求
     */
    public static UserPathAnalysisRequest createPathComparisonRequest(LocalDate startDate, LocalDate endDate, 
                                                                     List<UserPathAnalysisRepository.PathGroup> pathGroups, String dataScope) {
        return UserPathAnalysisRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .pathGroups(pathGroups)
                .dataScope(dataScope)
                .analysisType("PATH_COMPARISON")
                .includeComparison(true)
                .includeDetails(true)
                .build();
    }

    /**
     * 验证请求参数
     */
    public boolean isValid() {
        if (startDate == null || endDate == null) {
            return false;
        }
        
        if (startDate.isAfter(endDate)) {
            return false;
        }
        
        if (dataScope == null || dataScope.trim().isEmpty()) {
            return false;
        }
        
        // 根据分析类型验证特定参数
        if (analysisType != null) {
            switch (analysisType) {
                case "PATH_FLOW":
                case "PATH_STATISTICS":
                case "ANOMALY_DETECTION":
                case "EFFICIENCY_ANALYSIS":
                    return startNodes != null && !startNodes.isEmpty();
                case "PATH_COMPARISON":
                    return pathGroups != null && !pathGroups.isEmpty();
                default:
                    return true;
            }
        }
        
        return true;
    }

    /**
     * 获取请求摘要
     */
    public String getSummary() {
        return String.format("用户路径分析请求: 类型=%s, 时间范围=%s至%s, 起始节点数=%d", 
                analysisType != null ? analysisType : "未指定",
                startDate != null ? startDate.toString() : "未指定",
                endDate != null ? endDate.toString() : "未指定",
                startNodes != null ? startNodes.size() : 0);
    }
}
