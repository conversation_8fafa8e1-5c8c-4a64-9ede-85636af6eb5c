package com.foxit.crm.modules.behavioranalysis.domain.entity;

import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 功能使用分析聚合根
 * 封装功能使用分析的核心业务逻辑
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Getter
@Builder
public class FeatureUsageAggregate {

    /**
     * 聚合根ID
     */
    private final String id;

    /**
     * 分析类型：USAGE_STATISTICS, HEAT_ANALYSIS, PATH_ANALYSIS, VALUE_CONTRIBUTION, SATISFACTION_EVALUATION
     */
    private final FeatureAnalysisType analysisType;

    /**
     * 时间范围
     */
    private final TimeRange timeRange;

    /**
     * 产品线ID列表
     */
    private final List<Long> productLineIds;

    /**
     * 功能ID列表
     */
    private final List<String> featureIds;

    /**
     * 核心指标
     */
    private final Map<String, MetricValue> coreMetrics;

    /**
     * 功能使用统计数据
     */
    private final Map<String, FeatureUsageStats> usageStats;

    /**
     * 功能热度数据
     */
    private final Map<String, FeatureHeatData> heatData;

    /**
     * 功能路径数据
     */
    private final Map<String, FeaturePathData> pathData;

    /**
     * 功能价值贡献数据
     */
    private final Map<String, FeatureValueData> valueData;

    /**
     * 功能满意度数据
     */
    private final Map<String, FeatureSatisfactionData> satisfactionData;

    /**
     * 数据权限范围
     */
    private final String dataScope;

    /**
     * 最后更新时间
     */
    private final LocalDateTime lastUpdateTime;

    /**
     * 功能分析类型枚举
     */
    public enum FeatureAnalysisType {
        USAGE_STATISTICS("功能使用统计"),
        HEAT_ANALYSIS("功能热度分析"),
        PATH_ANALYSIS("功能路径分析"),
        VALUE_CONTRIBUTION("功能价值贡献"),
        SATISFACTION_EVALUATION("功能满意度评估");

        private final String description;

        FeatureAnalysisType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 功能使用统计数据
     */
    @Getter
    @Builder
    public static class FeatureUsageStats {
        private final String featureId;
        private final String featureName;
        private final String featureCategory;
        private final Long totalUsageCount;
        private final Long uniqueUsers;
        private final Double avgUsagePerUser;
        private final Double usageRate;
        private final List<String> categories;
        private final List<Number> usageCounts;
        private final List<Number> userCounts;
        private final String trendDirection;
        private final Double growthRate;
    }

    /**
     * 功能热度数据
     */
    @Getter
    @Builder
    public static class FeatureHeatData {
        private final String featureId;
        private final String featureName;
        private final Double heatScore;
        private final Integer heatRank;
        private final Double popularityIndex;
        private final Double engagementScore;
        private final Double retentionRate;
        private final List<HeatMapPoint> heatMapPoints;
        private final String heatLevel;
        private final Double changeRate;
    }

    /**
     * 热力图点数据
     */
    @Getter
    @Builder
    public static class HeatMapPoint {
        private final String dimension;
        private final String category;
        private final Double value;
        private final String color;
        private final Integer intensity;
    }

    /**
     * 功能路径数据
     */
    @Getter
    @Builder
    public static class FeaturePathData {
        private final String pathName;
        private final List<FeaturePathNode> nodes;
        private final List<FeaturePathLink> links;
        private final Long totalUsers;
        private final Double avgPathLength;
        private final String mostCommonPath;
        private final List<String> topPaths;
    }

    /**
     * 功能路径节点
     */
    @Getter
    @Builder
    public static class FeaturePathNode {
        private final String featureId;
        private final String featureName;
        private final Long userCount;
        private final Double percentage;
        private final Integer level;
        private final Double avgTimeSpent;
        private final String nodeType;
    }

    /**
     * 功能路径链接
     */
    @Getter
    @Builder
    public static class FeaturePathLink {
        private final String sourceFeatureId;
        private final String targetFeatureId;
        private final Long userCount;
        private final Double percentage;
        private final Double avgTransitionTime;
        private final Double conversionRate;
        private final String linkStrength;
    }

    /**
     * 功能价值贡献数据
     */
    @Getter
    @Builder
    public static class FeatureValueData {
        private final String featureId;
        private final String featureName;
        private final Double valueScore;
        private final Integer valueRank;
        private final Double businessImpact;
        private final Double userSatisfactionImpact;
        private final Double retentionImpact;
        private final Double revenueContribution;
        private final List<ValueMetric> valueMetrics;
        private final String valueLevel;
    }

    /**
     * 价值指标
     */
    @Getter
    @Builder
    public static class ValueMetric {
        private final String metricName;
        private final Double metricValue;
        private final String metricUnit;
        private final Double weight;
        private final String description;
    }

    /**
     * 功能满意度数据
     */
    @Getter
    @Builder
    public static class FeatureSatisfactionData {
        private final String featureId;
        private final String featureName;
        private final Double satisfactionScore;
        private final Integer satisfactionRank;
        private final Double usabilityScore;
        private final Double utilityScore;
        private final Double reliabilityScore;
        private final List<SatisfactionFactor> factors;
        private final List<String> positiveComments;
        private final List<String> negativeComments;
        private final String satisfactionLevel;
    }

    /**
     * 满意度因子
     */
    @Getter
    @Builder
    public static class SatisfactionFactor {
        private final String factorName;
        private final Double factorScore;
        private final Double weight;
        private final String impact;
        private final String description;
    }

    /**
     * 生成聚合根ID
     */
    private static String generateId(String prefix, TimeRange timeRange) {
        return String.format("%s_%s_%s_%s", 
            prefix, 
            timeRange.getStartDate(), 
            timeRange.getEndDate(),
            UUID.randomUUID().toString().substring(0, 8));
    }

    /**
     * 获取核心指标
     */
    public MetricValue getCoreMetric(String key) {
        return coreMetrics != null ? coreMetrics.get(key) : null;
    }

    /**
     * 创建功能使用统计分析
     */
    public static FeatureUsageAggregate createUsageStatisticsAnalysis(TimeRange timeRange, List<String> featureIds, String dataScope) {
        return FeatureUsageAggregate.builder()
                .id(generateId("feature_usage", timeRange))
                .analysisType(FeatureAnalysisType.USAGE_STATISTICS)
                .timeRange(timeRange)
                .featureIds(featureIds)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建功能热度分析
     */
    public static FeatureUsageAggregate createHeatAnalysis(TimeRange timeRange, List<String> featureIds, String dataScope) {
        return FeatureUsageAggregate.builder()
                .id(generateId("feature_heat", timeRange))
                .analysisType(FeatureAnalysisType.HEAT_ANALYSIS)
                .timeRange(timeRange)
                .featureIds(featureIds)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建功能路径分析
     */
    public static FeatureUsageAggregate createPathAnalysis(TimeRange timeRange, List<String> featureIds, String dataScope) {
        return FeatureUsageAggregate.builder()
                .id(generateId("feature_path", timeRange))
                .analysisType(FeatureAnalysisType.PATH_ANALYSIS)
                .timeRange(timeRange)
                .featureIds(featureIds)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建功能价值贡献分析
     */
    public static FeatureUsageAggregate createValueContributionAnalysis(TimeRange timeRange, List<String> featureIds, String dataScope) {
        return FeatureUsageAggregate.builder()
                .id(generateId("feature_value", timeRange))
                .analysisType(FeatureAnalysisType.VALUE_CONTRIBUTION)
                .timeRange(timeRange)
                .featureIds(featureIds)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建功能满意度评估
     */
    public static FeatureUsageAggregate createSatisfactionEvaluation(TimeRange timeRange, List<String> featureIds, String dataScope) {
        return FeatureUsageAggregate.builder()
                .id(generateId("feature_satisfaction", timeRange))
                .analysisType(FeatureAnalysisType.SATISFACTION_EVALUATION)
                .timeRange(timeRange)
                .featureIds(featureIds)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 验证聚合根是否有效
     */
    public boolean isValid() {
        return timeRange != null && 
               timeRange.isValid() && 
               analysisType != null &&
               dataScope != null && 
               !dataScope.trim().isEmpty() &&
               featureIds != null &&
               !featureIds.isEmpty();
    }

    /**
     * 验证数据完整性
     */
    public boolean isDataComplete() {
        return id != null && 
               analysisType != null && 
               timeRange != null && 
               dataScope != null && 
               lastUpdateTime != null &&
               coreMetrics != null && 
               !coreMetrics.isEmpty();
    }

    /**
     * 获取数据摘要
     */
    public String getDataSummary() {
        if (!isDataComplete()) {
            return "数据不完整";
        }
        
        MetricValue totalUsage = getCoreMetric("totalUsage");
        MetricValue uniqueUsers = getCoreMetric("uniqueUsers");
        
        return String.format("分析类型: %s, 时间范围: %s - %s, 总使用次数: %s, 独立用户: %s",
                analysisType.getDescription(),
                timeRange.getStartDate(),
                timeRange.getEndDate(),
                totalUsage != null ? totalUsage.getValue() : "N/A",
                uniqueUsers != null ? uniqueUsers.getValue() : "N/A");
    }
}
