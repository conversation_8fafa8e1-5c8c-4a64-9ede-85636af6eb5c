package com.foxit.crm.common.util;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * 安全工具类
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public class SecurityUtils {

    /**
     * 获取当前用户ID
     */
    public static Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            // 这里应该从认证信息中获取用户ID
            // 暂时返回固定值，实际项目中需要根据具体的认证实现来获取
            return 1L;
        }
        return null;
    }

    /**
     * 获取当前用户名
     */
    public static String getCurrentUserName() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            // 这里应该从认证信息中获取用户名
            // 暂时返回固定值，实际项目中需要根据具体的认证实现来获取
            return "veikin";
        }
        return null;
    }

    /**
     * 获取当前认证信息
     */
    public static Authentication getCurrentAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 检查是否已认证
     */
    public static boolean isAuthenticated() {
        Authentication authentication = getCurrentAuthentication();
        return authentication != null && authentication.isAuthenticated();
    }

    /**
     * 获取当前用户数据权限范围
     */
    public static String getCurrentUserDataScope() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            // 这里应该从认证信息中获取用户的数据权限范围
            // 暂时返回固定值，实际项目中需要根据具体的权限实现来获取
            // 可能的值：all（全部数据）、dept（部门数据）、own（个人数据）
            return "all";
        }
        return "own";
    }

    /**
     * 获取当前用户角色列表
     */
    public static java.util.List<String> getCurrentUserRoles() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            return authentication.getAuthorities().stream()
                    .map(authority -> authority.getAuthority())
                    .collect(java.util.stream.Collectors.toList());
        }
        return java.util.Collections.emptyList();
    }

    /**
     * 检查当前用户是否有指定权限
     */
    public static boolean hasPermission(String permission) {
        return getCurrentUserRoles().contains(permission);
    }

    /**
     * 检查当前用户是否为管理员
     */
    public static boolean isAdmin() {
        return hasPermission("ADMIN") || hasPermission("SUPER_ADMIN");
    }
}
