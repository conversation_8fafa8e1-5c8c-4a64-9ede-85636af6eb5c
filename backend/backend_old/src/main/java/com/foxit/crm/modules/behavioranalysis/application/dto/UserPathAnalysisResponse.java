package com.foxit.crm.modules.behavioranalysis.application.dto;

import com.foxit.crm.modules.behavioranalysis.domain.entity.UserPathAnalysisAggregate;
import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户路径分析响应DTO
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@Builder
@Schema(description = "用户路径分析响应")
public class UserPathAnalysisResponse {

    @Schema(description = "分析ID")
    private String analysisId;

    @Schema(description = "分析类型")
    private String analysisType;

    @Schema(description = "时间范围")
    private TimeRange timeRange;

    @Schema(description = "核心指标")
    private Map<String, MetricValue> coreMetrics;

    @Schema(description = "路径流向数据")
    private Map<String, UserPathAnalysisAggregate.PathFlowData> pathFlowData;

    @Schema(description = "路径统计数据")
    private Map<String, UserPathAnalysisAggregate.PathStatisticsData> pathStatisticsData;

    @Schema(description = "异常路径数据")
    private Map<String, UserPathAnalysisAggregate.AnomalyPathData> anomalyPathData;

    @Schema(description = "路径效率数据")
    private Map<String, UserPathAnalysisAggregate.PathEfficiencyData> pathEfficiencyData;

    @Schema(description = "路径对比数据")
    private Map<String, UserPathAnalysisAggregate.PathComparisonData> pathComparisonData;

    @Schema(description = "最后更新时间")
    private LocalDateTime lastUpdateTime;

    @Schema(description = "数据状态")
    private String dataStatus;

    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 路径流向数据DTO
     */
    @Data
    @Builder
    @Schema(description = "路径流向数据")
    public static class PathFlowData {

        @Schema(description = "路径名称")
        private String pathName;

        @Schema(description = "路径节点")
        private List<PathNodeData> nodes;

        @Schema(description = "路径链接")
        private List<PathLinkData> links;

        @Schema(description = "总用户数")
        private Long totalUsers;

        @Schema(description = "转化率")
        private Double conversionRate;

        @Schema(description = "流向方向")
        private String flowDirection;

        @Schema(description = "最大深度")
        private Integer maxDepth;

        @Schema(description = "平均路径长度")
        private Double avgPathLength;
    }

    /**
     * 路径节点数据DTO
     */
    @Data
    @Builder
    @Schema(description = "路径节点数据")
    public static class PathNodeData {

        @Schema(description = "节点ID")
        private String nodeId;

        @Schema(description = "节点名称")
        private String nodeName;

        @Schema(description = "节点类型")
        private String nodeType;

        @Schema(description = "用户数")
        private Long userCount;

        @Schema(description = "百分比")
        private Double percentage;

        @Schema(description = "层级")
        private Integer level;

        @Schema(description = "平均停留时间")
        private Double avgStayTime;

        @Schema(description = "跳出率")
        private Double bounceRate;

        @Schema(description = "节点分类")
        private String nodeCategory;
    }

    /**
     * 路径链接数据DTO
     */
    @Data
    @Builder
    @Schema(description = "路径链接数据")
    public static class PathLinkData {

        @Schema(description = "源节点ID")
        private String sourceNodeId;

        @Schema(description = "目标节点ID")
        private String targetNodeId;

        @Schema(description = "用户数")
        private Long userCount;

        @Schema(description = "百分比")
        private Double percentage;

        @Schema(description = "平均转换时间")
        private Double avgTransitionTime;

        @Schema(description = "转化率")
        private Double conversionRate;

        @Schema(description = "链接强度")
        private String linkStrength;

        @Schema(description = "流失率")
        private Double dropOffRate;
    }

    /**
     * 路径统计数据DTO
     */
    @Data
    @Builder
    @Schema(description = "路径统计数据")
    public static class PathStatisticsData {

        @Schema(description = "路径ID")
        private String pathId;

        @Schema(description = "路径名称")
        private String pathName;

        @Schema(description = "总路径数")
        private Long totalPaths;

        @Schema(description = "独立用户数")
        private Long uniqueUsers;

        @Schema(description = "平均路径长度")
        private Double avgPathLength;

        @Schema(description = "平均路径时间")
        private Double avgPathTime;

        @Schema(description = "完成率")
        private Double completionRate;

        @Schema(description = "常见步骤")
        private List<PathStepData> commonSteps;

        @Schema(description = "热门路径")
        private List<String> topPaths;

        @Schema(description = "路径分布")
        private Map<String, Long> pathDistribution;
    }

    /**
     * 路径步骤数据DTO
     */
    @Data
    @Builder
    @Schema(description = "路径步骤数据")
    public static class PathStepData {

        @Schema(description = "步骤ID")
        private String stepId;

        @Schema(description = "步骤名称")
        private String stepName;

        @Schema(description = "步骤顺序")
        private Integer stepOrder;

        @Schema(description = "用户数")
        private Long userCount;

        @Schema(description = "百分比")
        private Double percentage;

        @Schema(description = "平均耗时")
        private Double avgTimeSpent;

        @Schema(description = "退出率")
        private Double exitRate;
    }

    /**
     * 异常路径数据DTO
     */
    @Data
    @Builder
    @Schema(description = "异常路径数据")
    public static class AnomalyPathData {

        @Schema(description = "异常ID")
        private String anomalyId;

        @Schema(description = "异常类型")
        private String anomalyType;

        @Schema(description = "路径模式")
        private String pathPattern;

        @Schema(description = "影响用户数")
        private Long affectedUsers;

        @Schema(description = "异常分数")
        private Double anomalyScore;

        @Schema(description = "严重程度")
        private String severity;

        @Schema(description = "描述")
        private String description;

        @Schema(description = "异常节点")
        private List<String> anomalyNodes;

        @Schema(description = "检测时间")
        private LocalDateTime detectedTime;

        @Schema(description = "建议操作")
        private String recommendedAction;
    }

    /**
     * 路径效率数据DTO
     */
    @Data
    @Builder
    @Schema(description = "路径效率数据")
    public static class PathEfficiencyData {

        @Schema(description = "路径ID")
        private String pathId;

        @Schema(description = "路径名称")
        private String pathName;

        @Schema(description = "效率分数")
        private Double efficiencyScore;

        @Schema(description = "最优步骤数")
        private Integer optimalSteps;

        @Schema(description = "实际步骤数")
        private Integer actualSteps;

        @Schema(description = "时间效率")
        private Double timeEfficiency;

        @Schema(description = "转化效率")
        private Double conversionEfficiency;

        @Schema(description = "效率指标")
        private List<EfficiencyMetricData> metrics;

        @Schema(description = "优化建议")
        private List<String> optimizationSuggestions;

        @Schema(description = "效率等级")
        private String efficiencyLevel;
    }

    /**
     * 效率指标数据DTO
     */
    @Data
    @Builder
    @Schema(description = "效率指标数据")
    public static class EfficiencyMetricData {

        @Schema(description = "指标名称")
        private String metricName;

        @Schema(description = "指标值")
        private Double metricValue;

        @Schema(description = "指标单位")
        private String metricUnit;

        @Schema(description = "基准值")
        private Double benchmark;

        @Schema(description = "性能表现")
        private String performance;
    }

    /**
     * 路径对比数据DTO
     */
    @Data
    @Builder
    @Schema(description = "路径对比数据")
    public static class PathComparisonData {

        @Schema(description = "对比ID")
        private String comparisonId;

        @Schema(description = "对比名称")
        private String comparisonName;

        @Schema(description = "对比项")
        private List<PathComparisonItemData> items;

        @Schema(description = "对比指标")
        private Map<String, Double> comparisonMetrics;

        @Schema(description = "优胜路径")
        private String winnerPath;

        @Schema(description = "对比摘要")
        private String comparisonSummary;

        @Schema(description = "对比时间")
        private LocalDateTime comparisonTime;
    }

    /**
     * 路径对比项数据DTO
     */
    @Data
    @Builder
    @Schema(description = "路径对比项数据")
    public static class PathComparisonItemData {

        @Schema(description = "路径ID")
        private String pathId;

        @Schema(description = "路径名称")
        private String pathName;

        @Schema(description = "用户数")
        private Long userCount;

        @Schema(description = "转化率")
        private Double conversionRate;

        @Schema(description = "平均路径时间")
        private Double avgPathTime;

        @Schema(description = "效率分数")
        private Double efficiencyScore;

        @Schema(description = "性能表现")
        private String performance;
    }

    /**
     * 创建空响应
     */
    public static UserPathAnalysisResponse empty() {
        return UserPathAnalysisResponse.builder()
                .dataStatus("EMPTY")
                .errorMessage("未找到数据")
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建错误响应
     */
    public static UserPathAnalysisResponse error(String errorMessage) {
        return UserPathAnalysisResponse.builder()
                .dataStatus("ERROR")
                .errorMessage(errorMessage)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功响应
     */
    public static UserPathAnalysisResponse success(String analysisId, String analysisType) {
        return UserPathAnalysisResponse.builder()
                .analysisId(analysisId)
                .analysisType(analysisType)
                .dataStatus("SUCCESS")
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 检查是否有数据
     */
    public boolean hasData() {
        return "SUCCESS".equals(dataStatus) && 
               (coreMetrics != null && !coreMetrics.isEmpty() ||
                pathFlowData != null && !pathFlowData.isEmpty() ||
                pathStatisticsData != null && !pathStatisticsData.isEmpty() ||
                anomalyPathData != null && !anomalyPathData.isEmpty() ||
                pathEfficiencyData != null && !pathEfficiencyData.isEmpty() ||
                pathComparisonData != null && !pathComparisonData.isEmpty());
    }

    /**
     * 检查是否有错误
     */
    public boolean hasError() {
        return "ERROR".equals(dataStatus) || errorMessage != null;
    }

    /**
     * 获取数据摘要
     */
    public String getDataSummary() {
        if (hasError()) {
            return "错误: " + errorMessage;
        }
        
        if (!hasData()) {
            return "无数据";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("分析类型: ").append(analysisType);
        
        if (coreMetrics != null && !coreMetrics.isEmpty()) {
            summary.append(", 核心指标数: ").append(coreMetrics.size());
        }
        
        if (pathFlowData != null && !pathFlowData.isEmpty()) {
            summary.append(", 流向数据数: ").append(pathFlowData.size());
        }
        
        return summary.toString();
    }
}
