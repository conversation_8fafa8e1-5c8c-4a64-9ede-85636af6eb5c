package com.foxit.crm.common.database;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;

/**
 * 数据库修复工具
 * 用于修复数据库表结构问题
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Order(100) // 在数据库验证之后执行
public class DatabaseFixUtil implements CommandLineRunner {

    private final JdbcTemplate jdbcTemplate;
    private final DataSource dataSource;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始检查并修复数据库表结构...");
        
        try {
            fixUserTable();
            log.info("数据库表结构修复完成");
        } catch (Exception e) {
            log.error("数据库表结构修复失败", e);
            // 不抛出异常，避免影响应用启动
        }
    }

    /**
     * 修复sys_user表结构
     */
    private void fixUserTable() throws Exception {
        log.info("检查sys_user表结构...");
        
        DatabaseMetaData metaData = dataSource.getConnection().getMetaData();
        
        // 检查dept_id字段是否存在
        if (!columnExists(metaData, "sys_user", "dept_id")) {
            log.info("添加dept_id字段到sys_user表...");
            jdbcTemplate.execute("ALTER TABLE `sys_user` ADD COLUMN `dept_id` bigint DEFAULT NULL COMMENT '部门ID' AFTER `user_type`");
            log.info("dept_id字段添加成功");
        } else {
            log.info("dept_id字段已存在");
        }
        
        // 检查remark字段是否存在
        if (!columnExists(metaData, "sys_user", "remark")) {
            log.info("添加remark字段到sys_user表...");
            jdbcTemplate.execute("ALTER TABLE `sys_user` ADD COLUMN `remark` varchar(500) DEFAULT NULL COMMENT '备注' AFTER `last_login_ip`");
            log.info("remark字段添加成功");
        } else {
            log.info("remark字段已存在");
        }
    }

    /**
     * 检查字段是否存在
     */
    private boolean columnExists(DatabaseMetaData metaData, String tableName, String columnName) throws Exception {
        try (ResultSet rs = metaData.getColumns(null, null, tableName, columnName)) {
            return rs.next();
        }
    }
}
