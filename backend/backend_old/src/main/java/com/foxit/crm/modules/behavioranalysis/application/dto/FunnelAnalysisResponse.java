package com.foxit.crm.modules.behavioranalysis.application.dto;

import com.foxit.crm.modules.behavioranalysis.domain.entity.FunnelAnalysisAggregate;
import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 漏斗分析响应DTO
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@Builder
@Schema(description = "漏斗分析响应")
public class FunnelAnalysisResponse {

    @Schema(description = "分析ID")
    private String analysisId;

    @Schema(description = "分析类型")
    private String analysisType;

    @Schema(description = "时间范围")
    private TimeRange timeRange;

    @Schema(description = "核心指标")
    private Map<String, MetricValue> coreMetrics;

    @Schema(description = "漏斗转化数据")
    private Map<String, FunnelAnalysisAggregate.FunnelConversionData> conversionData;

    @Schema(description = "漏斗对比数据")
    private Map<String, FunnelAnalysisAggregate.FunnelComparisonData> comparisonData;

    @Schema(description = "流失点分析数据")
    private Map<String, FunnelAnalysisAggregate.DropoutAnalysisData> dropoutData;

    @Schema(description = "队列漏斗数据")
    private Map<String, FunnelAnalysisAggregate.CohortFunnelData> cohortData;

    @Schema(description = "最后更新时间")
    private LocalDateTime lastUpdateTime;

    @Schema(description = "数据状态")
    private String dataStatus;

    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 漏斗转化数据DTO
     */
    @Data
    @Builder
    @Schema(description = "漏斗转化数据")
    public static class FunnelConversionData {

        @Schema(description = "漏斗ID")
        private String funnelId;

        @Schema(description = "漏斗名称")
        private String funnelName;

        @Schema(description = "漏斗步骤")
        private List<FunnelStepData> steps;

        @Schema(description = "总用户数")
        private Long totalUsers;

        @Schema(description = "整体转化率")
        private Double overallConversionRate;

        @Schema(description = "平均转化时间")
        private Double avgTimeToConvert;

        @Schema(description = "转化趋势")
        private List<ConversionTrendData> trends;

        @Schema(description = "漏斗类型")
        private String funnelType;
    }

    /**
     * 漏斗步骤数据DTO
     */
    @Data
    @Builder
    @Schema(description = "漏斗步骤数据")
    public static class FunnelStepData {

        @Schema(description = "步骤ID")
        private String stepId;

        @Schema(description = "步骤名称")
        private String stepName;

        @Schema(description = "步骤顺序")
        private Integer stepOrder;

        @Schema(description = "用户数")
        private Long userCount;

        @Schema(description = "转化率")
        private Double conversionRate;

        @Schema(description = "流失率")
        private Double dropoffRate;

        @Schema(description = "平均耗时")
        private Double avgTimeSpent;

        @Schema(description = "步骤类型")
        private String stepType;

        @Schema(description = "步骤属性")
        private Map<String, Object> stepProperties;
    }

    /**
     * 转化趋势数据DTO
     */
    @Data
    @Builder
    @Schema(description = "转化趋势数据")
    public static class ConversionTrendData {

        @Schema(description = "时间标签")
        private String timeLabel;

        @Schema(description = "用户数")
        private Long userCount;

        @Schema(description = "转化率")
        private Double conversionRate;

        @Schema(description = "趋势方向")
        private String trendDirection;

        @Schema(description = "变化率")
        private Double changeRate;
    }

    /**
     * 漏斗对比数据DTO
     */
    @Data
    @Builder
    @Schema(description = "漏斗对比数据")
    public static class FunnelComparisonData {

        @Schema(description = "对比ID")
        private String comparisonId;

        @Schema(description = "对比名称")
        private String comparisonName;

        @Schema(description = "对比项")
        private List<FunnelComparisonItemData> items;

        @Schema(description = "对比指标")
        private Map<String, Double> comparisonMetrics;

        @Schema(description = "优胜漏斗")
        private String winnerFunnel;

        @Schema(description = "对比摘要")
        private String comparisonSummary;

        @Schema(description = "对比时间")
        private LocalDateTime comparisonTime;
    }

    /**
     * 漏斗对比项数据DTO
     */
    @Data
    @Builder
    @Schema(description = "漏斗对比项数据")
    public static class FunnelComparisonItemData {

        @Schema(description = "漏斗ID")
        private String funnelId;

        @Schema(description = "漏斗名称")
        private String funnelName;

        @Schema(description = "总用户数")
        private Long totalUsers;

        @Schema(description = "转化率")
        private Double conversionRate;

        @Schema(description = "平均转化时间")
        private Double avgTimeToConvert;

        @Schema(description = "漏斗步骤")
        private List<FunnelStepData> steps;

        @Schema(description = "性能表现")
        private String performance;
    }

    /**
     * 流失点分析数据DTO
     */
    @Data
    @Builder
    @Schema(description = "流失点分析数据")
    public static class DropoutAnalysisData {

        @Schema(description = "分析ID")
        private String analysisId;

        @Schema(description = "分析名称")
        private String analysisName;

        @Schema(description = "流失点")
        private List<DropoutPointData> dropoutPoints;

        @Schema(description = "主要流失原因")
        private List<DropoutReasonData> topReasons;

        @Schema(description = "流失分布")
        private Map<String, Long> dropoutDistribution;

        @Schema(description = "优化建议")
        private List<String> optimizationSuggestions;

        @Schema(description = "关键流失率")
        private Double criticalDropoutRate;
    }

    /**
     * 流失点数据DTO
     */
    @Data
    @Builder
    @Schema(description = "流失点数据")
    public static class DropoutPointData {

        @Schema(description = "步骤ID")
        private String stepId;

        @Schema(description = "步骤名称")
        private String stepName;

        @Schema(description = "流失用户数")
        private Long dropoutUsers;

        @Schema(description = "流失率")
        private Double dropoutRate;

        @Schema(description = "严重程度")
        private String severity;

        @Schema(description = "可能原因")
        private List<String> possibleReasons;

        @Schema(description = "影响分数")
        private Double impactScore;
    }

    /**
     * 流失原因数据DTO
     */
    @Data
    @Builder
    @Schema(description = "流失原因数据")
    public static class DropoutReasonData {

        @Schema(description = "原因ID")
        private String reasonId;

        @Schema(description = "原因名称")
        private String reasonName;

        @Schema(description = "原因分类")
        private String reasonCategory;

        @Schema(description = "影响用户数")
        private Long affectedUsers;

        @Schema(description = "影响百分比")
        private Double impactPercentage;

        @Schema(description = "描述")
        private String description;

        @Schema(description = "行动项")
        private List<String> actionItems;
    }

    /**
     * 队列漏斗数据DTO
     */
    @Data
    @Builder
    @Schema(description = "队列漏斗数据")
    public static class CohortFunnelData {

        @Schema(description = "队列ID")
        private String cohortId;

        @Schema(description = "队列名称")
        private String cohortName;

        @Schema(description = "队列周期")
        private String cohortPeriod;

        @Schema(description = "队列步骤")
        private List<CohortStepData> steps;

        @Schema(description = "转化矩阵")
        private Map<String, List<Double>> conversionMatrix;

        @Schema(description = "平均队列转化率")
        private Double avgCohortConversion;

        @Schema(description = "洞察")
        private List<String> insights;
    }

    /**
     * 队列步骤数据DTO
     */
    @Data
    @Builder
    @Schema(description = "队列步骤数据")
    public static class CohortStepData {

        @Schema(description = "步骤ID")
        private String stepId;

        @Schema(description = "步骤名称")
        private String stepName;

        @Schema(description = "步骤顺序")
        private Integer stepOrder;

        @Schema(description = "队列用户数")
        private Map<String, Long> cohortUserCounts;

        @Schema(description = "队列转化率")
        private Map<String, Double> cohortConversionRates;

        @Schema(description = "步骤洞察")
        private String stepInsight;
    }

    /**
     * 创建空响应
     */
    public static FunnelAnalysisResponse empty() {
        return FunnelAnalysisResponse.builder()
                .dataStatus("EMPTY")
                .errorMessage("未找到数据")
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建错误响应
     */
    public static FunnelAnalysisResponse error(String errorMessage) {
        return FunnelAnalysisResponse.builder()
                .dataStatus("ERROR")
                .errorMessage(errorMessage)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功响应
     */
    public static FunnelAnalysisResponse success(String analysisId, String analysisType) {
        return FunnelAnalysisResponse.builder()
                .analysisId(analysisId)
                .analysisType(analysisType)
                .dataStatus("SUCCESS")
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 检查是否有数据
     */
    public boolean hasData() {
        return "SUCCESS".equals(dataStatus) && 
               (coreMetrics != null && !coreMetrics.isEmpty() ||
                conversionData != null && !conversionData.isEmpty() ||
                comparisonData != null && !comparisonData.isEmpty() ||
                dropoutData != null && !dropoutData.isEmpty() ||
                cohortData != null && !cohortData.isEmpty());
    }

    /**
     * 检查是否有错误
     */
    public boolean hasError() {
        return "ERROR".equals(dataStatus) || errorMessage != null;
    }

    /**
     * 获取数据摘要
     */
    public String getDataSummary() {
        if (hasError()) {
            return "错误: " + errorMessage;
        }
        
        if (!hasData()) {
            return "无数据";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("分析类型: ").append(analysisType);
        
        if (coreMetrics != null && !coreMetrics.isEmpty()) {
            summary.append(", 核心指标数: ").append(coreMetrics.size());
        }
        
        if (conversionData != null && !conversionData.isEmpty()) {
            summary.append(", 转化数据数: ").append(conversionData.size());
        }
        
        return summary.toString();
    }

    /**
     * 检查是否为转化分析
     */
    public boolean isConversionAnalysis() {
        return "FUNNEL_CONVERSION".equals(analysisType);
    }

    /**
     * 检查是否为对比分析
     */
    public boolean isComparisonAnalysis() {
        return "FUNNEL_COMPARISON".equals(analysisType);
    }

    /**
     * 检查是否为流失点分析
     */
    public boolean isDropoutAnalysis() {
        return "DROPOUT_ANALYSIS".equals(analysisType);
    }

    /**
     * 检查是否为队列分析
     */
    public boolean isCohortAnalysis() {
        return "COHORT_FUNNEL".equals(analysisType);
    }
}
