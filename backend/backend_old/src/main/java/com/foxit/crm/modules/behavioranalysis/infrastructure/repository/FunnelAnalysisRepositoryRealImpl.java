package com.foxit.crm.modules.behavioranalysis.infrastructure.repository;

import com.foxit.crm.modules.behavioranalysis.domain.entity.FunnelAnalysisAggregate;
import com.foxit.crm.modules.behavioranalysis.domain.repository.FunnelAnalysisRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import com.foxit.crm.common.clickhouse.ClickHouseTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 漏斗分析仓储真实数据实现
 * 基于MySQL和ClickHouse的真实数据查询
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Slf4j
@Repository("funnelAnalysisRepositoryReal")
@Primary // 设置为主要实现，优先注入
@RequiredArgsConstructor
public class FunnelAnalysisRepositoryRealImpl implements FunnelAnalysisRepository {

    @Qualifier("mysqlJdbcTemplate")
    private final JdbcTemplate mysqlJdbcTemplate;

    private final ClickHouseTemplate clickHouseTemplate;

    @Override
    public Optional<FunnelAnalysisAggregate> getFunnelConversionAnalysis(TimeRange timeRange, List<String> funnelSteps,
            List<Long> productLineIds, String dataScope) {
        log.debug("获取漏斗转化分析数据: timeRange={}, funnelSteps={}, productLineIds={}, dataScope={}",
                timeRange, funnelSteps, productLineIds, dataScope);

        try {
            // 从ClickHouse获取漏斗转化数据
            String sql = """
                    SELECT
                        step_name,
                        step_sequence as step_order,
                        COUNT(DISTINCT user_id) as user_count,
                        COUNT(*) as event_count
                    FROM funnel_conversion_data
                    WHERE step_timestamp >= ? AND step_timestamp <= ?
                    """
                    + (funnelSteps != null && !funnelSteps.isEmpty()
                            ? " AND step_name IN ('" + String.join("','", funnelSteps) + "')"
                            : "")
                    + (productLineIds != null && !productLineIds.isEmpty()
                            ? " AND product_line_id IN ("
                                    + productLineIds.stream().map(String::valueOf).collect(Collectors.joining(","))
                                    + ")"
                            : "")
                    + " GROUP BY step_name, step_sequence ORDER BY step_sequence";

            List<Map<String, Object>> results = clickHouseTemplate.queryForList(sql,
                    timeRange.getStartDateTime(), timeRange.getEndDateTime());

            if (results.isEmpty()) {
                return Optional.empty();
            }

            // 构建漏斗步骤数据
            List<FunnelAnalysisAggregate.FunnelStep> steps = results.stream()
                    .map(row -> FunnelAnalysisAggregate.FunnelStep.builder()
                            .stepName((String) row.get("step_name"))
                            .stepOrder(((Number) row.get("step_order")).intValue())
                            .userCount(((Number) row.get("user_count")).longValue())
                            .conversionRate(0.0) // 需要计算
                            .dropoffRate(0.0) // 需要计算
                            .build())
                    .collect(Collectors.toList());

            // 计算转化率
            for (int i = 0; i < steps.size(); i++) {
                FunnelAnalysisAggregate.FunnelStep step = steps.get(i);
                if (i == 0) {
                    // 第一步转化率为100%
                    step = FunnelAnalysisAggregate.FunnelStep.builder()
                            .stepName(step.getStepName())
                            .stepOrder(step.getStepOrder())
                            .userCount(step.getUserCount())
                            .conversionRate(100.0)
                            .dropoffRate(0.0)
                            .build();
                } else {
                    // 计算相对于第一步的转化率
                    double conversionRate = (double) step.getUserCount() / steps.get(0).getUserCount() * 100;
                    double dropoffRate = 100.0 - conversionRate;
                    step = FunnelAnalysisAggregate.FunnelStep.builder()
                            .stepName(step.getStepName())
                            .stepOrder(step.getStepOrder())
                            .userCount(step.getUserCount())
                            .conversionRate(conversionRate)
                            .dropoffRate(dropoffRate)
                            .build();
                }
                steps.set(i, step);
            }

            // 构建漏斗转化数据
            FunnelAnalysisAggregate.FunnelConversionData conversionData = FunnelAnalysisAggregate.FunnelConversionData
                    .builder()
                    .funnelId("default_funnel")
                    .funnelName("默认漏斗")
                    .steps(steps)
                    .totalUsers(steps.isEmpty() ? 0L : steps.get(0).getUserCount())
                    .overallConversionRate(steps.isEmpty() ? 0.0
                            : (double) steps.get(steps.size() - 1).getUserCount() / steps.get(0).getUserCount() * 100)
                    .avgTimeToConvert(0.0) // 暂时设为0
                    .trends(List.of())
                    .funnelType("conversion")
                    .build();

            // 构建核心指标
            Map<String, MetricValue> coreMetrics = new HashMap<>();
            coreMetrics.put("totalUsers", MetricValue.ofCount("总用户数",
                    steps.isEmpty() ? 0L : steps.get(0).getUserCount(), null));
            coreMetrics.put("overallConversionRate", MetricValue.ofPercentage("整体转化率",
                    steps.isEmpty() ? 0.0
                            : (double) steps.get(steps.size() - 1).getUserCount() / steps.get(0).getUserCount() * 100,
                    null));

            // 构建聚合对象
            FunnelAnalysisAggregate aggregate = FunnelAnalysisAggregate.builder()
                    .id("funnel_" + System.currentTimeMillis())
                    .analysisType(FunnelAnalysisAggregate.FunnelAnalysisType.FUNNEL_CONVERSION)
                    .timeRange(timeRange)
                    .funnelSteps(funnelSteps)
                    .productLineIds(productLineIds)
                    .coreMetrics(coreMetrics)
                    .conversionData(Map.of("default", conversionData))
                    .dataScope(dataScope)
                    .lastUpdateTime(LocalDateTime.now())
                    .build();

            return Optional.of(aggregate);

        } catch (Exception e) {
            log.error("获取漏斗转化分析数据失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<FunnelAnalysisAggregate> getDropoutAnalysis(TimeRange timeRange, List<String> funnelSteps,
            List<Long> productLineIds, String dataScope) {
        log.debug("获取流失点分析数据");

        try {
            // TODO: 实现完整的FunnelAnalysisAggregate构建逻辑
            return Optional.empty(); // 暂时返回空，避免编译错误

        } catch (Exception e) {
            log.error("获取流失点分析数据失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<FunnelAnalysisAggregate> getCohortFunnelAnalysis(TimeRange timeRange, List<String> funnelSteps,
            String cohortPeriod, List<Long> productLineIds, String dataScope) {
        log.debug("获取队列漏斗分析数据");

        try {
            // TODO: 实现完整的FunnelAnalysisAggregate构建逻辑
            return Optional.empty(); // 暂时返回空，避免编译错误

        } catch (Exception e) {
            log.error("获取队列漏斗分析数据失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public List<FunnelUserSegmentAnalysis> getFunnelUserSegmentAnalysis(TimeRange timeRange, List<String> funnelSteps,
            List<String> segmentDimensions, List<Long> productLineIds, String dataScope) {
        log.debug("获取漏斗用户细分分析数据");

        try {
            // TODO: 实现完整的FunnelUserSegmentAnalysis构建逻辑
            return List.of(); // 暂时返回空列表，避免编译错误

        } catch (Exception e) {
            log.error("获取漏斗用户细分分析数据失败: {}", e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public Optional<FunnelAnalysisAggregate> getFunnelComparisonAnalysis(TimeRange timeRange,
            List<FunnelGroup> funnelGroups,
            List<Long> productLineIds, String dataScope) {
        log.debug("获取漏斗对比分析数据");

        try {
            // TODO: 实现完整的FunnelAnalysisAggregate构建逻辑
            return Optional.empty(); // 暂时返回空，避免编译错误

        } catch (Exception e) {
            log.error("获取漏斗对比分析数据失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public FunnelRealTimeStats getRealTimeFunnelStats(List<String> funnelSteps, List<Long> productLineIds,
            String dataScope) {
        log.debug("获取实时漏斗统计");

        try {
            // 获取最近1小时的实时统计
            String sql = """
                    SELECT
                        COUNT(DISTINCT user_id) as total_users,
                        AVG(conversion_time) as avg_time_to_convert
                    FROM funnel_events
                    WHERE event_time >= now() - INTERVAL 1 HOUR
                    """
                    + (funnelSteps != null && !funnelSteps.isEmpty()
                            ? " AND step_name IN ('" + String.join("','", funnelSteps) + "')"
                            : "")
                    + (productLineIds != null && !productLineIds.isEmpty()
                            ? " AND product_line_id IN ("
                                    + productLineIds.stream().map(String::valueOf).collect(Collectors.joining(","))
                                    + ")"
                            : "");

            List<Map<String, Object>> results = clickHouseTemplate.queryForList(sql);
            Map<String, Object> result = results.isEmpty() ? new HashMap<>() : results.get(0);

            Long totalUsers = ((Number) result.getOrDefault("total_users", 0)).longValue();
            Double avgTimeToConvert = ((Number) result.getOrDefault("avg_time_to_convert", 0)).doubleValue();

            return new FunnelRealTimeStats(
                    totalUsers,
                    0.0, // overallConversionRate - 简化实现
                    avgTimeToConvert,
                    List.of(), // stepStats - 简化实现
                    LocalDateTime.now());

        } catch (Exception e) {
            log.error("获取实时漏斗统计失败: {}", e.getMessage(), e);
            return new FunnelRealTimeStats(0L, 0.0, 0.0, List.of(), LocalDateTime.now());
        }
    }

    @Override
    public List<FunnelTemplate> getFunnelTemplates(List<Long> productLineIds, String dataScope) {
        log.debug("获取漏斗模板列表");

        try {
            // 从MySQL获取漏斗模板
            String sql = """
                    SELECT
                        t.template_id,
                        t.template_name,
                        t.description,
                        t.funnel_steps,
                        t.product_line_id,
                        p.name as product_line_name,
                        t.is_active,
                        t.created_by,
                        t.create_time
                    FROM funnel_templates t
                    LEFT JOIN product_lines p ON t.product_line_id = p.id
                    WHERE t.is_active = 1
                    """
                    + (productLineIds != null && !productLineIds.isEmpty()
                            ? " AND t.product_line_id IN ("
                                    + productLineIds.stream().map(String::valueOf).collect(Collectors.joining(","))
                                    + ")"
                            : "")
                    + " ORDER BY t.create_time DESC";

            List<Map<String, Object>> results = mysqlJdbcTemplate.queryForList(sql);

            return results.stream()
                    .map(row -> new FunnelTemplate(
                            (String) row.get("template_id"),
                            (String) row.get("template_name"),
                            "Default", // templateCategory - 简化实现
                            Arrays.asList(((String) row.get("funnel_steps")).split(",")),
                            (String) row.get("description"),
                            ((Number) row.get("product_line_id")).longValue(),
                            (String) row.get("product_line_name"),
                            (Boolean) row.get("is_active"),
                            0 // usageCount - 简化实现
                    ))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取漏斗模板列表失败: {}", e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public boolean saveFunnelAnalysis(FunnelAnalysisAggregate aggregate) {
        log.debug("保存漏斗分析结果");

        try {
            // 保存到MySQL用于缓存和历史记录
            String sql = """
                    INSERT INTO funnel_analysis_results (
                        id, analysis_type, time_range_start, time_range_end,
                        funnel_steps, product_line_ids, analysis_data, create_time, update_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                    ON DUPLICATE KEY UPDATE
                        analysis_data = VALUES(analysis_data),
                        update_time = NOW()
                    """;

            mysqlJdbcTemplate.update(sql,
                    aggregate.getId(),
                    aggregate.getAnalysisType().name(),
                    aggregate.getTimeRange().getStartDateTime(),
                    aggregate.getTimeRange().getEndDateTime(),
                    aggregate.getFunnelSteps() != null ? String.join(",", aggregate.getFunnelSteps()) : null,
                    aggregate.getProductLineIds() != null ? aggregate.getProductLineIds().stream().map(String::valueOf)
                            .collect(Collectors.joining(",")) : null,
                    aggregate.toString() // 简化版序列化
            );

            return true;

        } catch (Exception e) {
            log.error("保存漏斗分析结果失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean deleteFunnelAnalysis(String aggregateId) {
        log.debug("删除漏斗分析数据: aggregateId={}", aggregateId);

        try {
            String sql = "DELETE FROM funnel_analysis_results WHERE id = ?";
            int deletedRows = mysqlJdbcTemplate.update(sql, aggregateId);
            return deletedRows > 0;

        } catch (Exception e) {
            log.error("删除漏斗分析数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<FunnelAnalysisAggregate> batchGetFunnelAnalysisData(List<TimeRange> timeRanges,
            List<String> funnelSteps,
            List<Long> productLineIds, String dataScope) {
        log.debug("批量获取漏斗分析数据");

        List<FunnelAnalysisAggregate> results = new ArrayList<>();

        for (TimeRange timeRange : timeRanges) {
            Optional<FunnelAnalysisAggregate> aggregate = getFunnelConversionAnalysis(timeRange, funnelSteps,
                    productLineIds, dataScope);
            aggregate.ifPresent(results::add);
        }

        return results;
    }

    @Override
    public int deleteExpiredData(LocalDateTime beforeTime) {
        log.info("删除过期的漏斗分析数据: beforeTime={}", beforeTime);

        try {
            String sql = "DELETE FROM funnel_analysis_results WHERE create_time < ?";
            return mysqlJdbcTemplate.update(sql, beforeTime);

        } catch (Exception e) {
            log.error("删除过期数据失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public List<FunnelConversionTrend> getFunnelConversionTrends(TimeRange timeRange, List<String> funnelSteps,
            TimeRange.TimeGranularity granularity, List<Long> productLineIds, String dataScope) {
        log.debug("获取漏斗转化趋势数据");

        try {
            // TODO: 实现完整的FunnelConversionTrend构建逻辑
            return List.of(); // 暂时返回空列表，避免编译错误

        } catch (Exception e) {
            log.error("获取漏斗转化趋势数据失败: {}", e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public FunnelAnalysisSummary getFunnelAnalysisSummary(TimeRange timeRange, String dataScope) {
        log.debug("获取漏斗分析统计摘要");

        try {
            // 从ClickHouse获取统计摘要
            String sql = """
                    SELECT
                        COUNT(DISTINCT funnel_id) as total_funnels,
                        COUNT(DISTINCT user_id) as total_users,
                        AVG(conversion_time) as avg_conversion_time
                    FROM funnel_events
                    WHERE event_time >= ? AND event_time <= ?
                    """;

            List<Map<String, Object>> results = clickHouseTemplate.queryForList(sql,
                    timeRange.getStartDateTime(), timeRange.getEndDateTime());
            Map<String, Object> result = results.isEmpty() ? new HashMap<>() : results.get(0);

            Long totalFunnels = ((Number) result.getOrDefault("total_funnels", 0)).longValue();
            Long totalUsers = ((Number) result.getOrDefault("total_users", 0)).longValue();
            Double avgConversionTime = ((Number) result.getOrDefault("avg_conversion_time", 0)).doubleValue();

            return new FunnelAnalysisSummary(
                    totalFunnels,
                    totalUsers,
                    0.0, // overallConversionRate - 简化实现
                    avgConversionTime,
                    List.of(), // topPerformingFunnels - 简化实现
                    List.of(), // optimizationSuggestions - 简化实现
                    LocalDateTime.now());

        } catch (Exception e) {
            log.error("获取漏斗分析统计摘要失败: {}", e.getMessage(), e);
            return new FunnelAnalysisSummary(0L, 0L, 0.0, 0.0, List.of(), List.of(), LocalDateTime.now());
        }
    }

    @Override
    public List<FunnelOptimizationSuggestion> getFunnelOptimizationSuggestions(TimeRange timeRange,
            List<String> funnelSteps,
            List<Long> productLineIds, String dataScope) {
        log.debug("获取漏斗优化建议");

        try {
            // TODO: 实现完整的FunnelOptimizationSuggestion构建逻辑
            return List.of(); // 暂时返回空列表，避免编译错误

        } catch (Exception e) {
            log.error("获取漏斗优化建议失败: {}", e.getMessage(), e);
            return List.of();
        }
    }
}
