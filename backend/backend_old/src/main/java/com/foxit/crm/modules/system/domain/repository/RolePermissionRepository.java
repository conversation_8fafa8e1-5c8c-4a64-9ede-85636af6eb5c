package com.foxit.crm.modules.system.domain.repository;

import com.foxit.crm.modules.system.domain.model.aggregate.RolePermission;

import java.util.List;
import java.util.Optional;

/**
 * 角色权限关联仓储接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface RolePermissionRepository {

    /**
     * 保存角色权限关联
     */
    RolePermission save(RolePermission rolePermission);

    /**
     * 根据ID查找角色权限关联
     */
    Optional<RolePermission> findById(Long id);

    /**
     * 根据角色ID查找权限关联列表
     */
    List<RolePermission> findByRoleId(Long roleId);

    /**
     * 根据权限ID查找角色关联列表
     */
    List<RolePermission> findByPermissionId(Long permissionId);

    /**
     * 根据角色ID和权限ID查找关联
     */
    Optional<RolePermission> findByRoleIdAndPermissionId(Long roleId, Long permissionId);

    /**
     * 批量保存角色权限关联
     */
    void saveBatch(List<RolePermission> rolePermissions);

    /**
     * 根据角色ID删除所有权限关联
     */
    void deleteByRoleId(Long roleId);

    /**
     * 根据权限ID删除所有角色关联
     */
    void deleteByPermissionId(Long permissionId);

    /**
     * 根据角色ID和权限ID删除关联
     */
    void deleteByRoleIdAndPermissionId(Long roleId, Long permissionId);

    /**
     * 根据角色ID列表删除关联
     */
    void deleteByRoleIds(List<Long> roleIds);

    /**
     * 根据权限ID列表删除关联
     */
    void deleteByPermissionIds(List<Long> permissionIds);

    /**
     * 检查角色权限关联是否存在
     */
    boolean existsByRoleIdAndPermissionId(Long roleId, Long permissionId);

    /**
     * 统计权限下的角色数量
     */
    long countByPermissionId(Long permissionId);

    /**
     * 统计角色的权限数量
     */
    long countByRoleId(Long roleId);

    /**
     * 根据角色ID获取权限ID列表
     */
    List<Long> findPermissionIdsByRoleId(Long roleId);

    /**
     * 根据权限ID获取角色ID列表
     */
    List<Long> findRoleIdsByPermissionId(Long permissionId);

    /**
     * 根据角色ID列表获取权限ID列表
     */
    List<Long> findPermissionIdsByRoleIds(List<Long> roleIds);

    /**
     * 根据权限ID列表获取角色ID列表
     */
    List<Long> findRoleIdsByPermissionIds(List<Long> permissionIds);
}
