package com.foxit.crm.modules.system.domain.repository;

import com.foxit.crm.modules.system.domain.model.aggregate.UserRole;

import java.util.List;
import java.util.Optional;

/**
 * 用户角色关联仓储接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface UserRoleRepository {

    /**
     * 保存用户角色关联
     */
    UserRole save(UserRole userRole);

    /**
     * 根据ID查找用户角色关联
     */
    Optional<UserRole> findById(Long id);

    /**
     * 根据用户ID查找角色关联列表
     */
    List<UserRole> findByUserId(Long userId);

    /**
     * 根据角色ID查找用户关联列表
     */
    List<UserRole> findByRoleId(Long roleId);

    /**
     * 根据用户ID和角色ID查找关联
     */
    Optional<UserRole> findByUserIdAndRoleId(Long userId, Long roleId);

    /**
     * 批量保存用户角色关联
     */
    void saveBatch(List<UserRole> userRoles);

    /**
     * 根据用户ID删除所有角色关联
     */
    void deleteByUserId(Long userId);

    /**
     * 根据角色ID删除所有用户关联
     */
    void deleteByRoleId(Long roleId);

    /**
     * 根据用户ID和角色ID删除关联
     */
    void deleteByUserIdAndRoleId(Long userId, Long roleId);

    /**
     * 根据用户ID列表删除关联
     */
    void deleteByUserIds(List<Long> userIds);

    /**
     * 根据角色ID列表删除关联
     */
    void deleteByRoleIds(List<Long> roleIds);

    /**
     * 检查用户角色关联是否存在
     */
    boolean existsByUserIdAndRoleId(Long userId, Long roleId);

    /**
     * 统计角色下的用户数量
     */
    long countByRoleId(Long roleId);

    /**
     * 统计用户的角色数量
     */
    long countByUserId(Long userId);

    /**
     * 根据用户ID获取角色ID列表
     */
    List<Long> findRoleIdsByUserId(Long userId);

    /**
     * 根据角色ID获取用户ID列表
     */
    List<Long> findUserIdsByRoleId(Long roleId);
}
