package com.foxit.crm.modules.behavioranalysis.domain.repository;

import com.foxit.crm.modules.behavioranalysis.domain.entity.FunnelAnalysisAggregate;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 漏斗分析仓储接口
 * 定义漏斗分析数据访问的领域契约
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface FunnelAnalysisRepository {

    /**
     * 获取漏斗转化分析数据
     *
     * @param timeRange 时间范围
     * @param funnelSteps 漏斗步骤列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 漏斗分析聚合根
     */
    Optional<FunnelAnalysisAggregate> getFunnelConversionAnalysis(TimeRange timeRange, List<String> funnelSteps, 
                                                                 List<Long> productLineIds, String dataScope);

    /**
     * 获取漏斗对比分析数据
     *
     * @param timeRange 时间范围
     * @param funnelGroups 漏斗组列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 漏斗分析聚合根
     */
    Optional<FunnelAnalysisAggregate> getFunnelComparisonAnalysis(TimeRange timeRange, List<FunnelGroup> funnelGroups, 
                                                                 List<Long> productLineIds, String dataScope);

    /**
     * 获取流失点分析数据
     *
     * @param timeRange 时间范围
     * @param funnelSteps 漏斗步骤列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 漏斗分析聚合根
     */
    Optional<FunnelAnalysisAggregate> getDropoutAnalysis(TimeRange timeRange, List<String> funnelSteps, 
                                                        List<Long> productLineIds, String dataScope);

    /**
     * 获取队列漏斗分析数据
     *
     * @param timeRange 时间范围
     * @param funnelSteps 漏斗步骤列表
     * @param cohortPeriod 队列周期
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 漏斗分析聚合根
     */
    Optional<FunnelAnalysisAggregate> getCohortFunnelAnalysis(TimeRange timeRange, List<String> funnelSteps, 
                                                             String cohortPeriod, List<Long> productLineIds, String dataScope);

    /**
     * 获取实时漏斗统计
     *
     * @param funnelSteps 漏斗步骤列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 实时统计数据
     */
    FunnelRealTimeStats getRealTimeFunnelStats(List<String> funnelSteps, List<Long> productLineIds, String dataScope);

    /**
     * 获取漏斗模板列表
     *
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 漏斗模板列表
     */
    List<FunnelTemplate> getFunnelTemplates(List<Long> productLineIds, String dataScope);

    /**
     * 保存漏斗分析结果
     *
     * @param aggregate 漏斗分析聚合根
     * @return 是否保存成功
     */
    boolean saveFunnelAnalysis(FunnelAnalysisAggregate aggregate);

    /**
     * 删除漏斗分析数据
     *
     * @param aggregateId 聚合根ID
     * @return 是否删除成功
     */
    boolean deleteFunnelAnalysis(String aggregateId);

    /**
     * 批量获取漏斗分析数据
     *
     * @param timeRanges 时间范围列表
     * @param funnelSteps 漏斗步骤列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 漏斗分析聚合根列表
     */
    List<FunnelAnalysisAggregate> batchGetFunnelAnalysisData(List<TimeRange> timeRanges, List<String> funnelSteps, 
                                                            List<Long> productLineIds, String dataScope);

    /**
     * 删除过期数据
     *
     * @param beforeTime 过期时间点
     * @return 删除的数据条数
     */
    int deleteExpiredData(LocalDateTime beforeTime);

    /**
     * 漏斗组
     */
    record FunnelGroup(
            String groupId,
            String groupName,
            List<String> funnelSteps,
            String description
    ) {}

    /**
     * 漏斗实时统计数据
     */
    record FunnelRealTimeStats(
            Long totalUsers,
            Double overallConversionRate,
            Double avgTimeToConvert,
            List<FunnelStepStats> stepStats,
            LocalDateTime lastUpdateTime
    ) {}

    /**
     * 漏斗步骤统计数据
     */
    record FunnelStepStats(
            String stepId,
            String stepName,
            Integer stepOrder,
            Long userCount,
            Double conversionRate,
            Double dropoffRate,
            String trendDirection
    ) {}

    /**
     * 漏斗模板
     */
    record FunnelTemplate(
            String templateId,
            String templateName,
            String templateCategory,
            List<String> steps,
            String description,
            Long productLineId,
            String productLineName,
            Boolean isActive,
            Integer usageCount
    ) {}

    /**
     * 获取漏斗分析统计摘要
     *
     * @param timeRange 时间范围
     * @param dataScope 数据权限范围
     * @return 统计摘要数据
     */
    FunnelAnalysisSummary getFunnelAnalysisSummary(TimeRange timeRange, String dataScope);

    /**
     * 漏斗分析统计摘要
     */
    record FunnelAnalysisSummary(
            Long totalFunnels,
            Long totalUsers,
            Double avgConversionRate,
            Double avgTimeToConvert,
            List<String> topPerformingFunnels,
            List<String> criticalDropoutPoints,
            LocalDateTime summaryTime
    ) {}

    /**
     * 获取漏斗优化建议
     *
     * @param timeRange 时间范围
     * @param funnelSteps 漏斗步骤列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 漏斗优化建议
     */
    List<FunnelOptimizationSuggestion> getFunnelOptimizationSuggestions(TimeRange timeRange, List<String> funnelSteps, 
                                                                        List<Long> productLineIds, String dataScope);

    /**
     * 漏斗优化建议
     */
    record FunnelOptimizationSuggestion(
            String suggestionId,
            String suggestionType,
            String title,
            String description,
            String targetStep,
            Double currentConversionRate,
            Double expectedImprovement,
            String priority,
            List<String> actionItems
    ) {}

    /**
     * 获取漏斗转化趋势
     *
     * @param timeRange 时间范围
     * @param funnelSteps 漏斗步骤列表
     * @param granularity 时间粒度
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 转化趋势数据
     */
    List<FunnelConversionTrend> getFunnelConversionTrends(TimeRange timeRange, List<String> funnelSteps, 
                                                         TimeRange.TimeGranularity granularity, List<Long> productLineIds, String dataScope);

    /**
     * 漏斗转化趋势
     */
    record FunnelConversionTrend(
            String timeLabel,
            Long totalUsers,
            Double conversionRate,
            List<StepConversionData> stepData,
            String trendDirection,
            Double changeRate
    ) {}

    /**
     * 步骤转化数据
     */
    record StepConversionData(
            String stepId,
            String stepName,
            Long userCount,
            Double conversionRate
    ) {}

    /**
     * 获取漏斗用户细分分析
     *
     * @param timeRange 时间范围
     * @param funnelSteps 漏斗步骤列表
     * @param segmentDimensions 细分维度
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 用户细分分析数据
     */
    List<FunnelUserSegmentAnalysis> getFunnelUserSegmentAnalysis(TimeRange timeRange, List<String> funnelSteps, 
                                                                List<String> segmentDimensions, List<Long> productLineIds, String dataScope);

    /**
     * 漏斗用户细分分析
     */
    record FunnelUserSegmentAnalysis(
            String segmentId,
            String segmentName,
            String segmentDimension,
            Long userCount,
            Double conversionRate,
            List<StepConversionData> stepData,
            String performance,
            List<String> insights
    ) {}
}
