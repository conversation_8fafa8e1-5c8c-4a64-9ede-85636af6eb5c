package com.foxit.crm.modules.system.api.dto.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 角色详情响应DTO
 *
 * 优化说明：
 * 1. 继承BaseResponse，复用通用字段
 * 2. 移除冗余的statusText字段，前端通过枚举映射获取文本描述
 * 3. 移除前端不需要的技术字段（createBy、updateBy、version）
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleDetailResponse extends BaseResponse {

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 角色状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 权限ID列表
     */
    private List<Long> permissionIds;

}
