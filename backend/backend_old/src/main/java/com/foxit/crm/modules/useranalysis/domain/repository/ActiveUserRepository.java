package com.foxit.crm.modules.useranalysis.domain.repository;

import com.foxit.crm.modules.useranalysis.domain.entity.ActiveUserAggregate;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;

import java.util.List;
import java.util.Optional;

/**
 * 活跃用户仓储接口
 * 定义活跃用户数据访问的领域契约
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface ActiveUserRepository {

    /**
     * 获取活跃用户总览数据
     *
     * @param timeRange 时间范围
     * @param dataScope 数据权限范围
     * @return 活跃用户聚合根
     */
    Optional<ActiveUserAggregate> getActiveUserOverview(TimeRange timeRange, String dataScope);

    /**
     * 获取活跃用户趋势数据
     *
     * @param timeRange 时间范围
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 活跃用户聚合根
     */
    Optional<ActiveUserAggregate> getActiveUserTrends(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 获取活跃用户分布数据
     *
     * @param timeRange 时间范围
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 活跃用户聚合根
     */
    Optional<ActiveUserAggregate> getActiveUserDistribution(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 获取活跃频次分析数据
     *
     * @param timeRange 时间范围
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 活跃用户聚合根
     */
    Optional<ActiveUserAggregate> getActiveUserFrequency(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 获取产品线活跃用户对比数据
     *
     * @param timeRange 时间范围
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 活跃用户聚合根
     */
    Optional<ActiveUserAggregate> getActiveUserComparison(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 获取活跃用户留存数据
     *
     * @param timeRange 时间范围
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 活跃用户聚合根
     */
    Optional<ActiveUserAggregate> getActiveUserRetention(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 获取活跃用户队列分析数据
     *
     * @param timeRange 时间范围
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 活跃用户聚合根
     */
    Optional<ActiveUserAggregate> getActiveUserCohort(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 保存活跃用户分析结果
     *
     * @param aggregate 活跃用户聚合根
     */
    void save(ActiveUserAggregate aggregate);

    /**
     * 根据ID查找活跃用户分析
     *
     * @param id 聚合根ID
     * @return 活跃用户聚合根
     */
    Optional<ActiveUserAggregate> findById(String id);

    /**
     * 删除过期的分析数据
     *
     * @param beforeTime 删除此时间之前的数据
     * @return 删除的记录数
     */
    int deleteExpiredData(java.time.LocalDateTime beforeTime);

    /**
     * 检查数据是否存在
     *
     * @param timeRange 时间范围
     * @param productLineIds 产品线ID列表
     * @param analysisType 分析类型
     * @return 是否存在数据
     */
    boolean existsData(TimeRange timeRange, List<Long> productLineIds, ActiveUserAggregate.ActiveUserAnalysisType analysisType);

    /**
     * 获取数据最后更新时间
     *
     * @param timeRange 时间范围
     * @param productLineIds 产品线ID列表
     * @param analysisType 分析类型
     * @return 最后更新时间
     */
    Optional<java.time.LocalDateTime> getLastUpdateTime(TimeRange timeRange, List<Long> productLineIds, ActiveUserAggregate.ActiveUserAnalysisType analysisType);

    /**
     * 批量获取活跃用户数据
     *
     * @param timeRanges 时间范围列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 活跃用户聚合根列表
     */
    List<ActiveUserAggregate> batchGetActiveUserData(List<TimeRange> timeRanges, List<Long> productLineIds, String dataScope);

    /**
     * 获取活跃用户统计摘要
     *
     * @param timeRange 时间范围
     * @param dataScope 数据权限范围
     * @return 统计摘要数据
     */
    ActiveUserSummary getActiveUserSummary(TimeRange timeRange, String dataScope);

    /**
     * 活跃用户统计摘要
     */
    record ActiveUserSummary(
            Long totalActiveUsers,
            Long dailyActiveUsers,
            Long weeklyActiveUsers,
            Long monthlyActiveUsers,
            Double stickinessRatio,
            Double activeRate,
            java.time.LocalDateTime calculatedAt
    ) {}
}
