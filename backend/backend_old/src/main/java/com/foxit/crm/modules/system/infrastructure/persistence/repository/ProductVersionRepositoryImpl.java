package com.foxit.crm.modules.system.infrastructure.persistence.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foxit.crm.modules.system.domain.model.aggregate.ProductVersion;
import com.foxit.crm.modules.system.domain.model.valueobject.VersionReleaseHistory;
import com.foxit.crm.modules.system.domain.repository.ProductVersionRepository;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.ProductVersionPO;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.VersionDownloadStatsPO;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.VersionReleaseHistoryPO;
import com.foxit.crm.modules.system.infrastructure.persistence.mapper.ProductVersionMapper;
import com.foxit.crm.modules.system.infrastructure.persistence.mapper.VersionDownloadStatsMapper;
import com.foxit.crm.modules.system.infrastructure.persistence.mapper.VersionReleaseHistoryMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 产品版本仓储实现
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ProductVersionRepositoryImpl implements ProductVersionRepository {

    private final ProductVersionMapper productVersionMapper;
    private final VersionReleaseHistoryMapper versionReleaseHistoryMapper;
    private final VersionDownloadStatsMapper versionDownloadStatsMapper;
    private final ObjectMapper objectMapper;

    @Override
    @Transactional
    public ProductVersion save(ProductVersion productVersion) {
        ProductVersionPO po = convertToPO(productVersion);

        if (po.getId() == null) {
            productVersionMapper.insert(po);
        } else {
            productVersionMapper.updateById(po);
        }

        return convertToDomain(po);
    }

    @Override
    public Optional<ProductVersion> findById(Long id) {
        ProductVersionPO po = productVersionMapper.selectById(id);
        return po != null ? Optional.of(convertToDomain(po)) : Optional.empty();
    }

    @Override
    public Optional<ProductVersion> findByProductLineIdAndVersionNumber(Long productLineId, String versionNumber) {
        QueryWrapper<ProductVersionPO> wrapper = new QueryWrapper<>();
        wrapper.eq("product_line_id", productLineId)
                .eq("version_number", versionNumber);

        ProductVersionPO po = productVersionMapper.selectOne(wrapper);
        return po != null ? Optional.of(convertToDomain(po)) : Optional.empty();
    }

    @Override
    public Optional<ProductVersion> getCurrentVersionByProductLineId(Long productLineId) {
        ProductVersionPO po = productVersionMapper.getCurrentVersionByProductLineId(productLineId);
        return po != null ? Optional.of(convertToDomain(po)) : Optional.empty();
    }

    @Override
    public List<ProductVersion> findByProductLineId(Long productLineId) {
        QueryWrapper<ProductVersionPO> wrapper = new QueryWrapper<>();
        wrapper.eq("product_line_id", productLineId)
                .orderByDesc("create_time");

        List<ProductVersionPO> poList = productVersionMapper.selectList(wrapper);
        return poList.stream()
                .map(this::convertToDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductVersion> findByPage(int page, int size, Long productLineId, String keyword,
            ProductVersion.VersionStatus status, ProductVersion.VersionType versionType) {
        QueryWrapper<ProductVersionPO> wrapper = new QueryWrapper<>();

        if (productLineId != null) {
            wrapper.eq("product_line_id", productLineId);
        }

        if (keyword != null && !keyword.trim().isEmpty()) {
            wrapper.and(w -> w.like("version_number", keyword)
                    .or().like("version_name", keyword)
                    .or().like("description", keyword));
        }

        if (status != null) {
            wrapper.eq("status", status.getCode());
        }

        if (versionType != null) {
            wrapper.eq("version_type", versionType.getCode());
        }

        wrapper.orderByDesc("create_time");

        IPage<ProductVersionPO> pageResult = productVersionMapper.selectPage(new Page<>(page, size), wrapper);
        return pageResult.getRecords().stream()
                .map(this::convertToDomain)
                .collect(Collectors.toList());
    }

    @Override
    public long count(Long productLineId, String keyword, ProductVersion.VersionStatus status,
            ProductVersion.VersionType versionType) {
        QueryWrapper<ProductVersionPO> wrapper = new QueryWrapper<>();

        if (productLineId != null) {
            wrapper.eq("product_line_id", productLineId);
        }

        if (keyword != null && !keyword.trim().isEmpty()) {
            wrapper.and(w -> w.like("version_number", keyword)
                    .or().like("version_name", keyword)
                    .or().like("description", keyword));
        }

        if (status != null) {
            wrapper.eq("status", status.getCode());
        }

        if (versionType != null) {
            wrapper.eq("version_type", versionType.getCode());
        }

        return productVersionMapper.selectCount(wrapper);
    }

    @Override
    public List<ProductVersion> findByStatus(ProductVersion.VersionStatus status) {
        List<ProductVersionPO> poList = productVersionMapper.findByStatus(status.getCode());
        return poList.stream()
                .map(this::convertToDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductVersion> findByProductLineIdAndStatus(Long productLineId, ProductVersion.VersionStatus status) {
        List<ProductVersionPO> poList = productVersionMapper.findByProductLineIdAndStatus(productLineId,
                status.getCode());
        return poList.stream()
                .map(this::convertToDomain)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void deleteById(Long id) {
        productVersionMapper.deleteById(id);
    }

    @Override
    @Transactional
    public void deleteByIds(List<Long> ids) {
        productVersionMapper.deleteBatchIds(ids);
    }

    @Override
    public boolean existsByProductLineIdAndVersionNumber(Long productLineId, String versionNumber) {
        return productVersionMapper.countByProductLineIdAndVersionNumber(productLineId, versionNumber) > 0;
    }

    @Override
    public boolean existsByProductLineIdAndVersionNumberAndIdNot(Long productLineId, String versionNumber,
            Long excludeId) {
        return productVersionMapper.countByProductLineIdAndVersionNumberExcludeId(productLineId, versionNumber,
                excludeId) > 0;
    }

    @Override
    @Transactional
    public void clearCurrentVersionByProductLineId(Long productLineId) {
        productVersionMapper.clearCurrentVersionByProductLineId(productLineId);
    }

    @Override
    @Transactional
    public void setCurrentVersion(Long versionId) {
        productVersionMapper.setCurrentVersion(versionId);
    }

    @Override
    @Transactional
    public void increaseDownloadCount(Long versionId, Integer count) {
        productVersionMapper.increaseDownloadCount(versionId, count);
    }

    @Override
    public List<Object> getVersionStatsByProductLineId(Long productLineId) {
        return productVersionMapper.getVersionStatsByProductLineId(productLineId);
    }

    @Override
    public String getLatestVersionNumber(Long productLineId) {
        return productVersionMapper.getLatestVersionNumber(productLineId);
    }

    @Override
    @Transactional
    public void saveReleaseHistory(VersionReleaseHistory history) {
        VersionReleaseHistoryPO po = convertHistoryToPO(history);
        versionReleaseHistoryMapper.insert(po);
    }

    @Override
    public List<VersionReleaseHistory> findReleaseHistoryByVersionId(Long versionId) {
        List<VersionReleaseHistoryPO> poList = versionReleaseHistoryMapper.findByVersionId(versionId);
        return poList.stream()
                .map(this::convertHistoryToDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<VersionReleaseHistory> findReleaseHistoryByActionBy(Long actionBy, Integer limit) {
        List<VersionReleaseHistoryPO> poList = versionReleaseHistoryMapper.findByActionBy(actionBy, limit);
        return poList.stream()
                .map(this::convertHistoryToDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<VersionReleaseHistory> findReleaseHistoryByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        List<VersionReleaseHistoryPO> poList = versionReleaseHistoryMapper.findByTimeRange(startTime, endTime);
        return poList.stream()
                .map(this::convertHistoryToDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<VersionReleaseHistory> findRecentReleaseHistory(Integer limit) {
        List<VersionReleaseHistoryPO> poList = versionReleaseHistoryMapper.findRecentHistory(limit);
        return poList.stream()
                .map(this::convertHistoryToDomain)
                .collect(Collectors.toList());
    }

    @Override
    public Long getTotalDownloadsByVersionId(Long versionId) {
        return versionDownloadStatsMapper.getTotalDownloadsByVersionId(versionId);
    }

    @Override
    public Long getTotalUniqueDownloadsByVersionId(Long versionId) {
        return versionDownloadStatsMapper.getTotalUniqueDownloadsByVersionId(versionId);
    }

    @Override
    public List<Object> getTopDownloadVersions(Integer days, Integer limit) {
        LocalDate startDate = LocalDate.now().minusDays(days);
        return versionDownloadStatsMapper.getTopDownloadVersions(startDate, limit);
    }

    @Override
    @Transactional
    public void recordDownloadStats(Long versionId, String platform, String region) {
        LocalDate today = LocalDate.now();
        VersionDownloadStatsPO stats = versionDownloadStatsMapper.findByVersionIdAndDate(versionId, today);

        if (stats == null) {
            // 创建新的统计记录
            stats = new VersionDownloadStatsPO();
            stats.setVersionId(versionId);
            stats.setDownloadDate(today);
            stats.setDownloadCount(1);
            stats.setUniqueDownloads(1);
            stats.setPlatformStats("{}");
            stats.setRegionStats("{}");
            versionDownloadStatsMapper.insert(stats);
        } else {
            // 更新现有统计记录
            stats.setDownloadCount(stats.getDownloadCount() + 1);
            versionDownloadStatsMapper.updateById(stats);
        }

        // 同时更新版本表的下载次数
        increaseDownloadCount(versionId, 1);
    }

    /**
     * 领域对象转换为持久化对象
     */
    private ProductVersionPO convertToPO(ProductVersion domain) {
        ProductVersionPO po = new ProductVersionPO();
        po.setId(domain.getId());
        po.setProductLineId(domain.getProductLineId());
        po.setVersionNumber(domain.getVersionNumber());
        po.setVersionName(domain.getVersionName());
        po.setDescription(domain.getDescription());
        po.setReleaseNotes(domain.getReleaseNotes());
        po.setVersionType(domain.getVersionType() != null ? domain.getVersionType().getCode() : null);
        po.setStatus(domain.getStatus() != null ? domain.getStatus().getCode() : null);
        po.setIsCurrent(domain.getIsCurrent() != null && domain.getIsCurrent() ? 1 : 0);
        po.setReleaseDate(domain.getReleaseDate());
        po.setPlannedDate(domain.getPlannedDate());
        po.setFileSize(domain.getFileSize());
        po.setDownloadUrl(domain.getDownloadUrl());
        po.setDownloadCount(domain.getDownloadCount());
        po.setPlatforms(listToJson(domain.getPlatforms()));
        po.setFeatures(listToJson(domain.getFeatures()));
        po.setBugFixes(listToJson(domain.getBugFixes()));
        po.setBreakingChanges(listToJson(domain.getBreakingChanges()));
        po.setDependencies(listToJson(domain.getDependencies()));
        po.setSystemRequirements(listToJson(domain.getSystemRequirements()));
        po.setChecksumMd5(domain.getChecksumMd5());
        po.setChecksumSha256(domain.getChecksumSha256());
        po.setCreatedBy(domain.getCreatedBy());
        po.setCreatedByName(domain.getCreatedByName());
        po.setApprovedBy(domain.getApprovedBy());
        po.setApprovedByName(domain.getApprovedByName());
        po.setApprovedAt(domain.getApprovedAt());
        po.setRemark(domain.getRemark());
        po.setCreateTime(domain.getCreateTime());
        po.setUpdateTime(domain.getUpdateTime());
        po.setCreateBy(domain.getCreateBy());
        po.setUpdateBy(domain.getUpdateBy());
        po.setVersion(domain.getVersion());
        return po;
    }

    /**
     * 持久化对象转换为领域对象
     */
    private ProductVersion convertToDomain(ProductVersionPO po) {
        ProductVersion domain = new ProductVersion();
        domain.setId(po.getId());
        domain.setProductLineId(po.getProductLineId());
        domain.setVersionNumber(po.getVersionNumber());
        domain.setVersionName(po.getVersionName());
        domain.setDescription(po.getDescription());
        domain.setReleaseNotes(po.getReleaseNotes());
        domain.setVersionType(ProductVersion.VersionType.fromCode(po.getVersionType()));
        domain.setStatus(ProductVersion.VersionStatus.fromCode(po.getStatus()));
        domain.setIsCurrent(po.getIsCurrent() != null && po.getIsCurrent() == 1);
        domain.setReleaseDate(po.getReleaseDate());
        domain.setPlannedDate(po.getPlannedDate());
        domain.setFileSize(po.getFileSize());
        domain.setDownloadUrl(po.getDownloadUrl());
        domain.setDownloadCount(po.getDownloadCount());
        domain.setPlatforms(jsonToList(po.getPlatforms()));
        domain.setFeatures(jsonToList(po.getFeatures()));
        domain.setBugFixes(jsonToList(po.getBugFixes()));
        domain.setBreakingChanges(jsonToList(po.getBreakingChanges()));
        domain.setDependencies(jsonToList(po.getDependencies()));
        domain.setSystemRequirements(jsonToList(po.getSystemRequirements()));
        domain.setChecksumMd5(po.getChecksumMd5());
        domain.setChecksumSha256(po.getChecksumSha256());
        domain.setCreatedBy(po.getCreatedBy());
        domain.setCreatedByName(po.getCreatedByName());
        domain.setApprovedBy(po.getApprovedBy());
        domain.setApprovedByName(po.getApprovedByName());
        domain.setApprovedAt(po.getApprovedAt());
        domain.setRemark(po.getRemark());
        domain.setCreateTime(po.getCreateTime());
        domain.setUpdateTime(po.getUpdateTime());
        domain.setCreateBy(po.getCreateBy());
        domain.setUpdateBy(po.getUpdateBy());
        domain.setVersion(po.getVersion());
        return domain;
    }

    /**
     * 历史记录领域对象转换为持久化对象
     */
    private VersionReleaseHistoryPO convertHistoryToPO(VersionReleaseHistory domain) {
        VersionReleaseHistoryPO po = new VersionReleaseHistoryPO();
        po.setId(domain.getId());
        po.setVersionId(domain.getVersionId());
        po.setActionType(domain.getActionType() != null ? domain.getActionType().getCode() : null);
        po.setFromStatus(domain.getFromStatus());
        po.setToStatus(domain.getToStatus());
        po.setActionReason(domain.getActionReason());
        po.setActionBy(domain.getActionBy());
        po.setActionByName(domain.getActionByName());
        po.setActionTime(domain.getActionTime());
        po.setIpAddress(domain.getIpAddress());
        po.setUserAgent(domain.getUserAgent());
        po.setRemark(domain.getRemark());
        return po;
    }

    /**
     * 历史记录持久化对象转换为领域对象
     */
    private VersionReleaseHistory convertHistoryToDomain(VersionReleaseHistoryPO po) {
        VersionReleaseHistory domain = new VersionReleaseHistory();
        domain.setId(po.getId());
        domain.setVersionId(po.getVersionId());
        domain.setActionType(VersionReleaseHistory.ActionType.fromCode(po.getActionType()));
        domain.setFromStatus(po.getFromStatus());
        domain.setToStatus(po.getToStatus());
        domain.setActionReason(po.getActionReason());
        domain.setActionBy(po.getActionBy());
        domain.setActionByName(po.getActionByName());
        domain.setActionTime(po.getActionTime());
        domain.setIpAddress(po.getIpAddress());
        domain.setUserAgent(po.getUserAgent());
        domain.setRemark(po.getRemark());
        return domain;
    }

    /**
     * List转JSON字符串
     */
    private String listToJson(List<String> list) {
        if (list == null || list.isEmpty()) {
            return "[]";
        }
        try {
            return objectMapper.writeValueAsString(list);
        } catch (JsonProcessingException e) {
            log.error("List转JSON失败", e);
            return "[]";
        }
    }

    /**
     * JSON字符串转List
     */
    private List<String> jsonToList(String json) {
        if (json == null || json.trim().isEmpty()) {
            return List.of();
        }
        try {
            return objectMapper.readValue(json, new TypeReference<List<String>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("JSON转List失败", e);
            return List.of();
        }
    }
}
