package com.foxit.crm.modules.system.application.service;

import com.foxit.crm.modules.system.api.dto.request.RoleCreateRequest;
import com.foxit.crm.modules.system.api.dto.request.RoleUpdateRequest;
import com.foxit.crm.modules.system.api.dto.response.RoleDetailResponse;
import com.foxit.crm.modules.system.api.dto.response.RoleSimpleResponse;
import com.foxit.crm.modules.system.api.dto.response.PageResponse;

import java.util.List;
import java.util.Map;

/**
 * 角色应用服务接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface RoleService {

    /**
     * 创建角色
     */
    Long createRole(RoleCreateRequest request);

    /**
     * 更新角色
     */
    void updateRole(Long id, RoleUpdateRequest request);

    /**
     * 删除角色
     */
    void deleteRole(Long id);

    /**
     * 根据ID获取角色详情
     */
    RoleDetailResponse getRoleById(Long id);

    /**
     * 分页查询角色列表
     */
    PageResponse<RoleDetailResponse> getRoleList(int page, int size, String keyword);

    /**
     * 获取所有启用的角色（简单信息）
     */
    List<RoleSimpleResponse> getAllEnabledRoles();

    /**
     * 根据用户ID获取角色列表
     */
    List<RoleSimpleResponse> getRolesByUserId(Long userId);

    /**
     * 启用角色
     */
    void enableRole(Long id);

    /**
     * 禁用角色
     */
    void disableRole(Long id);

    /**
     * 为角色分配权限
     */
    void assignPermissions(Long roleId, List<Long> permissionIds);

    /**
     * 获取角色的权限ID列表
     */
    List<Long> getRolePermissions(Long roleId);

    /**
     * 移除角色权限
     */
    void removePermissions(Long roleId, List<Long> permissionIds);

    /**
     * 复制角色
     */
    Long copyRole(Long roleId, String newName);

    /**
     * 获取角色用户列表
     */
    List<Map<String, Object>> getRoleUsers(Long roleId);

    /**
     * 为角色添加用户
     */
    void addUsersToRole(Long roleId, List<Long> userIds);

    /**
     * 从角色移除用户
     */
    void removeUsersFromRole(Long roleId, List<Long> userIds);
}
