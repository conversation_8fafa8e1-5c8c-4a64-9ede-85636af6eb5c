package com.foxit.crm.common.converter;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 通用DTO转换器接口
 * 
 * 用于标准化实体对象与DTO对象之间的转换逻辑，提供统一的转换规范
 * 适用于整个SCRM-Next项目的所有模块
 * 
 * @param <E> 实体类型 (Entity)
 * @param <D> DTO类型 (Data Transfer Object)
 * <AUTHOR>
 * @since 2025-06-23
 */
public interface DtoConverter<E, D> {
    
    /**
     * 实体转换为DTO
     * 
     * @param entity 实体对象
     * @return DTO对象，如果输入为null则返回null
     */
    D toDto(E entity);
    
    /**
     * 实体列表转换为DTO列表
     * 
     * @param entities 实体列表
     * @return DTO列表，如果输入为null则返回null
     */
    default List<D> toDtoList(List<E> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }
    
    /**
     * DTO转换为实体（可选实现）
     * 
     * 注意：大多数情况下不需要实现此方法，因为通常只需要从实体转换为DTO
     * 只有在特殊业务场景下才需要实现此方法
     * 
     * @param dto DTO对象
     * @return 实体对象
     * @throws UnsupportedOperationException 如果子类未实现此方法
     */
    default E toEntity(D dto) {
        throw new UnsupportedOperationException("toEntity method not implemented");
    }
    
    /**
     * DTO列表转换为实体列表（可选实现）
     * 
     * @param dtos DTO列表
     * @return 实体列表
     * @throws UnsupportedOperationException 如果子类未实现toEntity方法
     */
    default List<E> toEntityList(List<D> dtos) {
        if (dtos == null) {
            return null;
        }
        return dtos.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
