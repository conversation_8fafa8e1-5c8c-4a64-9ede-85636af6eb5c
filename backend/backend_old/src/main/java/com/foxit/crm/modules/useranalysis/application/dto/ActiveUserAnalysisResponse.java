package com.foxit.crm.modules.useranalysis.application.dto;

import com.foxit.crm.modules.useranalysis.domain.entity.ActiveUserAggregate;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 活跃用户分析响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Builder
public class ActiveUserAnalysisResponse {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private ActiveUserAggregate data;

    /**
     * 响应时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 创建成功响应
     */
    public static ActiveUserAnalysisResponse success(ActiveUserAggregate data) {
        return ActiveUserAnalysisResponse.builder()
                .success(true)
                .message("获取数据成功")
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建空数据响应
     */
    public static ActiveUserAnalysisResponse empty(String message) {
        return ActiveUserAnalysisResponse.builder()
                .success(true)
                .message(message)
                .data(null)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建错误响应
     */
    public static ActiveUserAnalysisResponse error(String message) {
        return ActiveUserAnalysisResponse.builder()
                .success(false)
                .message(message)
                .data(null)
                .timestamp(LocalDateTime.now())
                .errorCode("ANALYSIS_ERROR")
                .build();
    }

    /**
     * 创建错误响应（带错误代码）
     */
    public static ActiveUserAnalysisResponse error(String message, String errorCode) {
        return ActiveUserAnalysisResponse.builder()
                .success(false)
                .message(message)
                .data(null)
                .timestamp(LocalDateTime.now())
                .errorCode(errorCode)
                .build();
    }
}
