package com.foxit.crm.modules.system.domain.model.aggregate;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 角色权限关联聚合根
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@NoArgsConstructor
public class RolePermission {

    /**
     * 关联ID
     */
    private Long id;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 权限ID
     */
    private Long permissionId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 构造函数
     */
    public RolePermission(Long roleId, Long permissionId) {
        this.roleId = roleId;
        this.permissionId = permissionId;
    }

    /**
     * 构造函数
     */
    public RolePermission(Long roleId, Long permissionId, Long createBy) {
        this.roleId = roleId;
        this.permissionId = permissionId;
        this.createBy = createBy;
    }

    /**
     * 检查角色ID是否有效
     */
    public boolean isValidRoleId() {
        return this.roleId != null && this.roleId > 0;
    }

    /**
     * 检查权限ID是否有效
     */
    public boolean isValidPermissionId() {
        return this.permissionId != null && this.permissionId > 0;
    }

    /**
     * 检查关联是否有效
     */
    public boolean isValid() {
        return isValidRoleId() && isValidPermissionId();
    }
}
