package com.foxit.crm.modules.system.api.dto.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统配置详情响应DTO
 *
 * 优化说明：
 * 1. 继承BaseResponse，复用通用字段
 * 2. 移除冗余的Text字段，前端通过枚举映射获取文本描述
 * 3. 移除前端不需要的技术字段（createBy、updateBy、version）
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SystemConfigDetailResponse extends BaseResponse {

    /**
     * 配置键
     */
    private String configKey;

    /**
     * 配置值
     */
    private String configValue;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 配置描述
     */
    private String description;

    /**
     * 配置类型：1-字符串，2-数字，3-布尔值，4-JSON
     */
    private Integer configType;

    /**
     * 配置分组
     */
    private String configGroup;

    /**
     * 是否系统内置：0-否，1-是
     */
    private Integer isSystem;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 备注
     */
    private String remark;

}
