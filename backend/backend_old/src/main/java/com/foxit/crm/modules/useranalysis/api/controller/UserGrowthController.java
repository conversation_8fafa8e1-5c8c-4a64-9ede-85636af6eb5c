package com.foxit.crm.modules.useranalysis.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.common.util.SecurityUtils;
import com.foxit.crm.modules.useranalysis.application.dto.UserGrowthAnalysisRequest;
import com.foxit.crm.modules.useranalysis.application.dto.UserGrowthAnalysisResponse;
import com.foxit.crm.modules.useranalysis.application.service.UserGrowthAnalysisService;
import com.foxit.crm.modules.useranalysis.domain.repository.UserGrowthRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 用户增长分析控制器
 * 提供用户增长分析相关的REST API接口
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@RestController
@RequestMapping("/user/growth")
@RequiredArgsConstructor
@Tag(name = "用户增长分析", description = "用户增长分析相关接口")
public class UserGrowthController {

    private final UserGrowthAnalysisService userGrowthAnalysisService;

    @Operation(summary = "获取用户增长总览", description = "获取指定时间范围内的用户增长总览数据")
    @GetMapping("/overview")
    @PreAuthorize("hasPermission('user:growth', 'READ')")
    public Result<UserGrowthAnalysisResponse> getOverview(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "时间粒度") @RequestParam(required = false, defaultValue = "DAY") TimeRange.TimeGranularity granularity,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds,
            @Parameter(description = "是否包含对比") @RequestParam(required = false, defaultValue = "true") Boolean includeComparison) {

        try {
            UserGrowthAnalysisRequest request = UserGrowthAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(granularity)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .includeComparison(includeComparison)
                    .build();

            UserGrowthAnalysisResponse response = userGrowthAnalysisService.getUserGrowthOverview(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取用户增长总览失败", e);
            return Result.error("获取用户增长总览失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取新增用户分析", description = "获取新增用户的分析数据")
    @GetMapping("/new-users")
    @PreAuthorize("hasPermission('user:growth', 'READ')")
    public Result<UserGrowthAnalysisResponse> getNewUserAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "时间粒度") @RequestParam(required = false, defaultValue = "DAY") TimeRange.TimeGranularity granularity,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            UserGrowthAnalysisRequest request = UserGrowthAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(granularity)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            UserGrowthAnalysisResponse response = userGrowthAnalysisService.getNewUserAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取新增用户分析失败", e);
            return Result.error("获取新增用户分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取用户留存分析", description = "获取用户留存的分析数据")
    @GetMapping("/retention")
    @PreAuthorize("hasPermission('user:growth', 'READ')")
    public Result<UserGrowthAnalysisResponse> getRetentionAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds,
            @Parameter(description = "留存周期") @RequestParam(required = false) List<Integer> retentionPeriods) {

        try {
            UserGrowthAnalysisRequest request = UserGrowthAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .productLineIds(productLineIds)
                    .retentionPeriods(retentionPeriods)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            UserGrowthAnalysisResponse response = userGrowthAnalysisService.getUserRetentionAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取用户留存分析失败", e);
            return Result.error("获取用户留存分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取增长趋势分析", description = "获取用户增长趋势的分析数据")
    @GetMapping("/trends")
    @PreAuthorize("hasPermission('user:growth', 'READ')")
    public Result<UserGrowthAnalysisResponse> getGrowthTrends(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "时间粒度") @RequestParam(required = false, defaultValue = "DAY") TimeRange.TimeGranularity granularity,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            UserGrowthAnalysisRequest request = UserGrowthAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(granularity)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            UserGrowthAnalysisResponse response = userGrowthAnalysisService.getGrowthTrendAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取增长趋势分析失败", e);
            return Result.error("获取增长趋势分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取用户来源分析", description = "获取用户来源的分析数据")
    @GetMapping("/sources")
    @PreAuthorize("hasPermission('user:growth', 'READ')")
    public Result<UserGrowthAnalysisResponse> getUserSourceAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds,
            @Parameter(description = "来源类型过滤") @RequestParam(required = false) List<String> sourceTypes) {

        try {
            UserGrowthAnalysisRequest request = UserGrowthAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .productLineIds(productLineIds)
                    .sourceTypes(sourceTypes)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            UserGrowthAnalysisResponse response = userGrowthAnalysisService.getUserSourceAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取用户来源分析失败", e);
            return Result.error("获取用户来源分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取队列留存分析", description = "获取队列留存的分析数据")
    @GetMapping("/cohort-retention")
    @PreAuthorize("hasPermission('user:growth', 'READ')")
    public Result<UserGrowthAnalysisResponse> getCohortRetentionAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            UserGrowthAnalysisRequest request = UserGrowthAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            UserGrowthAnalysisResponse response = userGrowthAnalysisService.getCohortRetentionAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取队列留存分析失败", e);
            return Result.error("获取队列留存分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取产品线对比分析", description = "获取多个产品线的对比分析数据")
    @GetMapping("/comparison")
    @PreAuthorize("hasPermission('user:growth', 'READ')")
    public Result<List<UserGrowthAnalysisResponse>> getProductLineComparison(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表", required = true) @RequestParam List<Long> productLineIds) {

        try {
            UserGrowthAnalysisRequest request = UserGrowthAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            List<UserGrowthAnalysisResponse> responses = userGrowthAnalysisService.getProductLineComparison(request);
            return Result.success(responses);

        } catch (Exception e) {
            log.error("获取产品线对比分析失败", e);
            return Result.error("获取产品线对比分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取用户增长统计摘要", description = "获取用户增长的统计摘要数据")
    @GetMapping("/summary")
    @PreAuthorize("hasPermission('user:growth', 'READ')")
    public Result<UserGrowthRepository.UserGrowthSummary> getGrowthSummary(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        try {
            UserGrowthAnalysisRequest request = UserGrowthAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            UserGrowthRepository.UserGrowthSummary summary = userGrowthAnalysisService.getUserGrowthSummary(request);
            return Result.success(summary);

        } catch (Exception e) {
            log.error("获取用户增长统计摘要失败", e);
            return Result.error("获取用户增长统计摘要失败: " + e.getMessage());
        }
    }

    // ==================== 公共接口（无需认证） ====================

    /**
     * 获取公共用户增长总览 - 无需认证
     */
    @Operation(summary = "获取公共用户增长总览", description = "获取用户增长总览数据（无需认证）")
    @GetMapping("/public/overview")
    public Result<UserGrowthRepository.UserGrowthSummary> getPublicGrowthOverview(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "时间粒度") @RequestParam(required = false, defaultValue = "DAY") TimeRange.TimeGranularity granularity,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds,
            @Parameter(description = "是否包含对比数据") @RequestParam(required = false, defaultValue = "false") boolean includeComparison) {

        try {
            UserGrowthAnalysisRequest request = UserGrowthAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(granularity)
                    .productLineIds(productLineIds)
                    .includeComparison(includeComparison)
                    .dataScope("PUBLIC")
                    .build();

            UserGrowthRepository.UserGrowthSummary summary = userGrowthAnalysisService.getUserGrowthSummary(request);
            return Result.success(summary);

        } catch (Exception e) {
            log.error("获取公共用户增长总览失败", e);
            return Result.error("获取公共用户增长总览失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共新增用户分析 - 无需认证
     */
    @Operation(summary = "获取公共新增用户分析", description = "获取新增用户分析数据（无需认证）")
    @GetMapping("/public/new-users")
    public Result<UserGrowthRepository.UserGrowthSummary> getPublicNewUsersAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "时间粒度") @RequestParam(required = false, defaultValue = "DAY") TimeRange.TimeGranularity granularity,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            UserGrowthAnalysisRequest request = UserGrowthAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(granularity)
                    .productLineIds(productLineIds)
                    .includeComparison(false)
                    .dataScope("PUBLIC")
                    .dataScope("PUBLIC")
                    .build();

            UserGrowthRepository.UserGrowthSummary summary = userGrowthAnalysisService.getUserGrowthSummary(request);
            return Result.success(summary);

        } catch (Exception e) {
            log.error("获取公共新增用户分析失败", e);
            return Result.error("获取公共新增用户分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共用户留存分析 - 无需认证
     */
    @Operation(summary = "获取公共用户留存分析", description = "获取用户留存分析数据（无需认证）")
    @GetMapping("/public/retention")
    public Result<UserGrowthAnalysisResponse> getPublicRetentionAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds,
            @Parameter(description = "留存周期列表") @RequestParam(required = false) List<Integer> retentionPeriods) {

        try {
            UserGrowthAnalysisRequest request = UserGrowthAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .productLineIds(productLineIds)
                    .includeComparison(false)
                    .dataScope("PUBLIC")
                    .build();

            UserGrowthAnalysisResponse analysis = userGrowthAnalysisService.getUserRetentionAnalysis(request);
            return Result.success(analysis);

        } catch (Exception e) {
            log.error("获取公共用户留存分析失败", e);
            return Result.error("获取公共用户留存分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共增长趋势分析 - 无需认证
     */
    @Operation(summary = "获取公共增长趋势分析", description = "获取增长趋势分析数据（无需认证）")
    @GetMapping("/public/trends")
    public Result<UserGrowthAnalysisResponse> getPublicGrowthTrends(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "时间粒度") @RequestParam(required = false, defaultValue = "DAY") TimeRange.TimeGranularity granularity,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            UserGrowthAnalysisRequest request = UserGrowthAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(granularity)
                    .productLineIds(productLineIds)
                    .includeComparison(false)
                    .dataScope("PUBLIC")
                    .build();

            UserGrowthAnalysisResponse analysis = userGrowthAnalysisService.getGrowthTrendAnalysis(request);
            return Result.success(analysis);

        } catch (Exception e) {
            log.error("获取公共增长趋势分析失败", e);
            return Result.error("获取公共增长趋势分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共用户来源分析 - 无需认证
     */
    @Operation(summary = "获取公共用户来源分析", description = "获取用户来源分析数据（无需认证）")
    @GetMapping("/public/sources")
    public Result<UserGrowthAnalysisResponse> getPublicSourceAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds,
            @Parameter(description = "来源类型列表") @RequestParam(required = false) List<String> sourceTypes) {

        try {
            UserGrowthAnalysisRequest request = UserGrowthAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .productLineIds(productLineIds)
                    .includeComparison(false)
                    .dataScope("PUBLIC")
                    .build();

            UserGrowthAnalysisResponse analysis = userGrowthAnalysisService.getUserSourceAnalysis(request);
            return Result.success(analysis);

        } catch (Exception e) {
            log.error("获取公共用户来源分析失败", e);
            return Result.error("获取公共用户来源分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共队列留存分析 - 无需认证
     */
    @Operation(summary = "获取公共队列留存分析", description = "获取队列留存分析数据（无需认证）")
    @GetMapping("/public/cohort-retention")
    public Result<UserGrowthAnalysisResponse> getPublicCohortRetentionAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            UserGrowthAnalysisRequest request = UserGrowthAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .productLineIds(productLineIds)
                    .includeComparison(false)
                    .dataScope("PUBLIC")
                    .build();

            UserGrowthAnalysisResponse analysis = userGrowthAnalysisService.getCohortRetentionAnalysis(request);
            return Result.success(analysis);

        } catch (Exception e) {
            log.error("获取公共队列留存分析失败", e);
            return Result.error("获取公共队列留存分析失败: " + e.getMessage());
        }
    }
}
