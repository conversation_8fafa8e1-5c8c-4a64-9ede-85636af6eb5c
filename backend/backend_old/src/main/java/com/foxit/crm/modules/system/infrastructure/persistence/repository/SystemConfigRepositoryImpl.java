package com.foxit.crm.modules.system.infrastructure.persistence.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.foxit.crm.modules.system.domain.model.aggregate.SystemConfig;
import com.foxit.crm.modules.system.domain.repository.SystemConfigRepository;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.SystemConfigPO;
import com.foxit.crm.modules.system.infrastructure.persistence.mapper.SystemConfigMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 系统配置仓储实现
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Repository
@RequiredArgsConstructor
public class SystemConfigRepositoryImpl implements SystemConfigRepository {

    private final SystemConfigMapper systemConfigMapper;

    @Override
    public SystemConfig save(SystemConfig systemConfig) {
        SystemConfigPO systemConfigPO = new SystemConfigPO();
        BeanUtils.copyProperties(systemConfig, systemConfigPO);

        if (systemConfig.getId() == null) {
            systemConfigMapper.insert(systemConfigPO);
            systemConfig.setId(systemConfigPO.getId());
        } else {
            systemConfigMapper.updateById(systemConfigPO);
        }

        return systemConfig;
    }

    @Override
    public Optional<SystemConfig> findById(Long id) {
        SystemConfigPO systemConfigPO = systemConfigMapper.selectById(id);
        if (systemConfigPO == null) {
            return Optional.empty();
        }

        SystemConfig systemConfig = new SystemConfig();
        BeanUtils.copyProperties(systemConfigPO, systemConfig);
        return Optional.of(systemConfig);
    }

    @Override
    public Optional<SystemConfig> findByConfigKey(String configKey) {
        QueryWrapper<SystemConfigPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("config_key", configKey);

        SystemConfigPO systemConfigPO = systemConfigMapper.selectOne(queryWrapper);
        if (systemConfigPO == null) {
            return Optional.empty();
        }

        SystemConfig systemConfig = new SystemConfig();
        BeanUtils.copyProperties(systemConfigPO, systemConfig);
        return Optional.of(systemConfig);
    }

    @Override
    public List<SystemConfig> findAllEnabled() {
        QueryWrapper<SystemConfigPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1)
                .orderByAsc("config_group", "sort_order");

        List<SystemConfigPO> systemConfigPOs = systemConfigMapper.selectList(queryWrapper);
        return systemConfigPOs.stream()
                .map(this::convertToSystemConfig)
                .collect(Collectors.toList());
    }

    @Override
    public List<SystemConfig> findByConfigGroup(String configGroup) {
        QueryWrapper<SystemConfigPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("config_group", configGroup)
                .eq("status", 1)
                .orderByAsc("sort_order");

        List<SystemConfigPO> systemConfigPOs = systemConfigMapper.selectList(queryWrapper);
        return systemConfigPOs.stream()
                .map(this::convertToSystemConfig)
                .collect(Collectors.toList());
    }

    @Override
    public List<SystemConfig> findByPage(int page, int size, String keyword, String configGroup, Integer status) {
        Page<SystemConfigPO> pageParam = new Page<>(page, size);
        QueryWrapper<SystemConfigPO> queryWrapper = buildQueryWrapper(keyword, configGroup, status);
        queryWrapper.orderByAsc("config_group", "sort_order");

        Page<SystemConfigPO> result = systemConfigMapper.selectPage(pageParam, queryWrapper);
        return result.getRecords().stream()
                .map(this::convertToSystemConfig)
                .collect(Collectors.toList());
    }

    @Override
    public long count(String keyword, String configGroup, Integer status) {
        QueryWrapper<SystemConfigPO> queryWrapper = buildQueryWrapper(keyword, configGroup, status);
        return systemConfigMapper.selectCount(queryWrapper);
    }

    @Override
    public void deleteById(Long id) {
        systemConfigMapper.deleteById(id);
    }

    @Override
    public boolean existsByConfigKey(String configKey) {
        QueryWrapper<SystemConfigPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("config_key", configKey);
        return systemConfigMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean existsByConfigKeyAndIdNot(String configKey, Long id) {
        QueryWrapper<SystemConfigPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("config_key", configKey)
                .ne("id", id);
        return systemConfigMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public List<SystemConfig> findByStatus(Integer status) {
        QueryWrapper<SystemConfigPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status)
                .orderByAsc("config_group", "sort_order");

        List<SystemConfigPO> systemConfigPOs = systemConfigMapper.selectList(queryWrapper);
        return systemConfigPOs.stream()
                .map(this::convertToSystemConfig)
                .collect(Collectors.toList());
    }

    @Override
    public List<SystemConfig> findSystemConfigs() {
        QueryWrapper<SystemConfigPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_system", 1)
                .orderByAsc("config_group", "sort_order");

        List<SystemConfigPO> systemConfigPOs = systemConfigMapper.selectList(queryWrapper);
        return systemConfigPOs.stream()
                .map(this::convertToSystemConfig)
                .collect(Collectors.toList());
    }

    @Override
    public List<SystemConfig> findUserConfigs() {
        QueryWrapper<SystemConfigPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_system", 0)
                .orderByAsc("config_group", "sort_order");

        List<SystemConfigPO> systemConfigPOs = systemConfigMapper.selectList(queryWrapper);
        return systemConfigPOs.stream()
                .map(this::convertToSystemConfig)
                .collect(Collectors.toList());
    }

    @Override
    public List<SystemConfig> findByConfigType(Integer configType) {
        QueryWrapper<SystemConfigPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("config_type", configType)
                .eq("status", 1)
                .orderByAsc("config_group", "sort_order");

        List<SystemConfigPO> systemConfigPOs = systemConfigMapper.selectList(queryWrapper);
        return systemConfigPOs.stream()
                .map(this::convertToSystemConfig)
                .collect(Collectors.toList());
    }

    @Override
    public void updateStatusBatch(List<Long> ids, Integer status) {
        if (ids == null || ids.isEmpty()) {
            return;
        }

        UpdateWrapper<SystemConfigPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", ids)
                .set("status", status);

        systemConfigMapper.update(null, updateWrapper);
    }

    @Override
    public List<String> findAllConfigGroups() {
        QueryWrapper<SystemConfigPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT config_group")
                .isNotNull("config_group")
                .ne("config_group", "")
                .orderByAsc("config_group");

        List<SystemConfigPO> systemConfigPOs = systemConfigMapper.selectList(queryWrapper);
        return systemConfigPOs.stream()
                .map(SystemConfigPO::getConfigGroup)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<SystemConfig> findByConfigKeys(List<String> configKeys) {
        if (configKeys == null || configKeys.isEmpty()) {
            return List.of();
        }

        QueryWrapper<SystemConfigPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("config_key", configKeys)
                .eq("status", 1);

        List<SystemConfigPO> systemConfigPOs = systemConfigMapper.selectList(queryWrapper);
        return systemConfigPOs.stream()
                .map(this::convertToSystemConfig)
                .collect(Collectors.toList());
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper<SystemConfigPO> buildQueryWrapper(String keyword, String configGroup, Integer status) {
        QueryWrapper<SystemConfigPO> queryWrapper = new QueryWrapper<>();

        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                    .like("config_name", keyword)
                    .or().like("config_key", keyword)
                    .or().like("description", keyword));
        }

        if (configGroup != null && !configGroup.trim().isEmpty()) {
            queryWrapper.eq("config_group", configGroup);
        }

        if (status != null) {
            queryWrapper.eq("status", status);
        }

        return queryWrapper;
    }

    /**
     * 转换PO为领域对象
     */
    private SystemConfig convertToSystemConfig(SystemConfigPO systemConfigPO) {
        SystemConfig systemConfig = new SystemConfig();
        BeanUtils.copyProperties(systemConfigPO, systemConfig);
        return systemConfig;
    }
}
