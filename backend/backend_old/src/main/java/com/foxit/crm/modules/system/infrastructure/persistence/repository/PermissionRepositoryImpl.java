package com.foxit.crm.modules.system.infrastructure.persistence.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.foxit.crm.modules.system.domain.model.aggregate.Permission;
import com.foxit.crm.modules.system.domain.repository.PermissionRepository;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.PermissionPO;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.RolePermissionPO;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.UserRolePO;
import com.foxit.crm.modules.system.infrastructure.persistence.mapper.PermissionMapper;
import com.foxit.crm.modules.system.infrastructure.persistence.mapper.RolePermissionMapper;
import com.foxit.crm.modules.system.infrastructure.persistence.mapper.UserRoleMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 权限仓储实现
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Repository
@RequiredArgsConstructor
public class PermissionRepositoryImpl implements PermissionRepository {

    private final PermissionMapper permissionMapper;
    private final RolePermissionMapper rolePermissionMapper;
    private final UserRoleMapper userRoleMapper;

    @Override
    public Permission save(Permission permission) {
        PermissionPO permissionPO = new PermissionPO();
        BeanUtils.copyProperties(permission, permissionPO);
        
        if (permission.getId() == null) {
            permissionMapper.insert(permissionPO);
            permission.setId(permissionPO.getId());
        } else {
            permissionMapper.updateById(permissionPO);
        }
        
        return permission;
    }

    @Override
    public Optional<Permission> findById(Long id) {
        PermissionPO permissionPO = permissionMapper.selectById(id);
        if (permissionPO == null) {
            return Optional.empty();
        }
        
        Permission permission = new Permission();
        BeanUtils.copyProperties(permissionPO, permission);
        return Optional.of(permission);
    }

    @Override
    public Optional<Permission> findByPermissionCode(String permissionCode) {
        QueryWrapper<PermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("permission_code", permissionCode);
        
        PermissionPO permissionPO = permissionMapper.selectOne(queryWrapper);
        if (permissionPO == null) {
            return Optional.empty();
        }
        
        Permission permission = new Permission();
        BeanUtils.copyProperties(permissionPO, permission);
        return Optional.of(permission);
    }

    @Override
    public List<Permission> findAllEnabled() {
        QueryWrapper<PermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1)
                   .orderByAsc("sort_order");
        
        List<PermissionPO> permissionPOs = permissionMapper.selectList(queryWrapper);
        return permissionPOs.stream()
                           .map(this::convertToPermission)
                           .collect(Collectors.toList());
    }

    @Override
    public List<Permission> findByParentId(Long parentId) {
        QueryWrapper<PermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", parentId)
                   .orderByAsc("sort_order");
        
        List<PermissionPO> permissionPOs = permissionMapper.selectList(queryWrapper);
        return permissionPOs.stream()
                           .map(this::convertToPermission)
                           .collect(Collectors.toList());
    }

    @Override
    public List<Permission> findRootPermissions() {
        QueryWrapper<PermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", 0)
                   .eq("status", 1)
                   .orderByAsc("sort_order");
        
        List<PermissionPO> permissionPOs = permissionMapper.selectList(queryWrapper);
        return permissionPOs.stream()
                           .map(this::convertToPermission)
                           .collect(Collectors.toList());
    }

    @Override
    public List<Permission> buildPermissionTree() {
        // 获取所有启用的权限
        List<Permission> allPermissions = findAllEnabled();
        
        // 构建权限树
        return buildTree(allPermissions, 0L);
    }

    @Override
    public List<Permission> findByPage(int page, int size, String keyword, Integer permissionType) {
        Page<PermissionPO> pageParam = new Page<>(page, size);
        QueryWrapper<PermissionPO> queryWrapper = new QueryWrapper<>();
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                .like("permission_name", keyword)
                .or()
                .like("permission_code", keyword)
            );
        }
        
        if (permissionType != null) {
            queryWrapper.eq("permission_type", permissionType);
        }
        
        queryWrapper.orderByAsc("sort_order");
        
        Page<PermissionPO> result = permissionMapper.selectPage(pageParam, queryWrapper);
        return result.getRecords().stream()
                    .map(this::convertToPermission)
                    .collect(Collectors.toList());
    }

    @Override
    public long count(String keyword, Integer permissionType) {
        QueryWrapper<PermissionPO> queryWrapper = new QueryWrapper<>();
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                .like("permission_name", keyword)
                .or()
                .like("permission_code", keyword)
            );
        }
        
        if (permissionType != null) {
            queryWrapper.eq("permission_type", permissionType);
        }
        
        return permissionMapper.selectCount(queryWrapper);
    }

    @Override
    public List<Permission> findByRoleId(Long roleId) {
        // 先查询角色权限关联
        QueryWrapper<RolePermissionPO> rolePermissionQuery = new QueryWrapper<>();
        rolePermissionQuery.eq("role_id", roleId);
        List<RolePermissionPO> rolePermissions = rolePermissionMapper.selectList(rolePermissionQuery);
        
        if (rolePermissions.isEmpty()) {
            return List.of();
        }
        
        // 获取权限ID列表
        List<Long> permissionIds = rolePermissions.stream()
                                                 .map(RolePermissionPO::getPermissionId)
                                                 .collect(Collectors.toList());
        
        // 查询权限信息
        List<PermissionPO> permissionPOs = permissionMapper.selectBatchIds(permissionIds);
        return permissionPOs.stream()
                           .map(this::convertToPermission)
                           .collect(Collectors.toList());
    }

    @Override
    public List<Permission> findByUserId(Long userId) {
        // 先查询用户角色关联
        QueryWrapper<UserRolePO> userRoleQuery = new QueryWrapper<>();
        userRoleQuery.eq("user_id", userId);
        List<UserRolePO> userRoles = userRoleMapper.selectList(userRoleQuery);
        
        if (userRoles.isEmpty()) {
            return List.of();
        }
        
        // 获取角色ID列表
        List<Long> roleIds = userRoles.stream()
                                    .map(UserRolePO::getRoleId)
                                    .collect(Collectors.toList());
        
        // 查询角色权限关联
        QueryWrapper<RolePermissionPO> rolePermissionQuery = new QueryWrapper<>();
        rolePermissionQuery.in("role_id", roleIds);
        List<RolePermissionPO> rolePermissions = rolePermissionMapper.selectList(rolePermissionQuery);
        
        if (rolePermissions.isEmpty()) {
            return List.of();
        }
        
        // 获取权限ID列表（去重）
        Set<Long> permissionIds = rolePermissions.stream()
                                                .map(RolePermissionPO::getPermissionId)
                                                .collect(Collectors.toSet());
        
        // 查询权限信息
        List<PermissionPO> permissionPOs = permissionMapper.selectBatchIds(permissionIds);
        return permissionPOs.stream()
                           .map(this::convertToPermission)
                           .collect(Collectors.toList());
    }

    @Override
    public List<Permission> findByPermissionType(Integer permissionType) {
        QueryWrapper<PermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("permission_type", permissionType)
                   .eq("status", 1)
                   .orderByAsc("sort_order");
        
        List<PermissionPO> permissionPOs = permissionMapper.selectList(queryWrapper);
        return permissionPOs.stream()
                           .map(this::convertToPermission)
                           .collect(Collectors.toList());
    }

    @Override
    public void deleteById(Long id) {
        permissionMapper.deleteById(id);
    }

    @Override
    public boolean existsByPermissionCode(String permissionCode) {
        QueryWrapper<PermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("permission_code", permissionCode);
        return permissionMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean existsByPermissionCodeAndIdNot(String permissionCode, Long id) {
        QueryWrapper<PermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("permission_code", permissionCode)
                   .ne("id", id);
        return permissionMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean hasChildren(Long parentId) {
        QueryWrapper<PermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", parentId);
        return permissionMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public List<Permission> findByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return List.of();
        }
        
        List<PermissionPO> permissionPOs = permissionMapper.selectBatchIds(ids);
        return permissionPOs.stream()
                           .map(this::convertToPermission)
                           .collect(Collectors.toList());
    }

    /**
     * 构建权限树
     */
    private List<Permission> buildTree(List<Permission> permissions, Long parentId) {
        List<Permission> tree = new ArrayList<>();
        
        for (Permission permission : permissions) {
            if (Objects.equals(permission.getParentId(), parentId)) {
                List<Permission> children = buildTree(permissions, permission.getId());
                permission.setChildren(children);
                tree.add(permission);
            }
        }
        
        return tree;
    }

    /**
     * 转换PO为领域对象
     */
    private Permission convertToPermission(PermissionPO permissionPO) {
        Permission permission = new Permission();
        BeanUtils.copyProperties(permissionPO, permission);
        return permission;
    }
}
