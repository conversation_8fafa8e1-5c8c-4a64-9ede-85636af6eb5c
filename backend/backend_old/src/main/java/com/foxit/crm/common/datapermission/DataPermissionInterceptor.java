package com.foxit.crm.common.datapermission;

import com.foxit.crm.common.annotation.DataPermission;
import com.foxit.crm.common.aspect.DataPermissionAspect;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Component;

import java.util.Properties;

/**
 * MyBatis数据权限拦截器
 * 在SQL执行前动态添加数据权限条件
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Component
@Intercepts({
        @Signature(type = Executor.class, method = "query", args = { MappedStatement.class, Object.class,
                RowBounds.class, ResultHandler.class })
})
public class DataPermissionInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 检查是否有数据权限配置
        if (!DataPermissionAspect.DataPermissionContext.hasDataPermission()) {
            return invocation.proceed();
        }

        DataPermission dataPermission = DataPermissionAspect.DataPermissionContext.getDataPermission();
        String permissionCondition = DataPermissionAspect.DataPermissionContext.getPermissionCondition();

        if (permissionCondition == null || permissionCondition.trim().isEmpty()) {
            return invocation.proceed();
        }

        Object[] args = invocation.getArgs();
        MappedStatement mappedStatement = (MappedStatement) args[0];
        Object parameter = args[1];
        RowBounds rowBounds = (RowBounds) args[2];
        ResultHandler resultHandler = (ResultHandler) args[3];

        // 获取原始SQL
        BoundSql boundSql = mappedStatement.getBoundSql(parameter);
        String originalSql = boundSql.getSql();

        try {
            // 解析并修改SQL
            String modifiedSql = addDataPermissionCondition(originalSql, permissionCondition, dataPermission);

            if (!originalSql.equals(modifiedSql)) {
                log.debug("应用数据权限SQL修改:\n原始SQL: {}\n修改后: {}", originalSql, modifiedSql);

                // 创建新的BoundSql
                BoundSql newBoundSql = new BoundSql(mappedStatement.getConfiguration(), modifiedSql,
                        boundSql.getParameterMappings(), parameter);

                // 复制额外参数（MyBatis 3.5+版本中getAdditionalParameterNames方法可能不存在）
                try {
                    // 使用反射获取额外参数
                    java.lang.reflect.Method getAdditionalParameterNamesMethod = boundSql.getClass()
                            .getMethod("getAdditionalParameterNames");
                    @SuppressWarnings("unchecked")
                    java.util.Set<String> paramNames = (java.util.Set<String>) getAdditionalParameterNamesMethod
                            .invoke(boundSql);
                    if (paramNames != null) {
                        for (String key : paramNames) {
                            newBoundSql.setAdditionalParameter(key, boundSql.getAdditionalParameter(key));
                        }
                    }
                } catch (Exception e) {
                    log.debug("复制额外参数失败，可能是MyBatis版本不支持: {}", e.getMessage());
                }

                // 创建新的MappedStatement
                MappedStatement newMappedStatement = copyMappedStatement(mappedStatement, newBoundSql);
                args[0] = newMappedStatement;
            }
        } catch (Exception e) {
            log.error("数据权限SQL修改失败，使用原始SQL: {}", originalSql, e);
        }

        return invocation.proceed();
    }

    /**
     * 添加数据权限条件到SQL
     */
    private String addDataPermissionCondition(String originalSql, String permissionCondition,
            DataPermission dataPermission) {
        try {
            Statement statement = CCJSqlParserUtil.parse(originalSql);

            if (statement instanceof Select) {
                Select select = (Select) statement;
                PlainSelect plainSelect = (PlainSelect) select.getSelectBody();

                // 解析权限条件
                Expression permissionExpression = CCJSqlParserUtil.parseCondExpression(permissionCondition);

                // 添加到WHERE条件
                Expression whereExpression = plainSelect.getWhere();
                if (whereExpression != null) {
                    plainSelect.setWhere(new AndExpression(whereExpression, permissionExpression));
                } else {
                    plainSelect.setWhere(permissionExpression);
                }

                return select.toString();
            }
        } catch (JSQLParserException e) {
            log.warn("SQL解析失败，尝试简单字符串拼接: {}", e.getMessage());
            return addConditionByStringConcat(originalSql, permissionCondition);
        }

        return originalSql;
    }

    /**
     * 通过字符串拼接添加条件（备用方案）
     */
    private String addConditionByStringConcat(String originalSql, String permissionCondition) {
        String sql = originalSql.trim();
        String upperSql = sql.toUpperCase();

        // 查找WHERE子句的位置
        int whereIndex = upperSql.indexOf(" WHERE ");
        if (whereIndex > 0) {
            // 已有WHERE子句，添加AND条件
            return sql.substring(0, whereIndex + 7) + "(" + permissionCondition + ") AND (" +
                    sql.substring(whereIndex + 7) + ")";
        } else {
            // 没有WHERE子句，查找ORDER BY、GROUP BY、HAVING等子句
            int orderByIndex = upperSql.indexOf(" ORDER BY ");
            int groupByIndex = upperSql.indexOf(" GROUP BY ");
            int havingIndex = upperSql.indexOf(" HAVING ");
            int limitIndex = upperSql.indexOf(" LIMIT ");

            // 找到最早出现的子句位置
            int insertIndex = sql.length();
            if (orderByIndex > 0)
                insertIndex = Math.min(insertIndex, orderByIndex);
            if (groupByIndex > 0)
                insertIndex = Math.min(insertIndex, groupByIndex);
            if (havingIndex > 0)
                insertIndex = Math.min(insertIndex, havingIndex);
            if (limitIndex > 0)
                insertIndex = Math.min(insertIndex, limitIndex);

            return sql.substring(0, insertIndex) + " WHERE " + permissionCondition + " " +
                    sql.substring(insertIndex);
        }
    }

    /**
     * 复制MappedStatement
     */
    private MappedStatement copyMappedStatement(MappedStatement mappedStatement, BoundSql newBoundSql) {
        MappedStatement.Builder builder = new MappedStatement.Builder(
                mappedStatement.getConfiguration(),
                mappedStatement.getId(),
                parameter -> newBoundSql,
                mappedStatement.getSqlCommandType());

        builder.resource(mappedStatement.getResource());
        builder.fetchSize(mappedStatement.getFetchSize());
        builder.statementType(mappedStatement.getStatementType());
        builder.keyGenerator(mappedStatement.getKeyGenerator());
        if (mappedStatement.getKeyProperties() != null) {
            builder.keyProperty(String.join(",", mappedStatement.getKeyProperties()));
        }
        builder.timeout(mappedStatement.getTimeout());
        builder.parameterMap(mappedStatement.getParameterMap());
        builder.resultMaps(mappedStatement.getResultMaps());
        builder.resultSetType(mappedStatement.getResultSetType());
        builder.cache(mappedStatement.getCache());
        builder.flushCacheRequired(mappedStatement.isFlushCacheRequired());
        builder.useCache(mappedStatement.isUseCache());

        return builder.build();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 可以从配置文件中读取属性
    }
}
