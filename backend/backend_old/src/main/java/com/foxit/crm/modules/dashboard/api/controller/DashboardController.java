package com.foxit.crm.modules.dashboard.api.controller;

import com.foxit.crm.common.annotation.ApiVersion;
import com.foxit.crm.common.annotation.RateLimit;
import com.foxit.crm.common.exception.Result;
import com.foxit.crm.modules.dashboard.application.dto.request.DashboardConfigRequest;
import com.foxit.crm.modules.dashboard.application.dto.request.DashboardQueryRequest;
import com.foxit.crm.modules.dashboard.application.dto.response.*;
import com.foxit.crm.modules.dashboard.application.service.DashboardService;
import com.foxit.crm.shared.domain.event.OperationLog;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * Dashboard控制器
 * 提供Dashboard相关的REST API接口
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Slf4j
@RestController
@RequestMapping("/dashboard")
@Validated
@RequiredArgsConstructor
@ApiVersion(value = "v1", isDefault = true, description = "Dashboard API v1.0")
@RateLimit(count = 200, time = 60, limitType = RateLimit.LimitType.USER, message = "Dashboard访问过于频繁，请稍后再试")
public class DashboardController {

    private final DashboardService dashboardService;

    /**
     * 获取总览仪表盘数据
     */
    @GetMapping("/overview")
    @PreAuthorize("hasAuthority('dashboard:overview') or hasAuthority('ADMIN')")
    @OperationLog(value = "获取总览仪表盘", operation = "GET_OVERVIEW_DASHBOARD")
    public Result<OverviewDashboardResponse> getOverviewDashboard(
            @RequestParam(required = false) String dataScope,
            @RequestParam(required = false) List<Long> productLineIds,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false, defaultValue = "day") String timeGranularity,
            @RequestParam(required = false) List<String> metricTypes,
            @RequestParam(required = false, defaultValue = "false") Boolean includeComparison,
            @RequestParam(required = false) String comparisonType) {

        DashboardQueryRequest request = DashboardQueryRequest.builder()
                .queryType("overview")
                .dataScope(dataScope != null ? dataScope : "all")
                .productLineIds(productLineIds)
                .startDate(startDate != null ? LocalDate.parse(startDate) : LocalDate.now().minusDays(30))
                .endDate(endDate != null ? LocalDate.parse(endDate) : LocalDate.now())
                .timeGranularity(timeGranularity)
                .metricTypes(metricTypes)
                .includeComparison(includeComparison)
                .comparisonType(comparisonType)
                .build();

        OverviewDashboardResponse response = dashboardService.getOverviewDashboard(request);
        return Result.success(response, "总览仪表盘数据获取成功");
    }

    /**
     * 获取产品线仪表盘数据
     */
    @GetMapping("/product-line")
    @PreAuthorize("hasAuthority('dashboard:product') or hasAuthority('ADMIN')")
    @OperationLog(value = "获取产品线仪表盘", operation = "GET_PRODUCT_LINE_DASHBOARD")
    public Result<ProductLineDashboardResponse> getProductLineDashboard(
            @RequestParam(value = "productLineIds", required = false) List<Long> productLineIds,
            @RequestParam(required = false) String dataScope,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false, defaultValue = "day") String timeGranularity,
            @RequestParam(required = false) List<String> metricTypes,
            @RequestParam(required = false, defaultValue = "false") Boolean includeComparison,
            @RequestParam(required = false) String comparisonType) {

        log.info("接收到产品线仪表盘请求参数: productLineIds={}, startDate={}, endDate={}, timeGranularity={}, includeComparison={}",
                productLineIds, startDate, endDate, timeGranularity, includeComparison);

        if (productLineIds == null || productLineIds.isEmpty()) {
            log.warn("产品线ID参数为空，返回错误");
            return Result.error("产品线ID不能为空");
        }

        DashboardQueryRequest request = DashboardQueryRequest.builder()
                .queryType("product")
                .productLineIds(productLineIds)
                .dataScope(dataScope != null ? dataScope : "all")
                .startDate(startDate != null ? LocalDate.parse(startDate) : LocalDate.now().minusDays(30))
                .endDate(endDate != null ? LocalDate.parse(endDate) : LocalDate.now())
                .timeGranularity(timeGranularity)
                .metricTypes(metricTypes)
                .includeComparison(includeComparison)
                .comparisonType(comparisonType)
                .build();

        ProductLineDashboardResponse response = dashboardService.getProductLineDashboard(request);
        return Result.success(response, "产品线仪表盘数据获取成功");
    }

    /**
     * 获取实时统计数据
     */
    @GetMapping("/realtime")
    @PreAuthorize("hasAuthority('dashboard:overview') or hasAuthority('ADMIN')")
    @OperationLog(value = "获取实时统计数据", operation = "GET_REALTIME_STATS")
    @RateLimit(count = 30, time = 60, limitType = RateLimit.LimitType.IP, message = "实时数据请求过于频繁")
    public Result<RealTimeStatsResponse> getRealTimeStats(
            @RequestParam(required = false, defaultValue = "all") String dataScope) {

        RealTimeStatsResponse response = dashboardService.getRealTimeStats(dataScope);
        return Result.success(response, "实时统计数据获取成功");
    }

    /**
     * 获取核心指标数据
     */
    @GetMapping("/metrics")
    @OperationLog(value = "获取核心指标数据", operation = "GET_CORE_METRICS")
    public Result<CoreMetricsResponse> getCoreMetrics(
            @RequestParam(required = false) String dataScope,
            @RequestParam(required = false) List<Long> productLineIds,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false, defaultValue = "day") String timeGranularity,
            @RequestParam(required = false) List<String> metricTypes) {

        DashboardQueryRequest request = DashboardQueryRequest.builder()
                .queryType("metrics")
                .dataScope(dataScope != null ? dataScope : "all")
                .productLineIds(productLineIds)
                .startDate(startDate != null ? LocalDate.parse(startDate) : LocalDate.now().minusDays(30))
                .endDate(endDate != null ? LocalDate.parse(endDate) : LocalDate.now())
                .timeGranularity(timeGranularity)
                .metricTypes(metricTypes)
                .build();

        CoreMetricsResponse response = dashboardService.getCoreMetrics(request);
        return Result.success(response, "核心指标数据获取成功");
    }

    /**
     * 获取图表数据
     */
    @GetMapping("/charts/{chartType}")
    @OperationLog(value = "获取图表数据", operation = "GET_CHART_DATA")
    public Result<ChartDataResponse> getChartData(
            @PathVariable String chartType,
            @RequestParam(required = false) String dataScope,
            @RequestParam(required = false) List<Long> productLineIds,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false, defaultValue = "day") String timeGranularity) {

        DashboardQueryRequest request = DashboardQueryRequest.builder()
                .queryType("chart")
                .dataScope(dataScope != null ? dataScope : "all")
                .productLineIds(productLineIds)
                .startDate(startDate != null ? LocalDate.parse(startDate) : LocalDate.now().minusDays(30))
                .endDate(endDate != null ? LocalDate.parse(endDate) : LocalDate.now())
                .timeGranularity(timeGranularity)
                .build();

        ChartDataResponse response = dashboardService.getChartData(chartType, request);
        return Result.success(response, "图表数据获取成功");
    }

    /**
     * 刷新Dashboard缓存
     */
    @PostMapping("/cache/refresh")
    @OperationLog(value = "刷新Dashboard缓存", operation = "REFRESH_DASHBOARD_CACHE")
    @PreAuthorize("hasAuthority('DASHBOARD_ADMIN')")
    public Result<String> refreshDashboardCache(
            @RequestParam String dashboardType,
            @RequestParam(required = false, defaultValue = "all") String dataScope) {

        dashboardService.refreshDashboardCache(dashboardType, dataScope);
        return Result.success("Dashboard缓存刷新成功");
    }

    /**
     * 获取Dashboard配置信息
     */
    @GetMapping("/config")
    @PreAuthorize("hasAuthority('dashboard:overview') or hasAuthority('ADMIN')")
    @OperationLog(value = "获取Dashboard配置", operation = "GET_DASHBOARD_CONFIG")
    public Result<DashboardConfigResponse> getDashboardConfig() {
        // 从当前用户上下文获取用户ID（这里简化处理，实际应该从SecurityContext获取）
        Long userId = 1L; // TODO: 从SecurityContext获取当前用户ID

        DashboardConfigResponse response = dashboardService.getDashboardConfig(userId);
        return Result.success(response, "Dashboard配置获取成功");
    }

    /**
     * 更新Dashboard配置
     */
    @PutMapping("/config")
    @OperationLog(value = "更新Dashboard配置", operation = "UPDATE_DASHBOARD_CONFIG", saveParams = true)
    public Result<String> updateDashboardConfig(@Valid @RequestBody DashboardConfigRequest request) {
        // 从当前用户上下文获取用户ID（这里简化处理，实际应该从SecurityContext获取）
        Long userId = 1L; // TODO: 从SecurityContext获取当前用户ID

        dashboardService.updateDashboardConfig(userId, request);
        return Result.success("Dashboard配置更新成功");
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("Dashboard服务运行正常");
    }
}