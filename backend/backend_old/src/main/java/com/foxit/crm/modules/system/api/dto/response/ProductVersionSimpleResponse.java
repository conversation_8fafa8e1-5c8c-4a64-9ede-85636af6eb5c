package com.foxit.crm.modules.system.api.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 产品版本简单响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
public class ProductVersionSimpleResponse {

    /**
     * 版本ID
     */
    private Long id;

    /**
     * 版本号
     */
    private String versionNumber;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 版本状态：1-开发中，2-测试中，3-预发布，4-已发布，5-已废弃
     */
    private Integer status;

    /**
     * 版本状态名称
     */
    private String statusName;

    /**
     * 是否当前版本
     */
    private Boolean isCurrent;

    /**
     * 发布时间
     */
    private LocalDateTime releaseDate;

    /**
     * 下载次数
     */
    private Integer downloadCount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
