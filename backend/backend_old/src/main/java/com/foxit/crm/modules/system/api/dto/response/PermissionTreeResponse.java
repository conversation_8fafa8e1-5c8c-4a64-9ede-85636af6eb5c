package com.foxit.crm.modules.system.api.dto.response;

import lombok.Data;

import java.util.List;

/**
 * 权限树响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
public class PermissionTreeResponse {

    /**
     * 权限ID
     */
    private Long id;

    /**
     * 父权限ID
     */
    private Long parentId;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 权限编码
     */
    private String permissionCode;

    /**
     * 权限类型：1-菜单，2-按钮，3-接口
     */
    private Integer permissionType;

    /**
     * 权限类型描述
     */
    private String permissionTypeText;

    /**
     * 路由路径
     */
    private String path;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 图标
     */
    private String icon;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusText;

    /**
     * 子权限列表
     */
    private List<PermissionTreeResponse> children;

    /**
     * 设置权限类型文本
     */
    public void setPermissionType(Integer permissionType) {
        this.permissionType = permissionType;
        if (permissionType != null) {
            switch (permissionType) {
                case 1:
                    this.permissionTypeText = "菜单";
                    break;
                case 2:
                    this.permissionTypeText = "按钮";
                    break;
                case 3:
                    this.permissionTypeText = "接口";
                    break;
                default:
                    this.permissionTypeText = "未知";
                    break;
            }
        }
    }

    /**
     * 设置状态文本
     */
    public void setStatus(Integer status) {
        this.status = status;
        this.statusText = status != null && status == 1 ? "启用" : "禁用";
    }
}
