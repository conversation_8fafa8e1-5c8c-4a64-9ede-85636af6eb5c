package com.foxit.crm.modules.useranalysis.domain.valueobject;

import lombok.Builder;
import lombok.Getter;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 时间范围值对象
 * 用于表示分析的时间范围
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Builder
@EqualsAndHashCode
public class TimeRange {

    /**
     * 开始日期
     */
    private final LocalDate startDate;

    /**
     * 结束日期
     */
    private final LocalDate endDate;

    /**
     * 时间粒度：DAY, WEEK, MONTH, QUARTER, YEAR
     */
    private final TimeGranularity granularity;

    /**
     * 时间粒度枚举
     */
    public enum TimeGranularity {
        DAY("日"),
        WEEK("周"),
        MONTH("月"),
        QUARTER("季度"),
        YEAR("年");

        private final String description;

        TimeGranularity(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 验证时间范围是否有效
     */
    public boolean isValid() {
        return startDate != null && 
               endDate != null && 
               !startDate.isAfter(endDate) &&
               granularity != null;
    }

    /**
     * 获取时间范围的天数
     */
    public long getDays() {
        if (!isValid()) {
            return 0;
        }
        return ChronoUnit.DAYS.between(startDate, endDate) + 1;
    }

    /**
     * 获取时间范围的周数
     */
    public long getWeeks() {
        return getDays() / 7;
    }

    /**
     * 获取时间范围的月数
     */
    public long getMonths() {
        if (!isValid()) {
            return 0;
        }
        return ChronoUnit.MONTHS.between(startDate, endDate) + 1;
    }

    /**
     * 检查是否包含指定日期
     */
    public boolean contains(LocalDate date) {
        if (!isValid() || date == null) {
            return false;
        }
        return !date.isBefore(startDate) && !date.isAfter(endDate);
    }

    /**
     * 检查是否与另一个时间范围重叠
     */
    public boolean overlaps(TimeRange other) {
        if (!isValid() || !other.isValid()) {
            return false;
        }
        return !endDate.isBefore(other.startDate) && !startDate.isAfter(other.endDate);
    }

    /**
     * 创建最近N天的时间范围
     */
    public static TimeRange ofLastDays(int days) {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(days - 1);
        return TimeRange.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeGranularity.DAY)
                .build();
    }

    /**
     * 创建最近N周的时间范围
     */
    public static TimeRange ofLastWeeks(int weeks) {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusWeeks(weeks);
        return TimeRange.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeGranularity.WEEK)
                .build();
    }

    /**
     * 创建最近N月的时间范围
     */
    public static TimeRange ofLastMonths(int months) {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusMonths(months);
        return TimeRange.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeGranularity.MONTH)
                .build();
    }

    /**
     * 创建当前月的时间范围
     */
    public static TimeRange ofCurrentMonth() {
        LocalDate now = LocalDate.now();
        LocalDate startDate = now.withDayOfMonth(1);
        LocalDate endDate = now.withDayOfMonth(now.lengthOfMonth());
        return TimeRange.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeGranularity.MONTH)
                .build();
    }

    /**
     * 创建当前周的时间范围
     */
    public static TimeRange ofCurrentWeek() {
        LocalDate now = LocalDate.now();
        LocalDate startDate = now.minusDays(now.getDayOfWeek().getValue() - 1);
        LocalDate endDate = startDate.plusDays(6);
        return TimeRange.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeGranularity.WEEK)
                .build();
    }

    /**
     * 创建今天的时间范围
     */
    public static TimeRange ofToday() {
        LocalDate today = LocalDate.now();
        return TimeRange.builder()
                .startDate(today)
                .endDate(today)
                .granularity(TimeGranularity.DAY)
                .build();
    }

    /**
     * 创建自定义时间范围
     */
    public static TimeRange of(LocalDate startDate, LocalDate endDate, TimeGranularity granularity) {
        return TimeRange.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(granularity)
                .build();
    }

    /**
     * 获取时间范围描述
     */
    public String getDescription() {
        if (!isValid()) {
            return "无效时间范围";
        }
        
        return String.format("%s 至 %s (%s)", 
                startDate.toString(), 
                endDate.toString(), 
                granularity.getDescription());
    }

    /**
     * 转换为字符串
     */
    @Override
    public String toString() {
        return getDescription();
    }

    /**
     * 获取开始时间（当天开始）
     */
    public LocalDateTime getStartDateTime() {
        return startDate != null ? startDate.atStartOfDay() : null;
    }

    /**
     * 获取结束时间（当天结束）
     */
    public LocalDateTime getEndDateTime() {
        return endDate != null ? endDate.atTime(23, 59, 59) : null;
    }

    /**
     * 分割时间范围为指定粒度的子范围
     */
    public java.util.List<TimeRange> split() {
        java.util.List<TimeRange> ranges = new java.util.ArrayList<>();
        
        if (!isValid()) {
            return ranges;
        }
        
        LocalDate current = startDate;
        while (!current.isAfter(endDate)) {
            LocalDate rangeEnd = switch (granularity) {
                case DAY -> current;
                case WEEK -> current.plusDays(6).isAfter(endDate) ? endDate : current.plusDays(6);
                case MONTH -> current.plusMonths(1).minusDays(1).isAfter(endDate) ? endDate : current.plusMonths(1).minusDays(1);
                case QUARTER -> current.plusMonths(3).minusDays(1).isAfter(endDate) ? endDate : current.plusMonths(3).minusDays(1);
                case YEAR -> current.plusYears(1).minusDays(1).isAfter(endDate) ? endDate : current.plusYears(1).minusDays(1);
            };
            
            ranges.add(TimeRange.builder()
                    .startDate(current)
                    .endDate(rangeEnd)
                    .granularity(granularity)
                    .build());
            
            current = switch (granularity) {
                case DAY -> current.plusDays(1);
                case WEEK -> current.plusWeeks(1);
                case MONTH -> current.plusMonths(1);
                case QUARTER -> current.plusMonths(3);
                case YEAR -> current.plusYears(1);
            };
        }
        
        return ranges;
    }
}
