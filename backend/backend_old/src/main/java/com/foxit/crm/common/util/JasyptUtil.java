package com.foxit.crm.common.util;

import org.jasypt.encryption.StringEncryptor;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Jasypt 加密解密工具类
 * <p>
 * 提供便捷的加密解密方法，方便开发测试和配置管理。
 * 支持命令行工具式使用和依赖注入方式使用。
 * </p>
 *
 * <AUTHOR> Dev Team
 */
@Component
public class JasyptUtil {

    private static StringEncryptor staticEncryptor;

    /**
     * 注入SpringBean方式的加密器
     * 
     * @param encryptor Jasypt加密器
     */
    @Autowired
    public JasyptUtil(StringEncryptor encryptor) {
        JasyptUtil.staticEncryptor = encryptor;
    }

    /**
     * 使用注入的加密器进行加密
     *
     * @param value 要加密的值
     * @return 加密后的值
     */
    public static String encrypt(String value) {
        return staticEncryptor != null ? staticEncryptor.encrypt(value) : defaultEncrypt(value);
    }

    /**
     * 使用注入的加密器进行解密
     *
     * @param value 要解密的值
     * @return 解密后的值
     */
    public static String decrypt(String value) {
        return staticEncryptor != null ? staticEncryptor.decrypt(value) : defaultDecrypt(value);
    }

    /**
     * 使用默认配置进行加密（适用于命令行工具场景）
     *
     * @param value 要加密的值
     * @return 加密后的值
     */
    public static String defaultEncrypt(String value) {
        return getDefaultEncryptor().encrypt(value);
    }

    /**
     * 使用默认配置进行解密（适用于命令行工具场景）
     *
     * @param value 要解密的值
     * @return 解密后的值
     */
    public static String defaultDecrypt(String value) {
        return getDefaultEncryptor().decrypt(value);
    }

    /**
     * 使用指定密钥进行加密
     *
     * @param value    要加密的值
     * @param password 加密密钥
     * @return 加密后的值
     */
    public static String encrypt(String value, String password) {
        return getCustomEncryptor(password).encrypt(value);
    }

    /**
     * 使用指定密钥进行解密
     *
     * @param value    要解密的值
     * @param password 解密密钥
     * @return 解密后的值
     */
    public static String decrypt(String value, String password) {
        return getCustomEncryptor(password).decrypt(value);
    }

    /**
     * 获取默认加密器
     *
     * @return Jasypt加密器
     */
    private static StringEncryptor getDefaultEncryptor() {
        return getCustomEncryptor("scrm_default_key");
    }

    /**
     * 获取自定义加密器
     *
     * @param password 加密密钥
     * @return Jasypt加密器
     */
    private static StringEncryptor getCustomEncryptor(String password) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();

        config.setPassword(password);
        config.setAlgorithm("PBEWithMD5AndDES");
        config.setKeyObtentionIterations(1000);
        config.setPoolSize(1);
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.NoIvGenerator");
        config.setStringOutputType("base64");

        encryptor.setConfig(config);
        return encryptor;
    }

    /**
     * 命令行工具入口方法
     * 用于快速加密/解密配置值
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 参数: 操作类型(enc/dec) 密钥 值
        if (args.length < 3) {
            System.out.println("使用方法: java -cp ... com.foxit.crm.common.util.JasyptUtil [enc|dec] [密钥] [值]");
            System.out.println("示例加密: java -cp ... com.foxit.crm.common.util.JasyptUtil enc mySecretKey myPassword");
            System.out
                    .println("示例解密: java -cp ... com.foxit.crm.common.util.JasyptUtil dec mySecretKey encrypted_value");
            return;
        }

        String operation = args[0];
        String password = args[1];
        String value = args[2];

        if ("enc".equalsIgnoreCase(operation)) {
            System.out.println("原始值: " + value);
            String encrypted = encrypt(value, password);
            System.out.println("加密后: " + encrypted);
            System.out.println("配置示例: password: ENC(" + encrypted + ")");
        } else if ("dec".equalsIgnoreCase(operation)) {
            System.out.println("加密值: " + value);
            String decrypted = decrypt(value, password);
            System.out.println("解密后: " + decrypted);
        } else {
            System.out.println("不支持的操作: " + operation + "，请使用 enc 或 dec");
        }
    }
}
