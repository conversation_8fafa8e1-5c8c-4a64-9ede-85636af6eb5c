package com.foxit.crm.modules.system.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.modules.system.api.dto.request.RoleCreateRequest;
import com.foxit.crm.modules.system.api.dto.request.RoleUpdateRequest;
import com.foxit.crm.modules.system.api.dto.response.RoleDetailResponse;
import com.foxit.crm.modules.system.api.dto.response.RoleSimpleResponse;
import com.foxit.crm.modules.system.api.dto.response.PageResponse;
import com.foxit.crm.modules.system.application.service.RoleService;
import com.foxit.crm.shared.domain.event.OperationLog;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 角色管理控制器
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@RestController
@RequestMapping("/admin/roles")
@Validated
@RequiredArgsConstructor
@PreAuthorize("hasAuthority('ADMIN')") // 整个控制器只允许管理员访问
public class RoleController {

    private final RoleService roleService;

    /**
     * 创建角色
     */
    @PostMapping
    @OperationLog(value = "创建角色", operation = "CREATE_ROLE", saveParams = true)
    public Result<Long> createRole(@Valid @RequestBody RoleCreateRequest request) {
        Long roleId = roleService.createRole(request);
        return Result.success(roleId, "角色创建成功");
    }

    /**
     * 更新角色
     */
    @PutMapping("/{id}")
    @OperationLog(value = "更新角色", operation = "UPDATE_ROLE", saveParams = true)
    public Result<String> updateRole(@PathVariable Long id, @Valid @RequestBody RoleUpdateRequest request) {
        roleService.updateRole(id, request);
        return Result.success("角色更新成功");
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{id}")
    @OperationLog(value = "删除角色", operation = "DELETE_ROLE")
    public Result<String> deleteRole(@PathVariable Long id) {
        roleService.deleteRole(id);
        return Result.success("角色删除成功");
    }

    /**
     * 获取角色详情
     */
    @GetMapping("/{id}")
    @OperationLog(value = "查看角色详情", operation = "VIEW_ROLE")
    public Result<RoleDetailResponse> getRoleById(@PathVariable Long id) {
        RoleDetailResponse response = roleService.getRoleById(id);
        return Result.success(response);
    }

    /**
     * 分页查询角色列表
     */
    @GetMapping
    @OperationLog(value = "查看角色列表", operation = "LIST_ROLES")
    public Result<PageResponse<RoleDetailResponse>> getRoleList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword) {
        PageResponse<RoleDetailResponse> response = roleService.getRoleList(page, size, keyword);
        return Result.success(response);
    }

    /**
     * 获取所有启用的角色（简单信息）
     */
    @GetMapping("/enabled")
    @OperationLog(value = "获取启用角色列表", operation = "LIST_ENABLED_ROLES")
    public Result<List<RoleSimpleResponse>> getAllEnabledRoles() {
        List<RoleSimpleResponse> response = roleService.getAllEnabledRoles();
        return Result.success(response);
    }

    /**
     * 启用角色
     */
    @PutMapping("/{id}/enable")
    @OperationLog(value = "启用角色", operation = "ENABLE_ROLE")
    public Result<String> enableRole(@PathVariable Long id) {
        roleService.enableRole(id);
        return Result.success("角色启用成功");
    }

    /**
     * 禁用角色
     */
    @PutMapping("/{id}/disable")
    @OperationLog(value = "禁用角色", operation = "DISABLE_ROLE")
    public Result<String> disableRole(@PathVariable Long id) {
        roleService.disableRole(id);
        return Result.success("角色禁用成功");
    }

    /**
     * 为角色分配权限
     */
    @PutMapping("/{id}/permissions")
    @OperationLog(value = "分配角色权限", operation = "ASSIGN_ROLE_PERMISSIONS", saveParams = true)
    public Result<String> assignPermissions(@PathVariable Long id, @RequestBody List<Long> permissionIds) {
        roleService.assignPermissions(id, permissionIds);
        return Result.success("权限分配成功");
    }

    /**
     * 获取角色的权限列表
     */
    @GetMapping("/{id}/permissions")
    @OperationLog(value = "查看角色权限", operation = "VIEW_ROLE_PERMISSIONS")
    public Result<List<Long>> getRolePermissions(@PathVariable Long id) {
        List<Long> permissionIds = roleService.getRolePermissions(id);
        return Result.success(permissionIds);
    }

    /**
     * 移除角色权限
     */
    @DeleteMapping("/{id}/permissions")
    @OperationLog(value = "移除角色权限", operation = "REMOVE_ROLE_PERMISSIONS", saveParams = true)
    public Result<String> removePermissions(@PathVariable Long id, @RequestBody List<Long> permissionIds) {
        roleService.removePermissions(id, permissionIds);
        return Result.success("权限移除成功");
    }

    /**
     * 复制角色
     */
    @PostMapping("/{id}/copy")
    @OperationLog(value = "复制角色", operation = "COPY_ROLE", saveParams = true)
    public Result<Long> copyRole(@PathVariable Long id, @RequestBody Map<String, String> request) {
        String newName = request.get("name");
        Long newRoleId = roleService.copyRole(id, newName);
        return Result.success(newRoleId, "角色复制成功");
    }

    /**
     * 获取角色用户列表
     */
    @GetMapping("/{id}/users")
    @OperationLog(value = "查看角色用户列表", operation = "VIEW_ROLE_USERS")
    public Result<List<Map<String, Object>>> getRoleUsers(@PathVariable Long id) {
        List<Map<String, Object>> users = roleService.getRoleUsers(id);
        return Result.success(users);
    }

    /**
     * 为角色添加用户
     */
    @PostMapping("/{id}/users")
    @OperationLog(value = "为角色添加用户", operation = "ADD_USERS_TO_ROLE", saveParams = true)
    public Result<String> addUsersToRole(@PathVariable Long id, @RequestBody Map<String, List<Long>> request) {
        List<Long> userIds = request.get("userIds");
        roleService.addUsersToRole(id, userIds);
        return Result.success("用户添加成功");
    }

    /**
     * 从角色移除用户
     */
    @DeleteMapping("/{id}/users")
    @OperationLog(value = "从角色移除用户", operation = "REMOVE_USERS_FROM_ROLE", saveParams = true)
    public Result<String> removeUsersFromRole(@PathVariable Long id, @RequestBody Map<String, List<Long>> request) {
        List<Long> userIds = request.get("userIds");
        roleService.removeUsersFromRole(id, userIds);
        return Result.success("用户移除成功");
    }
}
