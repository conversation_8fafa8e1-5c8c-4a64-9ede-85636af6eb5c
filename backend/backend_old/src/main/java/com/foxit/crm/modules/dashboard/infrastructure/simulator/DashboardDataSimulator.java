package com.foxit.crm.modules.dashboard.infrastructure.simulator;

import com.foxit.crm.modules.dashboard.domain.entity.DashboardAggregate;
import com.foxit.crm.modules.dashboard.domain.valueobject.MetricValue;
import com.foxit.crm.modules.dashboard.domain.valueobject.TimeRange;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Dashboard数据模拟器
 * 生成模拟的业务数据用于开发和测试
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Slf4j
@Component
public class DashboardDataSimulator {

    private static final Random RANDOM = new Random();
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter HOUR_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");

    /**
     * 生成核心指标数据
     */
    public Map<String, MetricValue> generateCoreMetrics(TimeRange timeRange, List<Long> productLineIds, String dataScope) {
        log.debug("生成核心指标数据: timeRange={}, productLineIds={}, dataScope={}", timeRange, productLineIds, dataScope);

        Map<String, MetricValue> metrics = new HashMap<>();

        // 基础数据范围（根据产品线数量调整）
        int productLineCount = productLineIds != null ? productLineIds.size() : 5;
        double multiplier = Math.max(1.0, productLineCount / 5.0);

        // 总用户数
        BigDecimal totalUsers = generateRandomValue(1000000, 1500000).multiply(BigDecimal.valueOf(multiplier));
        BigDecimal totalUsersComparison = totalUsers.multiply(BigDecimal.valueOf(0.85 + RANDOM.nextDouble() * 0.3));
        metrics.put("totalUsers", MetricValue.withComparison(totalUsers, "人", totalUsersComparison));

        // 活跃用户数
        BigDecimal activeUsers = totalUsers.multiply(BigDecimal.valueOf(0.6 + RANDOM.nextDouble() * 0.2));
        BigDecimal activeUsersComparison = activeUsers.multiply(BigDecimal.valueOf(0.9 + RANDOM.nextDouble() * 0.2));
        metrics.put("activeUsers", MetricValue.withComparison(activeUsers, "人", activeUsersComparison));

        // 新增用户数
        BigDecimal newUsers = generateRandomValue(15000, 25000).multiply(BigDecimal.valueOf(multiplier));
        BigDecimal newUsersComparison = newUsers.multiply(BigDecimal.valueOf(0.8 + RANDOM.nextDouble() * 0.4));
        metrics.put("newUsers", MetricValue.withComparison(newUsers, "人", newUsersComparison));

        // 总收入
        BigDecimal revenue = generateRandomValue(500000, 800000).multiply(BigDecimal.valueOf(multiplier));
        BigDecimal revenueComparison = revenue.multiply(BigDecimal.valueOf(0.85 + RANDOM.nextDouble() * 0.3));
        metrics.put("revenue", MetricValue.withComparison(revenue, "元", revenueComparison));

        // 下载量
        BigDecimal downloads = generateRandomValue(50000, 80000).multiply(BigDecimal.valueOf(multiplier));
        BigDecimal downloadsComparison = downloads.multiply(BigDecimal.valueOf(0.9 + RANDOM.nextDouble() * 0.2));
        metrics.put("downloads", MetricValue.withComparison(downloads, "次", downloadsComparison));

        // 留存率
        BigDecimal retention = generateRandomValue(65, 85);
        BigDecimal retentionComparison = retention.add(BigDecimal.valueOf(-5 + RANDOM.nextDouble() * 10));
        metrics.put("retention", MetricValue.withComparison(retention, "%", retentionComparison));

        return metrics;
    }

    /**
     * 生成用户增长图表数据
     */
    public DashboardAggregate.ChartData generateUserGrowthData(TimeRange timeRange, List<Long> productLineIds, String dataScope) {
        log.debug("生成用户增长图表数据");

        int days = (int) timeRange.getDaysBetween();
        List<String> categories = generateDateCategories(days);

        // 生成趋势数据
        List<BigDecimal> totalUsersData = generateTrendData(1000000, days, 0.02, 1000);
        List<BigDecimal> activeUsersData = generateTrendData(650000, days, 0.03, 800);
        List<BigDecimal> newUsersData = generateTrendData(20000, days, 0.15, 50);

        List<DashboardAggregate.ChartData.SeriesData> series = Arrays.asList(
            DashboardAggregate.ChartData.SeriesData.builder()
                .name("总用户数")
                .data(totalUsersData)
                .color("#1E88E5")
                .type("line")
                .build(),
            DashboardAggregate.ChartData.SeriesData.builder()
                .name("活跃用户数")
                .data(activeUsersData)
                .color("#43A047")
                .type("line")
                .build(),
            DashboardAggregate.ChartData.SeriesData.builder()
                .name("新增用户数")
                .data(newUsersData)
                .color("#FB8C00")
                .type("line")
                .build()
        );

        return DashboardAggregate.ChartData.builder()
            .chartType("line")
            .title("用户增长趋势")
            .categories(categories)
            .series(series)
            .options(Map.of("smooth", true, "showDataZoom", true))
            .build();
    }

    /**
     * 生成收入趋势图表数据
     */
    public DashboardAggregate.ChartData generateRevenueTrendData(TimeRange timeRange, List<Long> productLineIds, String dataScope) {
        log.debug("生成收入趋势图表数据");

        int days = (int) timeRange.getDaysBetween();
        List<String> categories = generateDateCategories(days);

        List<BigDecimal> revenueData = generateTrendData(500000, days, 0.08, 2000);
        List<BigDecimal> costData = generateTrendData(300000, days, 0.06, 1000);
        List<BigDecimal> profitData = IntStream.range(0, days)
            .mapToObj(i -> revenueData.get(i).subtract(costData.get(i)))
            .collect(Collectors.toList());

        List<DashboardAggregate.ChartData.SeriesData> series = Arrays.asList(
            DashboardAggregate.ChartData.SeriesData.builder()
                .name("收入")
                .data(revenueData)
                .color("#4CAF50")
                .type("area")
                .build(),
            DashboardAggregate.ChartData.SeriesData.builder()
                .name("成本")
                .data(costData)
                .color("#F44336")
                .type("area")
                .build(),
            DashboardAggregate.ChartData.SeriesData.builder()
                .name("利润")
                .data(profitData)
                .color("#2196F3")
                .type("line")
                .build()
        );

        return DashboardAggregate.ChartData.builder()
            .chartType("area")
            .title("收入统计")
            .categories(categories)
            .series(series)
            .options(Map.of("stacked", false))
            .build();
    }

    /**
     * 生成产品线分布图表数据
     */
    public DashboardAggregate.ChartData generateProductLineDistributionData(TimeRange timeRange, String dataScope) {
        log.debug("生成产品线分布图表数据");

        List<String> productLines = Arrays.asList("阅读器", "编辑器", "云服务", "工具类", "内容平台");
        List<String> colors = Arrays.asList("#1E88E5", "#43A047", "#FB8C00", "#8E24AA", "#E53935");

        List<BigDecimal> data = productLines.stream()
            .map(name -> generateRandomValue(100000, 300000))
            .collect(Collectors.toList());

        List<DashboardAggregate.ChartData.SeriesData> series = IntStream.range(0, productLines.size())
            .mapToObj(i -> DashboardAggregate.ChartData.SeriesData.builder()
                .name(productLines.get(i))
                .data(Collections.singletonList(data.get(i)))
                .color(colors.get(i))
                .type("pie")
                .build())
            .collect(Collectors.toList());

        return DashboardAggregate.ChartData.builder()
            .chartType("pie")
            .title("产品线分布")
            .categories(productLines)
            .series(series)
            .options(Map.of("donut", true, "legendPosition", "right"))
            .build();
    }

    /**
     * 生成用户类型分布图表数据
     */
    public DashboardAggregate.ChartData generateUserTypeDistributionData(TimeRange timeRange, List<Long> productLineIds, String dataScope) {
        log.debug("生成用户类型分布图表数据");

        List<String> userTypes = Arrays.asList("免费用户", "付费用户", "企业用户", "试用用户");
        List<BigDecimal> data = userTypes.stream()
            .map(type -> generateRandomValue(50000, 200000))
            .collect(Collectors.toList());

        List<DashboardAggregate.ChartData.SeriesData> series = Collections.singletonList(
            DashboardAggregate.ChartData.SeriesData.builder()
                .name("用户数量")
                .data(data)
                .color("#1890ff")
                .type("bar")
                .build()
        );

        return DashboardAggregate.ChartData.builder()
            .chartType("bar")
            .title("用户类型分布")
            .categories(userTypes)
            .series(series)
            .options(Map.of("horizontal", false))
            .build();
    }

    /**
     * 生成实时在线用户图表数据
     */
    public DashboardAggregate.ChartData generateRealTimeOnlineData(String dataScope) {
        log.debug("生成实时在线用户图表数据");

        List<String> hours = generateHourCategories(24);
        List<BigDecimal> onlineData = generateTrendData(50000, 24, 0.15, 0);

        List<DashboardAggregate.ChartData.SeriesData> series = Collections.singletonList(
            DashboardAggregate.ChartData.SeriesData.builder()
                .name("在线用户")
                .data(onlineData)
                .color("#52c41a")
                .type("line")
                .build()
        );

        return DashboardAggregate.ChartData.builder()
            .chartType("line")
            .title("实时在线用户")
            .categories(hours)
            .series(series)
            .options(Map.of("smooth", true, "showDataZoom", false))
            .build();
    }

    /**
     * 生成实时统计数据
     */
    public DashboardAggregate.RealTimeStats generateRealTimeStats(String dataScope) {
        log.debug("生成实时统计数据");

        return DashboardAggregate.RealTimeStats.builder()
            .currentOnline(generateRandomValue(45000, 65000).longValue())
            .peakOnline(generateRandomValue(75000, 85000).longValue())
            .totalDownloadsToday(generateRandomValue(150000, 200000).longValue())
            .systemLoad(generateRandomValue(60, 80))
            .updateTime(LocalDateTime.now())
            .build();
    }

    // 私有辅助方法
    private BigDecimal generateRandomValue(double min, double max) {
        double value = min + (max - min) * RANDOM.nextDouble();
        return BigDecimal.valueOf(value).setScale(0, RoundingMode.HALF_UP);
    }

    private List<BigDecimal> generateTrendData(double baseValue, int count, double volatility, double trend) {
        List<BigDecimal> data = new ArrayList<>();
        double currentValue = baseValue;

        for (int i = 0; i < count; i++) {
            currentValue += trend;
            double change = currentValue * volatility * (RANDOM.nextDouble() - 0.5) * 2;
            currentValue = Math.max(0, currentValue + change);
            data.add(BigDecimal.valueOf(currentValue).setScale(0, RoundingMode.HALF_UP));
        }

        return data;
    }

    private List<String> generateDateCategories(int days) {
        return IntStream.range(0, days)
            .mapToObj(i -> LocalDateTime.now().minusDays(days - 1 - i).format(DATE_FORMATTER))
            .collect(Collectors.toList());
    }

    private List<String> generateHourCategories(int hours) {
        return IntStream.range(0, hours)
            .mapToObj(i -> String.format("%02d:00", i))
            .collect(Collectors.toList());
    }
}
