package com.foxit.crm.modules.system.domain.model.valueobject;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 版本发布历史值对象
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@NoArgsConstructor
public class VersionReleaseHistory {

    /**
     * 历史记录ID
     */
    private Long id;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 操作类型
     */
    private ActionType actionType;

    /**
     * 原状态
     */
    private Integer fromStatus;

    /**
     * 目标状态
     */
    private Integer toStatus;

    /**
     * 操作原因
     */
    private String actionReason;

    /**
     * 操作人ID
     */
    private Long actionBy;

    /**
     * 操作人姓名
     */
    private String actionByName;

    /**
     * 操作时间
     */
    private LocalDateTime actionTime;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作类型枚举
     */
    public enum ActionType {
        CREATE(1, "创建"),
        UPDATE(2, "更新"),
        RELEASE(3, "发布"),
        ROLLBACK(4, "撤回"),
        DEPRECATE(5, "废弃");

        private final Integer code;
        private final String name;

        ActionType(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static ActionType fromCode(Integer code) {
            for (ActionType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 构造函数
     */
    public VersionReleaseHistory(Long versionId, ActionType actionType, Integer fromStatus, 
                               Integer toStatus, String actionReason, Long actionBy, 
                               String actionByName, String ipAddress, String userAgent) {
        this.versionId = versionId;
        this.actionType = actionType;
        this.fromStatus = fromStatus;
        this.toStatus = toStatus;
        this.actionReason = actionReason;
        this.actionBy = actionBy;
        this.actionByName = actionByName;
        this.actionTime = LocalDateTime.now();
        this.ipAddress = ipAddress;
        this.userAgent = userAgent;
    }

    /**
     * 创建版本创建历史记录
     */
    public static VersionReleaseHistory createHistory(Long versionId, Long actionBy, 
                                                    String actionByName, String ipAddress, 
                                                    String userAgent) {
        return new VersionReleaseHistory(versionId, ActionType.CREATE, null, 1, 
                                       "创建版本", actionBy, actionByName, ipAddress, userAgent);
    }

    /**
     * 创建版本更新历史记录
     */
    public static VersionReleaseHistory updateHistory(Long versionId, Long actionBy, 
                                                    String actionByName, String reason,
                                                    String ipAddress, String userAgent) {
        return new VersionReleaseHistory(versionId, ActionType.UPDATE, null, null, 
                                       reason, actionBy, actionByName, ipAddress, userAgent);
    }

    /**
     * 创建版本发布历史记录
     */
    public static VersionReleaseHistory releaseHistory(Long versionId, Integer fromStatus, 
                                                     Integer toStatus, Long actionBy, 
                                                     String actionByName, String reason,
                                                     String ipAddress, String userAgent) {
        return new VersionReleaseHistory(versionId, ActionType.RELEASE, fromStatus, toStatus, 
                                       reason, actionBy, actionByName, ipAddress, userAgent);
    }

    /**
     * 创建版本废弃历史记录
     */
    public static VersionReleaseHistory deprecateHistory(Long versionId, Integer fromStatus, 
                                                       Long actionBy, String actionByName, 
                                                       String reason, String ipAddress, 
                                                       String userAgent) {
        return new VersionReleaseHistory(versionId, ActionType.DEPRECATE, fromStatus, 5, 
                                       reason, actionBy, actionByName, ipAddress, userAgent);
    }

    /**
     * 获取操作描述
     */
    public String getActionDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append(actionByName).append(" ");
        
        switch (actionType) {
            case CREATE:
                desc.append("创建了版本");
                break;
            case UPDATE:
                desc.append("更新了版本");
                break;
            case RELEASE:
                desc.append("发布了版本");
                if (fromStatus != null && toStatus != null) {
                    desc.append("，状态从 ").append(getStatusName(fromStatus))
                        .append(" 变更为 ").append(getStatusName(toStatus));
                }
                break;
            case ROLLBACK:
                desc.append("撤回了版本");
                break;
            case DEPRECATE:
                desc.append("废弃了版本");
                break;
        }
        
        if (actionReason != null && !actionReason.trim().isEmpty()) {
            desc.append("，原因：").append(actionReason);
        }
        
        return desc.toString();
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        switch (status) {
            case 1: return "开发中";
            case 2: return "测试中";
            case 3: return "预发布";
            case 4: return "已发布";
            case 5: return "已废弃";
            default: return "未知";
        }
    }

    /**
     * 检查是否为重要操作
     */
    public boolean isImportantAction() {
        return actionType == ActionType.RELEASE || actionType == ActionType.DEPRECATE;
    }

    /**
     * 获取操作风险级别
     */
    public String getRiskLevel() {
        switch (actionType) {
            case CREATE:
            case UPDATE:
                return "低";
            case RELEASE:
                return "中";
            case ROLLBACK:
            case DEPRECATE:
                return "高";
            default:
                return "未知";
        }
    }
}
