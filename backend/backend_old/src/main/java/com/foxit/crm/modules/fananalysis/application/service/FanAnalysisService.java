package com.foxit.crm.modules.fananalysis.application.service;

import com.foxit.crm.modules.fananalysis.application.dto.FanAnalysisRequest;
import com.foxit.crm.modules.fananalysis.application.dto.FanAnalysisResponse;
import com.foxit.crm.modules.fananalysis.domain.entity.FanAggregate;
import com.foxit.crm.modules.fananalysis.domain.repository.FanAnalysisRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 粉丝分析应用服务
 * 处理粉丝分析相关的业务逻辑
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FanAnalysisService {

    private final FanAnalysisRepository fanAnalysisRepository;

    /**
     * 获取粉丝总览
     */
    @Cacheable(value = "fan:overview", key = "#request.cacheKey()")
    public FanAnalysisResponse getFanOverview(FanAnalysisRequest request) {
        log.info("获取粉丝总览数据: {}", request);

        try {
            // 验证请求参数
            validateRequest(request);

            // 构建时间范围
            TimeRange timeRange = buildTimeRange(request);

            // 获取数据
            Optional<FanAggregate> aggregateOpt = fanAnalysisRepository
                    .getFanOverview(timeRange, request.getPlatforms(), request.getSource(), 
                                  request.getFanType(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                log.warn("未找到粉丝总览数据: timeRange={}, dataScope={}", timeRange, request.getDataScope());
                return FanAnalysisResponse.empty("未找到粉丝数据");
            }

            FanAggregate aggregate = aggregateOpt.get();

            // 转换为响应对象
            FanAnalysisResponse response = FanAnalysisResponse.success(aggregate);

            log.info("成功获取粉丝总览数据: 粉丝数量={}",
                    aggregate.getFanList() != null ? aggregate.getFanList().size() : 0);

            return response;

        } catch (Exception e) {
            log.error("获取粉丝总览数据失败: {}", e.getMessage(), e);
            return FanAnalysisResponse.error("获取粉丝总览数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取粉丝增长分析
     */
    @Cacheable(value = "fan:growth", key = "#request.cacheKey()")
    public FanAnalysisResponse getFanGrowthAnalysis(FanAnalysisRequest request) {
        log.info("获取粉丝增长分析数据: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<FanAggregate> aggregateOpt = fanAnalysisRepository
                    .getFanGrowthAnalysis(timeRange, request.getPlatforms(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                return FanAnalysisResponse.empty("未找到粉丝增长数据");
            }

            return FanAnalysisResponse.success(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取粉丝增长分析数据失败: {}", e.getMessage(), e);
            return FanAnalysisResponse.error("获取粉丝增长分析数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取粉丝活跃度分析
     */
    @Cacheable(value = "fan:activity", key = "#request.cacheKey()")
    public FanAnalysisResponse getFanActivityAnalysis(FanAnalysisRequest request) {
        log.info("获取粉丝活跃度分析数据: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<FanAggregate> aggregateOpt = fanAnalysisRepository
                    .getFanActivityAnalysis(timeRange, request.getPlatforms(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                return FanAnalysisResponse.empty("未找到粉丝活跃度数据");
            }

            return FanAnalysisResponse.success(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取粉丝活跃度分析数据失败: {}", e.getMessage(), e);
            return FanAnalysisResponse.error("获取粉丝活跃度分析数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取粉丝价值分析
     */
    @Cacheable(value = "fan:value", key = "#request.cacheKey()")
    public FanAnalysisResponse getFanValueAnalysis(FanAnalysisRequest request) {
        log.info("获取粉丝价值分析数据: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<FanAggregate> aggregateOpt = fanAnalysisRepository
                    .getFanValueAnalysis(timeRange, request.getPlatforms(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                return FanAnalysisResponse.empty("未找到粉丝价值分析数据");
            }

            return FanAnalysisResponse.success(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取粉丝价值分析数据失败: {}", e.getMessage(), e);
            return FanAnalysisResponse.error("获取粉丝价值分析数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取平台分析
     */
    @Cacheable(value = "fan:platform", key = "#request.cacheKey()")
    public FanAnalysisResponse getPlatformAnalysis(FanAnalysisRequest request) {
        log.info("获取平台分析数据: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<FanAggregate> aggregateOpt = fanAnalysisRepository
                    .getPlatformAnalysis(timeRange, request.getPlatforms(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                return FanAnalysisResponse.empty("未找到平台分析数据");
            }

            return FanAnalysisResponse.success(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取平台分析数据失败: {}", e.getMessage(), e);
            return FanAnalysisResponse.error("获取平台分析数据失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询粉丝
     */
    public FanAnalysisResponse getFansByPage(FanAnalysisRequest request) {
        log.info("分页查询粉丝: {}", request);

        try {
            validatePageRequest(request);

            // 构建查询参数
            TimeRange timeRange = buildTimeRange(request);
            
            FanAnalysisRepository.FanPageQuery query = new FanAnalysisRepository.FanPageQuery(
                    timeRange,
                    request.getPlatforms(),
                    request.getSource(),
                    request.getFanType(),
                    request.getFanValue(),
                    request.getStatus(),
                    request.getKeyword(),
                    request.getPage() != null ? request.getPage() : 1,
                    request.getPageSize() != null ? request.getPageSize() : 10,
                    request.getSortField(),
                    request.getSortOrder(),
                    request.getDataScope()
            );

            // 执行查询
            FanAnalysisRepository.FanPageResult result = fanAnalysisRepository.getFansByPage(query);

            // 构建聚合根
            FanAggregate aggregate = FanAggregate.builder()
                    .id("fan_page_" + System.currentTimeMillis())
                    .analysisType(FanAggregate.FanAnalysisType.OVERVIEW)
                    .timeRange(timeRange)
                    .platforms(request.getPlatforms())
                    .fanList(result.fans())
                    .dataScope(request.getDataScope())
                    .lastUpdateTime(java.time.LocalDateTime.now())
                    .build();

            return FanAnalysisResponse.successWithPage(aggregate, result.total(), result.page(), result.size());

        } catch (Exception e) {
            log.error("分页查询粉丝失败: {}", e.getMessage(), e);
            return FanAnalysisResponse.error("分页查询粉丝失败: " + e.getMessage());
        }
    }

    /**
     * 获取实时统计
     */
    public FanAnalysisRepository.FanRealTimeStats getRealTimeFanStats(FanAnalysisRequest request) {
        log.info("获取实时粉丝统计: {}", request);

        try {
            return fanAnalysisRepository.getRealTimeFanStats(request.getPlatforms(), request.getDataScope());

        } catch (Exception e) {
            log.error("获取实时粉丝统计失败: {}", e.getMessage(), e);
            return new FanAnalysisRepository.FanRealTimeStats(0L, 0L, 0L, 0L, 0L, 0L, 0L, 0.0, 0.0, 0L);
        }
    }

    /**
     * 获取分析摘要
     */
    public FanAnalysisRepository.FanAnalysisSummary getFanAnalysisSummary(FanAnalysisRequest request) {
        log.info("获取粉丝分析摘要: {}", request);

        try {
            TimeRange timeRange = buildTimeRange(request);
            return fanAnalysisRepository.getFanAnalysisSummary(timeRange, request.getDataScope());

        } catch (Exception e) {
            log.error("获取粉丝分析摘要失败: {}", e.getMessage(), e);
            return new FanAnalysisRepository.FanAnalysisSummary(
                    "error", "ERROR", null, 0L, 0L, 0.0, "无", "获取数据失败"
            );
        }
    }

    /**
     * 验证请求参数
     */
    private void validateRequest(FanAnalysisRequest request) {
        if (request.getDataScope() == null || request.getDataScope().trim().isEmpty()) {
            throw new IllegalArgumentException("数据权限范围不能为空");
        }
    }

    /**
     * 验证分页请求参数
     */
    private void validatePageRequest(FanAnalysisRequest request) {
        validateRequest(request);
        
        if (request.getPage() != null && request.getPage() < 1) {
            throw new IllegalArgumentException("页码必须大于0");
        }
        
        if (request.getPageSize() != null && (request.getPageSize() < 1 || request.getPageSize() > 100)) {
            throw new IllegalArgumentException("页面大小必须在1-100之间");
        }
    }

    /**
     * 构建时间范围
     */
    private TimeRange buildTimeRange(FanAnalysisRequest request) {
        if (request.getStartDate() == null || request.getEndDate() == null) {
            // 如果没有指定时间范围，返回null，表示不进行时间过滤
            return null;
        }

        TimeRange.TimeGranularity granularity = request.getGranularity() != null
                ? request.getGranularity()
                : TimeRange.TimeGranularity.DAY;

        return TimeRange.of(request.getStartDate(), request.getEndDate(), granularity);
    }
}
