package com.foxit.crm.modules.system.application.impl;

import com.foxit.crm.common.exception.BusinessException;
import com.foxit.crm.common.util.PageResult;
import com.foxit.crm.modules.system.api.dto.request.RegisterRequest;
import com.foxit.crm.modules.system.api.dto.response.UserInfoResponse;
import com.foxit.crm.modules.system.application.service.UserManagementService;
import com.foxit.crm.modules.system.domain.model.aggregate.User;
import com.foxit.crm.modules.system.domain.model.valueobject.*;
import com.foxit.crm.modules.system.domain.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 用户管理应用服务实现
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
@RequiredArgsConstructor
public class UserManagementServiceImpl implements UserManagementService {

    private final UserRepository userRepository;

    @Override
    @Transactional
    public Long createUser(RegisterRequest request) {
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(new Username(request.getUsername()))) {
            throw new BusinessException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new BusinessException("邮箱已存在");
        }

        // 创建用户领域对象
        User user = new User(
                null, // UserId会在保存时自动生成
                new Username(request.getUsername()),
                Password.fromRaw(request.getPassword()),
                request.getRealName(),
                new Email(request.getEmail()));

        // 设置其他属性
        user.setPhone(request.getPhone());
        user.setUserType(2); // 默认为普通用户

        // 保存用户
        User savedUser = userRepository.save(user);
        return savedUser.getUserId().getValue();
    }

    @Override
    @Transactional
    public void disableUser(Long userId) {
        User user = userRepository.findById(new UserId(userId))
                .orElseThrow(() -> new BusinessException("用户不存在"));

        user.disable();
        userRepository.save(user);
    }

    @Override
    @Transactional
    public void enableUser(Long userId) {
        User user = userRepository.findById(new UserId(userId))
                .orElseThrow(() -> new BusinessException("用户不存在"));

        user.enable();
        userRepository.save(user);
    }

    @Override
    @Transactional
    public String resetPassword(Long userId) {
        User user = userRepository.findById(new UserId(userId))
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 生成新密码
        String newPassword = generateRandomPassword();
        user.setPassword(Password.fromRaw(newPassword));

        userRepository.save(user);
        return newPassword;
    }

    @Override
    public PageResult<UserInfoResponse> getUserList(int page, int size, String keyword) {
        PageResult<User> userPageResult = userRepository.findByPage(page, size, keyword);
        List<UserInfoResponse> userInfoList = userPageResult.getRecords().stream()
                .map(this::convertToUserInfoResponse)
                .collect(Collectors.toList());

        return PageResult.of(userInfoList, userPageResult.getTotal(),
                userPageResult.getPageNum(), userPageResult.getPageSize());
    }

    @Override
    public UserInfoResponse getUserById(Long userId) {
        User user = userRepository.findById(new UserId(userId))
                .orElseThrow(() -> new BusinessException("用户不存在"));

        return convertToUserInfoResponse(user);
    }

    @Override
    @Transactional
    public void deleteUser(Long userId) {
        User user = userRepository.findById(new UserId(userId))
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 检查是否为管理员
        if (user.isAdmin()) {
            throw new BusinessException("不能删除管理员用户");
        }

        userRepository.deleteById(new UserId(userId));
    }

    /**
     * 生成随机密码
     */
    private String generateRandomPassword() {
        return UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 转换为用户信息响应对象
     */
    private UserInfoResponse convertToUserInfoResponse(User user) {
        UserInfoResponse response = new UserInfoResponse();
        response.setId(user.getUserId().getValue());
        response.setUsername(user.getUsername().getValue());
        response.setRealName(user.getRealName());
        response.setEmail(user.getEmail().getValue());
        response.setPhone(user.getPhone());
        response.setUserType(user.getUserType());
        response.setStatus(user.getStatus());
        response.setCreateTime(user.getCreateTime());
        response.setLastLoginTime(user.getLastLoginTime());
        response.setLastLoginIp(user.getLastLoginIp());
        return response;
    }
}
