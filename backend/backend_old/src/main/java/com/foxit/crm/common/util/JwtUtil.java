package com.foxit.crm.common.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.springframework.util.StringUtils;

import java.util.Date;

/**
 * JWT工具类
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
public final class JwtUtil {

    private JwtUtil() {
        throw new UnsupportedOperationException("Utility class");
    }

    /**
     * 生成JWT Token
     * 
     * @param userId   用户ID
     * @param username 用户名
     * @param secret   密钥
     * @param expire   过期时间(秒)
     * @return JWT Token
     */
    public static String generateToken(Long userId, String username, String secret, Long expire) {
        return generateToken(userId, username, null, secret, expire);
    }

    /**
     * 生成JWT Token（包含用户类型）
     * 
     * @param userId   用户ID
     * @param username 用户名
     * @param userType 用户类型：1-管理员，2-普通用户
     * @param secret   密钥
     * @param expire   过期时间(秒)
     * @return JWT Token
     */
    public static String generateToken(Long userId, String username, Integer userType, String secret, Long expire) {
        if (userId == null || !StringUtils.hasText(username) || !StringUtils.hasText(secret) || expire == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        Date now = new Date();
        Date expireDate = new Date(now.getTime() + expire * 1000);

        return JWT.create()
                .withSubject(username)
                .withClaim("userId", userId)
                .withClaim("username", username)
                .withClaim("userType", userType != null ? userType : 2) // 默认普通用户
                .withIssuedAt(now)
                .withExpiresAt(expireDate)
                .sign(Algorithm.HMAC256(secret));
    }

    /**
     * 验证JWT Token
     * 
     * @param token  JWT Token
     * @param secret 密钥
     * @return 是否有效
     */
    public static boolean validateToken(String token, String secret) {
        if (!StringUtils.hasText(token) || !StringUtils.hasText(secret)) {
            return false;
        }

        try {
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm).build();
            verifier.verify(token);
            return true;
        } catch (JWTVerificationException e) {
            return false;
        }
    }

    /**
     * 验证JWT Token（简化版本，用于兼容）
     *
     * @param token JWT Token
     * @return 是否有效
     */
    public static boolean validateToken(String token) {
        if (!StringUtils.hasText(token)) {
            return false;
        }

        try {
            DecodedJWT jwt = JWT.decode(token);
            Date expiration = jwt.getExpiresAt();
            return expiration != null && expiration.after(new Date());
        } catch (JWTDecodeException e) {
            return false;
        }
    }

    /**
     * 获取Token中的用户ID
     *
     * @param token JWT Token
     * @return 用户ID
     */
    public static Long getUserId(String token) {
        if (!StringUtils.hasText(token)) {
            return null;
        }

        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim("userId").asLong();
        } catch (JWTDecodeException e) {
            return null;
        }
    }

    /**
     * 获取Token中的用户ID（兼容方法）
     *
     * @param token JWT Token
     * @return 用户ID
     */
    public static Long getUserIdFromToken(String token) {
        return getUserId(token);
    }

    /**
     * 获取Token中的用户名
     *
     * @param token JWT Token
     * @return 用户名
     */
    public static String getUsername(String token) {
        if (!StringUtils.hasText(token)) {
            return null;
        }

        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim("username").asString();
        } catch (JWTDecodeException e) {
            return null;
        }
    }

    /**
     * 获取Token中的用户名（兼容方法）
     *
     * @param token JWT Token
     * @return 用户名
     */
    public static String getUsernameFromToken(String token) {
        return getUsername(token);
    }

    /**
     * 获取Token的过期时间
     * 
     * @param token JWT Token
     * @return 过期时间
     */
    public static Date getExpirationDate(String token) {
        if (!StringUtils.hasText(token)) {
            return null;
        }

        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getExpiresAt();
        } catch (JWTDecodeException e) {
            return null;
        }
    }

    /**
     * 获取Token中的用户类型
     *
     * @param token JWT Token
     * @return 用户类型：1-管理员，2-普通用户
     */
    public static Integer getUserType(String token) {
        if (!StringUtils.hasText(token)) {
            return null;
        }

        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim("userType").asInt();
        } catch (JWTDecodeException e) {
            return null;
        }
    }

    /**
     * 判断是否为管理员
     *
     * @param token JWT Token
     * @return 是否为管理员
     */
    public static boolean isAdmin(String token) {
        Integer userType = getUserType(token);
        return userType != null && userType == 1;
    }
}
