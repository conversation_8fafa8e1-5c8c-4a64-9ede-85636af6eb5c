package com.foxit.crm.modules.behavioranalysis.domain.repository;

import com.foxit.crm.modules.behavioranalysis.domain.entity.UserPathAnalysisAggregate;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户路径分析仓储接口
 * 定义用户路径分析数据访问的领域契约
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface UserPathAnalysisRepository {

    /**
     * 获取路径流向分析数据
     *
     * @param timeRange 时间范围
     * @param startNodes 起始节点列表
     * @param endNodes 结束节点列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 用户路径分析聚合根
     */
    Optional<UserPathAnalysisAggregate> getPathFlowAnalysis(TimeRange timeRange, List<String> startNodes, 
                                                           List<String> endNodes, List<Long> productLineIds, String dataScope);

    /**
     * 获取路径统计分析数据
     *
     * @param timeRange 时间范围
     * @param startNodes 起始节点列表
     * @param endNodes 结束节点列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 用户路径分析聚合根
     */
    Optional<UserPathAnalysisAggregate> getPathStatisticsAnalysis(TimeRange timeRange, List<String> startNodes, 
                                                                 List<String> endNodes, List<Long> productLineIds, String dataScope);

    /**
     * 获取异常路径检测数据
     *
     * @param timeRange 时间范围
     * @param startNodes 起始节点列表
     * @param endNodes 结束节点列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 用户路径分析聚合根
     */
    Optional<UserPathAnalysisAggregate> getAnomalyPathDetection(TimeRange timeRange, List<String> startNodes, 
                                                               List<String> endNodes, List<Long> productLineIds, String dataScope);

    /**
     * 获取路径效率分析数据
     *
     * @param timeRange 时间范围
     * @param startNodes 起始节点列表
     * @param endNodes 结束节点列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 用户路径分析聚合根
     */
    Optional<UserPathAnalysisAggregate> getPathEfficiencyAnalysis(TimeRange timeRange, List<String> startNodes, 
                                                                 List<String> endNodes, List<Long> productLineIds, String dataScope);

    /**
     * 获取路径对比分析数据
     *
     * @param timeRange 时间范围
     * @param pathGroups 路径组列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 用户路径分析聚合根
     */
    Optional<UserPathAnalysisAggregate> getPathComparisonAnalysis(TimeRange timeRange, List<PathGroup> pathGroups, 
                                                                 List<Long> productLineIds, String dataScope);

    /**
     * 获取实时路径统计
     *
     * @param startNodes 起始节点列表
     * @param endNodes 结束节点列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 实时统计数据
     */
    PathRealTimeStats getRealTimePathStats(List<String> startNodes, List<String> endNodes, 
                                          List<Long> productLineIds, String dataScope);

    /**
     * 获取路径节点列表
     *
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 路径节点列表
     */
    List<PathNodeInfo> getPathNodeList(List<Long> productLineIds, String dataScope);

    /**
     * 保存用户路径分析结果
     *
     * @param aggregate 用户路径分析聚合根
     * @return 是否保存成功
     */
    boolean saveUserPathAnalysis(UserPathAnalysisAggregate aggregate);

    /**
     * 删除用户路径分析数据
     *
     * @param aggregateId 聚合根ID
     * @return 是否删除成功
     */
    boolean deleteUserPathAnalysis(String aggregateId);

    /**
     * 批量获取用户路径分析数据
     *
     * @param timeRanges 时间范围列表
     * @param startNodes 起始节点列表
     * @param endNodes 结束节点列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 用户路径分析聚合根列表
     */
    List<UserPathAnalysisAggregate> batchGetUserPathAnalysisData(List<TimeRange> timeRanges, List<String> startNodes, 
                                                                List<String> endNodes, List<Long> productLineIds, String dataScope);

    /**
     * 删除过期数据
     *
     * @param beforeTime 过期时间点
     * @return 删除的数据条数
     */
    int deleteExpiredData(LocalDateTime beforeTime);

    /**
     * 路径组
     */
    record PathGroup(
            String groupId,
            String groupName,
            List<String> startNodes,
            List<String> endNodes
    ) {}

    /**
     * 路径实时统计数据
     */
    record PathRealTimeStats(
            Long totalPaths,
            Long uniqueUsers,
            Double avgPathLength,
            Double avgPathTime,
            List<PathStats> topPaths,
            LocalDateTime lastUpdateTime
    ) {}

    /**
     * 路径统计数据
     */
    record PathStats(
            String pathId,
            String pathName,
            Long userCount,
            Double conversionRate,
            Double avgTime,
            String trendDirection
    ) {}

    /**
     * 路径节点信息
     */
    record PathNodeInfo(
            String nodeId,
            String nodeName,
            String nodeType,
            String nodeCategory,
            String description,
            Long productLineId,
            String productLineName,
            Boolean isActive,
            Integer priority
    ) {}

    /**
     * 获取用户路径分析统计摘要
     *
     * @param timeRange 时间范围
     * @param dataScope 数据权限范围
     * @return 统计摘要数据
     */
    UserPathAnalysisSummary getUserPathAnalysisSummary(TimeRange timeRange, String dataScope);

    /**
     * 用户路径分析统计摘要
     */
    record UserPathAnalysisSummary(
            Long totalPaths,
            Long uniqueUsers,
            Long activeNodes,
            Double avgPathLength,
            Double avgConversionRate,
            List<String> topPathPatterns,
            LocalDateTime summaryTime
    ) {}

    /**
     * 获取路径优化建议
     *
     * @param timeRange 时间范围
     * @param startNodes 起始节点列表
     * @param endNodes 结束节点列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 路径优化建议
     */
    List<PathOptimizationSuggestion> getPathOptimizationSuggestions(TimeRange timeRange, List<String> startNodes, 
                                                                   List<String> endNodes, List<Long> productLineIds, String dataScope);

    /**
     * 路径优化建议
     */
    record PathOptimizationSuggestion(
            String suggestionId,
            String suggestionType,
            String title,
            String description,
            String currentPath,
            String suggestedPath,
            Double expectedImprovement,
            String priority,
            List<String> actionItems
    ) {}
}
