package com.foxit.crm.modules.system.domain.model.aggregate;

import com.foxit.crm.modules.system.domain.model.valueobject.UserId;
import com.foxit.crm.modules.system.domain.model.valueobject.Username;
import com.foxit.crm.modules.system.domain.model.valueobject.Password;
import com.foxit.crm.modules.system.domain.model.valueobject.Email;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 用户聚合根
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@NoArgsConstructor
@ToString(exclude = { "password" }) // 排除密码字段，避免日志泄露
public class User {

    /**
     * 用户ID
     */
    private UserId userId;

    /**
     * 用户名
     */
    private Username username;

    /**
     * 密码
     */
    private Password password;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 邮箱
     */
    private Email email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 用户状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 用户类型：1-管理员，2-普通用户
     */
    private Integer userType;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 逻辑删除标志
     */
    private Integer deleted;

    /**
     * 版本号
     */
    private Integer version;

    // 业务构造函数
    public User(UserId userId, Username username, Password password, String realName, Email email) {
        this.userId = userId;
        this.username = username;
        this.password = password;
        this.realName = realName;
        this.email = email;
        this.status = 1; // 默认启用
        this.userType = 2; // 默认普通用户
        this.deleted = 0; // 默认未删除
        this.version = 0; // 默认版本
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 验证密码
     */
    public boolean validatePassword(String rawPassword) {
        return this.password.matches(rawPassword);
    }

    /**
     * 更新最后登录信息
     */
    public void updateLastLogin(String loginIp) {
        this.lastLoginTime = LocalDateTime.now();
        this.lastLoginIp = loginIp;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 启用用户
     */
    public void enable() {
        this.status = 1;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 禁用用户
     */
    public void disable() {
        this.status = 0;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 检查用户是否启用
     */
    public boolean isEnabled() {
        return this.status != null && this.status == 1;
    }

    /**
     * 检查用户是否为管理员
     */
    public boolean isAdmin() {
        return this.userType != null && this.userType == 1;
    }
}
