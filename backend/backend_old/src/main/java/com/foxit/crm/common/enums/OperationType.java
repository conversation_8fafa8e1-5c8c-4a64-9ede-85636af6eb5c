package com.foxit.crm.common.enums;

/**
 * 操作类型枚举
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
public enum OperationType {

    /**
     * 登录
     */
    LOGIN("LOGIN", "登录"),

    /**
     * 注册
     */
    REGISTER("REGISTER", "注册"),

    /**
     * 登出
     */
    LOGOUT("LOGOUT", "登出"),

    /**
     * 查询
     */
    QUERY("QUERY", "查询"),

    /**
     * 新增
     */
    INSERT("INSERT", "新增"),

    /**
     * 修改
     */
    UPDATE("UPDATE", "修改"),

    /**
     * 删除
     */
    DELETE("DELETE", "删除"),

    /**
     * 导入
     */
    IMPORT("IMPORT", "导入"),

    /**
     * 导出
     */
    EXPORT("EXPORT", "导出"),

    /**
     * 上传
     */
    UPLOAD("UPLOAD", "上传"),

    /**
     * 下载
     */
    DOWNLOAD("DOWNLOAD", "下载");

    private final String code;
    private final String description;

    OperationType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取枚举
     */
    public static OperationType getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (OperationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
