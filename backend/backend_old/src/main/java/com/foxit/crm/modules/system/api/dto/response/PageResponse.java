package com.foxit.crm.modules.system.api.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 通用分页响应基类
 * 
 * 用于统一所有分页查询的响应格式，避免在每个XxxListResponse中重复定义分页字段
 * 
 * @param <T> 数据项类型
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageResponse<T> {

    /**
     * 数据列表
     */
    private List<T> items;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 当前页码
     */
    private Integer page;
    
    /**
     * 每页大小
     */
    private Integer size;
    
    /**
     * 总页数
     */
    private Integer totalPages;
    
    /**
     * 构造函数 - 自动计算总页数
     * 
     * @param items 数据列表
     * @param total 总记录数
     * @param page 当前页码
     * @param size 每页大小
     */
    public PageResponse(List<T> items, Long total, Integer page, Integer size) {
        this.items = items;
        this.total = total;
        this.page = page;
        this.size = size;
        this.totalPages = size > 0 ? (int) Math.ceil((double) total / size) : 0;
    }
    
    /**
     * 创建空的分页响应
     * 
     * @param page 当前页码
     * @param size 每页大小
     * @param <T> 数据类型
     * @return 空的分页响应
     */
    public static <T> PageResponse<T> empty(Integer page, Integer size) {
        return new PageResponse<>(List.of(), 0L, page, size);
    }
    
    /**
     * 判断是否有数据
     * 
     * @return 是否有数据
     */
    public boolean hasData() {
        return items != null && !items.isEmpty();
    }
    
    /**
     * 判断是否为第一页
     * 
     * @return 是否为第一页
     */
    public boolean isFirstPage() {
        return page != null && page <= 1;
    }
    
    /**
     * 判断是否为最后一页
     * 
     * @return 是否为最后一页
     */
    public boolean isLastPage() {
        return page != null && totalPages != null && page >= totalPages;
    }
}
