package com.foxit.crm.modules.behavioranalysis.application.service;

import com.foxit.crm.modules.behavioranalysis.application.dto.UserPathAnalysisRequest;
import com.foxit.crm.modules.behavioranalysis.application.dto.UserPathAnalysisResponse;
import com.foxit.crm.modules.behavioranalysis.domain.entity.UserPathAnalysisAggregate;
import com.foxit.crm.modules.behavioranalysis.domain.repository.UserPathAnalysisRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 用户路径分析应用服务
 * 协调用户路径分析的业务流程
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserPathAnalysisService {

    private final UserPathAnalysisRepository userPathAnalysisRepository;

    /**
     * 获取路径流向分析
     */
    public UserPathAnalysisResponse getPathFlowAnalysis(UserPathAnalysisRequest request) {
        log.info("获取路径流向分析: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            // 验证请求参数
            validateUserPathAnalysisRequest(request);
            
            // 获取路径流向分析数据
            Optional<UserPathAnalysisAggregate> aggregateOpt = userPathAnalysisRepository.getPathFlowAnalysis(
                timeRange, request.getStartNodes(), request.getEndNodes(), 
                request.getProductLineIds(), request.getDataScope());
            
            if (aggregateOpt.isEmpty()) {
                log.warn("未找到路径流向分析数据: timeRange={}, startNodes={}, endNodes={}", 
                    timeRange, request.getStartNodes(), request.getEndNodes());
                return UserPathAnalysisResponse.empty();
            }
            
            UserPathAnalysisAggregate aggregate = aggregateOpt.get();
            
            // 转换为响应DTO
            return convertToResponse(aggregate);
            
        } catch (Exception e) {
            log.error("获取路径流向分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取路径流向分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取路径统计分析
     */
    public UserPathAnalysisResponse getPathStatisticsAnalysis(UserPathAnalysisRequest request) {
        log.info("获取路径统计分析: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            // 验证请求参数
            validateUserPathAnalysisRequest(request);
            
            // 获取路径统计分析数据
            Optional<UserPathAnalysisAggregate> aggregateOpt = userPathAnalysisRepository.getPathStatisticsAnalysis(
                timeRange, request.getStartNodes(), request.getEndNodes(), 
                request.getProductLineIds(), request.getDataScope());
            
            if (aggregateOpt.isEmpty()) {
                log.warn("未找到路径统计分析数据: timeRange={}, startNodes={}, endNodes={}", 
                    timeRange, request.getStartNodes(), request.getEndNodes());
                return UserPathAnalysisResponse.empty();
            }
            
            UserPathAnalysisAggregate aggregate = aggregateOpt.get();
            
            // 转换为响应DTO
            return convertToResponse(aggregate);
            
        } catch (Exception e) {
            log.error("获取路径统计分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取路径统计分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取异常路径检测
     */
    public UserPathAnalysisResponse getAnomalyPathDetection(UserPathAnalysisRequest request) {
        log.info("获取异常路径检测: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            // 验证请求参数
            validateUserPathAnalysisRequest(request);
            
            // 获取异常路径检测数据
            Optional<UserPathAnalysisAggregate> aggregateOpt = userPathAnalysisRepository.getAnomalyPathDetection(
                timeRange, request.getStartNodes(), request.getEndNodes(), 
                request.getProductLineIds(), request.getDataScope());
            
            if (aggregateOpt.isEmpty()) {
                log.warn("未找到异常路径检测数据: timeRange={}, startNodes={}, endNodes={}", 
                    timeRange, request.getStartNodes(), request.getEndNodes());
                return UserPathAnalysisResponse.empty();
            }
            
            UserPathAnalysisAggregate aggregate = aggregateOpt.get();
            
            // 转换为响应DTO
            return convertToResponse(aggregate);
            
        } catch (Exception e) {
            log.error("获取异常路径检测失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取异常路径检测失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取路径效率分析
     */
    public UserPathAnalysisResponse getPathEfficiencyAnalysis(UserPathAnalysisRequest request) {
        log.info("获取路径效率分析: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            // 验证请求参数
            validateUserPathAnalysisRequest(request);
            
            // 获取路径效率分析数据
            Optional<UserPathAnalysisAggregate> aggregateOpt = userPathAnalysisRepository.getPathEfficiencyAnalysis(
                timeRange, request.getStartNodes(), request.getEndNodes(), 
                request.getProductLineIds(), request.getDataScope());
            
            if (aggregateOpt.isEmpty()) {
                log.warn("未找到路径效率分析数据: timeRange={}, startNodes={}, endNodes={}", 
                    timeRange, request.getStartNodes(), request.getEndNodes());
                return UserPathAnalysisResponse.empty();
            }
            
            UserPathAnalysisAggregate aggregate = aggregateOpt.get();
            
            // 转换为响应DTO
            return convertToResponse(aggregate);
            
        } catch (Exception e) {
            log.error("获取路径效率分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取路径效率分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取路径对比分析
     */
    public UserPathAnalysisResponse getPathComparisonAnalysis(UserPathAnalysisRequest request) {
        log.info("获取路径对比分析: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            // 验证请求参数
            validateUserPathAnalysisRequest(request);
            
            // 获取路径对比分析数据
            Optional<UserPathAnalysisAggregate> aggregateOpt = userPathAnalysisRepository.getPathComparisonAnalysis(
                timeRange, request.getPathGroups(), request.getProductLineIds(), request.getDataScope());
            
            if (aggregateOpt.isEmpty()) {
                log.warn("未找到路径对比分析数据: timeRange={}, pathGroups={}", timeRange, request.getPathGroups());
                return UserPathAnalysisResponse.empty();
            }
            
            UserPathAnalysisAggregate aggregate = aggregateOpt.get();
            
            // 转换为响应DTO
            return convertToResponse(aggregate);
            
        } catch (Exception e) {
            log.error("获取路径对比分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取路径对比分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取路径节点列表
     */
    public List<UserPathAnalysisRepository.PathNodeInfo> getPathNodeList(List<Long> productLineIds, String dataScope) {
        log.info("获取路径节点列表: productLineIds={}, dataScope={}", productLineIds, dataScope);
        
        try {
            return userPathAnalysisRepository.getPathNodeList(productLineIds, dataScope);
        } catch (Exception e) {
            log.error("获取路径节点列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取路径节点列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取实时路径统计
     */
    public UserPathAnalysisRepository.PathRealTimeStats getRealTimePathStats(List<String> startNodes, List<String> endNodes,
                                                                            List<Long> productLineIds, String dataScope) {
        log.info("获取实时路径统计: startNodes={}, endNodes={}, productLineIds={}, dataScope={}", 
                startNodes, endNodes, productLineIds, dataScope);
        
        try {
            return userPathAnalysisRepository.getRealTimePathStats(startNodes, endNodes, productLineIds, dataScope);
        } catch (Exception e) {
            log.error("获取实时路径统计失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取实时路径统计失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取用户路径分析统计摘要
     */
    public UserPathAnalysisRepository.UserPathAnalysisSummary getUserPathAnalysisSummary(UserPathAnalysisRequest request) {
        log.info("获取用户路径分析统计摘要: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            return userPathAnalysisRepository.getUserPathAnalysisSummary(timeRange, request.getDataScope());
        } catch (Exception e) {
            log.error("获取用户路径分析统计摘要失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取用户路径分析统计摘要失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取路径优化建议
     */
    public List<UserPathAnalysisRepository.PathOptimizationSuggestion> getPathOptimizationSuggestions(UserPathAnalysisRequest request) {
        log.info("获取路径优化建议: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            return userPathAnalysisRepository.getPathOptimizationSuggestions(
                timeRange, request.getStartNodes(), request.getEndNodes(), 
                request.getProductLineIds(), request.getDataScope());
        } catch (Exception e) {
            log.error("获取路径优化建议失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取路径优化建议失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证用户路径分析请求参数
     */
    private void validateUserPathAnalysisRequest(UserPathAnalysisRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("用户路径分析请求不能为空");
        }
        
        if (request.getStartDate() == null || request.getEndDate() == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }
        
        if (request.getStartDate().isAfter(request.getEndDate())) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }
        
        if (request.getDataScope() == null || request.getDataScope().trim().isEmpty()) {
            throw new IllegalArgumentException("数据权限范围不能为空");
        }
        
        if (request.getStartNodes() == null || request.getStartNodes().isEmpty()) {
            throw new IllegalArgumentException("起始节点不能为空");
        }
    }

    /**
     * 转换聚合根为响应DTO
     */
    private UserPathAnalysisResponse convertToResponse(UserPathAnalysisAggregate aggregate) {
        // TODO: 实现具体的转换逻辑
        return UserPathAnalysisResponse.builder()
                .analysisId(aggregate.getId())
                .analysisType(aggregate.getAnalysisType().name())
                .timeRange(aggregate.getTimeRange())
                .coreMetrics(aggregate.getCoreMetrics())
                .pathFlowData(aggregate.getPathFlowData())
                .pathStatisticsData(aggregate.getPathStatisticsData())
                .anomalyPathData(aggregate.getAnomalyPathData())
                .pathEfficiencyData(aggregate.getPathEfficiencyData())
                .pathComparisonData(aggregate.getPathComparisonData())
                .lastUpdateTime(aggregate.getLastUpdateTime())
                .build();
    }
}
