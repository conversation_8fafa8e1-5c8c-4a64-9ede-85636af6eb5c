package com.foxit.crm.modules.system.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.shared.domain.event.OperationLog;
import com.foxit.crm.modules.system.api.dto.response.ProductLineSimpleResponse;
import com.foxit.crm.modules.system.application.service.ProductLineService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公共产品线控制器 - 提供普通用户可访问的产品线接口
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@RestController
@RequestMapping("/system/product-lines")
@Validated
@RequiredArgsConstructor
public class PublicProductLineController {

    private final ProductLineService productLineService;

    /**
     * 获取所有启用的产品线（简单信息）
     * 普通用户可访问，用于前端选择器等场景
     */
    @GetMapping("/enabled")
    @OperationLog(value = "获取启用产品线列表", operation = "LIST_ENABLED_PRODUCT_LINES")
    public Result<List<ProductLineSimpleResponse>> getAllEnabledProductLines() {
        List<ProductLineSimpleResponse> response = productLineService.getAllEnabledProductLines();
        return Result.success(response);
    }

    /**
     * 根据产品线类型获取产品线列表
     * 普通用户可访问
     */
    @GetMapping("/type/{type}")
    @OperationLog(value = "按类型获取产品线列表", operation = "GET_PRODUCT_LINES_BY_TYPE")
    public Result<List<ProductLineSimpleResponse>> getProductLinesByType(@PathVariable Integer type) {
        List<ProductLineSimpleResponse> response = productLineService.getProductLinesByType(type);
        return Result.success(response);
    }

    /**
     * 获取当前用户有权限的产品线列表
     * 普通用户可访问，根据用户权限返回可访问的产品线
     */
    @GetMapping("/accessible")
    @OperationLog(value = "获取用户可访问产品线列表", operation = "GET_ACCESSIBLE_PRODUCT_LINES")
    public Result<List<ProductLineSimpleResponse>> getAccessibleProductLines() {
        // TODO: 从SecurityContext获取当前用户ID
        Long currentUserId = 1L; // 临时硬编码，实际应该从认证上下文获取

        List<ProductLineSimpleResponse> response = productLineService.getProductLinesByUserId(currentUserId);
        return Result.success(response);
    }
}
