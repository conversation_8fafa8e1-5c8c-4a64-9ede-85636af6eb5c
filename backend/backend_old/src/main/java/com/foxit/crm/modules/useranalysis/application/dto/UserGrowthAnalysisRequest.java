package com.foxit.crm.modules.useranalysis.application.dto;

import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 用户增长分析请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@Builder
@EqualsAndHashCode
public class UserGrowthAnalysisRequest {

    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @NotNull(message = "结束日期不能为空")
    private LocalDate endDate;

    /**
     * 时间粒度
     */
    private TimeRange.TimeGranularity granularity;

    /**
     * 产品线ID列表
     */
    private List<Long> productLineIds;

    /**
     * 分析类型
     */
    private String analysisType;

    /**
     * 数据权限范围
     */
    @NotNull(message = "数据权限范围不能为空")
    private String dataScope;

    /**
     * 用户ID（用于权限控制）
     */
    private Long userId;

    /**
     * 是否包含历史对比
     */
    private Boolean includeComparison;

    /**
     * 是否包含详细数据
     */
    private Boolean includeDetails;

    /**
     * 留存分析周期（1,3,7,14,30天）
     */
    private List<Integer> retentionPeriods;

    /**
     * 用户来源类型过滤
     */
    private List<String> sourceTypes;

    /**
     * 导出格式
     */
    private String exportFormat;

    /**
     * 生成缓存键
     */
    public String cacheKey() {
        return String.format("userGrowth_%s_%s_%s_%s_%s",
                startDate,
                endDate,
                granularity != null ? granularity.name() : "DAY",
                productLineIds != null ? String.join(",", productLineIds.stream().map(String::valueOf).toList())
                        : "all",
                dataScope);
    }

    /**
     * 创建最近30天的请求
     */
    public static UserGrowthAnalysisRequest ofLast30Days(String dataScope) {
        return UserGrowthAnalysisRequest.builder()
                .startDate(LocalDate.now().minusDays(29))
                .endDate(LocalDate.now())
                .granularity(TimeRange.TimeGranularity.DAY)
                .dataScope(dataScope)
                .includeComparison(true)
                .includeDetails(false)
                .retentionPeriods(List.of(1, 3, 7, 14, 30))
                .build();
    }

    /**
     * 创建最近7天的请求
     */
    public static UserGrowthAnalysisRequest ofLast7Days(String dataScope) {
        return UserGrowthAnalysisRequest.builder()
                .startDate(LocalDate.now().minusDays(6))
                .endDate(LocalDate.now())
                .granularity(TimeRange.TimeGranularity.DAY)
                .dataScope(dataScope)
                .includeComparison(true)
                .includeDetails(false)
                .retentionPeriods(List.of(1, 3, 7))
                .build();
    }

    /**
     * 创建本月的请求
     */
    public static UserGrowthAnalysisRequest ofCurrentMonth(String dataScope) {
        LocalDate now = LocalDate.now();
        return UserGrowthAnalysisRequest.builder()
                .startDate(now.withDayOfMonth(1))
                .endDate(now)
                .granularity(TimeRange.TimeGranularity.DAY)
                .dataScope(dataScope)
                .includeComparison(true)
                .includeDetails(false)
                .retentionPeriods(List.of(1, 7, 30))
                .build();
    }

    /**
     * 验证请求参数
     */
    public boolean isValid() {
        if (startDate == null || endDate == null || dataScope == null) {
            return false;
        }

        if (startDate.isAfter(endDate)) {
            return false;
        }

        // 时间范围不能超过1年
        if (startDate.isBefore(endDate.minusYears(1))) {
            return false;
        }

        return true;
    }

    /**
     * 获取时间范围天数
     */
    public long getDaysBetween() {
        if (startDate == null || endDate == null) {
            return 0;
        }
        return java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate) + 1;
    }

    /**
     * 是否为短期分析（7天以内）
     */
    public boolean isShortTermAnalysis() {
        return getDaysBetween() <= 7;
    }

    /**
     * 是否为中期分析（8-30天）
     */
    public boolean isMediumTermAnalysis() {
        long days = getDaysBetween();
        return days > 7 && days <= 30;
    }

    /**
     * 是否为长期分析（30天以上）
     */
    public boolean isLongTermAnalysis() {
        return getDaysBetween() > 30;
    }

    /**
     * 获取建议的时间粒度
     */
    public TimeRange.TimeGranularity getSuggestedGranularity() {
        if (granularity != null) {
            return granularity;
        }

        long days = getDaysBetween();
        if (days <= 7) {
            return TimeRange.TimeGranularity.DAY;
        } else if (days <= 30) {
            return TimeRange.TimeGranularity.DAY;
        } else if (days <= 90) {
            return TimeRange.TimeGranularity.WEEK;
        } else {
            return TimeRange.TimeGranularity.MONTH;
        }
    }

    /**
     * 获取适用的留存周期
     */
    public List<Integer> getApplicableRetentionPeriods() {
        if (retentionPeriods != null && !retentionPeriods.isEmpty()) {
            return retentionPeriods;
        }

        long days = getDaysBetween();
        if (days <= 7) {
            return List.of(1, 3, 7);
        } else if (days <= 30) {
            return List.of(1, 3, 7, 14, 30);
        } else {
            return List.of(1, 7, 14, 30);
        }
    }

    /**
     * 转换为字符串表示
     */
    @Override
    public String toString() {
        return String.format(
                "UserGrowthAnalysisRequest{startDate=%s, endDate=%s, granularity=%s, productLineIds=%s, analysisType='%s', dataScope='%s'}",
                startDate, endDate, granularity, productLineIds, analysisType, dataScope);
    }
}
