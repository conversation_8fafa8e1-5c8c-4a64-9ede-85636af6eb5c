package com.foxit.crm.modules.dashboard.application.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;
import java.util.Map;

/**
 * Dashboard配置响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DashboardConfigResponse {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 默认时间范围
     */
    private String defaultTimeRange;

    /**
     * 默认时间粒度
     */
    private String defaultTimeGranularity;

    /**
     * 可访问的产品线列表
     */
    private List<ProductLineAccess> accessibleProductLines;

    /**
     * 默认显示的指标列表
     */
    private List<String> defaultMetrics;

    /**
     * 图表配置
     */
    private Map<String, ChartConfig> chartConfigs;

    /**
     * 刷新间隔（分钟）
     */
    private Integer refreshInterval;

    /**
     * 是否启用实时更新
     */
    private Boolean enableRealTimeUpdate;

    /**
     * 数据权限范围
     */
    private String dataScope;

    /**
     * 产品线访问权限内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ProductLineAccess {
        /**
         * 产品线ID
         */
        private Long productLineId;

        /**
         * 产品线名称
         */
        private String productLineName;

        /**
         * 访问权限：read-只读, write-读写, admin-管理
         */
        private String accessLevel;

        /**
         * 是否默认选中
         */
        private Boolean defaultSelected;
    }

    /**
     * 图表配置内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ChartConfig {
        /**
         * 图表类型
         */
        private String chartType;

        /**
         * 是否显示
         */
        private Boolean visible;

        /**
         * 显示顺序
         */
        private Integer order;

        /**
         * 图表高度
         */
        private Integer height;

        /**
         * 自定义配置
         */
        private Map<String, Object> customOptions;
    }
}
