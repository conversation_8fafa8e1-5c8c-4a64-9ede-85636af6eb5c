package com.foxit.crm.modules.system.infrastructure.persistence.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 产品版本持久化对象
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product_version")
public class ProductVersionPO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 产品线ID
     */
    @TableField("product_line_id")
    private Long productLineId;

    /**
     * 版本号（如：1.0.0）
     */
    @TableField("version_number")
    private String versionNumber;

    /**
     * 版本名称
     */
    @TableField("version_name")
    private String versionName;

    /**
     * 版本描述
     */
    @TableField("description")
    private String description;

    /**
     * 发布说明
     */
    @TableField("release_notes")
    private String releaseNotes;

    /**
     * 版本类型：1-主版本，2-次版本，3-修订版，4-预发布版
     */
    @TableField("version_type")
    private Integer versionType;

    /**
     * 版本状态：1-开发中，2-测试中，3-预发布，4-已发布，5-已废弃
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否当前版本：0-否，1-是
     */
    @TableField("is_current")
    private Integer isCurrent;

    /**
     * 发布时间
     */
    @TableField("release_date")
    private LocalDateTime releaseDate;

    /**
     * 计划发布时间
     */
    @TableField("planned_date")
    private LocalDateTime plannedDate;

    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 下载地址
     */
    @TableField("download_url")
    private String downloadUrl;

    /**
     * 下载次数
     */
    @TableField("download_count")
    private Integer downloadCount;

    /**
     * 支持平台列表（JSON格式）
     */
    @TableField("platforms")
    private String platforms;

    /**
     * 新功能列表（JSON格式）
     */
    @TableField("features")
    private String features;

    /**
     * 修复问题列表（JSON格式）
     */
    @TableField("bug_fixes")
    private String bugFixes;

    /**
     * 破坏性变更列表（JSON格式）
     */
    @TableField("breaking_changes")
    private String breakingChanges;

    /**
     * 依赖信息（JSON格式）
     */
    @TableField("dependencies")
    private String dependencies;

    /**
     * 系统要求（JSON格式）
     */
    @TableField("system_requirements")
    private String systemRequirements;

    /**
     * MD5校验值
     */
    @TableField("checksum_md5")
    private String checksumMd5;

    /**
     * SHA256校验值
     */
    @TableField("checksum_sha256")
    private String checksumSha256;

    /**
     * 创建人ID
     */
    @TableField("created_by")
    private Long createdBy;

    /**
     * 创建人姓名
     */
    @TableField("created_by_name")
    private String createdByName;

    /**
     * 审批人ID
     */
    @TableField("approved_by")
    private Long approvedBy;

    /**
     * 审批人姓名
     */
    @TableField("approved_by_name")
    private String approvedByName;

    /**
     * 审批时间
     */
    @TableField("approved_at")
    private LocalDateTime approvedAt;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 更新人ID
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 逻辑删除标志：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 版本号（乐观锁）
     */
    @Version
    @TableField("version")
    private Integer version;
}
