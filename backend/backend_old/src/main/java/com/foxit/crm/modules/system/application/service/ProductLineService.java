package com.foxit.crm.modules.system.application.service;

import com.foxit.crm.modules.system.api.dto.request.ProductLineCreateRequest;
import com.foxit.crm.modules.system.api.dto.request.ProductLineUpdateRequest;
import com.foxit.crm.modules.system.api.dto.response.ProductLineDetailResponse;
import com.foxit.crm.modules.system.api.dto.response.ProductLineListResponse;
import com.foxit.crm.modules.system.api.dto.response.ProductLineSimpleResponse;

import java.util.List;

/**
 * 产品线应用服务接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface ProductLineService {

    /**
     * 创建产品线
     */
    Long createProductLine(ProductLineCreateRequest request);

    /**
     * 更新产品线
     */
    void updateProductLine(Long id, ProductLineUpdateRequest request);

    /**
     * 删除产品线
     */
    void deleteProductLine(Long id);

    /**
     * 根据ID获取产品线详情
     */
    ProductLineDetailResponse getProductLineById(Long id);

    /**
     * 分页查询产品线列表
     */
    ProductLineListResponse getProductLineList(int page, int size, String keyword, Integer type, Integer status);

    /**
     * 获取所有启用的产品线（简单信息）
     */
    List<ProductLineSimpleResponse> getAllEnabledProductLines();

    /**
     * 根据产品线类型获取产品线列表
     */
    List<ProductLineSimpleResponse> getProductLinesByType(Integer type);

    /**
     * 根据负责人ID获取产品线列表
     */
    List<ProductLineSimpleResponse> getProductLinesByOwnerId(Long ownerId);

    /**
     * 根据用户ID获取有权限的产品线列表
     */
    List<ProductLineSimpleResponse> getProductLinesByUserId(Long userId);

    /**
     * 启用产品线
     */
    void enableProductLine(Long id);

    /**
     * 禁用产品线
     */
    void disableProductLine(Long id);

    /**
     * 批量启用产品线
     */
    void enableProductLinesBatch(List<Long> ids);

    /**
     * 批量禁用产品线
     */
    void disableProductLinesBatch(List<Long> ids);

    /**
     * 更新产品线数据源配置
     */
    void updateDataSourceConfig(Long id, String dataSourceConfig);

    /**
     * 获取产品线数据源配置
     */
    String getDataSourceConfig(Long id);

    /**
     * 检查产品线编码是否可用
     */
    boolean isCodeAvailable(String code);

    /**
     * 检查产品线编码是否可用（排除指定ID）
     */
    boolean isCodeAvailable(String code, Long excludeId);

    /**
     * 检查产品线名称是否可用
     */
    boolean isNameAvailable(String name);

    /**
     * 检查产品线名称是否可用（排除指定ID）
     */
    boolean isNameAvailable(String name, Long excludeId);

    /**
     * 获取产品线类型统计信息
     */
    List<ProductLineDetailResponse> getProductLineTypeStatistics();
}
