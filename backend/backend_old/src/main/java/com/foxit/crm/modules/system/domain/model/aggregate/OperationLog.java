package com.foxit.crm.modules.system.domain.model.aggregate;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 系统操作日志聚合根
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@NoArgsConstructor
public class OperationLog {

    /**
     * 日志ID
     */
    private Long id;

    /**
     * 操作标题
     */
    private String title;

    /**
     * 操作类型
     */
    private String operation;

    /**
     * 请求方法
     */
    private String method;

    /**
     * 请求方式
     */
    private String requestMethod;

    /**
     * 请求URL
     */
    private String requestUrl;

    /**
     * 请求参数
     */
    private String requestParams;

    /**
     * 响应结果
     */
    private String responseResult;

    /**
     * 操作用户ID
     */
    private Long userId;

    /**
     * 操作用户名
     */
    private String username;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 操作状态：0-失败，1-成功
     */
    private Integer status;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 执行时长（毫秒）
     */
    private Long executionTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 操作状态枚举
     */
    public enum OperationStatus {
        FAILED(0, "失败"),
        SUCCESS(1, "成功");

        private final Integer code;
        private final String name;

        OperationStatus(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static OperationStatus fromCode(Integer code) {
            for (OperationStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 构造函数
     */
    public OperationLog(String title, String operation, String method, String requestMethod, 
                       String requestUrl, String requestParams, Long userId, String username, 
                       String clientIp, String userAgent, LocalDateTime operationTime) {
        this.title = title;
        this.operation = operation;
        this.method = method;
        this.requestMethod = requestMethod;
        this.requestUrl = requestUrl;
        this.requestParams = requestParams;
        this.userId = userId;
        this.username = username;
        this.clientIp = clientIp;
        this.userAgent = userAgent;
        this.operationTime = operationTime;
        this.status = OperationStatus.SUCCESS.getCode(); // 默认成功
    }

    /**
     * 标记操作成功
     */
    public void markSuccess(String responseResult, Long executionTime) {
        this.status = OperationStatus.SUCCESS.getCode();
        this.responseResult = responseResult;
        this.executionTime = executionTime;
        this.errorMessage = null;
    }

    /**
     * 标记操作失败
     */
    public void markFailed(String errorMessage, Long executionTime) {
        this.status = OperationStatus.FAILED.getCode();
        this.errorMessage = errorMessage;
        this.executionTime = executionTime;
        this.responseResult = null;
    }

    /**
     * 检查操作是否成功
     */
    public boolean isSuccess() {
        return OperationStatus.SUCCESS.getCode().equals(this.status);
    }

    /**
     * 检查操作是否失败
     */
    public boolean isFailed() {
        return OperationStatus.FAILED.getCode().equals(this.status);
    }

    /**
     * 获取操作状态名称
     */
    public String getStatusName() {
        OperationStatus operationStatus = OperationStatus.fromCode(this.status);
        return operationStatus != null ? operationStatus.getName() : "未知";
    }

    /**
     * 检查是否为敏感操作
     */
    public boolean isSensitiveOperation() {
        if (this.operation == null) {
            return false;
        }
        
        // 定义敏感操作类型
        String[] sensitiveOperations = {
            "DELETE", "CREATE", "UPDATE", "LOGIN", "LOGOUT", 
            "ASSIGN", "REMOVE", "ENABLE", "DISABLE", "RESET"
        };
        
        String upperOperation = this.operation.toUpperCase();
        for (String sensitive : sensitiveOperations) {
            if (upperOperation.contains(sensitive)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查执行时间是否过长
     */
    public boolean isSlowOperation() {
        return this.executionTime != null && this.executionTime > 5000; // 超过5秒认为是慢操作
    }
}
