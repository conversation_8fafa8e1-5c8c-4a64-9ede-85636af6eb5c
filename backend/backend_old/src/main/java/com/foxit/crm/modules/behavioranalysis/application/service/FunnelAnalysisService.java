package com.foxit.crm.modules.behavioranalysis.application.service;

import com.foxit.crm.modules.behavioranalysis.application.dto.FunnelAnalysisRequest;
import com.foxit.crm.modules.behavioranalysis.application.dto.FunnelAnalysisResponse;
import com.foxit.crm.modules.behavioranalysis.domain.entity.FunnelAnalysisAggregate;
import com.foxit.crm.modules.behavioranalysis.domain.repository.FunnelAnalysisRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 漏斗分析应用服务
 * 协调漏斗分析的业务流程
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FunnelAnalysisService {

    private final FunnelAnalysisRepository funnelAnalysisRepository;

    /**
     * 获取漏斗转化分析
     */
    public FunnelAnalysisResponse getFunnelConversionAnalysis(FunnelAnalysisRequest request) {
        log.info("获取漏斗转化分析: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            // 验证请求参数
            validateFunnelAnalysisRequest(request);
            
            // 获取漏斗转化分析数据
            Optional<FunnelAnalysisAggregate> aggregateOpt = funnelAnalysisRepository.getFunnelConversionAnalysis(
                timeRange, request.getFunnelSteps(), request.getProductLineIds(), request.getDataScope());
            
            if (aggregateOpt.isEmpty()) {
                log.warn("未找到漏斗转化分析数据: timeRange={}, funnelSteps={}", timeRange, request.getFunnelSteps());
                return FunnelAnalysisResponse.empty();
            }
            
            FunnelAnalysisAggregate aggregate = aggregateOpt.get();
            
            // 转换为响应DTO
            return convertToResponse(aggregate);
            
        } catch (Exception e) {
            log.error("获取漏斗转化分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取漏斗转化分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取漏斗对比分析
     */
    public FunnelAnalysisResponse getFunnelComparisonAnalysis(FunnelAnalysisRequest request) {
        log.info("获取漏斗对比分析: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            // 验证请求参数
            validateFunnelAnalysisRequest(request);
            
            // 获取漏斗对比分析数据
            Optional<FunnelAnalysisAggregate> aggregateOpt = funnelAnalysisRepository.getFunnelComparisonAnalysis(
                timeRange, request.getFunnelGroups(), request.getProductLineIds(), request.getDataScope());
            
            if (aggregateOpt.isEmpty()) {
                log.warn("未找到漏斗对比分析数据: timeRange={}, funnelGroups={}", timeRange, request.getFunnelGroups());
                return FunnelAnalysisResponse.empty();
            }
            
            FunnelAnalysisAggregate aggregate = aggregateOpt.get();
            
            // 转换为响应DTO
            return convertToResponse(aggregate);
            
        } catch (Exception e) {
            log.error("获取漏斗对比分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取漏斗对比分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取流失点分析
     */
    public FunnelAnalysisResponse getDropoutAnalysis(FunnelAnalysisRequest request) {
        log.info("获取流失点分析: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            // 验证请求参数
            validateFunnelAnalysisRequest(request);
            
            // 获取流失点分析数据
            Optional<FunnelAnalysisAggregate> aggregateOpt = funnelAnalysisRepository.getDropoutAnalysis(
                timeRange, request.getFunnelSteps(), request.getProductLineIds(), request.getDataScope());
            
            if (aggregateOpt.isEmpty()) {
                log.warn("未找到流失点分析数据: timeRange={}, funnelSteps={}", timeRange, request.getFunnelSteps());
                return FunnelAnalysisResponse.empty();
            }
            
            FunnelAnalysisAggregate aggregate = aggregateOpt.get();
            
            // 转换为响应DTO
            return convertToResponse(aggregate);
            
        } catch (Exception e) {
            log.error("获取流失点分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取流失点分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取队列漏斗分析
     */
    public FunnelAnalysisResponse getCohortFunnelAnalysis(FunnelAnalysisRequest request) {
        log.info("获取队列漏斗分析: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            // 验证请求参数
            validateFunnelAnalysisRequest(request);
            
            // 获取队列漏斗分析数据
            Optional<FunnelAnalysisAggregate> aggregateOpt = funnelAnalysisRepository.getCohortFunnelAnalysis(
                timeRange, request.getFunnelSteps(), request.getCohortPeriod(), 
                request.getProductLineIds(), request.getDataScope());
            
            if (aggregateOpt.isEmpty()) {
                log.warn("未找到队列漏斗分析数据: timeRange={}, funnelSteps={}, cohortPeriod={}", 
                    timeRange, request.getFunnelSteps(), request.getCohortPeriod());
                return FunnelAnalysisResponse.empty();
            }
            
            FunnelAnalysisAggregate aggregate = aggregateOpt.get();
            
            // 转换为响应DTO
            return convertToResponse(aggregate);
            
        } catch (Exception e) {
            log.error("获取队列漏斗分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取队列漏斗分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取漏斗模板列表
     */
    public List<FunnelAnalysisRepository.FunnelTemplate> getFunnelTemplates(List<Long> productLineIds, String dataScope) {
        log.info("获取漏斗模板列表: productLineIds={}, dataScope={}", productLineIds, dataScope);
        
        try {
            return funnelAnalysisRepository.getFunnelTemplates(productLineIds, dataScope);
        } catch (Exception e) {
            log.error("获取漏斗模板列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取漏斗模板列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取实时漏斗统计
     */
    public FunnelAnalysisRepository.FunnelRealTimeStats getRealTimeFunnelStats(List<String> funnelSteps, 
                                                                              List<Long> productLineIds, String dataScope) {
        log.info("获取实时漏斗统计: funnelSteps={}, productLineIds={}, dataScope={}", 
                funnelSteps, productLineIds, dataScope);
        
        try {
            return funnelAnalysisRepository.getRealTimeFunnelStats(funnelSteps, productLineIds, dataScope);
        } catch (Exception e) {
            log.error("获取实时漏斗统计失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取实时漏斗统计失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取漏斗分析统计摘要
     */
    public FunnelAnalysisRepository.FunnelAnalysisSummary getFunnelAnalysisSummary(FunnelAnalysisRequest request) {
        log.info("获取漏斗分析统计摘要: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            return funnelAnalysisRepository.getFunnelAnalysisSummary(timeRange, request.getDataScope());
        } catch (Exception e) {
            log.error("获取漏斗分析统计摘要失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取漏斗分析统计摘要失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取漏斗优化建议
     */
    public List<FunnelAnalysisRepository.FunnelOptimizationSuggestion> getFunnelOptimizationSuggestions(FunnelAnalysisRequest request) {
        log.info("获取漏斗优化建议: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            return funnelAnalysisRepository.getFunnelOptimizationSuggestions(
                timeRange, request.getFunnelSteps(), request.getProductLineIds(), request.getDataScope());
        } catch (Exception e) {
            log.error("获取漏斗优化建议失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取漏斗优化建议失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取漏斗转化趋势
     */
    public List<FunnelAnalysisRepository.FunnelConversionTrend> getFunnelConversionTrends(FunnelAnalysisRequest request) {
        log.info("获取漏斗转化趋势: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            return funnelAnalysisRepository.getFunnelConversionTrends(
                timeRange, request.getFunnelSteps(), request.getGranularity(), 
                request.getProductLineIds(), request.getDataScope());
        } catch (Exception e) {
            log.error("获取漏斗转化趋势失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取漏斗转化趋势失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取漏斗用户细分分析
     */
    public List<FunnelAnalysisRepository.FunnelUserSegmentAnalysis> getFunnelUserSegmentAnalysis(FunnelAnalysisRequest request) {
        log.info("获取漏斗用户细分分析: request={}", request);
        
        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());
            
            return funnelAnalysisRepository.getFunnelUserSegmentAnalysis(
                timeRange, request.getFunnelSteps(), request.getSegmentDimensions(), 
                request.getProductLineIds(), request.getDataScope());
        } catch (Exception e) {
            log.error("获取漏斗用户细分分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取漏斗用户细分分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证漏斗分析请求参数
     */
    private void validateFunnelAnalysisRequest(FunnelAnalysisRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("漏斗分析请求不能为空");
        }
        
        if (request.getStartDate() == null || request.getEndDate() == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }
        
        if (request.getStartDate().isAfter(request.getEndDate())) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }
        
        if (request.getDataScope() == null || request.getDataScope().trim().isEmpty()) {
            throw new IllegalArgumentException("数据权限范围不能为空");
        }
        
        if (request.getFunnelSteps() == null || request.getFunnelSteps().isEmpty()) {
            throw new IllegalArgumentException("漏斗步骤不能为空");
        }
        
        if (request.getFunnelSteps().size() < 2) {
            throw new IllegalArgumentException("漏斗步骤至少需要2个");
        }
        
        if (request.getFunnelSteps().size() > 10) {
            throw new IllegalArgumentException("漏斗步骤不能超过10个");
        }
    }

    /**
     * 转换聚合根为响应DTO
     */
    private FunnelAnalysisResponse convertToResponse(FunnelAnalysisAggregate aggregate) {
        // TODO: 实现具体的转换逻辑
        return FunnelAnalysisResponse.builder()
                .analysisId(aggregate.getId())
                .analysisType(aggregate.getAnalysisType().name())
                .timeRange(aggregate.getTimeRange())
                .coreMetrics(aggregate.getCoreMetrics())
                .conversionData(aggregate.getConversionData())
                .comparisonData(aggregate.getComparisonData())
                .dropoutData(aggregate.getDropoutData())
                .cohortData(aggregate.getCohortData())
                .lastUpdateTime(aggregate.getLastUpdateTime())
                .build();
    }
}
