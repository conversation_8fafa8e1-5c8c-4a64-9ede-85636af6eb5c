package com.foxit.crm.modules.useranalysis.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.common.util.SecurityUtils;
import com.foxit.crm.modules.useranalysis.application.dto.ActiveUserAnalysisRequest;
import com.foxit.crm.modules.useranalysis.application.dto.ActiveUserAnalysisResponse;
import com.foxit.crm.modules.useranalysis.application.service.ActiveUserAnalysisService;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.List;

/**
 * 活跃用户分析API控制器
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@RestController
@RequestMapping("/user/active")
@RequiredArgsConstructor
@Tag(name = "活跃用户分析", description = "活跃用户分析相关API")
public class ActiveUserController {

    private final ActiveUserAnalysisService activeUserAnalysisService;

    @Operation(summary = "获取活跃用户总览", description = "获取指定时间范围内的活跃用户总览数据")
    @GetMapping("/overview")
    @PreAuthorize("hasPermission('user:active', 'READ')")
    public Result<ActiveUserAnalysisResponse> getOverview(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "时间粒度") @RequestParam(required = false, defaultValue = "DAY") TimeRange.TimeGranularity granularity,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds,
            @Parameter(description = "是否包含对比") @RequestParam(required = false, defaultValue = "true") Boolean includeComparison) {

        try {
            ActiveUserAnalysisRequest request = ActiveUserAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(granularity)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .includeComparison(includeComparison)
                    .build();

            ActiveUserAnalysisResponse response = activeUserAnalysisService.getActiveUserOverview(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取活跃用户总览失败", e);
            return Result.error("获取活跃用户总览失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取活跃用户趋势", description = "获取活跃用户的趋势变化数据")
    @GetMapping("/trends")
    @PreAuthorize("hasPermission('user:active', 'READ')")
    public Result<ActiveUserAnalysisResponse> getTrends(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "时间粒度") @RequestParam(required = false, defaultValue = "DAY") TimeRange.TimeGranularity granularity,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            ActiveUserAnalysisRequest request = ActiveUserAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(granularity)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            ActiveUserAnalysisResponse response = activeUserAnalysisService.getActiveUserTrends(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取活跃用户趋势失败", e);
            return Result.error("获取活跃用户趋势失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取活跃用户分布", description = "获取活跃用户的地域、设备等分布数据")
    @GetMapping("/distribution")
    @PreAuthorize("hasPermission('user:active', 'READ')")
    public Result<ActiveUserAnalysisResponse> getDistribution(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            ActiveUserAnalysisRequest request = ActiveUserAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            ActiveUserAnalysisResponse response = activeUserAnalysisService.getActiveUserDistribution(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取活跃用户分布失败", e);
            return Result.error("获取活跃用户分布失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取活跃频次分析", description = "获取用户活跃频次的分析数据")
    @GetMapping("/frequency")
    @PreAuthorize("hasPermission('user:active', 'READ')")
    public Result<ActiveUserAnalysisResponse> getFrequency(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            ActiveUserAnalysisRequest request = ActiveUserAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            ActiveUserAnalysisResponse response = activeUserAnalysisService.getActiveUserFrequency(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取活跃频次分析失败", e);
            return Result.error("获取活跃频次分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取产品线对比", description = "获取不同产品线的活跃用户对比数据")
    @GetMapping("/comparison")
    @PreAuthorize("hasPermission('user:active', 'READ')")
    public Result<ActiveUserAnalysisResponse> getComparison(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam List<Long> productLineIds) {

        try {
            ActiveUserAnalysisRequest request = ActiveUserAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .analysisType("comparison")
                    .build();

            ActiveUserAnalysisResponse response = activeUserAnalysisService.getActiveUserComparison(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取产品线对比失败", e);
            return Result.error("获取产品线对比失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取活跃用户留存", description = "获取活跃用户的留存分析数据")
    @GetMapping("/retention")
    @PreAuthorize("hasPermission('user:active', 'READ')")
    public Result<ActiveUserAnalysisResponse> getRetention(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            ActiveUserAnalysisRequest request = ActiveUserAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            ActiveUserAnalysisResponse response = activeUserAnalysisService.getActiveUserRetention(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取活跃用户留存失败", e);
            return Result.error("获取活跃用户留存失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取队列分析", description = "获取活跃用户的队列分析数据")
    @GetMapping("/cohort")
    @PreAuthorize("hasPermission('user:active', 'READ')")
    public Result<ActiveUserAnalysisResponse> getCohort(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            ActiveUserAnalysisRequest request = ActiveUserAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            ActiveUserAnalysisResponse response = activeUserAnalysisService.getActiveUserCohort(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取队列分析失败", e);
            return Result.error("获取队列分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "导出活跃用户数据", description = "导出活跃用户分析数据为Excel文件")
    @PostMapping("/export")
    @PreAuthorize("hasPermission('user:active', 'EXPORT')")
    public ResponseEntity<byte[]> exportData(@Valid @RequestBody ActiveUserAnalysisRequest request) {

        try {
            // 设置数据权限范围
            request.setDataScope(SecurityUtils.getCurrentUserDataScope());
            request.setUserId(SecurityUtils.getCurrentUserId());

            byte[] excelData = activeUserAnalysisService.exportActiveUserData(request);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment",
                    String.format("active_user_analysis_%s_%s.xlsx",
                            request.getStartDate(), request.getEndDate()));

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(excelData);

        } catch (Exception e) {
            log.error("导出活跃用户数据失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @Operation(summary = "获取每日活跃用户数据", description = "获取每日活跃用户的详细数据")
    @PostMapping("/daily")
    @PreAuthorize("hasPermission('user:active', 'READ')")
    public Result<List<java.util.Map<String, Object>>> getDailyActiveUsers(
            @RequestBody java.util.Map<String, Object> request) {

        try {
            String startDate = (String) request.get("startDate");
            String endDate = (String) request.get("endDate");
            String granularity = (String) request.getOrDefault("granularity", "DAY");

            // 模拟每日活跃用户数据
            java.util.List<java.util.Map<String, Object>> dailyData = new java.util.ArrayList<>();
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);

            for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
                java.util.Map<String, Object> dayData = new java.util.HashMap<>();
                dayData.put("date", date.toString());
                dayData.put("activeUsers", 8000 + (int) (Math.random() * 2000));
                dayData.put("activeUsersGrowth", -5.0 + Math.random() * 10);
                dayData.put("dau", 8000 + (int) (Math.random() * 2000));
                dayData.put("dauGrowth", -5.0 + Math.random() * 10);
                dayData.put("wau", 40000 + (int) (Math.random() * 10000));
                dayData.put("wauGrowth", -3.0 + Math.random() * 8);
                dayData.put("mau", 150000 + (int) (Math.random() * 20000));
                dayData.put("mauGrowth", -2.0 + Math.random() * 6);
                dayData.put("stickinessRatio", 15.0 + Math.random() * 10);
                dayData.put("stickinessRatioGrowth", -2.0 + Math.random() * 4);
                dayData.put("activeRate", 70.0 + Math.random() * 20);
                dayData.put("activeRateGrowth", -3.0 + Math.random() * 6);
                dailyData.add(dayData);
            }

            return Result.success(dailyData);

        } catch (Exception e) {
            log.error("获取每日活跃用户数据失败", e);
            return Result.error("获取每日活跃用户数据失败: " + e.getMessage());
        }
    }

    // ==================== 公共接口（无需认证） ====================

    /**
     * 获取公共每日活跃用户数据 - 无需认证
     */
    @Operation(summary = "获取公共每日活跃用户数据", description = "获取每日活跃用户的详细数据（无需认证）")
    @PostMapping("/public/daily")
    public Result<List<java.util.Map<String, Object>>> getPublicDailyActiveUsers(
            @RequestBody java.util.Map<String, Object> request) {

        try {
            String startDate = (String) request.get("startDate");
            String endDate = (String) request.get("endDate");
            String granularity = (String) request.getOrDefault("granularity", "DAY");

            // 模拟每日活跃用户数据
            java.util.List<java.util.Map<String, Object>> dailyData = new java.util.ArrayList<>();
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);

            for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
                java.util.Map<String, Object> dayData = new java.util.HashMap<>();
                dayData.put("date", date.toString());
                dayData.put("activeUsers", 1000 + (int) (Math.random() * 500)); // 模拟数据
                dayData.put("newUsers", 50 + (int) (Math.random() * 100)); // 模拟数据
                dayData.put("retentionRate", 70 + Math.random() * 20); // 模拟数据
                dailyData.add(dayData);
            }

            return Result.success(dailyData);

        } catch (Exception e) {
            log.error("获取公共每日活跃用户数据失败", e);
            return Result.error("获取公共每日活跃用户数据失败: " + e.getMessage());
        }
    }
}
