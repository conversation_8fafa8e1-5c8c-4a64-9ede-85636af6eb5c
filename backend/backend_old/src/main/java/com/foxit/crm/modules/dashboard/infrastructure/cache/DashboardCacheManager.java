package com.foxit.crm.modules.dashboard.infrastructure.cache;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RKeys;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.util.Map;
import java.util.function.Supplier;

/**
 * Dashboard缓存管理器
 * 专门处理Dashboard相关的缓存操作
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DashboardCacheManager {

    private final RedissonClient redissonClient;

    // 缓存键前缀
    private static final String DASHBOARD_PREFIX = "dashboard:";
    private static final String METRICS_PREFIX = DASHBOARD_PREFIX + "metrics:";
    private static final String CHART_PREFIX = DASHBOARD_PREFIX + "chart:";
    private static final String REALTIME_PREFIX = DASHBOARD_PREFIX + "realtime:";
    private static final String CONFIG_PREFIX = DASHBOARD_PREFIX + "config:";

    // 缓存过期时间（秒）
    private static final int METRICS_EXPIRE = 30 * 60; // 30分钟
    private static final int CHART_EXPIRE = 15 * 60; // 15分钟
    private static final int REALTIME_EXPIRE = 60; // 1分钟
    private static final int CONFIG_EXPIRE = 60 * 60; // 1小时

    /**
     * 缓存核心指标数据
     */
    @Cacheable(value = "dashboardMetrics", key = "#cacheKey")
    public Map<String, Object> getCoreMetrics(String cacheKey, Supplier<Map<String, Object>> dataLoader) {
        log.debug("加载核心指标数据: key={}", cacheKey);
        return dataLoader.get();
    }

    /**
     * 缓存图表数据
     */
    @Cacheable(value = "dashboardChart", key = "#cacheKey")
    public Map<String, Object> getChartData(String cacheKey, Supplier<Map<String, Object>> dataLoader) {
        log.debug("加载图表数据: key={}", cacheKey);
        return dataLoader.get();
    }

    /**
     * 缓存实时数据
     */
    public <T> T getRealTimeData(String cacheKey, Supplier<T> dataLoader, Class<T> clazz) {
        String fullKey = REALTIME_PREFIX + cacheKey;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);

        T cachedData = bucket.get();
        if (cachedData != null) {
            log.debug("从缓存获取实时数据: key={}", fullKey);
            return cachedData;
        }

        T data = dataLoader.get();
        bucket.setAsync(data, Duration.ofSeconds(REALTIME_EXPIRE));
        log.debug("缓存实时数据: key={}", fullKey);
        return data;
    }

    /**
     * 缓存Dashboard配置
     */
    public <T> T getDashboardConfig(Long userId, Supplier<T> dataLoader, Class<T> clazz) {
        String cacheKey = CONFIG_PREFIX + "user:" + userId;
        RBucket<T> bucket = redissonClient.getBucket(cacheKey);

        T cachedConfig = bucket.get();
        if (cachedConfig != null) {
            log.debug("从缓存获取Dashboard配置: userId={}", userId);
            return cachedConfig;
        }

        T config = dataLoader.get();
        bucket.setAsync(config, Duration.ofSeconds(CONFIG_EXPIRE));
        log.debug("缓存Dashboard配置: userId={}", userId);
        return config;
    }

    /**
     * 缓存产品线数据
     */
    public <T> T getProductLineData(String cacheKey, Supplier<T> dataLoader, Class<T> clazz) {
        String fullKey = DASHBOARD_PREFIX + "productline:" + cacheKey;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);

        T cachedData = bucket.get();
        if (cachedData != null) {
            log.debug("从缓存获取产品线数据: key={}", fullKey);
            return cachedData;
        }

        T data = dataLoader.get();
        bucket.setAsync(data, Duration.ofSeconds(METRICS_EXPIRE));
        log.debug("缓存产品线数据: key={}", fullKey);
        return data;
    }

    /**
     * 清除指定类型的缓存
     */
    @CacheEvict(value = { "dashboardMetrics", "dashboardChart" }, allEntries = true)
    public void evictDashboardCache(String cacheType) {
        log.info("清除Dashboard缓存: type={}", cacheType);

        String pattern;
        switch (cacheType) {
            case "metrics":
                pattern = METRICS_PREFIX + "*";
                break;
            case "chart":
                pattern = CHART_PREFIX + "*";
                break;
            case "realtime":
                pattern = REALTIME_PREFIX + "*";
                break;
            case "config":
                pattern = CONFIG_PREFIX + "*";
                break;
            default:
                pattern = DASHBOARD_PREFIX + "*";
        }

        deleteByPattern(pattern);
    }

    /**
     * 清除用户相关的缓存
     */
    public void evictUserCache(Long userId) {
        log.info("清除用户Dashboard缓存: userId={}", userId);

        String pattern = DASHBOARD_PREFIX + "*:user:" + userId + "*";
        deleteByPattern(pattern);

        // 清除用户配置缓存
        String configKey = CONFIG_PREFIX + "user:" + userId;
        redissonClient.getBucket(configKey).deleteAsync();
    }

    /**
     * 清除产品线相关的缓存
     */
    public void evictProductLineCache(Long productLineId) {
        log.info("清除产品线Dashboard缓存: productLineId={}", productLineId);

        String pattern = DASHBOARD_PREFIX + "*:productline:" + productLineId + "*";
        deleteByPattern(pattern);
    }

    /**
     * 清除日期相关的缓存
     */
    public void evictDateCache(LocalDate date) {
        log.info("清除日期Dashboard缓存: date={}", date);

        String pattern = DASHBOARD_PREFIX + "*:" + date.toString() + "*";
        deleteByPattern(pattern);
    }

    /**
     * 预热Dashboard缓存
     */
    public void warmUpCache(String dataScope, Long userId) {
        log.info("预热Dashboard缓存: dataScope={}, userId={}", dataScope, userId);

        // 这里可以预加载一些常用的Dashboard数据
        // 例如：最近30天的核心指标、常用图表数据等

        // 预热核心指标缓存
        String metricsKey = METRICS_PREFIX + "overview:" + dataScope + ":last30days";
        RBucket<Object> metricsBucket = redissonClient.getBucket(metricsKey);
        if (!metricsBucket.isExists()) {
            // 这里应该调用实际的数据加载逻辑
            log.debug("预热核心指标缓存: key={}", metricsKey);
        }

        // 预热图表数据缓存
        String chartKey = CHART_PREFIX + "userGrowth:" + dataScope + ":last30days";
        RBucket<Object> chartBucket = redissonClient.getBucket(chartKey);
        if (!chartBucket.isExists()) {
            log.debug("预热图表数据缓存: key={}", chartKey);
        }
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        RKeys keys = redissonClient.getKeys();

        // 使用getKeysByPattern获取键列表，然后计算数量
        long metricsCount = countKeys(keys, METRICS_PREFIX + "*");
        long chartCount = countKeys(keys, CHART_PREFIX + "*");
        long realtimeCount = countKeys(keys, REALTIME_PREFIX + "*");
        long configCount = countKeys(keys, CONFIG_PREFIX + "*");
        long totalCount = countKeys(keys, DASHBOARD_PREFIX + "*");

        return Map.of(
                "totalKeys", totalCount,
                "metricsKeys", metricsCount,
                "chartKeys", chartCount,
                "realtimeKeys", realtimeCount,
                "configKeys", configCount);
    }

    /**
     * 计算匹配模式的键数量
     */
    private long countKeys(RKeys keys, String pattern) {
        try {
            Iterable<String> keyIterable = keys.getKeysByPattern(pattern);
            long count = 0;
            for (String key : keyIterable) {
                count++;
            }
            return count;
        } catch (Exception e) {
            log.warn("计算键数量失败: pattern={}", pattern, e);
            return 0L;
        }
    }

    /**
     * 根据模式删除缓存键
     */
    private void deleteByPattern(String pattern) {
        try {
            RKeys keys = redissonClient.getKeys();
            keys.deleteByPatternAsync(pattern);
            log.debug("删除缓存键: pattern={}", pattern);
        } catch (Exception e) {
            log.error("删除缓存键失败: pattern={}, error={}", pattern, e.getMessage());
        }
    }

    /**
     * 构建缓存键
     */
    public static String buildCacheKey(String prefix, String... parts) {
        StringBuilder key = new StringBuilder(prefix);
        for (String part : parts) {
            if (part != null && !part.isEmpty()) {
                key.append(":").append(part);
            }
        }
        return key.toString();
    }

    /**
     * 构建指标缓存键
     */
    public static String buildMetricsCacheKey(String dataScope, String timeRange, String productLineIds) {
        return buildCacheKey(METRICS_PREFIX, dataScope, timeRange, productLineIds);
    }

    /**
     * 构建图表缓存键
     */
    public static String buildChartCacheKey(String chartType, String dataScope, String timeRange,
            String productLineIds) {
        return buildCacheKey(CHART_PREFIX, chartType, dataScope, timeRange, productLineIds);
    }

    /**
     * 构建实时数据缓存键
     */
    public static String buildRealtimeCacheKey(String dataType, String dataScope) {
        return buildCacheKey(REALTIME_PREFIX, dataType, dataScope);
    }
}
