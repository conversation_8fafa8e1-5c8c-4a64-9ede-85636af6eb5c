package com.foxit.crm.modules.campaignanalysis.domain.entity;

import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 活动分析聚合根
 * 用于表示营销活动的分析数据和统计信息
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Getter
@Builder
public class CampaignAggregate {

    /**
     * 聚合根ID
     */
    private final String id;

    /**
     * 分析类型
     */
    private final CampaignAnalysisType analysisType;

    /**
     * 时间范围
     */
    private final TimeRange timeRange;

    /**
     * 活动ID列表
     */
    private final List<Long> campaignIds;

    /**
     * 核心指标
     */
    private final Map<String, MetricValue> coreMetrics;

    /**
     * 活动列表数据
     */
    private final List<CampaignData> campaignList;

    /**
     * 趋势数据
     */
    private final Map<String, TrendData> trendData;

    /**
     * 渠道分析数据
     */
    private final Map<String, ChannelData> channelData;

    /**
     * 数据权限范围
     */
    private final String dataScope;

    /**
     * 最后更新时间
     */
    private final LocalDateTime lastUpdateTime;

    /**
     * 活动分析类型枚举
     */
    public enum CampaignAnalysisType {
        OVERVIEW("活动总览"),
        PERFORMANCE("活动效果"),
        CHANNEL("渠道分析"),
        ROI("投资回报率"),
        TREND("趋势分析");

        private final String description;

        CampaignAnalysisType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 活动数据
     */
    @Getter
    @Builder
    public static class CampaignData {
        private final Long campaignId;
        private final String campaignName;
        private final String campaignType;
        private final String status;
        private final BigDecimal budget;
        private final BigDecimal actualCost;
        private final Long impressions;
        private final Long clicks;
        private final Long conversions;
        private final Long participants;
        private final BigDecimal revenue;
        private final Double clickRate;
        private final Double conversionRate;
        private final Double roi;
        private final LocalDateTime startTime;
        private final LocalDateTime endTime;
    }

    /**
     * 趋势数据
     */
    @Getter
    @Builder
    public static class TrendData {
        private final String name;
        private final List<TrendPoint> points;
        private final String chartType;
    }

    /**
     * 趋势点数据
     */
    @Getter
    @Builder
    public static class TrendPoint {
        private final String date;
        private final Double value;
        private final String label;
    }

    /**
     * 渠道数据
     */
    @Getter
    @Builder
    public static class ChannelData {
        private final String channelName;
        private final Long impressions;
        private final Long clicks;
        private final Long conversions;
        private final BigDecimal cost;
        private final BigDecimal revenue;
        private final Double clickRate;
        private final Double conversionRate;
        private final Double roi;
    }

    /**
     * 获取核心指标
     */
    public MetricValue getCoreMetric(String metricName) {
        return coreMetrics != null ? coreMetrics.get(metricName) : null;
    }

    /**
     * 获取趋势数据
     */
    public TrendData getTrendData(String trendName) {
        return trendData != null ? trendData.get(trendName) : null;
    }

    /**
     * 获取渠道数据
     */
    public ChannelData getChannelData(String channelName) {
        return channelData != null ? channelData.get(channelName) : null;
    }

    /**
     * 计算总ROI
     */
    public Double calculateTotalROI() {
        if (campaignList == null || campaignList.isEmpty()) {
            return 0.0;
        }

        BigDecimal totalRevenue = campaignList.stream()
                .map(CampaignData::getRevenue)
                .filter(revenue -> revenue != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalCost = campaignList.stream()
                .map(CampaignData::getActualCost)
                .filter(cost -> cost != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalCost.compareTo(BigDecimal.ZERO) == 0) {
            return 0.0;
        }

        return totalRevenue.subtract(totalCost)
                .divide(totalCost, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .doubleValue();
    }

    /**
     * 获取最佳表现活动
     */
    public CampaignData getBestPerformingCampaign() {
        if (campaignList == null || campaignList.isEmpty()) {
            return null;
        }

        return campaignList.stream()
                .filter(campaign -> campaign.getRoi() != null)
                .max((c1, c2) -> Double.compare(c1.getRoi(), c2.getRoi()))
                .orElse(null);
    }

    /**
     * 获取数据摘要
     */
    public String getDataSummary() {
        int totalCampaigns = campaignList != null ? campaignList.size() : 0;
        Double totalROI = calculateTotalROI();
        
        return String.format("分析类型: %s, 时间范围: %s - %s, 活动数量: %d, 总ROI: %.2f%%",
                analysisType.getDescription(),
                timeRange.getStartDate(),
                timeRange.getEndDate(),
                totalCampaigns,
                totalROI);
    }

    /**
     * 创建活动总览分析
     */
    public static CampaignAggregate createOverview(TimeRange timeRange, String dataScope) {
        return CampaignAggregate.builder()
                .id(generateId("overview", timeRange))
                .analysisType(CampaignAnalysisType.OVERVIEW)
                .timeRange(timeRange)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 生成聚合根ID
     */
    private static String generateId(String type, TimeRange timeRange) {
        return String.format("campaign_%s_%s_%s", 
                type, 
                timeRange.getStartDate(), 
                timeRange.getEndDate());
    }
}
