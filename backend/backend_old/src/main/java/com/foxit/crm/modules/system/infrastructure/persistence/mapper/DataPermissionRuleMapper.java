package com.foxit.crm.modules.system.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.DataPermissionRulePO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 数据权限规则Mapper接口
 * 使用 MyBatis-Plus，无需自定义方法，通过 QueryWrapper 实现复杂查询
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Mapper
public interface DataPermissionRuleMapper extends BaseMapper<DataPermissionRulePO> {
    // MyBatis-Plus 已提供基础的 CRUD 方法
    // 复杂查询通过 QueryWrapper 在 Service 层实现
}
