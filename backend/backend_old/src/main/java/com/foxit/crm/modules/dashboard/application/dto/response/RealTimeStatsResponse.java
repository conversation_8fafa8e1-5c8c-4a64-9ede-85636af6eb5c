package com.foxit.crm.modules.dashboard.application.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 实时统计响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RealTimeStatsResponse {

    /**
     * 当前在线用户数
     */
    private Long currentOnline;

    /**
     * 今日峰值在线用户数
     */
    private Long peakOnline;

    /**
     * 今日总下载量
     */
    private Long totalDownloadsToday;

    /**
     * 当前系统负载
     */
    private BigDecimal systemLoad;

    /**
     * 内存使用率
     */
    private BigDecimal memoryUsage;

    /**
     * CPU使用率
     */
    private BigDecimal cpuUsage;

    /**
     * 当前活跃会话数
     */
    private Long activeSessions;

    /**
     * 今日新增用户数
     */
    private Long newUsersToday;

    /**
     * 今日收入
     */
    private BigDecimal revenueToday;

    /**
     * 实时事件列表
     */
    private List<RealTimeEvent> recentEvents;

    /**
     * 按小时统计数据
     */
    private Map<String, HourlyStats> hourlyStats;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 实时事件内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class RealTimeEvent {
        /**
         * 事件ID
         */
        private String eventId;

        /**
         * 事件类型
         */
        private String eventType;

        /**
         * 事件描述
         */
        private String description;

        /**
         * 用户ID
         */
        private Long userId;

        /**
         * 用户名
         */
        private String username;

        /**
         * 产品线
         */
        private String productLine;

        /**
         * 事件时间
         */
        private LocalDateTime eventTime;

        /**
         * 事件级别：info, warning, error
         */
        private String level;
    }

    /**
     * 小时统计内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class HourlyStats {
        /**
         * 小时（0-23）
         */
        private Integer hour;

        /**
         * 在线用户数
         */
        private Long onlineUsers;

        /**
         * 下载次数
         */
        private Long downloads;

        /**
         * 收入
         */
        private BigDecimal revenue;

        /**
         * 新增用户数
         */
        private Long newUsers;
    }
}
