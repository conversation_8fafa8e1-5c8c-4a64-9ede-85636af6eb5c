package com.foxit.crm.modules.system.domain.model.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

/**
 * 用户ID值对象
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Getter
@EqualsAndHashCode
@ToString
public class UserId {

    private final Long value;

    public UserId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("用户ID不能为空或小于等于0");
        }
        this.value = value;
    }
}
