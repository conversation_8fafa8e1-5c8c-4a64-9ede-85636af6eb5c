package com.foxit.crm.modules.system.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.modules.system.api.dto.request.ProductVersionCreateRequest;
import com.foxit.crm.modules.system.api.dto.request.ProductVersionUpdateRequest;
import com.foxit.crm.modules.system.api.dto.response.ProductVersionDetailResponse;
import com.foxit.crm.modules.system.api.dto.response.ProductVersionListResponse;
import com.foxit.crm.modules.system.api.dto.response.ProductVersionSimpleResponse;
import com.foxit.crm.modules.system.api.dto.response.VersionReleaseHistoryResponse;
import com.foxit.crm.modules.system.application.service.ProductVersionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 产品版本管理控制器
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Slf4j
@RestController
@RequestMapping("/product-versions")
@RequiredArgsConstructor
@Validated
public class ProductVersionController {

    private final ProductVersionService productVersionService;

    /**
     * 创建产品版本
     */
    @PostMapping
    @PreAuthorize("hasPermission('product:version', 'create')")
    public Result<Long> createProductVersion(@Valid @RequestBody ProductVersionCreateRequest request) {
        log.info("创建产品版本，产品线ID: {}, 版本号: {}", request.getProductLineId(), request.getVersionNumber());
        Long versionId = productVersionService.createProductVersion(request);
        return Result.success(versionId);
    }

    /**
     * 更新产品版本
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasPermission('product:version', 'update')")
    public Result<Void> updateProductVersion(@PathVariable Long id,
            @Valid @RequestBody ProductVersionUpdateRequest request) {
        log.info("更新产品版本，版本ID: {}", id);
        productVersionService.updateProductVersion(id, request);
        return Result.success();
    }

    /**
     * 删除产品版本
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasPermission('product:version', 'delete')")
    public Result<Void> deleteProductVersion(@PathVariable Long id) {
        log.info("删除产品版本，版本ID: {}", id);
        productVersionService.deleteProductVersion(id);
        return Result.success();
    }

    /**
     * 批量删除产品版本
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasPermission('product:version', 'delete')")
    public Result<Void> deleteProductVersionsBatch(@RequestBody @NotEmpty List<Long> ids) {
        log.info("批量删除产品版本，数量: {}", ids.size());
        productVersionService.deleteProductVersionsBatch(ids);
        return Result.success();
    }

    /**
     * 获取产品版本详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasPermission('product:version', 'read')")
    public Result<ProductVersionDetailResponse> getProductVersionById(@PathVariable Long id) {
        ProductVersionDetailResponse response = productVersionService.getProductVersionById(id);
        return Result.success(response);
    }

    /**
     * 分页查询产品版本列表
     */
    @GetMapping
    @PreAuthorize("hasPermission('product:version', 'read')")
    public Result<ProductVersionListResponse> getProductVersionList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) Long productLineId,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String versionType) {
        ProductVersionListResponse response = productVersionService.getProductVersionList(
                page, size, productLineId, keyword, status, versionType);
        return Result.success(response);
    }

    /**
     * 获取产品线版本列表
     */
    @GetMapping("/by-product-line/{productLineId}")
    @PreAuthorize("hasPermission('product:version', 'read')")
    public Result<List<ProductVersionSimpleResponse>> getVersionsByProductLineId(@PathVariable Long productLineId) {
        List<ProductVersionSimpleResponse> response = productVersionService.getVersionsByProductLineId(productLineId);
        return Result.success(response);
    }

    /**
     * 获取产品线当前版本
     */
    @GetMapping("/current/{productLineId}")
    @PreAuthorize("hasPermission('product:version', 'read')")
    public Result<ProductVersionDetailResponse> getCurrentVersionByProductLineId(@PathVariable Long productLineId) {
        ProductVersionDetailResponse response = productVersionService.getCurrentVersionByProductLineId(productLineId);
        return Result.success(response);
    }

    /**
     * 更新版本状态（统一接口）
     * 支持发布(RELEASED)、设置当前(CURRENT)、废弃(DEPRECATED)等状态
     */
    @PutMapping("/{id}/status")
    @PreAuthorize("hasPermission('product:version', 'update')")
    public Result<Void> updateVersionStatus(@PathVariable Long id, @RequestParam @NotNull String status,
            @RequestParam(required = false) String reason) {
        log.info("更新版本状态，版本ID: {}, 新状态: {}, 原因: {}", id, status, reason);

        // 根据状态调用相应的服务方法
        switch (status.toUpperCase()) {
            case "RELEASED":
                productVersionService.releaseVersion(id, reason);
                break;
            case "CURRENT":
                productVersionService.setCurrentVersion(id);
                break;
            case "DEPRECATED":
                productVersionService.deprecateVersion(id, reason);
                break;
            default:
                productVersionService.updateVersionStatus(id, status, reason);
                break;
        }

        return Result.success();
    }

    /**
     * 记录版本下载
     */
    @PostMapping("/{id}/download")
    public Result<Void> downloadVersion(@PathVariable Long id, @RequestParam(required = false) String platform,
            @RequestParam(required = false) String region) {
        log.info("记录版本下载，版本ID: {}, 平台: {}, 地区: {}", id, platform, region);
        productVersionService.downloadVersion(id, platform, region);
        return Result.success();
    }

    /**
     * 获取版本发布历史
     */
    @GetMapping("/{id}/history")
    @PreAuthorize("hasPermission('product:version', 'read')")
    public Result<List<VersionReleaseHistoryResponse>> getVersionReleaseHistory(@PathVariable Long id) {
        List<VersionReleaseHistoryResponse> response = productVersionService.getVersionReleaseHistory(id);
        return Result.success(response);
    }

    /**
     * 获取版本统计信息
     */
    @GetMapping("/stats/{productLineId}")
    @PreAuthorize("hasPermission('product:version', 'read')")
    public Result<Object> getVersionStatsByProductLineId(@PathVariable Long productLineId) {
        Object response = productVersionService.getVersionStatsByProductLineId(productLineId);
        return Result.success(response);
    }

    /**
     * 获取热门版本排行
     */
    @GetMapping("/top-downloads")
    @PreAuthorize("hasPermission('product:version', 'read')")
    public Result<List<Object>> getTopDownloadVersions(@RequestParam(defaultValue = "30") Integer days,
            @RequestParam(defaultValue = "10") Integer limit) {
        List<Object> response = productVersionService.getTopDownloadVersions(days, limit);
        return Result.success(response);
    }

    /**
     * 检查版本号可用性
     */
    @GetMapping("/check-version-number")
    @PreAuthorize("hasPermission('product:version', 'read')")
    public Result<Boolean> checkVersionNumberAvailability(@RequestParam Long productLineId,
            @RequestParam String versionNumber, @RequestParam(required = false) Long excludeId) {
        boolean available = excludeId != null
                ? productVersionService.isVersionNumberAvailable(productLineId, versionNumber, excludeId)
                : productVersionService.isVersionNumberAvailable(productLineId, versionNumber);
        return Result.success(available);
    }

    /**
     * 建议下一个版本号
     */
    @GetMapping("/suggest-version-number")
    @PreAuthorize("hasPermission('product:version', 'read')")
    public Result<String> suggestNextVersionNumber(@RequestParam Long productLineId, @RequestParam String versionType) {
        String suggestion = productVersionService.suggestNextVersionNumber(productLineId, versionType);
        return Result.success(suggestion);
    }

    /**
     * 批量更新版本状态
     */
    @PostMapping("/batch/status")
    @PreAuthorize("hasPermission('product:version', 'update')")
    public Result<Void> updateVersionStatusBatch(@RequestParam @NotEmpty List<Long> ids,
            @RequestParam @NotNull String status, @RequestParam(required = false) String reason) {
        log.info("批量更新版本状态，数量: {}, 新状态: {}", ids.size(), status);
        productVersionService.updateVersionStatusBatch(ids, status, reason);
        return Result.success();
    }

    /**
     * 获取版本下载统计
     */
    @GetMapping("/{id}/download-stats")
    @PreAuthorize("hasPermission('product:version', 'read')")
    public Result<Object> getVersionDownloadStats(@PathVariable Long id,
            @RequestParam(defaultValue = "30") Integer days) {
        Object response = productVersionService.getVersionDownloadStats(id, days);
        return Result.success(response);
    }

    /**
     * 同步版本信息
     */
    @PostMapping("/sync/{productLineId}")
    @PreAuthorize("hasPermission('product:version', 'sync')")
    public Result<Void> syncVersionInfo(@PathVariable Long productLineId) {
        log.info("同步版本信息，产品线ID: {}", productLineId);
        productVersionService.syncVersionInfo(productLineId);
        return Result.success();
    }
}
