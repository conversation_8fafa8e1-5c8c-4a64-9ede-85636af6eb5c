package com.foxit.crm.modules.system.api.dto.response;

import lombok.Data;

/**
 * 产品线简单信息响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
public class ProductLineSimpleResponse {

    /**
     * 产品线ID
     */
    private Long id;

    /**
     * 产品线编码
     */
    private String code;

    /**
     * 产品线名称
     */
    private String name;

    /**
     * 产品线描述
     */
    private String description;

    /**
     * 产品线类型：1-阅读器，2-编辑器，3-云服务，4-工具类，5-内容平台
     */
    private Integer type;

    /**
     * 产品线类型描述
     */
    private String typeText;

    /**
     * 产品线状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 产品线状态描述
     */
    private String statusText;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 产品线图标
     */
    private String icon;

    /**
     * 产品线颜色
     */
    private String color;

    /**
     * 负责人姓名
     */
    private String ownerName;

    /**
     * 设置产品线类型文本
     */
    public void setType(Integer type) {
        this.type = type;
        if (type != null) {
            switch (type) {
                case 1:
                    this.typeText = "阅读器";
                    break;
                case 2:
                    this.typeText = "编辑器";
                    break;
                case 3:
                    this.typeText = "云服务";
                    break;
                case 4:
                    this.typeText = "工具类";
                    break;
                case 5:
                    this.typeText = "内容平台";
                    break;
                default:
                    this.typeText = "未知";
                    break;
            }
        }
    }

    /**
     * 设置状态文本
     */
    public void setStatus(Integer status) {
        this.status = status;
        this.statusText = status != null && status == 1 ? "启用" : "禁用";
    }
}
