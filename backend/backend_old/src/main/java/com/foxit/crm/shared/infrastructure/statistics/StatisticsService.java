package com.foxit.crm.shared.infrastructure.statistics;

import com.foxit.crm.shared.infrastructure.cache.StatisticsCacheManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 统计数据服务
 * 提供各种统计数据查询功能，集成缓存机制
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsService {

    private final StatisticsCacheManager cacheManager;

    /**
     * 获取每日操作统计
     */
    public Map<String, Object> getDailyOperationStatistics(LocalDate date) {
        return cacheManager.getDailyOperationStats(date, () -> {
            // 模拟从数据库查询每日操作统计
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalOperations", 1250L);
            stats.put("successOperations", 1200L);
            stats.put("failedOperations", 50L);
            stats.put("uniqueUsers", 85L);
            stats.put("peakHour", "14:00-15:00");
            stats.put("mostUsedModule", "系统管理");
            
            log.debug("从数据库查询每日操作统计: date={}", date);
            return stats;
        });
    }

    /**
     * 获取用户操作统计
     */
    public Map<String, Object> getUserOperationStatistics(Long userId, LocalDate startDate, LocalDate endDate) {
        return cacheManager.getUserOperationStats(userId, startDate, endDate, () -> {
            // 模拟从数据库查询用户操作统计
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalOperations", 156L);
            stats.put("loginCount", 12L);
            stats.put("avgSessionDuration", "45分钟");
            stats.put("favoriteModule", "产品线管理");
            stats.put("lastLoginTime", LocalDateTime.now().minusHours(2));
            
            log.debug("从数据库查询用户操作统计: userId={}, startDate={}, endDate={}", userId, startDate, endDate);
            return stats;
        });
    }

    /**
     * 获取系统访问统计
     */
    public Map<String, Object> getSystemAccessStatistics(LocalDate date) {
        return cacheManager.getSystemAccessStats(date, () -> {
            // 模拟从数据库查询系统访问统计
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalVisits", 2580L);
            stats.put("uniqueVisitors", 156L);
            stats.put("pageViews", 8750L);
            stats.put("avgSessionDuration", "28分钟");
            stats.put("bounceRate", "15.6%");
            
            // 按小时分布
            Map<String, Long> hourlyDistribution = new HashMap<>();
            for (int i = 0; i < 24; i++) {
                hourlyDistribution.put(String.format("%02d:00", i), (long) (Math.random() * 200 + 50));
            }
            stats.put("hourlyDistribution", hourlyDistribution);
            
            log.debug("从数据库查询系统访问统计: date={}", date);
            return stats;
        });
    }

    /**
     * 获取用户活跃度统计
     */
    public Map<String, Object> getUserActivityStatistics(LocalDate date) {
        return cacheManager.getUserActivityStats(date, () -> {
            // 模拟从数据库查询用户活跃度统计
            Map<String, Object> stats = new HashMap<>();
            stats.put("activeUsers", 89L);
            stats.put("newUsers", 5L);
            stats.put("returningUsers", 84L);
            stats.put("inactiveUsers", 12L);
            stats.put("activityRate", "88.1%");
            
            log.debug("从数据库查询用户活跃度统计: date={}", date);
            return stats;
        });
    }

    /**
     * 获取模块使用统计
     */
    public Map<String, Object> getModuleUsageStatistics(String module, LocalDate date) {
        return cacheManager.getModuleUsageStats(module, date, () -> {
            // 模拟从数据库查询模块使用统计
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalUsage", 456L);
            stats.put("uniqueUsers", 67L);
            stats.put("avgUsageTime", "12分钟");
            stats.put("popularFeatures", Arrays.asList("查询", "新增", "编辑"));
            
            log.debug("从数据库查询模块使用统计: module={}, date={}", module, date);
            return stats;
        });
    }

    /**
     * 获取API调用统计
     */
    public Map<String, Object> getApiCallStatistics(String apiPath, LocalDate date) {
        return cacheManager.getApiCallStats(apiPath, date, () -> {
            // 模拟从数据库查询API调用统计
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalCalls", 1890L);
            stats.put("successCalls", 1850L);
            stats.put("errorCalls", 40L);
            stats.put("avgResponseTime", "125ms");
            stats.put("maxResponseTime", "2.5s");
            stats.put("successRate", "97.9%");
            
            log.debug("从数据库查询API调用统计: apiPath={}, date={}", apiPath, date);
            return stats;
        });
    }

    /**
     * 获取错误统计
     */
    public Map<String, Object> getErrorStatistics(LocalDate date) {
        return cacheManager.getErrorStats(date, () -> {
            // 模拟从数据库查询错误统计
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalErrors", 23L);
            stats.put("criticalErrors", 2L);
            stats.put("warningErrors", 8L);
            stats.put("infoErrors", 13L);
            stats.put("errorRate", "1.8%");
            
            // 错误类型分布
            Map<String, Long> errorTypes = new HashMap<>();
            errorTypes.put("业务异常", 15L);
            errorTypes.put("系统异常", 5L);
            errorTypes.put("网络异常", 3L);
            stats.put("errorTypes", errorTypes);
            
            log.debug("从数据库查询错误统计: date={}", date);
            return stats;
        });
    }

    /**
     * 获取性能统计
     */
    public Map<String, Object> getPerformanceStatistics(LocalDate date) {
        return cacheManager.getPerformanceStats(date, () -> {
            // 模拟从数据库查询性能统计
            Map<String, Object> stats = new HashMap<>();
            stats.put("avgResponseTime", "156ms");
            stats.put("maxResponseTime", "3.2s");
            stats.put("minResponseTime", "12ms");
            stats.put("p95ResponseTime", "450ms");
            stats.put("p99ResponseTime", "1.2s");
            stats.put("throughput", "1250 req/min");
            
            log.debug("从数据库查询性能统计: date={}", date);
            return stats;
        });
    }

    /**
     * 获取实时统计数据
     */
    public Map<String, Object> getRealtimeStatistics(String type) {
        // 实时数据缓存时间较短（1分钟）
        return cacheManager.getRealtimeStats(type, 1, () -> {
            // 模拟实时统计数据
            Map<String, Object> stats = new HashMap<>();
            stats.put("onlineUsers", 45L);
            stats.put("currentRequests", 12L);
            stats.put("systemLoad", "65%");
            stats.put("memoryUsage", "72%");
            stats.put("cpuUsage", "38%");
            stats.put("timestamp", LocalDateTime.now());
            
            log.debug("查询实时统计数据: type={}", type);
            return stats;
        });
    }

    /**
     * 获取趋势分析数据
     */
    public List<Map<String, Object>> getTrendAnalysis(String type, LocalDate startDate, LocalDate endDate) {
        return cacheManager.getTrendAnalysis(type, startDate, endDate, () -> {
            // 模拟趋势分析数据
            List<Map<String, Object>> trendData = new ArrayList<>();
            
            LocalDate current = startDate;
            while (!current.isAfter(endDate)) {
                Map<String, Object> dayData = new HashMap<>();
                dayData.put("date", current);
                dayData.put("value", (long) (Math.random() * 1000 + 500));
                dayData.put("growth", Math.random() * 20 - 10); // -10% to +10%
                trendData.add(dayData);
                
                current = current.plusDays(1);
            }
            
            log.debug("查询趋势分析数据: type={}, startDate={}, endDate={}", type, startDate, endDate);
            return trendData;
        });
    }

    /**
     * 获取排行榜数据
     */
    public List<Map<String, Object>> getRankingData(String type, LocalDate date, int limit) {
        return cacheManager.getRankingData(type, date, limit, () -> {
            // 模拟排行榜数据
            List<Map<String, Object>> ranking = new ArrayList<>();
            
            for (int i = 1; i <= limit; i++) {
                Map<String, Object> item = new HashMap<>();
                item.put("rank", i);
                item.put("name", "用户" + i);
                item.put("value", (long) (Math.random() * 500 + 100));
                item.put("percentage", Math.random() * 100);
                ranking.add(item);
            }
            
            log.debug("查询排行榜数据: type={}, date={}, limit={}", type, date, limit);
            return ranking;
        });
    }

    /**
     * 获取汇总统计数据
     */
    public Map<String, Object> getSummaryStatistics(String type, String period) {
        return cacheManager.getSummaryStats(type, period, () -> {
            // 模拟汇总统计数据
            Map<String, Object> summary = new HashMap<>();
            summary.put("total", 15680L);
            summary.put("average", 523L);
            summary.put("maximum", 1250L);
            summary.put("minimum", 89L);
            summary.put("growth", "+12.5%");
            summary.put("period", period);
            
            log.debug("查询汇总统计数据: type={}, period={}", type, period);
            return summary;
        });
    }

    /**
     * 刷新统计缓存
     */
    public void refreshStatisticsCache(LocalDate date) {
        cacheManager.evictDailyStats(date);
        cacheManager.evictRealtimeStats("system");
        log.info("已刷新统计缓存: date={}", date);
    }

    /**
     * 清理过期统计缓存
     */
    public void cleanupExpiredStatistics() {
        LocalDate cutoffDate = LocalDate.now().minusDays(30);
        cacheManager.evictExpiredStats(cutoffDate);
        log.info("已清理过期统计缓存: cutoffDate={}", cutoffDate);
    }

    /**
     * 预热统计缓存
     */
    public void warmUpStatisticsCache() {
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        
        // 预热常用统计数据
        getDailyOperationStatistics(today);
        getDailyOperationStatistics(yesterday);
        getSystemAccessStatistics(today);
        getUserActivityStatistics(today);
        getRealtimeStatistics("system");
        
        log.info("统计缓存预热完成");
    }
}
