package com.foxit.crm.common.config;

import com.foxit.crm.shared.infrastructure.security.JwtAuthenticationFilter;
import com.foxit.crm.shared.infrastructure.security.CustomPermissionEvaluator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler;
import org.springframework.security.access.expression.method.MethodSecurityExpressionHandler;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfigurationSource;

/**
 * Spring Security配置
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

        @Autowired
        private JwtAuthenticationFilter jwtAuthenticationFilter;

        @Autowired
        private CorsConfigurationSource corsConfigurationSource;

        /**
         * 认证管理器
         */
        @Bean
        public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
                return config.getAuthenticationManager();
        }

        /**
         * 自定义权限评估器
         */
        @Bean
        public CustomPermissionEvaluator customPermissionEvaluator() {
                return new CustomPermissionEvaluator();
        }

        /**
         * 方法安全表达式处理器
         */
        @Bean
        public MethodSecurityExpressionHandler methodSecurityExpressionHandler() {
                DefaultMethodSecurityExpressionHandler expressionHandler = new DefaultMethodSecurityExpressionHandler();
                expressionHandler.setPermissionEvaluator(customPermissionEvaluator());
                return expressionHandler;
        }

        /**
         * 安全过滤器链
         */
        @Bean
        public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
                http
                                // 禁用CSRF
                                .csrf(AbstractHttpConfigurer::disable)

                                // 配置CORS
                                .cors(cors -> cors.configurationSource(corsConfigurationSource))

                                // 配置会话管理
                                .sessionManagement(session -> session
                                                .sessionCreationPolicy(SessionCreationPolicy.STATELESS))

                                // 配置请求授权
                                .authorizeHttpRequests(auth -> auth
                                                // 允许访问的路径（注意：由于context-path=/api，这里的路径不需要/api前缀）
                                                .requestMatchers(
                                                                "/auth/login", // 登录接口
                                                                "/auth/logout", // 登出接口
                                                                "/auth/health", // 健康检查
                                                                "/health",
                                                                "/health/**",
                                                                "/doc/**",
                                                                "/static/doc/**",
                                                                "/favicon.ico",
                                                                "/error",
                                                                "/dashboard/test", // 临时允许Dashboard测试接口
                                                                "/dashboard/simple-test", // 简单测试接口
                                                                "/dashboard/config", // 临时允许Dashboard配置接口
                                                                "/dashboard/overview", // 临时允许Dashboard总览接口
                                                                "/user/active/**", // 临时允许用户活跃分析接口
                                                                "/public/**", // 公共接口，无需认证
                                                                "/behavior/event/public/**", // 公共事件分析接口
                                                                "/behavior/feature/public/**", // 公共功能使用分析接口
                                                                "/behavior/funnel/public/**", // 公共漏斗分析接口
                                                                "/behavior/path/public/**", // 公共路径分析接口
                                                                "/user/growth/public/**", // 公共用户增长分析接口
                                                                "/user/active/public/**", // 公共活跃用户分析接口
                                                                "/activity/campaign/public/**", // 公共活动分析接口
                                                                "/private-domain/fan/public/**", // 公共粉丝分析接口
                                                                "/admin/operation-logs/public", // 公共操作日志接口
                                                                "/test") // 基础测试接口
                                                .permitAll()

                                                // 管理员才能访问的路径
                                                .requestMatchers(
                                                                "/auth/register", // 注册接口需要管理员权限
                                                                "/admin/**") // 管理员接口
                                                .hasAuthority("ADMIN")

                                                // 其他请求需要认证
                                                .anyRequest().authenticated())

                                // 添加JWT过滤器
                                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

                return http.build();
        }
}
