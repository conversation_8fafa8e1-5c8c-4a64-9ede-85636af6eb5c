package com.foxit.crm.modules.system.application.service;

import com.foxit.crm.modules.system.api.dto.request.PermissionCreateRequest;
import com.foxit.crm.modules.system.api.dto.request.PermissionUpdateRequest;
import com.foxit.crm.modules.system.api.dto.response.PermissionDetailResponse;
import com.foxit.crm.modules.system.api.dto.response.PermissionTreeResponse;
import com.foxit.crm.modules.system.api.dto.response.PageResponse;

import java.util.List;

/**
 * 权限应用服务接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface PermissionService {

    /**
     * 创建权限
     */
    Long createPermission(PermissionCreateRequest request);

    /**
     * 更新权限
     */
    void updatePermission(Long id, PermissionUpdateRequest request);

    /**
     * 删除权限
     */
    void deletePermission(Long id);

    /**
     * 根据ID获取权限详情
     */
    PermissionDetailResponse getPermissionById(Long id);

    /**
     * 分页查询权限列表
     */
    PageResponse<PermissionDetailResponse> getPermissionList(int page, int size, String keyword,
            Integer permissionType);

    /**
     * 获取权限树结构
     */
    List<PermissionTreeResponse> getPermissionTree();

    /**
     * 根据父权限ID获取子权限列表
     */
    List<PermissionDetailResponse> getPermissionsByParentId(Long parentId);

    /**
     * 根据权限类型获取权限列表
     */
    List<PermissionDetailResponse> getPermissionsByType(Integer permissionType);

    /**
     * 根据用户ID获取权限列表
     */
    List<PermissionDetailResponse> getPermissionsByUserId(Long userId);

    /**
     * 根据角色ID获取权限列表
     */
    List<PermissionDetailResponse> getPermissionsByRoleId(Long roleId);

    /**
     * 启用权限
     */
    void enablePermission(Long id);

    /**
     * 禁用权限
     */
    void disablePermission(Long id);

    /**
     * 获取所有启用的菜单权限（用于前端菜单构建）
     */
    List<PermissionTreeResponse> getEnabledMenuPermissions();

    /**
     * 根据权限ID列表获取权限详情
     */
    List<PermissionDetailResponse> getPermissionsByIds(List<Long> ids);
}
