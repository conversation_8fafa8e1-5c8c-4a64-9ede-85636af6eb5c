package com.foxit.crm.shared.infrastructure.cache;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 分布式缓存服务
 * 基于Redisson实现分布式锁、缓存预热等高级功能
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DistributedCacheService {

    private final RedissonClient redissonClient;

    // ============================
    // 分布式锁功能
    // ============================

    /**
     * 获取分布式锁
     */
    public RLock getLock(String lockKey) {
        return redissonClient.getLock(lockKey);
    }

    /**
     * 尝试获取锁
     */
    public boolean tryLock(String lockKey, long waitTime, long leaseTime, TimeUnit unit) {
        RLock lock = getLock(lockKey);
        try {
            return lock.tryLock(waitTime, leaseTime, unit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取分布式锁被中断: {}", lockKey, e);
            return false;
        }
    }

    /**
     * 释放锁
     */
    public void unlock(String lockKey) {
        RLock lock = getLock(lockKey);
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    /**
     * 执行带锁的操作
     */
    public <T> T executeWithLock(String lockKey, long waitTime, long leaseTime, TimeUnit unit,
            java.util.function.Supplier<T> supplier) {
        RLock lock = getLock(lockKey);
        try {
            if (lock.tryLock(waitTime, leaseTime, unit)) {
                try {
                    return supplier.get();
                } finally {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                throw new RuntimeException("获取分布式锁失败: " + lockKey);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("获取分布式锁被中断: " + lockKey, e);
        }
    }

    // ============================
    // 分布式集合功能
    // ============================

    /**
     * 获取分布式Set
     */
    public <T> RSet<T> getSet(String key) {
        return redissonClient.getSet(key);
    }

    /**
     * 获取分布式List
     */
    public <T> RList<T> getList(String key) {
        return redissonClient.getList(key);
    }

    /**
     * 获取分布式Map
     */
    public <K, V> RMap<K, V> getMap(String key) {
        return redissonClient.getMap(key);
    }

    /**
     * 获取分布式Queue
     */
    public <T> RQueue<T> getQueue(String key) {
        return redissonClient.getQueue(key);
    }

    // ============================
    // 发布订阅功能
    // ============================

    /**
     * 获取发布订阅主题
     */
    public RTopic getTopic(String topicName) {
        return redissonClient.getTopic(topicName);
    }

    /**
     * 发布消息
     */
    public long publish(String topicName, Object message) {
        RTopic topic = getTopic(topicName);
        return topic.publish(message);
    }

    /**
     * 订阅消息
     */
    public void subscribe(String topicName, org.redisson.api.listener.MessageListener<Object> listener) {
        RTopic topic = getTopic(topicName);
        topic.addListener(Object.class, listener);
    }

    // ============================
    // 限流功能
    // ============================

    /**
     * 获取限流器
     */
    public RRateLimiter getRateLimiter(String key) {
        return redissonClient.getRateLimiter(key);
    }

    /**
     * 设置限流规则
     */
    public void setRateLimit(String key, RateType rateType, long rate, long rateInterval,
            RateIntervalUnit rateIntervalUnit) {
        RRateLimiter rateLimiter = getRateLimiter(key);
        rateLimiter.trySetRate(rateType, rate, rateInterval, rateIntervalUnit);
    }

    /**
     * 尝试获取许可
     */
    public boolean tryAcquire(String key, long permits) {
        RRateLimiter rateLimiter = getRateLimiter(key);
        return rateLimiter.tryAcquire(permits);
    }

    // ============================
    // 分布式信号量
    // ============================

    /**
     * 获取分布式信号量
     */
    public RSemaphore getSemaphore(String key) {
        return redissonClient.getSemaphore(key);
    }

    /**
     * 设置信号量许可数
     */
    public void setSemaphorePermits(String key, int permits) {
        RSemaphore semaphore = getSemaphore(key);
        semaphore.trySetPermits(permits);
    }

    /**
     * 尝试获取信号量许可
     */
    public boolean tryAcquireSemaphore(String key, int permits, long waitTime, TimeUnit unit) {
        RSemaphore semaphore = getSemaphore(key);
        try {
            return semaphore.tryAcquire(permits, waitTime, unit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取信号量许可被中断: {}", key, e);
            return false;
        }
    }

    /**
     * 释放信号量许可
     */
    public void releaseSemaphore(String key, int permits) {
        RSemaphore semaphore = getSemaphore(key);
        semaphore.release(permits);
    }

    // ============================
    // 分布式计数器
    // ============================

    /**
     * 获取分布式原子长整型
     */
    public RAtomicLong getAtomicLong(String key) {
        return redissonClient.getAtomicLong(key);
    }

    /**
     * 原子递增
     */
    public long incrementAndGet(String key) {
        return getAtomicLong(key).incrementAndGet();
    }

    /**
     * 原子递减
     */
    public long decrementAndGet(String key) {
        return getAtomicLong(key).decrementAndGet();
    }

    /**
     * 原子加法
     */
    public long addAndGet(String key, long delta) {
        return getAtomicLong(key).addAndGet(delta);
    }

    // ============================
    // 缓存预热功能
    // ============================

    /**
     * 缓存预热锁前缀
     */
    private static final String CACHE_WARM_UP_LOCK_PREFIX = "cache:warmup:lock:";

    /**
     * 执行缓存预热
     */
    public void warmUpCache(String cacheKey, Runnable warmUpTask) {
        String lockKey = CACHE_WARM_UP_LOCK_PREFIX + cacheKey;

        executeWithLock(lockKey, 1, 300, TimeUnit.SECONDS, () -> {
            log.info("开始缓存预热: {}", cacheKey);
            try {
                warmUpTask.run();
                log.info("缓存预热完成: {}", cacheKey);
            } catch (Exception e) {
                log.error("缓存预热失败: {}", cacheKey, e);
                throw new RuntimeException("缓存预热失败", e);
            }
            return null;
        });
    }

    /**
     * 异步缓存预热（简化版，避免序列化问题）
     */
    public void warmUpCacheAsync(String cacheKey, Runnable warmUpTask) {
        String lockKey = CACHE_WARM_UP_LOCK_PREFIX + cacheKey;

        RLock lock = getLock(lockKey);
        if (lock.tryLock()) {
            try {
                // 使用CompletableFuture替代Redisson ExecutorService避免序列化问题
                java.util.concurrent.CompletableFuture.runAsync(() -> {
                    try {
                        log.info("开始异步缓存预热: {}", cacheKey);
                        warmUpTask.run();
                        log.info("异步缓存预热完成: {}", cacheKey);
                    } catch (Exception e) {
                        log.error("异步缓存预热失败: {}", cacheKey, e);
                    } finally {
                        if (lock.isHeldByCurrentThread()) {
                            lock.unlock();
                        }
                    }
                });
            } catch (Exception e) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
                throw e;
            }
        } else {
            log.debug("缓存预热任务已在执行中: {}", cacheKey);
        }
    }

    // ============================
    // 缓存监控功能
    // ============================

    /**
     * 获取缓存统计信息
     */
    public void logCacheStats() {
        try {
            // 获取Redis信息
            RKeys keys = redissonClient.getKeys();
            long keyCount = keys.count();

            log.info("Redis缓存统计 - 总键数: {}", keyCount);

            // 可以添加更多统计信息
            // 如内存使用情况、命中率等
        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
        }
    }
}
