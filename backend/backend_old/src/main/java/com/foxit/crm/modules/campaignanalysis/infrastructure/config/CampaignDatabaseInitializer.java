package com.foxit.crm.modules.campaignanalysis.infrastructure.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

/**
 * 活动分析数据库初始化器
 * 在应用启动时创建活动分析相关的表和数据
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CampaignDatabaseInitializer implements CommandLineRunner {

    private final JdbcTemplate mysqlJdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始初始化活动分析数据库表...");
        
        try {
            // 创建营销活动表
            createMarketingCampaignsTable();
            
            // 创建活动效果统计表
            createCampaignStatisticsTable();
            
            // 插入模拟数据
            insertMockData();
            
            log.info("活动分析数据库表初始化完成");
            
        } catch (Exception e) {
            log.error("活动分析数据库表初始化失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建营销活动表
     */
    private void createMarketingCampaignsTable() {
        String sql = """
                CREATE TABLE IF NOT EXISTS marketing_campaigns (
                    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '活动ID',
                    campaign_name VARCHAR(255) NOT NULL COMMENT '活动名称',
                    campaign_type VARCHAR(50) NOT NULL COMMENT '活动类型',
                    campaign_description TEXT COMMENT '活动描述',
                    status VARCHAR(20) NOT NULL DEFAULT 'draft' COMMENT '活动状态',
                    budget DECIMAL(15,2) COMMENT '活动预算',
                    actual_cost DECIMAL(15,2) DEFAULT 0 COMMENT '实际花费',
                    target_audience TEXT COMMENT '目标受众描述',
                    channels JSON COMMENT '投放渠道',
                    start_time DATETIME COMMENT '活动开始时间',
                    end_time DATETIME COMMENT '活动结束时间',
                    created_by BIGINT COMMENT '创建人ID',
                    updated_by BIGINT COMMENT '更新人ID',
                    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    deleted TINYINT DEFAULT 0 COMMENT '是否删除',
                    version INT DEFAULT 0 COMMENT '版本号',
                    
                    INDEX idx_campaign_type (campaign_type),
                    INDEX idx_status (status),
                    INDEX idx_start_time (start_time),
                    INDEX idx_end_time (end_time),
                    INDEX idx_create_time (create_time)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='营销活动表'
                """;
        
        mysqlJdbcTemplate.execute(sql);
        log.info("营销活动表创建成功");
    }

    /**
     * 创建活动效果统计表
     */
    private void createCampaignStatisticsTable() {
        String sql = """
                CREATE TABLE IF NOT EXISTS campaign_statistics (
                    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '统计ID',
                    campaign_id BIGINT NOT NULL COMMENT '活动ID',
                    stat_date DATE NOT NULL COMMENT '统计日期',
                    impressions BIGINT DEFAULT 0 COMMENT '曝光量',
                    clicks BIGINT DEFAULT 0 COMMENT '点击量',
                    conversions BIGINT DEFAULT 0 COMMENT '转化数',
                    participants BIGINT DEFAULT 0 COMMENT '参与人数',
                    new_users BIGINT DEFAULT 0 COMMENT '新增用户数',
                    revenue DECIMAL(15,2) DEFAULT 0 COMMENT '收入',
                    cost DECIMAL(15,2) DEFAULT 0 COMMENT '当日花费',
                    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    
                    UNIQUE KEY uk_campaign_date (campaign_id, stat_date),
                    INDEX idx_campaign_id (campaign_id),
                    INDEX idx_stat_date (stat_date)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动效果统计表'
                """;
        
        mysqlJdbcTemplate.execute(sql);
        log.info("活动效果统计表创建成功");
    }

    /**
     * 插入模拟数据
     */
    private void insertMockData() {
        // 检查是否已有数据
        Integer count = mysqlJdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM marketing_campaigns", Integer.class);
        
        if (count != null && count > 0) {
            log.info("活动数据已存在，跳过数据插入");
            return;
        }

        // 插入营销活动数据
        String campaignSql = """
                INSERT INTO marketing_campaigns (id, campaign_name, campaign_type, campaign_description, status, budget, actual_cost, target_audience, channels, start_time, end_time, created_by, create_time) VALUES
                (1, '夏季促销活动', 'promotion', '夏季大促销，全场商品8折优惠', 'completed', 50000.00, 45000.00, '全体用户', '["email", "sms", "push", "social"]', '2025-07-01 00:00:00', '2025-07-31 23:59:59', 1, '2025-06-25 10:00:00'),
                (2, '新产品发布会', 'product', '新版本产品发布推广活动', 'active', 80000.00, 35000.00, '潜在客户和现有用户', '["social", "ads", "email"]', '2025-07-15 00:00:00', '2025-08-15 23:59:59', 1, '2025-07-10 14:30:00'),
                (3, '品牌形象推广', 'brand', '提升品牌知名度和影响力', 'active', 120000.00, 60000.00, '目标市场用户', '["ads", "social", "content"]', '2025-07-01 00:00:00', '2025-09-30 23:59:59', 1, '2025-06-20 09:15:00'),
                (4, '用户回馈活动', 'user', '感谢老用户的支持，专属优惠', 'paused', 30000.00, 15000.00, '老用户', '["email", "push"]', '2025-07-20 00:00:00', '2025-08-20 23:59:59', 1, '2025-07-15 16:45:00'),
                (5, '中秋节营销', 'holiday', '中秋节主题营销活动', 'draft', 40000.00, 0.00, '全体用户', '["email", "sms", "social"]', '2025-09-01 00:00:00', '2025-09-30 23:59:59', 1, '2025-07-25 11:20:00'),
                (6, '双十一预热', 'promotion', '双十一购物节预热活动', 'draft', 200000.00, 0.00, '购物用户', '["ads", "social", "email", "sms"]', '2025-10-20 00:00:00', '2025-11-11 23:59:59', 1, '2025-07-30 13:10:00')
                """;
        
        mysqlJdbcTemplate.execute(campaignSql);
        log.info("营销活动数据插入成功");

        // 插入活动效果统计数据
        String statsSql = """
                INSERT INTO campaign_statistics (campaign_id, stat_date, impressions, clicks, conversions, participants, new_users, revenue, cost) VALUES
                (1, '2025-07-01', 50000, 2500, 125, 800, 50, 12500.00, 1500.00),
                (1, '2025-07-02', 52000, 2600, 130, 850, 55, 13000.00, 1550.00),
                (1, '2025-07-03', 48000, 2400, 120, 780, 45, 12000.00, 1450.00),
                (1, '2025-07-04', 55000, 2750, 138, 900, 60, 13800.00, 1600.00),
                (1, '2025-07-05', 60000, 3000, 150, 950, 65, 15000.00, 1700.00),
                (2, '2025-07-15', 80000, 4000, 200, 1200, 80, 20000.00, 2000.00),
                (2, '2025-07-16', 85000, 4250, 213, 1300, 85, 21300.00, 2100.00),
                (2, '2025-07-17', 78000, 3900, 195, 1150, 75, 19500.00, 1950.00),
                (2, '2025-07-18', 90000, 4500, 225, 1400, 90, 22500.00, 2200.00),
                (2, '2025-07-19', 95000, 4750, 238, 1500, 95, 23800.00, 2300.00),
                (3, '2025-07-01', 120000, 3600, 180, 2000, 100, 18000.00, 3000.00),
                (3, '2025-07-02', 125000, 3750, 188, 2100, 105, 18800.00, 3100.00),
                (3, '2025-07-03', 118000, 3540, 177, 1950, 98, 17700.00, 2950.00),
                (3, '2025-07-04', 130000, 3900, 195, 2200, 110, 19500.00, 3200.00),
                (3, '2025-07-05', 135000, 4050, 203, 2300, 115, 20300.00, 3300.00),
                (4, '2025-07-20', 25000, 1250, 63, 500, 25, 6300.00, 800.00),
                (4, '2025-07-21', 28000, 1400, 70, 550, 28, 7000.00, 850.00),
                (4, '2025-07-22', 22000, 1100, 55, 450, 22, 5500.00, 750.00),
                (4, '2025-07-23', 30000, 1500, 75, 600, 30, 7500.00, 900.00)
                """;
        
        mysqlJdbcTemplate.execute(statsSql);
        log.info("活动效果统计数据插入成功");
    }
}
