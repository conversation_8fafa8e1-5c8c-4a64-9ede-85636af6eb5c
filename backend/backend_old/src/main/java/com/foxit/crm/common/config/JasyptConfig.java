package com.foxit.crm.common.config;

import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.jasypt.encryption.StringEncryptor;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Jasypt 配置加密配置类
 * <p>
 * 提供自定义的Jasypt加密器配置，增强应用程序的安全性。
 * 支持通过环境变量或系统属性获取主密钥。
 * </p>
 *
 * <AUTHOR> Dev Team
 */
@Configuration
@EnableEncryptableProperties
public class JasyptConfig {
    /**
     * 主密钥，优先从环境变量FX_MASTER_KEY获取，
     * 如未设置则使用默认值（不推荐在生产环境使用）
     */
    @Value("${jasypt.encryptor.password:scrm_default_key}")
    private String password;

    /**
     * 加密算法
     */
    @Value("${jasypt.encryptor.algorithm:PBEWithMD5AndDES}")
    private String algorithm;

    /**
     * 密钥获取迭代次数
     */
    @Value("${jasypt.encryptor.key-obtention-iterations:1000}")
    private Integer keyObtentionIterations;

    /**
     * 加密器池大小
     */
    @Value("${jasypt.encryptor.pool-size:1}")
    private Integer poolSize;

    /**
     * 字符串输出类型
     */
    @Value("${jasypt.encryptor.string-output-type:base64}")
    private String stringOutputType;

    /**
     * 配置Jasypt加密器
     *
     * @return 自定义配置的StringEncryptor实例
     */
    @Bean("jasyptStringEncryptor")
    @Primary
    public StringEncryptor stringEncryptor() {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();

        // 设置密码
        config.setPassword(password);
        // 设置算法
        config.setAlgorithm(algorithm);
        // 设置密钥获取迭代次数
        config.setKeyObtentionIterations(keyObtentionIterations);
        // 设置加密器池大小
        config.setPoolSize(poolSize);
        // 设置提供者名称
        config.setProviderName("SunJCE");
        // 设置盐生成器
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        // 设置IV生成器
        config.setIvGeneratorClassName("org.jasypt.iv.NoIvGenerator");
        // 设置输出类型
        config.setStringOutputType(stringOutputType);

        encryptor.setConfig(config);
        return encryptor;
    }
}
