package com.foxit.crm.modules.system.domain.repository;

import com.foxit.crm.common.util.PageResult;
import com.foxit.crm.modules.system.domain.model.aggregate.User;
import com.foxit.crm.modules.system.domain.model.valueobject.UserId;
import com.foxit.crm.modules.system.domain.model.valueobject.Username;

import java.util.Optional;

/**
 * 用户仓储接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface UserRepository {

    /**
     * 根据ID查找用户
     *
     * @param userId 用户ID
     * @return 用户
     */
    Optional<User> findById(UserId userId);

    /**
     * 根据用户名查找用户
     *
     * @param username 用户名
     * @return 用户
     */
    Optional<User> findByUsername(Username username);

    /**
     * 保存用户
     *
     * @param user 用户
     * @return 保存后的用户
     */
    User save(User user);

    /**
     * 删除用户
     *
     * @param userId 用户ID
     */
    void deleteById(UserId userId);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(Username username);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 分页查询用户列表
     *
     * @param page    页码
     * @param size    每页大小
     * @param keyword 关键词
     * @return 分页结果
     */
    PageResult<User> findByPage(int page, int size, String keyword);
}
