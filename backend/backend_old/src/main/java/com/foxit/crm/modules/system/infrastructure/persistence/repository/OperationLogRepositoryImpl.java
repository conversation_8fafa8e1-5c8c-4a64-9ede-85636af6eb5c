package com.foxit.crm.modules.system.infrastructure.persistence.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.foxit.crm.modules.system.domain.model.aggregate.OperationLog;
import com.foxit.crm.modules.system.domain.repository.OperationLogRepository;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.OperationLogPO;
import com.foxit.crm.modules.system.infrastructure.persistence.mapper.OperationLogMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 系统操作日志仓储实现
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Repository
@RequiredArgsConstructor
public class OperationLogRepositoryImpl implements OperationLogRepository {

    private final OperationLogMapper operationLogMapper;

    @Override
    public OperationLog save(OperationLog operationLog) {
        OperationLogPO operationLogPO = new OperationLogPO();
        BeanUtils.copyProperties(operationLog, operationLogPO);
        
        if (operationLog.getId() == null) {
            operationLogMapper.insert(operationLogPO);
            operationLog.setId(operationLogPO.getId());
        } else {
            operationLogMapper.updateById(operationLogPO);
        }
        
        return operationLog;
    }

    @Override
    public Optional<OperationLog> findById(Long id) {
        OperationLogPO operationLogPO = operationLogMapper.selectById(id);
        if (operationLogPO == null) {
            return Optional.empty();
        }
        
        OperationLog operationLog = new OperationLog();
        BeanUtils.copyProperties(operationLogPO, operationLog);
        return Optional.of(operationLog);
    }

    @Override
    public List<OperationLog> findByPage(int page, int size, String keyword, Integer status, 
                                        LocalDateTime startTime, LocalDateTime endTime, Long userId) {
        Page<OperationLogPO> pageParam = new Page<>(page, size);
        QueryWrapper<OperationLogPO> queryWrapper = buildQueryWrapper(keyword, status, startTime, endTime, userId);
        queryWrapper.orderByDesc("operation_time");
        
        Page<OperationLogPO> result = operationLogMapper.selectPage(pageParam, queryWrapper);
        return result.getRecords().stream()
                    .map(this::convertToOperationLog)
                    .collect(Collectors.toList());
    }

    @Override
    public long count(String keyword, Integer status, LocalDateTime startTime, LocalDateTime endTime, Long userId) {
        QueryWrapper<OperationLogPO> queryWrapper = buildQueryWrapper(keyword, status, startTime, endTime, userId);
        return operationLogMapper.selectCount(queryWrapper);
    }

    @Override
    public List<OperationLog> findByUserId(Long userId, int page, int size) {
        Page<OperationLogPO> pageParam = new Page<>(page, size);
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .orderByDesc("operation_time");
        
        Page<OperationLogPO> result = operationLogMapper.selectPage(pageParam, queryWrapper);
        return result.getRecords().stream()
                    .map(this::convertToOperationLog)
                    .collect(Collectors.toList());
    }

    @Override
    public List<OperationLog> findByOperation(String operation, int page, int size) {
        Page<OperationLogPO> pageParam = new Page<>(page, size);
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation", operation)
                   .orderByDesc("operation_time");
        
        Page<OperationLogPO> result = operationLogMapper.selectPage(pageParam, queryWrapper);
        return result.getRecords().stream()
                    .map(this::convertToOperationLog)
                    .collect(Collectors.toList());
    }

    @Override
    public List<OperationLog> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime, int page, int size) {
        Page<OperationLogPO> pageParam = new Page<>(page, size);
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        
        if (startTime != null) {
            queryWrapper.ge("operation_time", startTime);
        }
        if (endTime != null) {
            queryWrapper.le("operation_time", endTime);
        }
        
        queryWrapper.orderByDesc("operation_time");
        
        Page<OperationLogPO> result = operationLogMapper.selectPage(pageParam, queryWrapper);
        return result.getRecords().stream()
                    .map(this::convertToOperationLog)
                    .collect(Collectors.toList());
    }

    @Override
    public List<OperationLog> findFailedOperations(int page, int size) {
        Page<OperationLogPO> pageParam = new Page<>(page, size);
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 0)
                   .orderByDesc("operation_time");
        
        Page<OperationLogPO> result = operationLogMapper.selectPage(pageParam, queryWrapper);
        return result.getRecords().stream()
                    .map(this::convertToOperationLog)
                    .collect(Collectors.toList());
    }

    @Override
    public List<OperationLog> findSlowOperations(Long minExecutionTime, int page, int size) {
        Page<OperationLogPO> pageParam = new Page<>(page, size);
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("execution_time", minExecutionTime)
                   .orderByDesc("execution_time");
        
        Page<OperationLogPO> result = operationLogMapper.selectPage(pageParam, queryWrapper);
        return result.getRecords().stream()
                    .map(this::convertToOperationLog)
                    .collect(Collectors.toList());
    }

    @Override
    public List<OperationLog> findSensitiveOperations(int page, int size) {
        Page<OperationLogPO> pageParam = new Page<>(page, size);
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        
        // 查找敏感操作
        queryWrapper.and(wrapper -> wrapper
            .like("operation", "DELETE")
            .or().like("operation", "CREATE")
            .or().like("operation", "UPDATE")
            .or().like("operation", "LOGIN")
            .or().like("operation", "LOGOUT")
            .or().like("operation", "ASSIGN")
            .or().like("operation", "REMOVE")
            .or().like("operation", "ENABLE")
            .or().like("operation", "DISABLE")
            .or().like("operation", "RESET")
        );
        
        queryWrapper.orderByDesc("operation_time");
        
        Page<OperationLogPO> result = operationLogMapper.selectPage(pageParam, queryWrapper);
        return result.getRecords().stream()
                    .map(this::convertToOperationLog)
                    .collect(Collectors.toList());
    }

    @Override
    public List<OperationLog> findByClientIp(String clientIp, int page, int size) {
        Page<OperationLogPO> pageParam = new Page<>(page, size);
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("client_ip", clientIp)
                   .orderByDesc("operation_time");
        
        Page<OperationLogPO> result = operationLogMapper.selectPage(pageParam, queryWrapper);
        return result.getRecords().stream()
                    .map(this::convertToOperationLog)
                    .collect(Collectors.toList());
    }

    @Override
    public void deleteByCreateTimeBefore(LocalDateTime beforeTime) {
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lt("create_time", beforeTime);
        operationLogMapper.delete(queryWrapper);
    }

    @Override
    public long countByUserId(Long userId) {
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        return operationLogMapper.selectCount(queryWrapper);
    }

    @Override
    public long countByOperation(String operation) {
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation", operation);
        return operationLogMapper.selectCount(queryWrapper);
    }

    @Override
    public long countByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        
        if (startTime != null) {
            queryWrapper.ge("operation_time", startTime);
        }
        if (endTime != null) {
            queryWrapper.le("operation_time", endTime);
        }
        
        return operationLogMapper.selectCount(queryWrapper);
    }

    @Override
    public long countFailedOperations() {
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 0);
        return operationLogMapper.selectCount(queryWrapper);
    }

    @Override
    public long countSuccessOperations() {
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        return operationLogMapper.selectCount(queryWrapper);
    }

    @Override
    public List<OperationLog> findRecentOperations(int limit) {
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("operation_time")
                   .last("LIMIT " + limit);
        
        List<OperationLogPO> operationLogPOs = operationLogMapper.selectList(queryWrapper);
        return operationLogPOs.stream()
                             .map(this::convertToOperationLog)
                             .collect(Collectors.toList());
    }

    @Override
    public List<OperationLog> findRecentOperationsByUserId(Long userId, int limit) {
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .orderByDesc("operation_time")
                   .last("LIMIT " + limit);
        
        List<OperationLogPO> operationLogPOs = operationLogMapper.selectList(queryWrapper);
        return operationLogPOs.stream()
                             .map(this::convertToOperationLog)
                             .collect(Collectors.toList());
    }

    @Override
    public List<OperationLog> getOperationStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        
        if (startTime != null) {
            queryWrapper.ge("operation_time", startTime);
        }
        if (endTime != null) {
            queryWrapper.le("operation_time", endTime);
        }
        
        queryWrapper.select("operation", "COUNT(*) as count")
                   .groupBy("operation")
                   .orderByDesc("count");
        
        List<OperationLogPO> operationLogPOs = operationLogMapper.selectList(queryWrapper);
        return operationLogPOs.stream()
                             .map(this::convertToOperationLog)
                             .collect(Collectors.toList());
    }

    @Override
    public List<OperationLog> getUserOperationStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        
        if (startTime != null) {
            queryWrapper.ge("operation_time", startTime);
        }
        if (endTime != null) {
            queryWrapper.le("operation_time", endTime);
        }
        
        queryWrapper.select("user_id", "username", "COUNT(*) as count")
                   .groupBy("user_id", "username")
                   .orderByDesc("count");
        
        List<OperationLogPO> operationLogPOs = operationLogMapper.selectList(queryWrapper);
        return operationLogPOs.stream()
                             .map(this::convertToOperationLog)
                             .collect(Collectors.toList());
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper<OperationLogPO> buildQueryWrapper(String keyword, Integer status, 
                                                          LocalDateTime startTime, LocalDateTime endTime, Long userId) {
        QueryWrapper<OperationLogPO> queryWrapper = new QueryWrapper<>();
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                .like("title", keyword)
                .or().like("operation", keyword)
                .or().like("username", keyword)
                .or().like("client_ip", keyword)
            );
        }
        
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        
        if (startTime != null) {
            queryWrapper.ge("operation_time", startTime);
        }
        
        if (endTime != null) {
            queryWrapper.le("operation_time", endTime);
        }
        
        if (userId != null) {
            queryWrapper.eq("user_id", userId);
        }
        
        return queryWrapper;
    }

    /**
     * 转换PO为领域对象
     */
    private OperationLog convertToOperationLog(OperationLogPO operationLogPO) {
        OperationLog operationLog = new OperationLog();
        BeanUtils.copyProperties(operationLogPO, operationLog);
        return operationLog;
    }
}
