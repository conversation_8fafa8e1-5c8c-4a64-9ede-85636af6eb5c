package com.foxit.crm.modules.system.application.service;

import com.foxit.crm.modules.system.api.dto.request.SystemConfigCreateRequest;
import com.foxit.crm.modules.system.api.dto.request.SystemConfigUpdateRequest;
import com.foxit.crm.modules.system.api.dto.response.SystemConfigDetailResponse;
import com.foxit.crm.modules.system.api.dto.response.PageResponse;

import java.util.List;
import java.util.Map;

/**
 * 系统配置应用服务接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface SystemConfigService {

    /**
     * 创建系统配置
     */
    Long createSystemConfig(SystemConfigCreateRequest request);

    /**
     * 更新系统配置
     */
    void updateSystemConfig(Long id, SystemConfigUpdateRequest request);

    /**
     * 删除系统配置
     */
    void deleteSystemConfig(Long id);

    /**
     * 根据ID获取系统配置详情
     */
    SystemConfigDetailResponse getSystemConfigById(Long id);

    /**
     * 根据配置键获取系统配置详情
     */
    SystemConfigDetailResponse getSystemConfigByKey(String configKey);

    /**
     * 分页查询系统配置列表
     */
    PageResponse<SystemConfigDetailResponse> getSystemConfigList(int page, int size, String keyword, String configGroup,
            Integer status);

    /**
     * 根据配置分组获取系统配置列表
     */
    List<SystemConfigDetailResponse> getSystemConfigsByGroup(String configGroup);

    /**
     * 获取所有启用的系统配置
     */
    List<SystemConfigDetailResponse> getAllEnabledSystemConfigs();

    /**
     * 获取系统内置配置
     */
    List<SystemConfigDetailResponse> getSystemConfigs();

    /**
     * 获取用户自定义配置
     */
    List<SystemConfigDetailResponse> getUserConfigs();

    /**
     * 启用系统配置
     */
    void enableSystemConfig(Long id);

    /**
     * 禁用系统配置
     */
    void disableSystemConfig(Long id);

    /**
     * 批量启用系统配置
     */
    void enableSystemConfigsBatch(List<Long> ids);

    /**
     * 批量禁用系统配置
     */
    void disableSystemConfigsBatch(List<Long> ids);

    /**
     * 获取所有配置分组
     */
    List<String> getAllConfigGroups();

    /**
     * 检查配置键是否可用
     */
    boolean isConfigKeyAvailable(String configKey);

    /**
     * 检查配置键是否可用（排除指定ID）
     */
    boolean isConfigKeyAvailable(String configKey, Long excludeId);

    /**
     * 根据配置键获取配置值
     */
    String getConfigValue(String configKey);

    /**
     * 根据配置键获取字符串值
     */
    String getStringValue(String configKey);

    /**
     * 根据配置键获取整数值
     */
    Integer getIntValue(String configKey);

    /**
     * 根据配置键获取长整数值
     */
    Long getLongValue(String configKey);

    /**
     * 根据配置键获取双精度值
     */
    Double getDoubleValue(String configKey);

    /**
     * 根据配置键获取布尔值
     */
    Boolean getBooleanValue(String configKey);

    /**
     * 根据配置键设置配置值
     */
    void setConfigValue(String configKey, String configValue);

    /**
     * 根据配置键设置字符串值
     */
    void setStringValue(String configKey, String value);

    /**
     * 根据配置键设置整数值
     */
    void setIntValue(String configKey, Integer value);

    /**
     * 根据配置键设置长整数值
     */
    void setLongValue(String configKey, Long value);

    /**
     * 根据配置键设置双精度值
     */
    void setDoubleValue(String configKey, Double value);

    /**
     * 根据配置键设置布尔值
     */
    void setBooleanValue(String configKey, Boolean value);

    /**
     * 批量获取配置值
     */
    Map<String, String> getConfigValues(List<String> configKeys);

    /**
     * 批量设置配置值
     */
    void setConfigValues(Map<String, String> configValues);

    /**
     * 刷新配置缓存
     */
    void refreshConfigCache();
}
