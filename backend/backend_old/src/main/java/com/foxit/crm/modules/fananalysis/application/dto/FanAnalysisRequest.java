package com.foxit.crm.modules.fananalysis.application.dto;

import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDate;
import java.util.List;

/**
 * 粉丝分析请求DTO
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Getter
@Builder
public class FanAnalysisRequest {

    /**
     * 开始日期
     */
    private final LocalDate startDate;

    /**
     * 结束日期
     */
    private final LocalDate endDate;

    /**
     * 时间粒度
     */
    private final TimeRange.TimeGranularity granularity;

    /**
     * 平台列表
     */
    private final List<String> platforms;

    /**
     * 来源筛选
     */
    private final String source;

    /**
     * 粉丝类型筛选
     */
    private final String fanType;

    /**
     * 粉丝价值筛选
     */
    private final String fanValue;

    /**
     * 状态筛选
     */
    private final String status;

    /**
     * 关键词搜索
     */
    private final String keyword;

    /**
     * 分页页码
     */
    private final Integer page;

    /**
     * 分页大小
     */
    private final Integer pageSize;

    /**
     * 排序字段
     */
    private final String sortField;

    /**
     * 排序方向
     */
    private final String sortOrder;

    /**
     * 数据权限范围
     */
    private final String dataScope;

    /**
     * 用户ID
     */
    private final Long userId;

    /**
     * 生成缓存键
     */
    public String cacheKey() {
        return String.format("fan_analysis_%s_%s_%s_%s_%s_%s_%s_%s_%s_%s_%s_%s_%s",
                startDate, endDate, granularity,
                platforms != null ? String.join(",", platforms) : "all",
                source != null ? source : "all",
                fanType != null ? fanType : "all",
                fanValue != null ? fanValue : "all",
                status != null ? status : "all",
                keyword != null ? keyword : "all",
                page != null ? page : 1,
                pageSize != null ? pageSize : 10,
                sortField != null ? sortField : "activity_level",
                dataScope);
    }
}
