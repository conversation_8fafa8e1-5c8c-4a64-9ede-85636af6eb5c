package com.foxit.crm.modules.system.domain.model.aggregate;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 数据权限规则聚合根
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@NoArgsConstructor
public class DataPermissionRule {

    /**
     * 规则ID
     */
    private Long id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则编码
     */
    private String ruleCode;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 权限类型：1-行级权限，2-列级权限，3-查询权限
     */
    private Integer permissionType;

    /**
     * 目标表名
     */
    private String tableName;

    /**
     * 目标字段名（列级权限使用）
     */
    private String columnName;

    /**
     * 权限条件表达式
     */
    private String conditionExpression;

    /**
     * 权限范围：1-全部数据，2-本人数据，3-本部门数据，4-本部门及下级数据，5-自定义条件
     */
    private Integer permissionScope;

    /**
     * 角色ID（关联角色）
     */
    private Long roleId;

    /**
     * 用户ID（关联用户，优先级高于角色）
     */
    private Long userId;

    /**
     * 产品线ID（数据隔离）
     */
    private Long productLineId;

    /**
     * 优先级（数字越小优先级越高）
     */
    private Integer priority;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 版本号（乐观锁）
     */
    private Integer version;

    /**
     * 权限类型枚举
     */
    public enum PermissionType {
        ROW_LEVEL(1, "行级权限"),
        COLUMN_LEVEL(2, "列级权限"),
        QUERY_LEVEL(3, "查询权限");

        private final Integer code;
        private final String name;

        PermissionType(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static PermissionType fromCode(Integer code) {
            for (PermissionType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 权限范围枚举
     */
    public enum PermissionScope {
        ALL_DATA(1, "全部数据"),
        SELF_DATA(2, "本人数据"),
        DEPT_DATA(3, "本部门数据"),
        DEPT_AND_SUB_DATA(4, "本部门及下级数据"),
        CUSTOM_CONDITION(5, "自定义条件");

        private final Integer code;
        private final String name;

        PermissionScope(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static PermissionScope fromCode(Integer code) {
            for (PermissionScope scope : values()) {
                if (scope.getCode().equals(code)) {
                    return scope;
                }
            }
            return null;
        }
    }

    /**
     * 构造函数
     */
    public DataPermissionRule(String ruleName, String ruleCode, String description, Integer permissionType,
                             String tableName, String columnName, String conditionExpression, Integer permissionScope,
                             Long roleId, Long userId, Long productLineId, Integer priority, Integer status,
                             Integer sortOrder, String remark) {
        this.ruleName = ruleName;
        this.ruleCode = ruleCode;
        this.description = description;
        this.permissionType = permissionType;
        this.tableName = tableName;
        this.columnName = columnName;
        this.conditionExpression = conditionExpression;
        this.permissionScope = permissionScope;
        this.roleId = roleId;
        this.userId = userId;
        this.productLineId = productLineId;
        this.priority = priority;
        this.status = status;
        this.sortOrder = sortOrder;
        this.remark = remark;
    }

    /**
     * 启用规则
     */
    public void enable() {
        this.status = 1;
    }

    /**
     * 禁用规则
     */
    public void disable() {
        this.status = 0;
    }

    /**
     * 检查规则是否启用
     */
    public boolean isEnabled() {
        return this.status != null && this.status == 1;
    }

    /**
     * 检查是否为行级权限
     */
    public boolean isRowLevelPermission() {
        return PermissionType.ROW_LEVEL.getCode().equals(this.permissionType);
    }

    /**
     * 检查是否为列级权限
     */
    public boolean isColumnLevelPermission() {
        return PermissionType.COLUMN_LEVEL.getCode().equals(this.permissionType);
    }

    /**
     * 检查是否为查询权限
     */
    public boolean isQueryLevelPermission() {
        return PermissionType.QUERY_LEVEL.getCode().equals(this.permissionType);
    }

    /**
     * 检查是否为用户级规则（优先级高于角色级规则）
     */
    public boolean isUserLevelRule() {
        return this.userId != null;
    }

    /**
     * 检查是否为角色级规则
     */
    public boolean isRoleLevelRule() {
        return this.roleId != null && this.userId == null;
    }

    /**
     * 检查是否有产品线隔离
     */
    public boolean hasProductLineIsolation() {
        return this.productLineId != null;
    }

    /**
     * 获取权限类型名称
     */
    public String getPermissionTypeName() {
        PermissionType permissionType = PermissionType.fromCode(this.permissionType);
        return permissionType != null ? permissionType.getName() : "未知";
    }

    /**
     * 获取权限范围名称
     */
    public String getPermissionScopeName() {
        PermissionScope permissionScope = PermissionScope.fromCode(this.permissionScope);
        return permissionScope != null ? permissionScope.getName() : "未知";
    }

    /**
     * 检查规则编码是否有效
     */
    public boolean isValidRuleCode() {
        return this.ruleCode != null && !this.ruleCode.trim().isEmpty();
    }

    /**
     * 检查规则名称是否有效
     */
    public boolean isValidRuleName() {
        return this.ruleName != null && !this.ruleName.trim().isEmpty();
    }

    /**
     * 检查表名是否有效
     */
    public boolean isValidTableName() {
        return this.tableName != null && !this.tableName.trim().isEmpty();
    }

    /**
     * 检查条件表达式是否有效
     */
    public boolean hasValidConditionExpression() {
        return this.conditionExpression != null && !this.conditionExpression.trim().isEmpty();
    }
}
