package com.foxit.crm.modules.system.application.impl;

import com.foxit.crm.common.exception.BusinessException;
import com.foxit.crm.modules.system.api.dto.request.SystemConfigCreateRequest;
import com.foxit.crm.modules.system.api.dto.request.SystemConfigUpdateRequest;
import com.foxit.crm.modules.system.api.dto.response.SystemConfigDetailResponse;
import com.foxit.crm.modules.system.api.dto.response.PageResponse;
import com.foxit.crm.modules.system.api.dto.converter.SystemConfigConverter;
import com.foxit.crm.modules.system.application.service.SystemConfigService;
import com.foxit.crm.modules.system.domain.model.aggregate.SystemConfig;
import com.foxit.crm.modules.system.domain.repository.SystemConfigRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统配置应用服务实现
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
@RequiredArgsConstructor
public class SystemConfigServiceImpl implements SystemConfigService {

    private final SystemConfigRepository systemConfigRepository;
    private final SystemConfigConverter systemConfigConverter;

    @Override
    @Transactional
    @CacheEvict(value = "systemConfig", allEntries = true)
    public Long createSystemConfig(SystemConfigCreateRequest request) {
        // 检查配置键是否已存在
        if (systemConfigRepository.existsByConfigKey(request.getConfigKey())) {
            throw new BusinessException("配置键已存在");
        }

        // 创建系统配置领域对象
        SystemConfig systemConfig = new SystemConfig(
                request.getConfigKey(),
                request.getConfigValue(),
                request.getConfigName(),
                request.getDescription(),
                request.getConfigType(),
                request.getConfigGroup(),
                request.getIsSystem(),
                request.getStatus(),
                request.getSortOrder(),
                request.getRemark());

        // 保存系统配置
        SystemConfig savedSystemConfig = systemConfigRepository.save(systemConfig);
        return savedSystemConfig.getId();
    }

    @Override
    @Transactional
    @CacheEvict(value = "systemConfig", allEntries = true)
    public void updateSystemConfig(Long id, SystemConfigUpdateRequest request) {
        // 查找系统配置
        SystemConfig systemConfig = systemConfigRepository.findById(id)
                .orElseThrow(() -> new BusinessException("系统配置不存在"));

        // 检查配置键是否已被其他配置使用
        if (systemConfigRepository.existsByConfigKeyAndIdNot(request.getConfigKey(), id)) {
            throw new BusinessException("配置键已存在");
        }

        // 更新系统配置信息
        systemConfig.setConfigKey(request.getConfigKey());
        systemConfig.setConfigValue(request.getConfigValue());
        systemConfig.setConfigName(request.getConfigName());
        systemConfig.setDescription(request.getDescription());
        systemConfig.setConfigType(request.getConfigType());
        systemConfig.setConfigGroup(request.getConfigGroup());
        systemConfig.setIsSystem(request.getIsSystem());
        systemConfig.setStatus(request.getStatus());
        systemConfig.setSortOrder(request.getSortOrder());
        systemConfig.setRemark(request.getRemark());
        systemConfig.setVersion(request.getVersion());

        // 保存更新
        systemConfigRepository.save(systemConfig);
    }

    @Override
    @Transactional
    @CacheEvict(value = "systemConfig", allEntries = true)
    public void deleteSystemConfig(Long id) {
        // 检查系统配置是否存在
        SystemConfig systemConfig = systemConfigRepository.findById(id)
                .orElseThrow(() -> new BusinessException("系统配置不存在"));

        // 检查是否为系统内置配置
        if (systemConfig.isSystemConfig()) {
            throw new BusinessException("系统内置配置不允许删除");
        }

        // 删除系统配置
        systemConfigRepository.deleteById(id);
    }

    @Override
    public SystemConfigDetailResponse getSystemConfigById(Long id) {
        SystemConfig systemConfig = systemConfigRepository.findById(id)
                .orElseThrow(() -> new BusinessException("系统配置不存在"));

        return convertToDetailResponse(systemConfig);
    }

    @Override
    @Cacheable(value = "systemConfig", key = "#configKey")
    public SystemConfigDetailResponse getSystemConfigByKey(String configKey) {
        SystemConfig systemConfig = systemConfigRepository.findByConfigKey(configKey)
                .orElseThrow(() -> new BusinessException("系统配置不存在"));

        return convertToDetailResponse(systemConfig);
    }

    @Override
    public PageResponse<SystemConfigDetailResponse> getSystemConfigList(int page, int size, String keyword,
            String configGroup, Integer status) {
        List<SystemConfig> systemConfigs = systemConfigRepository.findByPage(page, size, keyword, configGroup, status);
        long total = systemConfigRepository.count(keyword, configGroup, status);

        List<SystemConfigDetailResponse> systemConfigResponses = systemConfigConverter.toDtoList(systemConfigs);

        return new PageResponse<>(systemConfigResponses, total, page, size);
    }

    @Override
    public List<SystemConfigDetailResponse> getSystemConfigsByGroup(String configGroup) {
        List<SystemConfig> systemConfigs = systemConfigRepository.findByConfigGroup(configGroup);
        return systemConfigs.stream()
                .map(this::convertToDetailResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<SystemConfigDetailResponse> getAllEnabledSystemConfigs() {
        List<SystemConfig> systemConfigs = systemConfigRepository.findAllEnabled();
        return systemConfigs.stream()
                .map(this::convertToDetailResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<SystemConfigDetailResponse> getSystemConfigs() {
        List<SystemConfig> systemConfigs = systemConfigRepository.findSystemConfigs();
        return systemConfigs.stream()
                .map(this::convertToDetailResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<SystemConfigDetailResponse> getUserConfigs() {
        List<SystemConfig> systemConfigs = systemConfigRepository.findUserConfigs();
        return systemConfigs.stream()
                .map(this::convertToDetailResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    @CacheEvict(value = "systemConfig", allEntries = true)
    public void enableSystemConfig(Long id) {
        SystemConfig systemConfig = systemConfigRepository.findById(id)
                .orElseThrow(() -> new BusinessException("系统配置不存在"));

        systemConfig.enable();
        systemConfigRepository.save(systemConfig);
    }

    @Override
    @Transactional
    @CacheEvict(value = "systemConfig", allEntries = true)
    public void disableSystemConfig(Long id) {
        SystemConfig systemConfig = systemConfigRepository.findById(id)
                .orElseThrow(() -> new BusinessException("系统配置不存在"));

        systemConfig.disable();
        systemConfigRepository.save(systemConfig);
    }

    @Override
    @Transactional
    @CacheEvict(value = "systemConfig", allEntries = true)
    public void enableSystemConfigsBatch(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        systemConfigRepository.updateStatusBatch(ids, 1);
    }

    @Override
    @Transactional
    @CacheEvict(value = "systemConfig", allEntries = true)
    public void disableSystemConfigsBatch(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        systemConfigRepository.updateStatusBatch(ids, 0);
    }

    @Override
    public List<String> getAllConfigGroups() {
        return systemConfigRepository.findAllConfigGroups();
    }

    @Override
    public boolean isConfigKeyAvailable(String configKey) {
        return !systemConfigRepository.existsByConfigKey(configKey);
    }

    @Override
    public boolean isConfigKeyAvailable(String configKey, Long excludeId) {
        return !systemConfigRepository.existsByConfigKeyAndIdNot(configKey, excludeId);
    }

    @Override
    @Cacheable(value = "systemConfigValue", key = "#configKey")
    public String getConfigValue(String configKey) {
        return systemConfigRepository.findByConfigKey(configKey)
                .map(SystemConfig::getConfigValue)
                .orElse(null);
    }

    @Override
    public String getStringValue(String configKey) {
        return systemConfigRepository.findByConfigKey(configKey)
                .map(SystemConfig::getStringValue)
                .orElse(null);
    }

    @Override
    public Integer getIntValue(String configKey) {
        return systemConfigRepository.findByConfigKey(configKey)
                .map(SystemConfig::getIntValue)
                .orElse(null);
    }

    @Override
    public Long getLongValue(String configKey) {
        return systemConfigRepository.findByConfigKey(configKey)
                .map(SystemConfig::getLongValue)
                .orElse(null);
    }

    @Override
    public Double getDoubleValue(String configKey) {
        return systemConfigRepository.findByConfigKey(configKey)
                .map(SystemConfig::getDoubleValue)
                .orElse(null);
    }

    @Override
    public Boolean getBooleanValue(String configKey) {
        return systemConfigRepository.findByConfigKey(configKey)
                .map(SystemConfig::getBooleanValue)
                .orElse(null);
    }

    @Override
    @Transactional
    @CacheEvict(value = { "systemConfig", "systemConfigValue" }, allEntries = true)
    public void setConfigValue(String configKey, String configValue) {
        SystemConfig systemConfig = systemConfigRepository.findByConfigKey(configKey)
                .orElseThrow(() -> new BusinessException("系统配置不存在"));

        systemConfig.setConfigValue(configValue);
        systemConfigRepository.save(systemConfig);
    }

    @Override
    @Transactional
    @CacheEvict(value = { "systemConfig", "systemConfigValue" }, allEntries = true)
    public void setStringValue(String configKey, String value) {
        SystemConfig systemConfig = systemConfigRepository.findByConfigKey(configKey)
                .orElseThrow(() -> new BusinessException("系统配置不存在"));

        systemConfig.setStringValue(value);
        systemConfigRepository.save(systemConfig);
    }

    @Override
    @Transactional
    @CacheEvict(value = { "systemConfig", "systemConfigValue" }, allEntries = true)
    public void setIntValue(String configKey, Integer value) {
        SystemConfig systemConfig = systemConfigRepository.findByConfigKey(configKey)
                .orElseThrow(() -> new BusinessException("系统配置不存在"));

        systemConfig.setIntValue(value);
        systemConfigRepository.save(systemConfig);
    }

    @Override
    @Transactional
    @CacheEvict(value = { "systemConfig", "systemConfigValue" }, allEntries = true)
    public void setLongValue(String configKey, Long value) {
        SystemConfig systemConfig = systemConfigRepository.findByConfigKey(configKey)
                .orElseThrow(() -> new BusinessException("系统配置不存在"));

        systemConfig.setLongValue(value);
        systemConfigRepository.save(systemConfig);
    }

    @Override
    @Transactional
    @CacheEvict(value = { "systemConfig", "systemConfigValue" }, allEntries = true)
    public void setDoubleValue(String configKey, Double value) {
        SystemConfig systemConfig = systemConfigRepository.findByConfigKey(configKey)
                .orElseThrow(() -> new BusinessException("系统配置不存在"));

        systemConfig.setDoubleValue(value);
        systemConfigRepository.save(systemConfig);
    }

    @Override
    @Transactional
    @CacheEvict(value = { "systemConfig", "systemConfigValue" }, allEntries = true)
    public void setBooleanValue(String configKey, Boolean value) {
        SystemConfig systemConfig = systemConfigRepository.findByConfigKey(configKey)
                .orElseThrow(() -> new BusinessException("系统配置不存在"));

        systemConfig.setBooleanValue(value);
        systemConfigRepository.save(systemConfig);
    }

    @Override
    public Map<String, String> getConfigValues(List<String> configKeys) {
        List<SystemConfig> systemConfigs = systemConfigRepository.findByConfigKeys(configKeys);
        Map<String, String> configValues = new HashMap<>();

        for (SystemConfig systemConfig : systemConfigs) {
            configValues.put(systemConfig.getConfigKey(), systemConfig.getConfigValue());
        }

        return configValues;
    }

    @Override
    @Transactional
    @CacheEvict(value = { "systemConfig", "systemConfigValue" }, allEntries = true)
    public void setConfigValues(Map<String, String> configValues) {
        for (Map.Entry<String, String> entry : configValues.entrySet()) {
            setConfigValue(entry.getKey(), entry.getValue());
        }
    }

    @Override
    @CacheEvict(value = { "systemConfig", "systemConfigValue" }, allEntries = true)
    public void refreshConfigCache() {
        // 缓存已通过注解清除
    }

    /**
     * 转换为详情响应对象
     */
    private SystemConfigDetailResponse convertToDetailResponse(SystemConfig systemConfig) {
        SystemConfigDetailResponse response = new SystemConfigDetailResponse();
        BeanUtils.copyProperties(systemConfig, response);
        response.setConfigType(systemConfig.getConfigType()); // 触发类型文本设置
        response.setIsSystem(systemConfig.getIsSystem()); // 触发系统内置文本设置
        response.setStatus(systemConfig.getStatus()); // 触发状态文本设置
        return response;
    }
}
