package com.foxit.crm.modules.campaignanalysis.infrastructure.repository;

import com.foxit.crm.modules.campaignanalysis.domain.entity.CampaignAggregate;
import com.foxit.crm.modules.campaignanalysis.domain.repository.CampaignAnalysisRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动分析仓储真实数据实现
 * 基于MySQL的真实数据查询
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Repository
@Primary
@RequiredArgsConstructor
public class CampaignAnalysisRepositoryRealImpl implements CampaignAnalysisRepository {

    @Qualifier("mysqlJdbcTemplate")
    private final JdbcTemplate mysqlJdbcTemplate;

    @Override
    public Optional<CampaignAggregate> getCampaignOverview(TimeRange timeRange, List<Long> campaignIds, String dataScope) {
        log.info("获取活动总览数据: timeRange={}, campaignIds={}, dataScope={}", timeRange, campaignIds, dataScope);

        try {
            // 构建SQL查询
            StringBuilder sql = new StringBuilder("""
                    SELECT 
                        c.id as campaign_id,
                        c.campaign_name,
                        c.campaign_type,
                        c.status,
                        c.budget,
                        c.actual_cost,
                        c.start_time,
                        c.end_time,
                        COALESCE(SUM(s.impressions), 0) as total_impressions,
                        COALESCE(SUM(s.clicks), 0) as total_clicks,
                        COALESCE(SUM(s.conversions), 0) as total_conversions,
                        COALESCE(SUM(s.participants), 0) as total_participants,
                        COALESCE(SUM(s.revenue), 0) as total_revenue,
                        CASE 
                            WHEN SUM(s.impressions) > 0 THEN (SUM(s.clicks) * 100.0 / SUM(s.impressions))
                            ELSE 0 
                        END as click_rate,
                        CASE 
                            WHEN SUM(s.clicks) > 0 THEN (SUM(s.conversions) * 100.0 / SUM(s.clicks))
                            ELSE 0 
                        END as conversion_rate,
                        CASE 
                            WHEN c.actual_cost > 0 THEN ((SUM(s.revenue) - c.actual_cost) * 100.0 / c.actual_cost)
                            ELSE 0 
                        END as roi
                    FROM marketing_campaigns c
                    LEFT JOIN campaign_statistics s ON c.id = s.campaign_id 
                        AND s.stat_date >= ? AND s.stat_date <= ?
                    WHERE c.deleted = 0
                    """);

            List<Object> params = new ArrayList<>();
            params.add(timeRange.getStartDate());
            params.add(timeRange.getEndDate());

            // 添加活动ID过滤条件
            if (campaignIds != null && !campaignIds.isEmpty()) {
                sql.append(" AND c.id IN (");
                sql.append(campaignIds.stream().map(id -> "?").collect(Collectors.joining(",")));
                sql.append(")");
                params.addAll(campaignIds);
            }

            sql.append(" GROUP BY c.id, c.campaign_name, c.campaign_type, c.status, c.budget, c.actual_cost, c.start_time, c.end_time");
            sql.append(" ORDER BY total_revenue DESC");

            List<Map<String, Object>> results = mysqlJdbcTemplate.queryForList(sql.toString(), params.toArray());

            if (results != null && !results.isEmpty()) {
                return Optional.of(buildCampaignOverview(results, timeRange, campaignIds));
            }

            return Optional.empty();

        } catch (Exception e) {
            log.error("获取活动总览数据失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<CampaignAggregate> getCampaignPerformance(TimeRange timeRange, List<Long> campaignIds, String dataScope) {
        // 复用总览数据逻辑，但分析类型不同
        Optional<CampaignAggregate> overview = getCampaignOverview(timeRange, campaignIds, dataScope);
        if (overview.isPresent()) {
            CampaignAggregate original = overview.get();
            return Optional.of(CampaignAggregate.builder()
                    .id(original.getId().replace("overview", "performance"))
                    .analysisType(CampaignAggregate.CampaignAnalysisType.PERFORMANCE)
                    .timeRange(original.getTimeRange())
                    .campaignIds(original.getCampaignIds())
                    .coreMetrics(original.getCoreMetrics())
                    .campaignList(original.getCampaignList())
                    .trendData(original.getTrendData())
                    .channelData(original.getChannelData())
                    .dataScope(original.getDataScope())
                    .lastUpdateTime(LocalDateTime.now())
                    .build());
        }
        return Optional.empty();
    }

    @Override
    public Optional<CampaignAggregate> getChannelAnalysis(TimeRange timeRange, List<Long> campaignIds, String dataScope) {
        // 暂时返回空，可以后续实现
        return Optional.empty();
    }

    @Override
    public Optional<CampaignAggregate> getROIAnalysis(TimeRange timeRange, List<Long> campaignIds, String dataScope) {
        // 暂时返回空，可以后续实现
        return Optional.empty();
    }

    @Override
    public Optional<CampaignAggregate> getTrendAnalysis(TimeRange timeRange, List<Long> campaignIds, String dataScope) {
        // 暂时返回空，可以后续实现
        return Optional.empty();
    }

    @Override
    public void save(CampaignAggregate aggregate) {
        log.info("保存活动分析结果: id={}", aggregate.getId());
        // 可以实现保存到缓存或历史记录表
    }

    @Override
    public Optional<CampaignAggregate> findById(String id) {
        log.debug("根据ID查找活动分析: id={}", id);
        // 可以从缓存或历史记录表中查找
        return Optional.empty();
    }

    @Override
    public CampaignRealTimeStats getRealTimeStats(List<Long> campaignIds, String dataScope) {
        log.info("获取实时活动统计: campaignIds={}, dataScope={}", campaignIds, dataScope);

        try {
            StringBuilder sql = new StringBuilder("""
                    SELECT 
                        COUNT(DISTINCT c.id) as total_campaigns,
                        COUNT(DISTINCT CASE WHEN c.status = 'active' THEN c.id END) as active_campaigns,
                        COALESCE(SUM(s.impressions), 0) as total_impressions,
                        COALESCE(SUM(s.clicks), 0) as total_clicks,
                        COALESCE(SUM(s.conversions), 0) as total_conversions,
                        CASE 
                            WHEN SUM(s.impressions) > 0 THEN (SUM(s.clicks) * 100.0 / SUM(s.impressions))
                            ELSE 0 
                        END as avg_click_rate,
                        CASE 
                            WHEN SUM(s.clicks) > 0 THEN (SUM(s.conversions) * 100.0 / SUM(s.clicks))
                            ELSE 0 
                        END as avg_conversion_rate,
                        CASE 
                            WHEN SUM(c.actual_cost) > 0 THEN ((SUM(s.revenue) - SUM(c.actual_cost)) * 100.0 / SUM(c.actual_cost))
                            ELSE 0 
                        END as avg_roi
                    FROM marketing_campaigns c
                    LEFT JOIN campaign_statistics s ON c.id = s.campaign_id 
                        AND s.stat_date >= CURDATE() - INTERVAL 30 DAY
                    WHERE c.deleted = 0
                    """);

            List<Object> params = new ArrayList<>();

            if (campaignIds != null && !campaignIds.isEmpty()) {
                sql.append(" AND c.id IN (");
                sql.append(campaignIds.stream().map(id -> "?").collect(Collectors.joining(",")));
                sql.append(")");
                params.addAll(campaignIds);
            }

            Map<String, Object> result = mysqlJdbcTemplate.queryForMap(sql.toString(), params.toArray());

            return new CampaignRealTimeStats(
                    ((Number) result.get("total_campaigns")).longValue(),
                    ((Number) result.get("active_campaigns")).longValue(),
                    ((Number) result.get("total_impressions")).longValue(),
                    ((Number) result.get("total_clicks")).longValue(),
                    ((Number) result.get("total_conversions")).longValue(),
                    ((Number) result.get("avg_click_rate")).doubleValue(),
                    ((Number) result.get("avg_conversion_rate")).doubleValue(),
                    ((Number) result.get("avg_roi")).doubleValue()
            );

        } catch (Exception e) {
            log.error("获取实时活动统计失败: {}", e.getMessage(), e);
            return new CampaignRealTimeStats(0L, 0L, 0L, 0L, 0L, 0.0, 0.0, 0.0);
        }
    }

    @Override
    public CampaignAnalysisSummary getAnalysisSummary(TimeRange timeRange, String dataScope) {
        log.info("获取活动分析摘要: timeRange={}, dataScope={}", timeRange, dataScope);

        try {
            CampaignRealTimeStats stats = getRealTimeStats(null, dataScope);
            
            return new CampaignAnalysisSummary(
                    "summary_" + timeRange.getStartDate() + "_" + timeRange.getEndDate(),
                    "OVERVIEW",
                    timeRange,
                    stats.totalCampaigns(),
                    stats.activeCampaigns(),
                    stats.averageROI(),
                    "最佳活动待分析",
                    String.format("总活动数: %d, 活跃活动: %d, 平均ROI: %.2f%%", 
                            stats.totalCampaigns(), stats.activeCampaigns(), stats.averageROI())
            );

        } catch (Exception e) {
            log.error("获取活动分析摘要失败: {}", e.getMessage(), e);
            return new CampaignAnalysisSummary(
                    "error", "ERROR", timeRange, 0L, 0L, 0.0, "无", "获取数据失败"
            );
        }
    }

    /**
     * 构建活动总览聚合根
     */
    private CampaignAggregate buildCampaignOverview(List<Map<String, Object>> results, 
                                                   TimeRange timeRange, 
                                                   List<Long> campaignIds) {
        log.debug("构建活动总览聚合根，数据条数: {}", results.size());

        // 构建活动列表
        List<CampaignAggregate.CampaignData> campaignList = results.stream()
                .map(this::buildCampaignData)
                .collect(Collectors.toList());

        // 构建核心指标
        Map<String, MetricValue> coreMetrics = buildCoreMetrics(campaignList);

        return CampaignAggregate.builder()
                .id("campaign_overview_" + timeRange.getStartDate() + "_" + timeRange.getEndDate())
                .analysisType(CampaignAggregate.CampaignAnalysisType.OVERVIEW)
                .timeRange(timeRange)
                .campaignIds(campaignIds)
                .coreMetrics(coreMetrics)
                .campaignList(campaignList)
                .trendData(new HashMap<>())
                .channelData(new HashMap<>())
                .dataScope("PUBLIC")
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 构建活动数据
     */
    private CampaignAggregate.CampaignData buildCampaignData(Map<String, Object> row) {
        return CampaignAggregate.CampaignData.builder()
                .campaignId(((Number) row.get("campaign_id")).longValue())
                .campaignName((String) row.get("campaign_name"))
                .campaignType((String) row.get("campaign_type"))
                .status((String) row.get("status"))
                .budget((BigDecimal) row.get("budget"))
                .actualCost((BigDecimal) row.get("actual_cost"))
                .impressions(((Number) row.get("total_impressions")).longValue())
                .clicks(((Number) row.get("total_clicks")).longValue())
                .conversions(((Number) row.get("total_conversions")).longValue())
                .participants(((Number) row.get("total_participants")).longValue())
                .revenue((BigDecimal) row.get("total_revenue"))
                .clickRate(((Number) row.get("click_rate")).doubleValue())
                .conversionRate(((Number) row.get("conversion_rate")).doubleValue())
                .roi(((Number) row.get("roi")).doubleValue())
                .startTime(row.get("start_time") != null ? ((java.sql.Timestamp) row.get("start_time")).toLocalDateTime() : null)
                .endTime(row.get("end_time") != null ? ((java.sql.Timestamp) row.get("end_time")).toLocalDateTime() : null)
                .build();
    }

    /**
     * 构建核心指标
     */
    private Map<String, MetricValue> buildCoreMetrics(List<CampaignAggregate.CampaignData> campaignList) {
        Map<String, MetricValue> metrics = new HashMap<>();

        long totalCampaigns = campaignList.size();
        long totalImpressions = campaignList.stream().mapToLong(CampaignAggregate.CampaignData::getImpressions).sum();
        long totalClicks = campaignList.stream().mapToLong(CampaignAggregate.CampaignData::getClicks).sum();
        long totalConversions = campaignList.stream().mapToLong(CampaignAggregate.CampaignData::getConversions).sum();

        metrics.put("totalCampaigns", MetricValue.ofCount("总活动数", totalCampaigns, null));
        metrics.put("totalImpressions", MetricValue.ofCount("总曝光量", totalImpressions, null));
        metrics.put("totalClicks", MetricValue.ofCount("总点击量", totalClicks, null));
        metrics.put("totalConversions", MetricValue.ofCount("总转化数", totalConversions, null));

        double avgClickRate = totalImpressions > 0 ? (double) totalClicks / totalImpressions * 100 : 0.0;
        double avgConversionRate = totalClicks > 0 ? (double) totalConversions / totalClicks * 100 : 0.0;

        metrics.put("avgClickRate", MetricValue.ofPercentage("平均点击率", avgClickRate, null));
        metrics.put("avgConversionRate", MetricValue.ofPercentage("平均转化率", avgConversionRate, null));

        return metrics;
    }
}
