package com.foxit.crm.modules.system.infrastructure.persistence.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 版本发布历史持久化对象
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("version_release_history")
public class VersionReleaseHistoryPO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 版本ID
     */
    @TableField("version_id")
    private Long versionId;

    /**
     * 操作类型：1-创建，2-更新，3-发布，4-撤回，5-废弃
     */
    @TableField("action_type")
    private Integer actionType;

    /**
     * 原状态
     */
    @TableField("from_status")
    private Integer fromStatus;

    /**
     * 目标状态
     */
    @TableField("to_status")
    private Integer toStatus;

    /**
     * 操作原因
     */
    @TableField("action_reason")
    private String actionReason;

    /**
     * 操作人ID
     */
    @TableField("action_by")
    private Long actionBy;

    /**
     * 操作人姓名
     */
    @TableField("action_by_name")
    private String actionByName;

    /**
     * 操作时间
     */
    @TableField("action_time")
    private LocalDateTime actionTime;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 更新人ID
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 逻辑删除标志：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 版本号（乐观锁）
     */
    @Version
    @TableField("version")
    private Integer version;
}
