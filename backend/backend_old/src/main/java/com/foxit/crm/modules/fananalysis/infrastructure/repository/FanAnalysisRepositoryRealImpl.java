package com.foxit.crm.modules.fananalysis.infrastructure.repository;

import com.foxit.crm.modules.fananalysis.domain.entity.FanAggregate;
import com.foxit.crm.modules.fananalysis.domain.repository.FanAnalysisRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 粉丝分析仓储真实数据实现
 * 基于MySQL的真实数据查询
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Repository
@Primary
@RequiredArgsConstructor
public class FanAnalysisRepositoryRealImpl implements FanAnalysisRepository {

    @Qualifier("mysqlJdbcTemplate")
    private final JdbcTemplate mysqlJdbcTemplate;

    @Override
    public Optional<FanAggregate> getFanOverview(TimeRange timeRange, List<String> platforms, 
                                               String source, String fanType, String dataScope) {
        log.info("获取粉丝总览数据: timeRange={}, platforms={}, source={}, fanType={}, dataScope={}", 
                timeRange, platforms, source, fanType, dataScope);

        try {
            // 构建SQL查询
            StringBuilder sql = new StringBuilder("""
                    SELECT 
                        f.id as fan_id,
                        f.fan_id as fan_code,
                        f.nickname,
                        f.platform,
                        f.platform_user_id,
                        f.gender,
                        f.age_range,
                        f.region,
                        f.city,
                        f.follow_time,
                        f.unfollow_time,
                        f.status,
                        f.source,
                        f.fan_type,
                        f.fan_value,
                        f.activity_level,
                        f.interaction_count,
                        f.last_interaction_time,
                        f.tags,
                        f.remark
                    FROM fans f
                    WHERE f.deleted = 0
                    """);

            List<Object> params = new ArrayList<>();

            // 添加时间范围过滤 - 只有当timeRange不为null且有有效日期时才添加
            if (timeRange != null && timeRange.getStartDate() != null && timeRange.getEndDate() != null) {
                sql.append(" AND f.follow_time >= ? AND f.follow_time <= ?");
                params.add(timeRange.getStartDate().atStartOfDay());
                params.add(timeRange.getEndDate().atTime(23, 59, 59));
            }

            // 添加平台过滤条件
            if (platforms != null && !platforms.isEmpty()) {
                sql.append(" AND f.platform IN (");
                sql.append(platforms.stream().map(p -> "?").collect(Collectors.joining(",")));
                sql.append(")");
                params.addAll(platforms);
            }

            // 添加来源过滤条件
            if (source != null && !source.trim().isEmpty()) {
                sql.append(" AND f.source = ?");
                params.add(source);
            }

            // 添加粉丝类型过滤条件
            if (fanType != null && !fanType.trim().isEmpty()) {
                sql.append(" AND f.fan_type = ?");
                params.add(fanType);
            }

            sql.append(" ORDER BY f.activity_level DESC, f.follow_time DESC");

            List<Map<String, Object>> results = mysqlJdbcTemplate.queryForList(sql.toString(), params.toArray());

            if (results != null && !results.isEmpty()) {
                return Optional.of(buildFanOverview(results, timeRange, platforms));
            }

            return Optional.empty();

        } catch (Exception e) {
            log.error("获取粉丝总览数据失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<FanAggregate> getFanGrowthAnalysis(TimeRange timeRange, List<String> platforms, String dataScope) {
        // 复用总览数据逻辑，但分析类型不同
        Optional<FanAggregate> overview = getFanOverview(timeRange, platforms, null, null, dataScope);
        if (overview.isPresent()) {
            FanAggregate original = overview.get();
            return Optional.of(FanAggregate.builder()
                    .id(original.getId().replace("overview", "growth"))
                    .analysisType(FanAggregate.FanAnalysisType.GROWTH)
                    .timeRange(original.getTimeRange())
                    .platforms(original.getPlatforms())
                    .coreMetrics(original.getCoreMetrics())
                    .fanList(original.getFanList())
                    .trendData(original.getTrendData())
                    .platformData(original.getPlatformData())
                    .dataScope(original.getDataScope())
                    .lastUpdateTime(LocalDateTime.now())
                    .build());
        }
        return Optional.empty();
    }

    @Override
    public Optional<FanAggregate> getFanActivityAnalysis(TimeRange timeRange, List<String> platforms, String dataScope) {
        // 暂时返回空，可以后续实现
        return Optional.empty();
    }

    @Override
    public Optional<FanAggregate> getFanValueAnalysis(TimeRange timeRange, List<String> platforms, String dataScope) {
        // 暂时返回空，可以后续实现
        return Optional.empty();
    }

    @Override
    public Optional<FanAggregate> getPlatformAnalysis(TimeRange timeRange, List<String> platforms, String dataScope) {
        // 暂时返回空，可以后续实现
        return Optional.empty();
    }

    @Override
    public void save(FanAggregate aggregate) {
        log.info("保存粉丝分析结果: id={}", aggregate.getId());
        // 可以实现保存到缓存或历史记录表
    }

    @Override
    public Optional<FanAggregate> findById(String id) {
        log.debug("根据ID查找粉丝分析: id={}", id);
        // 可以从缓存或历史记录表中查找
        return Optional.empty();
    }

    @Override
    public FanRealTimeStats getRealTimeFanStats(List<String> platforms, String dataScope) {
        log.info("获取实时粉丝统计: platforms={}, dataScope={}", platforms, dataScope);

        try {
            StringBuilder sql = new StringBuilder("""
                    SELECT 
                        COUNT(*) as total_fans,
                        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_fans,
                        COUNT(CASE WHEN DATE(follow_time) = CURDATE() THEN 1 END) as new_fans_today,
                        COUNT(CASE WHEN DATE(unfollow_time) = CURDATE() THEN 1 END) as unfollowed_fans_today,
                        COUNT(CASE WHEN fan_value = 'high' THEN 1 END) as high_value_fans,
                        COUNT(CASE WHEN fan_value = 'medium' THEN 1 END) as medium_value_fans,
                        COUNT(CASE WHEN fan_value = 'low' THEN 1 END) as low_value_fans,
                        AVG(activity_level) as avg_activity_level,
                        SUM(interaction_count) as total_interactions
                    FROM fans
                    WHERE deleted = 0
                    """);

            List<Object> params = new ArrayList<>();

            if (platforms != null && !platforms.isEmpty()) {
                sql.append(" AND platform IN (");
                sql.append(platforms.stream().map(p -> "?").collect(Collectors.joining(",")));
                sql.append(")");
                params.addAll(platforms);
            }

            Map<String, Object> result = mysqlJdbcTemplate.queryForMap(sql.toString(), params.toArray());

            // 计算留存率（简化计算）
            double retentionRate = 95.0; // 默认值，可以根据实际业务逻辑计算

            return new FanRealTimeStats(
                    ((Number) result.get("total_fans")).longValue(),
                    ((Number) result.get("active_fans")).longValue(),
                    ((Number) result.get("new_fans_today")).longValue(),
                    ((Number) result.get("unfollowed_fans_today")).longValue(),
                    ((Number) result.get("high_value_fans")).longValue(),
                    ((Number) result.get("medium_value_fans")).longValue(),
                    ((Number) result.get("low_value_fans")).longValue(),
                    result.get("avg_activity_level") != null ? ((Number) result.get("avg_activity_level")).doubleValue() : 0.0,
                    retentionRate,
                    ((Number) result.get("total_interactions")).longValue()
            );

        } catch (Exception e) {
            log.error("获取实时粉丝统计失败: {}", e.getMessage(), e);
            return new FanRealTimeStats(0L, 0L, 0L, 0L, 0L, 0L, 0L, 0.0, 0.0, 0L);
        }
    }

    @Override
    public FanAnalysisSummary getFanAnalysisSummary(TimeRange timeRange, String dataScope) {
        log.info("获取粉丝分析摘要: timeRange={}, dataScope={}", timeRange, dataScope);

        try {
            FanRealTimeStats stats = getRealTimeFanStats(null, dataScope);
            
            return new FanAnalysisSummary(
                    "summary_" + (timeRange != null ? timeRange.getStartDate() + "_" + timeRange.getEndDate() : "all"),
                    "OVERVIEW",
                    timeRange,
                    stats.totalFans(),
                    stats.activeFans(),
                    stats.avgActivityLevel(),
                    "微信", // 可以根据实际数据计算最大平台
                    String.format("总粉丝数: %d, 活跃粉丝: %d, 平均活跃度: %.2f", 
                            stats.totalFans(), stats.activeFans(), stats.avgActivityLevel())
            );

        } catch (Exception e) {
            log.error("获取粉丝分析摘要失败: {}", e.getMessage(), e);
            return new FanAnalysisSummary(
                    "error", "ERROR", timeRange, 0L, 0L, 0.0, "无", "获取数据失败"
            );
        }
    }

    @Override
    public FanPageResult getFansByPage(FanPageQuery query) {
        log.info("分页查询粉丝: {}", query);

        try {
            // 构建查询SQL
            StringBuilder sql = new StringBuilder("""
                    SELECT 
                        f.id as fan_id,
                        f.fan_id as fan_code,
                        f.nickname,
                        f.platform,
                        f.platform_user_id,
                        f.gender,
                        f.age_range,
                        f.region,
                        f.city,
                        f.follow_time,
                        f.unfollow_time,
                        f.status,
                        f.source,
                        f.fan_type,
                        f.fan_value,
                        f.activity_level,
                        f.interaction_count,
                        f.last_interaction_time,
                        f.tags,
                        f.remark
                    FROM fans f
                    WHERE f.deleted = 0
                    """);

            List<Object> params = new ArrayList<>();

            // 添加各种过滤条件
            addQueryConditions(sql, params, query);

            // 添加排序
            if (query.sortField() != null && !query.sortField().trim().isEmpty()) {
                sql.append(" ORDER BY f.").append(query.sortField());
                if ("desc".equalsIgnoreCase(query.sortOrder())) {
                    sql.append(" DESC");
                } else {
                    sql.append(" ASC");
                }
            } else {
                sql.append(" ORDER BY f.activity_level DESC, f.follow_time DESC");
            }

            // 添加分页
            sql.append(" LIMIT ? OFFSET ?");
            params.add(query.size());
            params.add((query.page() - 1) * query.size());

            List<Map<String, Object>> results = mysqlJdbcTemplate.queryForList(sql.toString(), params.toArray());

            // 查询总数
            long total = getTotalCount(query);

            // 转换为粉丝数据
            List<FanAggregate.FanData> fans = results.stream()
                    .map(this::buildFanData)
                    .collect(Collectors.toList());

            return new FanPageResult(fans, total, query.page(), query.size());

        } catch (Exception e) {
            log.error("分页查询粉丝失败: {}", e.getMessage(), e);
            return new FanPageResult(Collections.emptyList(), 0, query.page(), query.size());
        }
    }

    /**
     * 构建粉丝总览聚合根
     */
    private FanAggregate buildFanOverview(List<Map<String, Object>> results, 
                                         TimeRange timeRange, 
                                         List<String> platforms) {
        log.debug("构建粉丝总览聚合根，数据条数: {}", results.size());

        // 构建粉丝列表
        List<FanAggregate.FanData> fanList = results.stream()
                .map(this::buildFanData)
                .collect(Collectors.toList());

        // 构建核心指标
        Map<String, MetricValue> coreMetrics = buildCoreMetrics(fanList);

        return FanAggregate.builder()
                .id("fan_overview_" + (timeRange != null ? timeRange.getStartDate() + "_" + timeRange.getEndDate() : "all"))
                .analysisType(FanAggregate.FanAnalysisType.OVERVIEW)
                .timeRange(timeRange)
                .platforms(platforms)
                .coreMetrics(coreMetrics)
                .fanList(fanList)
                .trendData(new HashMap<>())
                .platformData(new HashMap<>())
                .dataScope("PUBLIC")
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 构建粉丝数据
     */
    private FanAggregate.FanData buildFanData(Map<String, Object> row) {
        return FanAggregate.FanData.builder()
                .fanId(((Number) row.get("fan_id")).longValue())
                .fanCode((String) row.get("fan_code"))
                .nickname((String) row.get("nickname"))
                .platform((String) row.get("platform"))
                .platformUserId((String) row.get("platform_user_id"))
                .gender(row.get("gender") != null ? ((Number) row.get("gender")).intValue() : null)
                .ageRange((String) row.get("age_range"))
                .region((String) row.get("region"))
                .city((String) row.get("city"))
                .followTime(row.get("follow_time") != null ? ((java.sql.Timestamp) row.get("follow_time")).toLocalDateTime() : null)
                .unfollowTime(row.get("unfollow_time") != null ? ((java.sql.Timestamp) row.get("unfollow_time")).toLocalDateTime() : null)
                .status((String) row.get("status"))
                .source((String) row.get("source"))
                .fanType((String) row.get("fan_type"))
                .fanValue((String) row.get("fan_value"))
                .activityLevel(row.get("activity_level") != null ? ((Number) row.get("activity_level")).intValue() : null)
                .interactionCount(row.get("interaction_count") != null ? ((Number) row.get("interaction_count")).longValue() : null)
                .lastInteractionTime(row.get("last_interaction_time") != null ? ((java.sql.Timestamp) row.get("last_interaction_time")).toLocalDateTime() : null)
                .tags(parseJsonArray((String) row.get("tags")))
                .remark((String) row.get("remark"))
                .build();
    }

    /**
     * 构建核心指标
     */
    private Map<String, MetricValue> buildCoreMetrics(List<FanAggregate.FanData> fanList) {
        Map<String, MetricValue> metrics = new HashMap<>();

        long totalFans = fanList.size();
        long activeFans = fanList.stream().filter(fan -> "active".equals(fan.getStatus())).count();
        long highValueFans = fanList.stream().filter(fan -> "high".equals(fan.getFanValue())).count();

        metrics.put("totalFans", MetricValue.ofCount("总粉丝数", totalFans, null));
        metrics.put("activeFans", MetricValue.ofCount("活跃粉丝数", activeFans, null));
        metrics.put("highValueFans", MetricValue.ofCount("高价值粉丝数", highValueFans, null));

        double avgActivityLevel = fanList.stream()
                .filter(fan -> fan.getActivityLevel() != null)
                .mapToInt(FanAggregate.FanData::getActivityLevel)
                .average()
                .orElse(0.0);

        metrics.put("avgActivityLevel", MetricValue.ofPercentage("平均活跃度", avgActivityLevel, null));

        return metrics;
    }

    /**
     * 添加查询条件
     */
    private void addQueryConditions(StringBuilder sql, List<Object> params, FanPageQuery query) {
        // 时间范围 - 只有当timeRange不为null时才添加时间条件
        if (query.timeRange() != null && query.timeRange().getStartDate() != null && query.timeRange().getEndDate() != null) {
            sql.append(" AND f.follow_time >= ? AND f.follow_time <= ?");
            params.add(query.timeRange().getStartDate().atStartOfDay());
            params.add(query.timeRange().getEndDate().atTime(23, 59, 59));
        }

        // 平台
        if (query.platforms() != null && !query.platforms().isEmpty()) {
            sql.append(" AND f.platform IN (");
            sql.append(query.platforms().stream().map(p -> "?").collect(Collectors.joining(",")));
            sql.append(")");
            params.addAll(query.platforms());
        }

        // 其他条件...
        if (query.source() != null && !query.source().trim().isEmpty()) {
            sql.append(" AND f.source = ?");
            params.add(query.source());
        }

        if (query.fanType() != null && !query.fanType().trim().isEmpty()) {
            sql.append(" AND f.fan_type = ?");
            params.add(query.fanType());
        }

        if (query.fanValue() != null && !query.fanValue().trim().isEmpty()) {
            sql.append(" AND f.fan_value = ?");
            params.add(query.fanValue());
        }

        if (query.status() != null && !query.status().trim().isEmpty()) {
            sql.append(" AND f.status = ?");
            params.add(query.status());
        }

        if (query.keyword() != null && !query.keyword().trim().isEmpty()) {
            sql.append(" AND (f.nickname LIKE ? OR f.fan_id LIKE ?)");
            String keyword = "%" + query.keyword() + "%";
            params.add(keyword);
            params.add(keyword);
        }
    }

    /**
     * 获取总数
     */
    private long getTotalCount(FanPageQuery query) {
        StringBuilder sql = new StringBuilder("SELECT COUNT(*) FROM fans f WHERE f.deleted = 0");
        List<Object> params = new ArrayList<>();
        addQueryConditions(sql, params, query);

        Long result = mysqlJdbcTemplate.queryForObject(sql.toString(), params.toArray(), Long.class);
        return result != null ? result : 0L;
    }

    /**
     * 解析JSON数组
     */
    private List<String> parseJsonArray(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        try {
            // 简单的JSON数组解析，实际项目中应该使用JSON库
            jsonStr = jsonStr.trim();
            if (jsonStr.startsWith("[") && jsonStr.endsWith("]")) {
                jsonStr = jsonStr.substring(1, jsonStr.length() - 1);
                return Arrays.stream(jsonStr.split(","))
                        .map(s -> s.trim().replaceAll("\"", ""))
                        .filter(s -> !s.isEmpty())
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.warn("解析JSON数组失败: {}", jsonStr, e);
        }
        
        return Collections.emptyList();
    }
}
