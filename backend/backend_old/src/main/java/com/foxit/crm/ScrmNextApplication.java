package com.foxit.crm;

import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration;
import org.springframework.core.env.Environment;

/**
 * SCRM-Next 数据分析平台启动类
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@SpringBootApplication(exclude = { SqlInitializationAutoConfiguration.class })
@MapperScan(basePackages = {
        "com.foxit.crm.modules.system.infrastructure.persistence.mapper",
        "com.foxit.crm.mapper" // 兼容旧的mapper位置
})
@EnableEncryptableProperties // 启用Jasypt配置加密
public class ScrmNextApplication {

    public static void main(String[] args) {
        Environment env = SpringApplication.run(ScrmNextApplication.class, args).getEnvironment();
        String activeProfile = env.getProperty("spring.profiles.active", "default");

        System.out.println("\n=================================");
        System.out.println("SCRM-Next Backend Started Successfully!");
        System.out.println("当前环境: " + activeProfile);
        System.out.println("API Documentation: http://localhost:8080/static/doc/index.html");
        System.out.println("Generate API Doc: mvn smart-doc:html");

        // 检查加密配置
        String jasyptPassword = System.getProperty("jasypt.encryptor.password");
        String envPassword = System.getenv("FX_MASTER_KEY");
        if (jasyptPassword != null || envPassword != null) {
            System.out.println("Jasypt 配置解密: 已启用");
        } else {
            System.out.println("注意: 未检测到Jasypt主密钥，使用默认密钥");
            System.out.println("生产环境请设置: -Djasypt.encryptor.password=<主密钥> 或环境变量 FX_MASTER_KEY");
        }
        System.out.println("=================================\n");
    }
}
