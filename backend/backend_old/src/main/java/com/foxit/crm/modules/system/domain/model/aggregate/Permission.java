package com.foxit.crm.modules.system.domain.model.aggregate;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 权限聚合根
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@NoArgsConstructor
public class Permission {

    /**
     * 权限ID
     */
    private Long id;

    /**
     * 父权限ID
     */
    private Long parentId;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 权限编码
     */
    private String permissionCode;

    /**
     * 权限类型：1-菜单，2-按钮，3-接口
     */
    private Integer permissionType;

    /**
     * 路由路径
     */
    private String path;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 图标
     */
    private String icon;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 子权限列表
     */
    private List<Permission> children;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 版本号（乐观锁）
     */
    private Integer version;

    /**
     * 权限类型枚举
     */
    public enum PermissionType {
        MENU(1, "菜单"),
        BUTTON(2, "按钮"),
        API(3, "接口");

        private final Integer code;
        private final String name;

        PermissionType(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 构造函数
     */
    public Permission(Long parentId, String permissionName, String permissionCode, 
                     Integer permissionType, String path, String component, 
                     String icon, Integer sortOrder, Integer status) {
        this.parentId = parentId;
        this.permissionName = permissionName;
        this.permissionCode = permissionCode;
        this.permissionType = permissionType;
        this.path = path;
        this.component = component;
        this.icon = icon;
        this.sortOrder = sortOrder;
        this.status = status;
    }

    /**
     * 启用权限
     */
    public void enable() {
        this.status = 1;
    }

    /**
     * 禁用权限
     */
    public void disable() {
        this.status = 0;
    }

    /**
     * 检查权限是否启用
     */
    public boolean isEnabled() {
        return this.status != null && this.status == 1;
    }

    /**
     * 检查是否为菜单权限
     */
    public boolean isMenu() {
        return PermissionType.MENU.getCode().equals(this.permissionType);
    }

    /**
     * 检查是否为按钮权限
     */
    public boolean isButton() {
        return PermissionType.BUTTON.getCode().equals(this.permissionType);
    }

    /**
     * 检查是否为接口权限
     */
    public boolean isApi() {
        return PermissionType.API.getCode().equals(this.permissionType);
    }

    /**
     * 检查是否为根权限
     */
    public boolean isRoot() {
        return this.parentId == null || this.parentId == 0;
    }

    /**
     * 检查权限编码是否有效
     */
    public boolean isValidPermissionCode() {
        return this.permissionCode != null && !this.permissionCode.trim().isEmpty();
    }

    /**
     * 检查权限名称是否有效
     */
    public boolean isValidPermissionName() {
        return this.permissionName != null && !this.permissionName.trim().isEmpty();
    }
}
