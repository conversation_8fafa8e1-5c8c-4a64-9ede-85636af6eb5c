package com.foxit.crm.modules.system.application.service.impl;

import com.foxit.crm.common.exception.BusinessException;
import com.foxit.crm.common.util.SecurityUtils;
import com.foxit.crm.modules.system.api.dto.request.ProductVersionCreateRequest;
import com.foxit.crm.modules.system.api.dto.request.ProductVersionUpdateRequest;
import com.foxit.crm.modules.system.api.dto.response.ProductVersionDetailResponse;
import com.foxit.crm.modules.system.api.dto.response.ProductVersionListResponse;
import com.foxit.crm.modules.system.api.dto.response.ProductVersionSimpleResponse;
import com.foxit.crm.modules.system.api.dto.response.VersionReleaseHistoryResponse;
import com.foxit.crm.modules.system.application.service.ProductVersionService;
import com.foxit.crm.modules.system.domain.model.aggregate.ProductVersion;
import com.foxit.crm.modules.system.domain.model.valueobject.VersionReleaseHistory;
import com.foxit.crm.modules.system.domain.repository.ProductVersionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 产品版本应用服务实现
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductVersionServiceImpl implements ProductVersionService {

    private final ProductVersionRepository productVersionRepository;
    private final HttpServletRequest httpServletRequest;

    @Override
    @Transactional
    public Long createProductVersion(ProductVersionCreateRequest request) {
        // 验证版本号是否已存在
        if (productVersionRepository.existsByProductLineIdAndVersionNumber(
                request.getProductLineId(), request.getVersionNumber())) {
            throw new BusinessException("版本号已存在");
        }

        // 获取当前用户信息
        Long currentUserId = SecurityUtils.getCurrentUserId();
        String currentUserName = SecurityUtils.getCurrentUserName();

        // 创建版本领域对象
        ProductVersion productVersion = new ProductVersion(
                request.getProductLineId(),
                request.getVersionNumber(),
                request.getVersionName(),
                request.getDescription(),
                request.getReleaseNotes(),
                ProductVersion.VersionType.fromCode(request.getVersionType()),
                currentUserId,
                currentUserName);

        // 设置其他属性
        productVersion.setPlannedDate(request.getPlannedDate());
        productVersion.setFileSize(request.getFileSize());
        productVersion.setDownloadUrl(request.getDownloadUrl());
        productVersion.setPlatforms(request.getPlatforms());
        productVersion.setFeatures(request.getFeatures());
        productVersion.setBugFixes(request.getBugFixes());
        productVersion.setBreakingChanges(request.getBreakingChanges());
        productVersion.setDependencies(request.getDependencies());
        productVersion.setSystemRequirements(request.getSystemRequirements());
        productVersion.setChecksumMd5(request.getChecksumMd5());
        productVersion.setChecksumSha256(request.getChecksumSha256());
        productVersion.setRemark(request.getRemark());

        // 保存版本
        ProductVersion savedVersion = productVersionRepository.save(productVersion);

        // 记录创建历史
        VersionReleaseHistory history = VersionReleaseHistory.createHistory(
                savedVersion.getId(),
                currentUserId,
                currentUserName,
                getClientIpAddress(),
                getUserAgent());
        productVersionRepository.saveReleaseHistory(history);

        log.info("创建产品版本成功，版本ID: {}, 版本号: {}", savedVersion.getId(), savedVersion.getVersionNumber());
        return savedVersion.getId();
    }

    @Override
    @Transactional
    public void updateProductVersion(Long id, ProductVersionUpdateRequest request) {
        // 查找版本
        ProductVersion productVersion = findProductVersionById(id);

        // 获取当前用户信息
        Long currentUserId = SecurityUtils.getCurrentUserId();
        String currentUserName = SecurityUtils.getCurrentUserName();

        // 更新版本信息
        if (request.getVersionName() != null) {
            productVersion.setVersionName(request.getVersionName());
        }
        if (request.getDescription() != null) {
            productVersion.setDescription(request.getDescription());
        }
        if (request.getReleaseNotes() != null) {
            productVersion.setReleaseNotes(request.getReleaseNotes());
        }
        if (request.getVersionType() != null) {
            productVersion.setVersionType(ProductVersion.VersionType.fromCode(request.getVersionType()));
        }
        if (request.getPlannedDate() != null) {
            productVersion.setPlannedDate(request.getPlannedDate());
        }
        if (request.getFileSize() != null) {
            productVersion.setFileSize(request.getFileSize());
        }
        if (request.getDownloadUrl() != null) {
            productVersion.setDownloadUrl(request.getDownloadUrl());
        }
        if (request.getPlatforms() != null) {
            productVersion.setPlatforms(request.getPlatforms());
        }
        if (request.getFeatures() != null) {
            productVersion.setFeatures(request.getFeatures());
        }
        if (request.getBugFixes() != null) {
            productVersion.setBugFixes(request.getBugFixes());
        }
        if (request.getBreakingChanges() != null) {
            productVersion.setBreakingChanges(request.getBreakingChanges());
        }
        if (request.getDependencies() != null) {
            productVersion.setDependencies(request.getDependencies());
        }
        if (request.getSystemRequirements() != null) {
            productVersion.setSystemRequirements(request.getSystemRequirements());
        }
        if (request.getChecksumMd5() != null) {
            productVersion.setChecksumMd5(request.getChecksumMd5());
        }
        if (request.getChecksumSha256() != null) {
            productVersion.setChecksumSha256(request.getChecksumSha256());
        }
        if (request.getRemark() != null) {
            productVersion.setRemark(request.getRemark());
        }

        productVersion.setUpdateBy(currentUserId);
        productVersion.setUpdateTime(LocalDateTime.now());

        // 保存版本
        productVersionRepository.save(productVersion);

        // 记录更新历史
        VersionReleaseHistory history = VersionReleaseHistory.updateHistory(
                id,
                currentUserId,
                currentUserName,
                "更新版本信息",
                getClientIpAddress(),
                getUserAgent());
        productVersionRepository.saveReleaseHistory(history);

        log.info("更新产品版本成功，版本ID: {}", id);
    }

    @Override
    @Transactional
    public void deleteProductVersion(Long id) {
        // 查找版本
        ProductVersion productVersion = findProductVersionById(id);

        // 检查是否可以删除
        if (!productVersion.canBeDeleted()) {
            throw new BusinessException("当前版本或已发布版本不能删除");
        }

        // 删除版本
        productVersionRepository.deleteById(id);

        log.info("删除产品版本成功，版本ID: {}", id);
    }

    @Override
    @Transactional
    public void deleteProductVersionsBatch(List<Long> ids) {
        // 检查所有版本是否可以删除
        for (Long id : ids) {
            ProductVersion productVersion = findProductVersionById(id);
            if (!productVersion.canBeDeleted()) {
                throw new BusinessException("版本 " + productVersion.getVersionNumber() + " 不能删除");
            }
        }

        // 批量删除
        productVersionRepository.deleteByIds(ids);

        log.info("批量删除产品版本成功，删除数量: {}", ids.size());
    }

    @Override
    public ProductVersionDetailResponse getProductVersionById(Long id) {
        ProductVersion productVersion = findProductVersionById(id);
        return convertToDetailResponse(productVersion);
    }

    @Override
    public ProductVersionListResponse getProductVersionList(int page, int size, Long productLineId,
            String keyword, String status, String versionType) {
        // 转换状态和类型
        ProductVersion.VersionStatus statusEnum = null;
        if (status != null && !status.trim().isEmpty()) {
            statusEnum = ProductVersion.VersionStatus.fromCode(Integer.parseInt(status));
        }

        ProductVersion.VersionType typeEnum = null;
        if (versionType != null && !versionType.trim().isEmpty()) {
            typeEnum = ProductVersion.VersionType.fromCode(Integer.parseInt(versionType));
        }

        // 查询版本列表
        List<ProductVersion> versions = productVersionRepository.findByPage(
                page, size, productLineId, keyword, statusEnum, typeEnum);

        // 统计总数
        long total = productVersionRepository.count(productLineId, keyword, statusEnum, typeEnum);

        // 转换响应
        ProductVersionListResponse response = new ProductVersionListResponse();
        response.setItems(versions.stream()
                .map(this::convertToListItem)
                .collect(Collectors.toList()));
        response.setTotal(total);
        response.setPage(page);
        response.setSize(size);
        response.setTotalPages((int) Math.ceil((double) total / size));

        return response;
    }

    @Override
    public List<ProductVersionSimpleResponse> getVersionsByProductLineId(Long productLineId) {
        List<ProductVersion> versions = productVersionRepository.findByProductLineId(productLineId);
        return versions.stream()
                .map(this::convertToSimpleResponse)
                .collect(Collectors.toList());
    }

    @Override
    public ProductVersionDetailResponse getCurrentVersionByProductLineId(Long productLineId) {
        Optional<ProductVersion> currentVersion = productVersionRepository
                .getCurrentVersionByProductLineId(productLineId);
        return currentVersion.map(this::convertToDetailResponse).orElse(null);
    }

    @Override
    @Transactional
    public void releaseVersion(Long id, String reason) {
        // 查找版本
        ProductVersion productVersion = findProductVersionById(id);

        // 获取当前用户信息
        Long currentUserId = SecurityUtils.getCurrentUserId();
        String currentUserName = SecurityUtils.getCurrentUserName();

        // 记录原状态
        Integer fromStatus = productVersion.getStatus().getCode();

        // 发布版本
        productVersion.release(currentUserId, currentUserName, reason);

        // 保存版本
        productVersionRepository.save(productVersion);

        // 记录发布历史
        VersionReleaseHistory history = VersionReleaseHistory.releaseHistory(
                id,
                fromStatus,
                productVersion.getStatus().getCode(),
                currentUserId,
                currentUserName,
                reason,
                getClientIpAddress(),
                getUserAgent());
        productVersionRepository.saveReleaseHistory(history);

        log.info("发布产品版本成功，版本ID: {}, 版本号: {}", id, productVersion.getVersionNumber());
    }

    @Override
    @Transactional
    public void setCurrentVersion(Long id) {
        // 查找版本
        ProductVersion productVersion = findProductVersionById(id);

        // 清除产品线的当前版本标记
        productVersionRepository.clearCurrentVersionByProductLineId(productVersion.getProductLineId());

        // 设置当前版本
        productVersion.setAsCurrent();
        productVersionRepository.save(productVersion);

        log.info("设置当前版本成功，版本ID: {}, 版本号: {}", id, productVersion.getVersionNumber());
    }

    @Override
    @Transactional
    public void deprecateVersion(Long id, String reason) {
        // 查找版本
        ProductVersion productVersion = findProductVersionById(id);

        // 获取当前用户信息
        Long currentUserId = SecurityUtils.getCurrentUserId();
        String currentUserName = SecurityUtils.getCurrentUserName();

        // 记录原状态
        Integer fromStatus = productVersion.getStatus().getCode();

        // 废弃版本
        productVersion.deprecate(reason);

        // 保存版本
        productVersionRepository.save(productVersion);

        // 记录废弃历史
        VersionReleaseHistory history = VersionReleaseHistory.deprecateHistory(
                id,
                fromStatus,
                currentUserId,
                currentUserName,
                reason,
                getClientIpAddress(),
                getUserAgent());
        productVersionRepository.saveReleaseHistory(history);

        log.info("废弃产品版本成功，版本ID: {}, 版本号: {}", id, productVersion.getVersionNumber());
    }

    @Override
    @Transactional
    public void updateVersionStatus(Long id, String status, String reason) {
        // 查找版本
        ProductVersion productVersion = findProductVersionById(id);

        // 获取当前用户信息
        Long currentUserId = SecurityUtils.getCurrentUserId();
        String currentUserName = SecurityUtils.getCurrentUserName();

        // 记录原状态
        Integer fromStatus = productVersion.getStatus().getCode();

        // 更新状态
        ProductVersion.VersionStatus newStatus = ProductVersion.VersionStatus.fromCode(Integer.parseInt(status));
        productVersion.updateStatus(newStatus, reason);

        // 保存版本
        productVersionRepository.save(productVersion);

        // 记录状态变更历史
        VersionReleaseHistory history = VersionReleaseHistory.releaseHistory(
                id,
                fromStatus,
                newStatus.getCode(),
                currentUserId,
                currentUserName,
                reason,
                getClientIpAddress(),
                getUserAgent());
        productVersionRepository.saveReleaseHistory(history);

        log.info("更新版本状态成功，版本ID: {}, 新状态: {}", id, newStatus.getName());
    }

    @Override
    @Transactional
    public void downloadVersion(Long id, String platform, String region) {
        // 查找版本
        ProductVersion productVersion = findProductVersionById(id);

        // 增加下载次数
        productVersion.increaseDownloadCount(1);
        productVersionRepository.save(productVersion);

        // 记录下载统计
        productVersionRepository.recordDownloadStats(id, platform, region);

        log.info("记录版本下载，版本ID: {}, 平台: {}, 地区: {}", id, platform, region);
    }

    @Override
    public List<VersionReleaseHistoryResponse> getVersionReleaseHistory(Long versionId) {
        List<VersionReleaseHistory> histories = productVersionRepository.findReleaseHistoryByVersionId(versionId);
        return histories.stream()
                .map(this::convertToHistoryResponse)
                .collect(Collectors.toList());
    }

    @Override
    public Object getVersionStatsByProductLineId(Long productLineId) {
        return productVersionRepository.getVersionStatsByProductLineId(productLineId);
    }

    @Override
    public List<Object> getTopDownloadVersions(Integer days, Integer limit) {
        return productVersionRepository.getTopDownloadVersions(days, limit);
    }

    @Override
    public boolean isVersionNumberAvailable(Long productLineId, String versionNumber) {
        return !productVersionRepository.existsByProductLineIdAndVersionNumber(productLineId, versionNumber);
    }

    @Override
    public boolean isVersionNumberAvailable(Long productLineId, String versionNumber, Long excludeId) {
        return !productVersionRepository.existsByProductLineIdAndVersionNumberAndIdNot(productLineId, versionNumber,
                excludeId);
    }

    @Override
    public String suggestNextVersionNumber(Long productLineId, String versionType) {
        String latestVersion = productVersionRepository.getLatestVersionNumber(productLineId);
        if (latestVersion == null) {
            return "1.0.0";
        }

        // 解析版本号
        String[] parts = latestVersion.split("\\.");
        if (parts.length < 3) {
            return "1.0.0";
        }

        int major = Integer.parseInt(parts[0]);
        int minor = Integer.parseInt(parts[1]);
        int patch = Integer.parseInt(parts[2]);

        // 根据版本类型生成下一个版本号
        switch (versionType) {
            case "1": // 主版本
                return (major + 1) + ".0.0";
            case "2": // 次版本
                return major + "." + (minor + 1) + ".0";
            case "3": // 修订版
                return major + "." + minor + "." + (patch + 1);
            default:
                return major + "." + minor + "." + (patch + 1);
        }
    }

    @Override
    @Transactional
    public void updateVersionStatusBatch(List<Long> ids, String status, String reason) {
        ProductVersion.VersionStatus newStatus = ProductVersion.VersionStatus.fromCode(Integer.parseInt(status));

        for (Long id : ids) {
            updateVersionStatus(id, status, reason);
        }

        log.info("批量更新版本状态成功，更新数量: {}, 新状态: {}", ids.size(), newStatus.getName());
    }

    @Override
    public Object getVersionDownloadStats(Long versionId, Integer days) {
        // 获取总下载次数
        Long totalDownloads = productVersionRepository.getTotalDownloadsByVersionId(versionId);
        Long uniqueDownloads = productVersionRepository.getTotalUniqueDownloadsByVersionId(versionId);

        // 创建统计响应对象
        return new VersionDownloadStatsResponse(totalDownloads, uniqueDownloads);
    }

    /**
     * 版本下载统计响应
     */
    private static class VersionDownloadStatsResponse {
        public final Long totalDownloads;
        public final Long uniqueDownloads;

        public VersionDownloadStatsResponse(Long totalDownloads, Long uniqueDownloads) {
            this.totalDownloads = totalDownloads;
            this.uniqueDownloads = uniqueDownloads;
        }
    }

    @Override
    public byte[] exportVersionList(Long productLineId, String format) {
        // TODO: 实现版本列表导出功能
        throw new BusinessException("导出功能暂未实现");
    }

    @Override
    public void syncVersionInfo(Long productLineId) {
        // TODO: 实现版本信息同步功能
        log.info("同步版本信息，产品线ID: {}", productLineId);
    }

    // ==================== 私有工具方法 ====================

    /**
     * 根据ID查找产品版本
     */
    private ProductVersion findProductVersionById(Long id) {
        return productVersionRepository.findById(id)
                .orElseThrow(() -> new BusinessException("产品版本不存在"));
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress() {
        String xForwardedFor = httpServletRequest.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = httpServletRequest.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return httpServletRequest.getRemoteAddr();
    }

    /**
     * 获取用户代理
     */
    private String getUserAgent() {
        return httpServletRequest.getHeader("User-Agent");
    }

    /**
     * 转换为详情响应
     */
    private ProductVersionDetailResponse convertToDetailResponse(ProductVersion productVersion) {
        ProductVersionDetailResponse response = new ProductVersionDetailResponse();
        response.setId(productVersion.getId());
        response.setProductLineId(productVersion.getProductLineId());
        response.setVersionNumber(productVersion.getVersionNumber());
        response.setVersionName(productVersion.getVersionName());
        response.setDescription(productVersion.getDescription());
        response.setReleaseNotes(productVersion.getReleaseNotes());
        response.setVersionType(
                productVersion.getVersionType() != null ? productVersion.getVersionType().getCode() : null);
        response.setVersionTypeName(
                productVersion.getVersionType() != null ? productVersion.getVersionType().getName() : null);
        response.setStatus(productVersion.getStatus() != null ? productVersion.getStatus().getCode() : null);
        response.setStatusName(productVersion.getStatus() != null ? productVersion.getStatus().getName() : null);
        response.setIsCurrent(productVersion.getIsCurrent());
        response.setReleaseDate(productVersion.getReleaseDate());
        response.setPlannedDate(productVersion.getPlannedDate());
        response.setFileSize(productVersion.getFileSize());
        response.setFormattedFileSize(productVersion.getFormattedFileSize());
        response.setDownloadUrl(productVersion.getDownloadUrl());
        response.setDownloadCount(productVersion.getDownloadCount());
        response.setPlatforms(productVersion.getPlatforms());
        response.setFeatures(productVersion.getFeatures());
        response.setBugFixes(productVersion.getBugFixes());
        response.setBreakingChanges(productVersion.getBreakingChanges());
        response.setDependencies(productVersion.getDependencies());
        response.setSystemRequirements(productVersion.getSystemRequirements());
        response.setChecksumMd5(productVersion.getChecksumMd5());
        response.setChecksumSha256(productVersion.getChecksumSha256());
        response.setCreatedBy(productVersion.getCreatedBy());
        response.setCreatedByName(productVersion.getCreatedByName());
        response.setApprovedBy(productVersion.getApprovedBy());
        response.setApprovedByName(productVersion.getApprovedByName());
        response.setApprovedAt(productVersion.getApprovedAt());
        response.setRemark(productVersion.getRemark());
        response.setCreateTime(productVersion.getCreateTime());
        response.setUpdateTime(productVersion.getUpdateTime());
        response.setVersion(productVersion.getVersion());

        // 设置操作权限
        response.setCanBeDeleted(productVersion.canBeDeleted());
        response.setCanBeReleased(productVersion.getStatus() == ProductVersion.VersionStatus.PRERELEASE);
        response.setCanBeSetAsCurrent(
                productVersion.getStatus() == ProductVersion.VersionStatus.RELEASED && !productVersion.getIsCurrent());
        response.setCanBeDeprecated(!productVersion.getIsCurrent()
                && productVersion.getStatus() != ProductVersion.VersionStatus.DEPRECATED);

        return response;
    }

    /**
     * 转换为列表项
     */
    private ProductVersionListResponse.ProductVersionItem convertToListItem(ProductVersion productVersion) {
        ProductVersionListResponse.ProductVersionItem item = new ProductVersionListResponse.ProductVersionItem();
        item.setId(productVersion.getId());
        item.setProductLineId(productVersion.getProductLineId());
        item.setVersionNumber(productVersion.getVersionNumber());
        item.setVersionName(productVersion.getVersionName());
        item.setDescription(productVersion.getDescription());
        item.setVersionType(productVersion.getVersionType() != null ? productVersion.getVersionType().getCode() : null);
        item.setVersionTypeName(
                productVersion.getVersionType() != null ? productVersion.getVersionType().getName() : null);
        item.setStatus(productVersion.getStatus() != null ? productVersion.getStatus().getCode() : null);
        item.setStatusName(productVersion.getStatus() != null ? productVersion.getStatus().getName() : null);
        item.setIsCurrent(productVersion.getIsCurrent());
        item.setReleaseDate(productVersion.getReleaseDate());
        item.setPlannedDate(productVersion.getPlannedDate());
        item.setFileSize(productVersion.getFileSize());
        item.setFormattedFileSize(productVersion.getFormattedFileSize());
        item.setDownloadCount(productVersion.getDownloadCount());
        item.setPlatforms(productVersion.getPlatforms());
        item.setCreatedByName(productVersion.getCreatedByName());
        item.setCreateTime(productVersion.getCreateTime());
        item.setUpdateTime(productVersion.getUpdateTime());

        // 设置操作权限
        item.setCanBeDeleted(productVersion.canBeDeleted());
        item.setCanBeReleased(productVersion.getStatus() == ProductVersion.VersionStatus.PRERELEASE);
        item.setCanBeSetAsCurrent(
                productVersion.getStatus() == ProductVersion.VersionStatus.RELEASED && !productVersion.getIsCurrent());
        item.setCanBeDeprecated(!productVersion.getIsCurrent()
                && productVersion.getStatus() != ProductVersion.VersionStatus.DEPRECATED);

        return item;
    }

    /**
     * 转换为简单响应
     */
    private ProductVersionSimpleResponse convertToSimpleResponse(ProductVersion productVersion) {
        ProductVersionSimpleResponse response = new ProductVersionSimpleResponse();
        response.setId(productVersion.getId());
        response.setVersionNumber(productVersion.getVersionNumber());
        response.setVersionName(productVersion.getVersionName());
        response.setStatus(productVersion.getStatus() != null ? productVersion.getStatus().getCode() : null);
        response.setStatusName(productVersion.getStatus() != null ? productVersion.getStatus().getName() : null);
        response.setIsCurrent(productVersion.getIsCurrent());
        response.setReleaseDate(productVersion.getReleaseDate());
        response.setDownloadCount(productVersion.getDownloadCount());
        response.setCreateTime(productVersion.getCreateTime());
        return response;
    }

    /**
     * 转换为历史响应
     */
    private VersionReleaseHistoryResponse convertToHistoryResponse(VersionReleaseHistory history) {
        VersionReleaseHistoryResponse response = new VersionReleaseHistoryResponse();
        response.setId(history.getId());
        response.setVersionId(history.getVersionId());
        response.setActionType(history.getActionType() != null ? history.getActionType().getCode() : null);
        response.setActionTypeName(history.getActionType() != null ? history.getActionType().getName() : null);
        response.setFromStatus(history.getFromStatus());
        response.setFromStatusName(getStatusName(history.getFromStatus()));
        response.setToStatus(history.getToStatus());
        response.setToStatusName(getStatusName(history.getToStatus()));
        response.setActionReason(history.getActionReason());
        response.setActionBy(history.getActionBy());
        response.setActionByName(history.getActionByName());
        response.setActionTime(history.getActionTime());
        response.setIpAddress(history.getIpAddress());
        response.setActionDescription(history.getActionDescription());
        response.setRiskLevel(history.getRiskLevel());
        response.setIsImportantAction(history.isImportantAction());
        response.setRemark(history.getRemark());
        return response;
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null)
            return null;
        ProductVersion.VersionStatus statusEnum = ProductVersion.VersionStatus.fromCode(status);
        return statusEnum != null ? statusEnum.getName() : null;
    }
}
