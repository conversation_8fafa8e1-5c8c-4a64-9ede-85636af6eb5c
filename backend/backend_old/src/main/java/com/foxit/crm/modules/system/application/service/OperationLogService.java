package com.foxit.crm.modules.system.application.service;

import com.foxit.crm.modules.system.api.dto.response.OperationLogDetailResponse;
import com.foxit.crm.modules.system.api.dto.response.OperationLogListResponse;
import com.foxit.crm.modules.system.api.dto.response.OperationLogStatisticsResponse;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作日志应用服务接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface OperationLogService {

    /**
     * 记录操作日志
     */
    void recordOperationLog(String title, String operation, String method, String requestMethod,
                           String requestUrl, String requestParams, String responseResult,
                           Long userId, String username, String clientIp, String userAgent,
                           Integer status, String errorMessage, Long executionTime);

    /**
     * 根据ID获取操作日志详情
     */
    OperationLogDetailResponse getOperationLogById(Long id);

    /**
     * 分页查询操作日志列表
     */
    OperationLogListResponse getOperationLogList(int page, int size, String keyword, Integer status,
                                                LocalDateTime startTime, LocalDateTime endTime, Long userId);

    /**
     * 获取用户操作日志列表
     */
    OperationLogListResponse getUserOperationLogs(Long userId, int page, int size);

    /**
     * 获取操作类型日志列表
     */
    OperationLogListResponse getOperationLogsByType(String operation, int page, int size);

    /**
     * 获取失败操作日志列表
     */
    OperationLogListResponse getFailedOperationLogs(int page, int size);

    /**
     * 获取慢操作日志列表
     */
    OperationLogListResponse getSlowOperationLogs(Long minExecutionTime, int page, int size);

    /**
     * 获取敏感操作日志列表
     */
    OperationLogListResponse getSensitiveOperationLogs(int page, int size);

    /**
     * 根据IP地址获取操作日志列表
     */
    OperationLogListResponse getOperationLogsByIp(String clientIp, int page, int size);

    /**
     * 获取最近操作日志
     */
    List<OperationLogDetailResponse> getRecentOperationLogs(int limit);

    /**
     * 获取用户最近操作日志
     */
    List<OperationLogDetailResponse> getUserRecentOperationLogs(Long userId, int limit);

    /**
     * 获取操作日志统计信息
     */
    OperationLogStatisticsResponse getOperationLogStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取今日操作统计
     */
    OperationLogStatisticsResponse getTodayOperationStatistics();

    /**
     * 获取本周操作统计
     */
    OperationLogStatisticsResponse getWeekOperationStatistics();

    /**
     * 获取本月操作统计
     */
    OperationLogStatisticsResponse getMonthOperationStatistics();

    /**
     * 清理过期操作日志
     */
    void cleanExpiredOperationLogs(int retentionDays);

    /**
     * 导出操作日志
     */
    byte[] exportOperationLogs(String keyword, Integer status, LocalDateTime startTime, 
                              LocalDateTime endTime, Long userId);

    /**
     * 生成审计报告
     */
    byte[] generateAuditReport(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取公共操作日志列表（无需认证）
     */
    OperationLogListResponse getPublicOperationLogList(int page, int size, String keyword, Integer status,
                                                      LocalDateTime startTime, LocalDateTime endTime, Long userId);
}
