package com.foxit.crm.modules.system.api.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 创建系统配置请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
public class SystemConfigCreateRequest {

    /**
     * 配置键
     */
    @NotBlank(message = "配置键不能为空")
    @Size(max = 100, message = "配置键长度不能超过100个字符")
    @Pattern(regexp = "^[a-z][a-z0-9._]*$", message = "配置键必须以小写字母开头，只能包含小写字母、数字、点和下划线")
    private String configKey;

    /**
     * 配置值
     */
    @NotBlank(message = "配置值不能为空")
    @Size(max = 2000, message = "配置值长度不能超过2000个字符")
    private String configValue;

    /**
     * 配置名称
     */
    @NotBlank(message = "配置名称不能为空")
    @Size(max = 100, message = "配置名称长度不能超过100个字符")
    private String configName;

    /**
     * 配置描述
     */
    @Size(max = 500, message = "配置描述长度不能超过500个字符")
    private String description;

    /**
     * 配置类型：1-字符串，2-数字，3-布尔值，4-JSON
     */
    @NotNull(message = "配置类型不能为空")
    private Integer configType;

    /**
     * 配置分组
     */
    @NotBlank(message = "配置分组不能为空")
    @Size(max = 50, message = "配置分组长度不能超过50个字符")
    private String configGroup;

    /**
     * 是否系统内置：0-否，1-是
     */
    @NotNull(message = "是否系统内置不能为空")
    private Integer isSystem;

    /**
     * 状态：0-禁用，1-启用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
