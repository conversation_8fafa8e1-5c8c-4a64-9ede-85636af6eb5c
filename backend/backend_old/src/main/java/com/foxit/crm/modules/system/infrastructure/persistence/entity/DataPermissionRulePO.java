package com.foxit.crm.modules.system.infrastructure.persistence.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 数据权限规则持久化对象
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_data_permission_rule")
public class DataPermissionRulePO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 规则名称
     */
    @TableField("rule_name")
    private String ruleName;

    /**
     * 规则编码
     */
    @TableField("rule_code")
    private String ruleCode;

    /**
     * 规则描述
     */
    @TableField("description")
    private String description;

    /**
     * 权限类型：1-行级权限，2-列级权限，3-查询权限
     */
    @TableField("permission_type")
    private Integer permissionType;

    /**
     * 目标表名
     */
    @TableField("table_name")
    private String tableName;

    /**
     * 目标字段名（列级权限使用）
     */
    @TableField("column_name")
    private String columnName;

    /**
     * 权限条件表达式
     */
    @TableField("condition_expression")
    private String conditionExpression;

    /**
     * 权限范围：1-全部数据，2-本人数据，3-本部门数据，4-本部门及下级数据，5-自定义条件
     */
    @TableField("permission_scope")
    private Integer permissionScope;

    /**
     * 角色ID（关联角色）
     */
    @TableField("role_id")
    private Long roleId;

    /**
     * 用户ID（关联用户，优先级高于角色）
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 产品线ID（数据隔离）
     */
    @TableField("product_line_id")
    private Long productLineId;

    /**
     * 优先级（数字越小优先级越高）
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 状态：0-禁用，1-启用
     */
    @TableField("status")
    private Integer status;

    /**
     * 排序号
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 更新人ID
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 逻辑删除标志：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 版本号（乐观锁）
     */
    @Version
    @TableField("version")
    private Integer version;
}
