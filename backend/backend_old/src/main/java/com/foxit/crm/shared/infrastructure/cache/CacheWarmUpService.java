package com.foxit.crm.shared.infrastructure.cache;

import com.foxit.crm.modules.system.application.service.PermissionService;
import com.foxit.crm.modules.system.application.service.ProductLineService;
import com.foxit.crm.modules.system.application.service.SystemConfigService;
import com.foxit.crm.shared.infrastructure.statistics.StatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

/**
 * 缓存预热服务
 * 在应用启动时预热关键缓存数据
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CacheWarmUpService implements ApplicationRunner {

    private final DistributedCacheService distributedCacheService;
    private final SystemConfigService systemConfigService;
    private final PermissionService permissionService;
    private final ProductLineService productLineService;
    private final StatisticsService statisticsService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始执行缓存预热...");

        // 异步预热各个模块的缓存
        warmUpSystemConfigCache();
        warmUpPermissionCache();
        warmUpProductLineCache();
        warmUpStatisticsCache();

        log.info("缓存预热任务已启动");
    }

    /**
     * 预热系统配置缓存
     */
    private void warmUpSystemConfigCache() {
        distributedCacheService.warmUpCacheAsync("system-config", () -> {
            try {
                // 预热启用的系统配置
                systemConfigService.getAllEnabledSystemConfigs();

                // 预热系统配置
                systemConfigService.getSystemConfigs();

                // 预热用户配置
                systemConfigService.getUserConfigs();

                // 预热配置分组
                systemConfigService.getAllConfigGroups();

                log.debug("系统配置缓存预热完成");
            } catch (Exception e) {
                log.error("系统配置缓存预热失败", e);
            }
        });
    }

    /**
     * 预热权限缓存
     */
    private void warmUpPermissionCache() {
        distributedCacheService.warmUpCacheAsync("permission", () -> {
            try {
                // 预热权限树
                permissionService.getPermissionTree();

                // 预热菜单权限树
                permissionService.getEnabledMenuPermissions();

                log.debug("权限缓存预热完成");
            } catch (Exception e) {
                log.error("权限缓存预热失败", e);
            }
        });
    }

    /**
     * 预热产品线缓存
     */
    private void warmUpProductLineCache() {
        distributedCacheService.warmUpCacheAsync("product-line", () -> {
            try {
                // 预热启用的产品线列表
                productLineService.getAllEnabledProductLines();

                log.debug("产品线缓存预热完成");
            } catch (Exception e) {
                log.error("产品线缓存预热失败", e);
            }
        });
    }

    /**
     * 预热统计数据缓存
     */
    private void warmUpStatisticsCache() {
        distributedCacheService.warmUpCacheAsync("statistics", () -> {
            try {
                // 预热统计数据缓存
                statisticsService.warmUpStatisticsCache();

                log.debug("统计数据缓存预热完成");
            } catch (Exception e) {
                log.error("统计数据缓存预热失败", e);
            }
        });
    }

    /**
     * 手动触发缓存预热
     */
    public void manualWarmUp() {
        log.info("手动触发缓存预热...");

        warmUpSystemConfigCache();
        warmUpPermissionCache();
        warmUpProductLineCache();
        warmUpStatisticsCache();

        log.info("手动缓存预热任务已启动");
    }

    /**
     * 预热指定模块的缓存
     */
    public void warmUpModule(String module) {
        log.info("预热模块缓存: {}", module);

        switch (module.toLowerCase()) {
            case "system-config":
                warmUpSystemConfigCache();
                break;
            case "permission":
                warmUpPermissionCache();
                break;
            case "product-line":
                warmUpProductLineCache();
                break;
            case "statistics":
                warmUpStatisticsCache();
                break;
            default:
                log.warn("未知的缓存模块: {}", module);
        }
    }

    /**
     * 清除所有缓存并重新预热
     */
    public void refreshAllCache() {
        log.info("清除所有缓存并重新预热...");

        try {
            // 清除系统配置缓存
            systemConfigService.refreshConfigCache();

            // 重新预热
            manualWarmUp();

            log.info("缓存刷新完成");
        } catch (Exception e) {
            log.error("缓存刷新失败", e);
            throw new RuntimeException("缓存刷新失败", e);
        }
    }
}
