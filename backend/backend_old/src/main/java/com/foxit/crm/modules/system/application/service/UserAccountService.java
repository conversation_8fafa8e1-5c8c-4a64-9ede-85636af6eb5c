package com.foxit.crm.modules.system.application.service;

import com.foxit.crm.modules.system.api.dto.request.RegisterRequest;
import com.foxit.crm.modules.system.api.dto.response.LoginResponse;
import com.foxit.crm.modules.system.api.dto.response.UserInfoResponse;

/**
 * 用户账户应用服务接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface UserAccountService {

    /**
     * 用户登录
     *
     * @param username 用户名
     * @param password 密码
     * @param loginIp 登录IP
     * @return 登录响应
     */
    LoginResponse login(String username, String password, String loginIp);

    /**
     * 用户注册
     *
     * @param request 注册请求
     */
    void register(RegisterRequest request);

    /**
     * 用户登出
     *
     * @param token JWT令牌
     */
    void logout(String token);

    /**
     * 根据令牌获取用户信息
     *
     * @param token JWT令牌
     * @return 用户信息
     */
    UserInfoResponse getUserInfoByToken(String token);
}
