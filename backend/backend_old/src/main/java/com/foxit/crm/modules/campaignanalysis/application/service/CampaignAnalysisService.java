package com.foxit.crm.modules.campaignanalysis.application.service;

import com.foxit.crm.modules.campaignanalysis.application.dto.CampaignAnalysisRequest;
import com.foxit.crm.modules.campaignanalysis.application.dto.CampaignAnalysisResponse;
import com.foxit.crm.modules.campaignanalysis.domain.entity.CampaignAggregate;
import com.foxit.crm.modules.campaignanalysis.domain.repository.CampaignAnalysisRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 活动分析应用服务
 * 处理活动分析相关的业务逻辑
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CampaignAnalysisService {

    private final CampaignAnalysisRepository campaignAnalysisRepository;

    /**
     * 获取活动总览
     */
    @Cacheable(value = "campaign:overview", key = "#request.cacheKey()")
    public CampaignAnalysisResponse getCampaignOverview(CampaignAnalysisRequest request) {
        log.info("获取活动总览数据: {}", request);

        try {
            // 验证请求参数
            validateRequest(request);

            // 构建时间范围
            TimeRange timeRange = buildTimeRange(request);

            // 获取数据
            Optional<CampaignAggregate> aggregateOpt = campaignAnalysisRepository
                    .getCampaignOverview(timeRange, request.getCampaignIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                log.warn("未找到活动总览数据: timeRange={}, dataScope={}", timeRange, request.getDataScope());
                return CampaignAnalysisResponse.empty("未找到活动数据");
            }

            CampaignAggregate aggregate = aggregateOpt.get();

            // 转换为响应对象
            CampaignAnalysisResponse response = CampaignAnalysisResponse.success(aggregate);

            log.info("成功获取活动总览数据: 活动数量={}",
                    aggregate.getCampaignList() != null ? aggregate.getCampaignList().size() : 0);

            return response;

        } catch (Exception e) {
            log.error("获取活动总览数据失败: {}", e.getMessage(), e);
            return CampaignAnalysisResponse.error("获取活动总览数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取活动效果分析
     */
    @Cacheable(value = "campaign:performance", key = "#request.cacheKey()")
    public CampaignAnalysisResponse getCampaignPerformance(CampaignAnalysisRequest request) {
        log.info("获取活动效果分析数据: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<CampaignAggregate> aggregateOpt = campaignAnalysisRepository
                    .getCampaignPerformance(timeRange, request.getCampaignIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                return CampaignAnalysisResponse.empty("未找到活动效果数据");
            }

            return CampaignAnalysisResponse.success(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取活动效果分析数据失败: {}", e.getMessage(), e);
            return CampaignAnalysisResponse.error("获取活动效果分析数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取渠道分析
     */
    @Cacheable(value = "campaign:channel", key = "#request.cacheKey()")
    public CampaignAnalysisResponse getChannelAnalysis(CampaignAnalysisRequest request) {
        log.info("获取渠道分析数据: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<CampaignAggregate> aggregateOpt = campaignAnalysisRepository
                    .getChannelAnalysis(timeRange, request.getCampaignIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                return CampaignAnalysisResponse.empty("未找到渠道分析数据");
            }

            return CampaignAnalysisResponse.success(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取渠道分析数据失败: {}", e.getMessage(), e);
            return CampaignAnalysisResponse.error("获取渠道分析数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取ROI分析
     */
    @Cacheable(value = "campaign:roi", key = "#request.cacheKey()")
    public CampaignAnalysisResponse getROIAnalysis(CampaignAnalysisRequest request) {
        log.info("获取ROI分析数据: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<CampaignAggregate> aggregateOpt = campaignAnalysisRepository
                    .getROIAnalysis(timeRange, request.getCampaignIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                return CampaignAnalysisResponse.empty("未找到ROI分析数据");
            }

            return CampaignAnalysisResponse.success(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取ROI分析数据失败: {}", e.getMessage(), e);
            return CampaignAnalysisResponse.error("获取ROI分析数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取趋势分析
     */
    @Cacheable(value = "campaign:trend", key = "#request.cacheKey()")
    public CampaignAnalysisResponse getTrendAnalysis(CampaignAnalysisRequest request) {
        log.info("获取趋势分析数据: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<CampaignAggregate> aggregateOpt = campaignAnalysisRepository
                    .getTrendAnalysis(timeRange, request.getCampaignIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                return CampaignAnalysisResponse.empty("未找到趋势分析数据");
            }

            return CampaignAnalysisResponse.success(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取趋势分析数据失败: {}", e.getMessage(), e);
            return CampaignAnalysisResponse.error("获取趋势分析数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取实时统计
     */
    public CampaignAnalysisRepository.CampaignRealTimeStats getRealTimeStats(CampaignAnalysisRequest request) {
        log.info("获取实时活动统计: {}", request);

        try {
            return campaignAnalysisRepository.getRealTimeStats(request.getCampaignIds(), request.getDataScope());

        } catch (Exception e) {
            log.error("获取实时活动统计失败: {}", e.getMessage(), e);
            return new CampaignAnalysisRepository.CampaignRealTimeStats(0L, 0L, 0L, 0L, 0L, 0.0, 0.0, 0.0);
        }
    }

    /**
     * 获取分析摘要
     */
    public CampaignAnalysisRepository.CampaignAnalysisSummary getAnalysisSummary(CampaignAnalysisRequest request) {
        log.info("获取活动分析摘要: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            return campaignAnalysisRepository.getAnalysisSummary(timeRange, request.getDataScope());

        } catch (Exception e) {
            log.error("获取活动分析摘要失败: {}", e.getMessage(), e);
            return new CampaignAnalysisRepository.CampaignAnalysisSummary(
                    "error", "ERROR", null, 0L, 0L, 0.0, "无", "获取数据失败"
            );
        }
    }

    /**
     * 验证请求参数
     */
    private void validateRequest(CampaignAnalysisRequest request) {
        if (request.getStartDate() == null || request.getEndDate() == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }

        if (request.getStartDate().isAfter(request.getEndDate())) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }

        if (request.getDataScope() == null || request.getDataScope().trim().isEmpty()) {
            throw new IllegalArgumentException("数据权限范围不能为空");
        }
    }

    /**
     * 构建时间范围
     */
    private TimeRange buildTimeRange(CampaignAnalysisRequest request) {
        TimeRange.TimeGranularity granularity = request.getGranularity() != null 
                ? request.getGranularity() 
                : TimeRange.TimeGranularity.DAY;

        return TimeRange.of(request.getStartDate(), request.getEndDate(), granularity);
    }
}
