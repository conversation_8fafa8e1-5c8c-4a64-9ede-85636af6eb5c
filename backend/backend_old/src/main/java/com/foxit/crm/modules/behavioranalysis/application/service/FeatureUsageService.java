package com.foxit.crm.modules.behavioranalysis.application.service;

import com.foxit.crm.modules.behavioranalysis.application.dto.FeatureUsageRequest;
import com.foxit.crm.modules.behavioranalysis.application.dto.FeatureUsageResponse;
import com.foxit.crm.modules.behavioranalysis.domain.entity.FeatureUsageAggregate;
import com.foxit.crm.modules.behavioranalysis.domain.repository.FeatureUsageRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 功能使用分析应用服务
 * 协调功能使用分析的业务流程
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FeatureUsageService {

    private final FeatureUsageRepository featureUsageRepository;

    /**
     * 获取功能使用统计分析
     */
    public FeatureUsageResponse getFeatureUsageStatistics(FeatureUsageRequest request) {
        log.info("获取功能使用统计分析: request={}", request);

        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());

            // 验证请求参数
            validateFeatureUsageRequest(request);

            // 获取功能使用统计分析数据
            Optional<FeatureUsageAggregate> aggregateOpt = featureUsageRepository.getFeatureUsageStatistics(
                    timeRange, request.getFeatureIds(), request.getProductLineIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                log.warn("未找到功能使用统计分析数据: timeRange={}, featureIds={}", timeRange, request.getFeatureIds());
                return FeatureUsageResponse.empty();
            }

            FeatureUsageAggregate aggregate = aggregateOpt.get();

            // 转换为响应DTO
            return convertToResponse(aggregate);

        } catch (Exception e) {
            log.error("获取功能使用统计分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取功能使用统计分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取功能热度分析
     */
    public FeatureUsageResponse getFeatureHeatAnalysis(FeatureUsageRequest request) {
        log.info("获取功能热度分析: request={}", request);

        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());

            // 验证请求参数
            validateFeatureUsageRequest(request);

            // 获取功能热度分析数据
            Optional<FeatureUsageAggregate> aggregateOpt = featureUsageRepository.getFeatureHeatAnalysis(
                    timeRange, request.getFeatureIds(), request.getProductLineIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                log.warn("未找到功能热度分析数据: timeRange={}, featureIds={}", timeRange, request.getFeatureIds());
                return FeatureUsageResponse.empty();
            }

            FeatureUsageAggregate aggregate = aggregateOpt.get();

            // 转换为响应DTO
            return convertToResponse(aggregate);

        } catch (Exception e) {
            log.error("获取功能热度分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取功能热度分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取功能路径分析
     */
    public FeatureUsageResponse getFeaturePathAnalysis(FeatureUsageRequest request) {
        log.info("获取功能路径分析: request={}", request);

        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());

            // 验证请求参数
            validateFeatureUsageRequest(request);

            // 获取功能路径分析数据
            Optional<FeatureUsageAggregate> aggregateOpt = featureUsageRepository.getFeaturePathAnalysis(
                    timeRange, request.getStartFeatureIds(), request.getEndFeatureIds(),
                    request.getProductLineIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                log.warn("未找到功能路径分析数据: timeRange={}, startFeatureIds={}, endFeatureIds={}",
                        timeRange, request.getStartFeatureIds(), request.getEndFeatureIds());
                return FeatureUsageResponse.empty();
            }

            FeatureUsageAggregate aggregate = aggregateOpt.get();

            // 转换为响应DTO
            return convertToResponse(aggregate);

        } catch (Exception e) {
            log.error("获取功能路径分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取功能路径分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取功能价值贡献分析
     */
    public FeatureUsageResponse getFeatureValueContribution(FeatureUsageRequest request) {
        log.info("获取功能价值贡献分析: request={}", request);

        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());

            // 验证请求参数
            validateFeatureUsageRequest(request);

            // 获取功能价值贡献分析数据
            Optional<FeatureUsageAggregate> aggregateOpt = featureUsageRepository.getFeatureValueContribution(
                    timeRange, request.getFeatureIds(), request.getProductLineIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                log.warn("未找到功能价值贡献分析数据: timeRange={}, featureIds={}", timeRange, request.getFeatureIds());
                return FeatureUsageResponse.empty();
            }

            FeatureUsageAggregate aggregate = aggregateOpt.get();

            // 转换为响应DTO
            return convertToResponse(aggregate);

        } catch (Exception e) {
            log.error("获取功能价值贡献分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取功能价值贡献分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取功能满意度评估
     */
    public FeatureUsageResponse getFeatureSatisfactionEvaluation(FeatureUsageRequest request) {
        log.info("获取功能满意度评估: request={}", request);

        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());

            // 验证请求参数
            validateFeatureUsageRequest(request);

            // 获取功能满意度评估数据
            Optional<FeatureUsageAggregate> aggregateOpt = featureUsageRepository.getFeatureSatisfactionEvaluation(
                    timeRange, request.getFeatureIds(), request.getProductLineIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                log.warn("未找到功能满意度评估数据: timeRange={}, featureIds={}", timeRange, request.getFeatureIds());
                return FeatureUsageResponse.empty();
            }

            FeatureUsageAggregate aggregate = aggregateOpt.get();

            // 转换为响应DTO
            return convertToResponse(aggregate);

        } catch (Exception e) {
            log.error("获取功能满意度评估失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取功能满意度评估失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取功能列表
     */
    public List<FeatureUsageRepository.FeatureInfo> getFeatureList(List<Long> productLineIds, String dataScope) {
        log.info("获取功能列表: productLineIds={}, dataScope={}", productLineIds, dataScope);

        try {
            return featureUsageRepository.getFeatureList(productLineIds, dataScope);
        } catch (Exception e) {
            log.error("获取功能列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取功能列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取实时功能使用统计
     */
    public FeatureUsageRepository.FeatureRealTimeStats getRealTimeFeatureStats(List<String> featureIds,
            List<Long> productLineIds, String dataScope) {
        log.info("获取实时功能使用统计: featureIds={}, productLineIds={}, dataScope={}", featureIds, productLineIds, dataScope);

        try {
            return featureUsageRepository.getRealTimeFeatureStats(featureIds, productLineIds, dataScope);
        } catch (Exception e) {
            log.error("获取实时功能使用统计失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取实时功能使用统计失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取功能使用分析统计摘要
     */
    public FeatureUsageRepository.FeatureUsageAnalysisSummary getFeatureUsageAnalysisSummary(
            FeatureUsageRequest request) {
        log.info("获取功能使用分析统计摘要: request={}", request);

        try {
            // 构建时间范围
            TimeRange timeRange = TimeRange.of(request.getStartDate(), request.getEndDate(), request.getGranularity());

            return featureUsageRepository.getFeatureUsageAnalysisSummary(timeRange, request.getDataScope());
        } catch (Exception e) {
            log.error("获取功能使用分析统计摘要失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取功能使用分析统计摘要失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取功能使用趋势对比
     */
    public FeatureUsageRepository.FeatureUsageTrendComparison getFeatureUsageTrendComparison(
            FeatureUsageRequest request) {
        log.info("获取功能使用趋势对比: request={}", request);

        try {
            // 构建当前时间范围
            TimeRange currentTimeRange = TimeRange.of(request.getStartDate(), request.getEndDate(),
                    request.getGranularity());

            // 构建对比时间范围（前一个周期）
            long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(request.getStartDate(), request.getEndDate());
            LocalDate previousStartDate = request.getStartDate().minusDays(daysBetween + 1);
            LocalDate previousEndDate = request.getStartDate().minusDays(1);
            TimeRange previousTimeRange = TimeRange.of(previousStartDate, previousEndDate, request.getGranularity());

            return featureUsageRepository.getFeatureUsageTrendComparison(
                    currentTimeRange, previousTimeRange, request.getFeatureIds(),
                    request.getProductLineIds(), request.getDataScope());
        } catch (Exception e) {
            log.error("获取功能使用趋势对比失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取功能使用趋势对比失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证功能使用分析请求参数
     */
    private void validateFeatureUsageRequest(FeatureUsageRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("功能使用分析请求不能为空");
        }

        if (request.getStartDate() == null || request.getEndDate() == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }

        if (request.getStartDate().isAfter(request.getEndDate())) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }

        if (request.getDataScope() == null || request.getDataScope().trim().isEmpty()) {
            throw new IllegalArgumentException("数据权限范围不能为空");
        }
    }

    /**
     * 转换聚合根为响应DTO
     */
    private FeatureUsageResponse convertToResponse(FeatureUsageAggregate aggregate) {
        // TODO: 实现具体的转换逻辑
        return FeatureUsageResponse.builder()
                .analysisId(aggregate.getId())
                .analysisType(aggregate.getAnalysisType().name())
                .timeRange(aggregate.getTimeRange())
                .coreMetrics(aggregate.getCoreMetrics())
                .usageStats(aggregate.getUsageStats())
                .heatData(aggregate.getHeatData())
                .pathData(aggregate.getPathData())
                .valueData(aggregate.getValueData())
                .satisfactionData(aggregate.getSatisfactionData())
                .lastUpdateTime(aggregate.getLastUpdateTime())
                .build();
    }
}
