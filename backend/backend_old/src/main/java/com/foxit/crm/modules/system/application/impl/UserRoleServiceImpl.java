package com.foxit.crm.modules.system.application.impl;

import com.foxit.crm.common.exception.BusinessException;
import com.foxit.crm.modules.system.application.service.UserRoleService;
import com.foxit.crm.modules.system.domain.model.aggregate.UserRole;
import com.foxit.crm.modules.system.domain.model.valueobject.UserId;
import com.foxit.crm.modules.system.domain.repository.RoleRepository;
import com.foxit.crm.modules.system.domain.repository.UserRepository;
import com.foxit.crm.modules.system.domain.repository.UserRoleRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户角色关联应用服务实现
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
@RequiredArgsConstructor
public class UserRoleServiceImpl implements UserRoleService {

    private final UserRoleRepository userRoleRepository;
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;

    @Override
    @Transactional
    public void assignRolesToUser(Long userId, List<Long> roleIds) {
        // 检查用户是否存在
        userRepository.findById(new UserId(userId))
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 检查角色是否存在
        for (Long roleId : roleIds) {
            roleRepository.findById(roleId)
                    .orElseThrow(() -> new BusinessException("角色不存在：" + roleId));
        }

        // 先删除用户现有的角色关联
        userRoleRepository.deleteByUserId(userId);

        // 创建新的角色关联
        List<UserRole> userRoles = roleIds.stream()
                .map(roleId -> new UserRole(userId, roleId))
                .collect(Collectors.toList());

        // 批量保存
        userRoleRepository.saveBatch(userRoles);
    }

    @Override
    @Transactional
    public void assignRoleToUser(Long userId, Long roleId) {
        // 检查用户是否存在
        userRepository.findById(new UserId(userId))
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 检查角色是否存在
        roleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException("角色不存在"));

        // 检查是否已经关联
        if (userRoleRepository.existsByUserIdAndRoleId(userId, roleId)) {
            throw new BusinessException("用户已拥有该角色");
        }

        // 创建关联
        UserRole userRole = new UserRole(userId, roleId);
        userRoleRepository.save(userRole);
    }

    @Override
    @Transactional
    public void removeRoleFromUser(Long userId, Long roleId) {
        // 检查关联是否存在
        if (!userRoleRepository.existsByUserIdAndRoleId(userId, roleId)) {
            throw new BusinessException("用户角色关联不存在");
        }

        // 删除关联
        userRoleRepository.deleteByUserIdAndRoleId(userId, roleId);
    }

    @Override
    @Transactional
    public void removeAllRolesFromUser(Long userId) {
        // 检查用户是否存在
        userRepository.findById(new UserId(userId))
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 删除用户的所有角色关联
        userRoleRepository.deleteByUserId(userId);
    }

    @Override
    @Transactional
    public void assignUsersToRole(Long roleId, List<Long> userIds) {
        // 检查角色是否存在
        roleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException("角色不存在"));

        // 检查用户是否存在
        for (Long userId : userIds) {
            userRepository.findById(new UserId(userId))
                    .orElseThrow(() -> new BusinessException("用户不存在：" + userId));
        }

        // 先删除角色现有的用户关联
        userRoleRepository.deleteByRoleId(roleId);

        // 创建新的用户关联
        List<UserRole> userRoles = userIds.stream()
                .map(userId -> new UserRole(userId, roleId))
                .collect(Collectors.toList());

        // 批量保存
        userRoleRepository.saveBatch(userRoles);
    }

    @Override
    @Transactional
    public void removeUserFromRole(Long roleId, Long userId) {
        // 检查关联是否存在
        if (!userRoleRepository.existsByUserIdAndRoleId(userId, roleId)) {
            throw new BusinessException("用户角色关联不存在");
        }

        // 删除关联
        userRoleRepository.deleteByUserIdAndRoleId(userId, roleId);
    }

    @Override
    @Transactional
    public void removeAllUsersFromRole(Long roleId) {
        // 检查角色是否存在
        roleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException("角色不存在"));

        // 删除角色的所有用户关联
        userRoleRepository.deleteByRoleId(roleId);
    }

    @Override
    public List<Long> getUserRoleIds(Long userId) {
        return userRoleRepository.findRoleIdsByUserId(userId);
    }

    @Override
    public List<Long> getRoleUserIds(Long roleId) {
        return userRoleRepository.findUserIdsByRoleId(roleId);
    }

    @Override
    public boolean hasRole(Long userId, Long roleId) {
        return userRoleRepository.existsByUserIdAndRoleId(userId, roleId);
    }

    @Override
    public boolean hasAnyRole(Long userId, List<Long> roleIds) {
        List<Long> userRoleIds = userRoleRepository.findRoleIdsByUserId(userId);
        return userRoleIds.stream().anyMatch(roleIds::contains);
    }

    @Override
    public boolean hasAllRoles(Long userId, List<Long> roleIds) {
        List<Long> userRoleIds = userRoleRepository.findRoleIdsByUserId(userId);
        return userRoleIds.containsAll(roleIds);
    }

    @Override
    public long countUsersByRole(Long roleId) {
        return userRoleRepository.countByRoleId(roleId);
    }

    @Override
    public long countRolesByUser(Long userId) {
        return userRoleRepository.countByUserId(userId);
    }

    @Override
    @Transactional
    public void removeUserRolesByUserIds(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return;
        }
        userRoleRepository.deleteByUserIds(userIds);
    }

    @Override
    @Transactional
    public void removeUserRolesByRoleIds(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return;
        }
        userRoleRepository.deleteByRoleIds(roleIds);
    }
}
