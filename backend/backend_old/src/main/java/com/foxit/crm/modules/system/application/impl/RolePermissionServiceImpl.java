package com.foxit.crm.modules.system.application.impl;

import com.foxit.crm.common.exception.BusinessException;
import com.foxit.crm.modules.system.application.service.RolePermissionService;
import com.foxit.crm.modules.system.domain.model.aggregate.RolePermission;
import com.foxit.crm.modules.system.domain.repository.PermissionRepository;
import com.foxit.crm.modules.system.domain.repository.RolePermissionRepository;
import com.foxit.crm.modules.system.domain.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色权限关联应用服务实现
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
@RequiredArgsConstructor
public class RolePermissionServiceImpl implements RolePermissionService {

    private final RolePermissionRepository rolePermissionRepository;
    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;

    @Override
    @Transactional
    public void assignPermissionsToRole(Long roleId, List<Long> permissionIds) {
        // 检查角色是否存在
        roleRepository.findById(roleId)
            .orElseThrow(() -> new BusinessException("角色不存在"));

        // 检查权限是否存在
        for (Long permissionId : permissionIds) {
            permissionRepository.findById(permissionId)
                .orElseThrow(() -> new BusinessException("权限不存在：" + permissionId));
        }

        // 先删除角色现有的权限关联
        rolePermissionRepository.deleteByRoleId(roleId);

        // 创建新的权限关联
        List<RolePermission> rolePermissions = permissionIds.stream()
            .map(permissionId -> new RolePermission(roleId, permissionId))
            .collect(Collectors.toList());

        // 批量保存
        rolePermissionRepository.saveBatch(rolePermissions);
    }

    @Override
    @Transactional
    public void assignPermissionToRole(Long roleId, Long permissionId) {
        // 检查角色是否存在
        roleRepository.findById(roleId)
            .orElseThrow(() -> new BusinessException("角色不存在"));

        // 检查权限是否存在
        permissionRepository.findById(permissionId)
            .orElseThrow(() -> new BusinessException("权限不存在"));

        // 检查是否已经关联
        if (rolePermissionRepository.existsByRoleIdAndPermissionId(roleId, permissionId)) {
            throw new BusinessException("角色已拥有该权限");
        }

        // 创建关联
        RolePermission rolePermission = new RolePermission(roleId, permissionId);
        rolePermissionRepository.save(rolePermission);
    }

    @Override
    @Transactional
    public void removePermissionFromRole(Long roleId, Long permissionId) {
        // 检查关联是否存在
        if (!rolePermissionRepository.existsByRoleIdAndPermissionId(roleId, permissionId)) {
            throw new BusinessException("角色权限关联不存在");
        }

        // 删除关联
        rolePermissionRepository.deleteByRoleIdAndPermissionId(roleId, permissionId);
    }

    @Override
    @Transactional
    public void removeAllPermissionsFromRole(Long roleId) {
        // 检查角色是否存在
        roleRepository.findById(roleId)
            .orElseThrow(() -> new BusinessException("角色不存在"));

        // 删除角色的所有权限关联
        rolePermissionRepository.deleteByRoleId(roleId);
    }

    @Override
    @Transactional
    public void assignRolesToPermission(Long permissionId, List<Long> roleIds) {
        // 检查权限是否存在
        permissionRepository.findById(permissionId)
            .orElseThrow(() -> new BusinessException("权限不存在"));

        // 检查角色是否存在
        for (Long roleId : roleIds) {
            roleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException("角色不存在：" + roleId));
        }

        // 先删除权限现有的角色关联
        rolePermissionRepository.deleteByPermissionId(permissionId);

        // 创建新的角色关联
        List<RolePermission> rolePermissions = roleIds.stream()
            .map(roleId -> new RolePermission(roleId, permissionId))
            .collect(Collectors.toList());

        // 批量保存
        rolePermissionRepository.saveBatch(rolePermissions);
    }

    @Override
    @Transactional
    public void removeRoleFromPermission(Long permissionId, Long roleId) {
        // 检查关联是否存在
        if (!rolePermissionRepository.existsByRoleIdAndPermissionId(roleId, permissionId)) {
            throw new BusinessException("角色权限关联不存在");
        }

        // 删除关联
        rolePermissionRepository.deleteByRoleIdAndPermissionId(roleId, permissionId);
    }

    @Override
    @Transactional
    public void removeAllRolesFromPermission(Long permissionId) {
        // 检查权限是否存在
        permissionRepository.findById(permissionId)
            .orElseThrow(() -> new BusinessException("权限不存在"));

        // 删除权限的所有角色关联
        rolePermissionRepository.deleteByPermissionId(permissionId);
    }

    @Override
    public List<Long> getRolePermissionIds(Long roleId) {
        return rolePermissionRepository.findPermissionIdsByRoleId(roleId);
    }

    @Override
    public List<Long> getPermissionRoleIds(Long permissionId) {
        return rolePermissionRepository.findRoleIdsByPermissionId(permissionId);
    }

    @Override
    public boolean hasPermission(Long roleId, Long permissionId) {
        return rolePermissionRepository.existsByRoleIdAndPermissionId(roleId, permissionId);
    }

    @Override
    public boolean hasAnyPermission(Long roleId, List<Long> permissionIds) {
        List<Long> rolePermissionIds = rolePermissionRepository.findPermissionIdsByRoleId(roleId);
        return rolePermissionIds.stream().anyMatch(permissionIds::contains);
    }

    @Override
    public boolean hasAllPermissions(Long roleId, List<Long> permissionIds) {
        List<Long> rolePermissionIds = rolePermissionRepository.findPermissionIdsByRoleId(roleId);
        return rolePermissionIds.containsAll(permissionIds);
    }

    @Override
    public long countRolesByPermission(Long permissionId) {
        return rolePermissionRepository.countByPermissionId(permissionId);
    }

    @Override
    public long countPermissionsByRole(Long roleId) {
        return rolePermissionRepository.countByRoleId(roleId);
    }

    @Override
    @Transactional
    public void removeRolePermissionsByRoleIds(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return;
        }
        rolePermissionRepository.deleteByRoleIds(roleIds);
    }

    @Override
    @Transactional
    public void removeRolePermissionsByPermissionIds(List<Long> permissionIds) {
        if (permissionIds == null || permissionIds.isEmpty()) {
            return;
        }
        rolePermissionRepository.deleteByPermissionIds(permissionIds);
    }

    @Override
    public List<Long> getPermissionIdsByRoleIds(List<Long> roleIds) {
        return rolePermissionRepository.findPermissionIdsByRoleIds(roleIds);
    }

    @Override
    public List<Long> getRoleIdsByPermissionIds(List<Long> permissionIds) {
        return rolePermissionRepository.findRoleIdsByPermissionIds(permissionIds);
    }
}
