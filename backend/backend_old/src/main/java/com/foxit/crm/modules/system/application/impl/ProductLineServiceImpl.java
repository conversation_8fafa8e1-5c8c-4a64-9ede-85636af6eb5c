package com.foxit.crm.modules.system.application.impl;

import com.foxit.crm.common.exception.BusinessException;
import com.foxit.crm.modules.system.api.dto.request.ProductLineCreateRequest;
import com.foxit.crm.modules.system.api.dto.request.ProductLineUpdateRequest;
import com.foxit.crm.modules.system.api.dto.response.ProductLineDetailResponse;
import com.foxit.crm.modules.system.api.dto.response.ProductLineListResponse;
import com.foxit.crm.modules.system.api.dto.response.ProductLineSimpleResponse;
import com.foxit.crm.modules.system.application.service.ProductLineService;
import com.foxit.crm.modules.system.domain.model.aggregate.ProductLine;
import com.foxit.crm.modules.system.domain.repository.ProductLineRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品线应用服务实现
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
@RequiredArgsConstructor
public class ProductLineServiceImpl implements ProductLineService {

    private final ProductLineRepository productLineRepository;

    @Override
    @Transactional
    @CacheEvict(value = { "productLine", "productLineList" }, allEntries = true)
    public Long createProductLine(ProductLineCreateRequest request) {
        // 检查产品线编码是否已存在
        if (productLineRepository.existsByCode(request.getCode())) {
            throw new BusinessException("产品线编码已存在");
        }

        // 检查产品线名称是否已存在
        if (productLineRepository.existsByName(request.getName())) {
            throw new BusinessException("产品线名称已存在");
        }

        // 创建产品线领域对象
        ProductLine productLine = new ProductLine(
                request.getCode(),
                request.getName(),
                request.getDescription(),
                request.getType(),
                request.getStatus(),
                request.getSortOrder(),
                request.getIcon(),
                request.getColor(),
                request.getOwnerId(),
                request.getOwnerName(),
                request.getDataSourceConfig(),
                request.getRemark());

        // 保存产品线
        ProductLine savedProductLine = productLineRepository.save(productLine);

        return savedProductLine.getId();
    }

    @Override
    @Transactional
    @CacheEvict(value = { "productLine", "productLineList" }, key = "#id")
    public void updateProductLine(Long id, ProductLineUpdateRequest request) {
        // 查找产品线
        ProductLine productLine = productLineRepository.findById(id)
                .orElseThrow(() -> new BusinessException("产品线不存在"));

        // 检查产品线编码是否已被其他产品线使用
        if (productLineRepository.existsByCodeAndIdNot(request.getCode(), id)) {
            throw new BusinessException("产品线编码已存在");
        }

        // 检查产品线名称是否已被其他产品线使用
        if (productLineRepository.existsByNameAndIdNot(request.getName(), id)) {
            throw new BusinessException("产品线名称已存在");
        }

        // 更新产品线信息
        productLine.setCode(request.getCode());
        productLine.setName(request.getName());
        productLine.setDescription(request.getDescription());
        productLine.setType(request.getType());
        productLine.setStatus(request.getStatus());
        productLine.setSortOrder(request.getSortOrder());
        productLine.setIcon(request.getIcon());
        productLine.setColor(request.getColor());
        productLine.setOwnerId(request.getOwnerId());
        productLine.setOwnerName(request.getOwnerName());
        productLine.setDataSourceConfig(request.getDataSourceConfig());
        productLine.setRemark(request.getRemark());
        productLine.setVersion(request.getVersion());

        // 保存更新
        productLineRepository.save(productLine);
    }

    @Override
    @Transactional
    @CacheEvict(value = { "productLine", "productLineList" }, key = "#id")
    public void deleteProductLine(Long id) {
        // 检查产品线是否存在
        ProductLine productLine = productLineRepository.findById(id)
                .orElseThrow(() -> new BusinessException("产品线不存在"));

        // TODO: 检查是否有相关数据关联此产品线，如果有则不允许删除

        // 删除产品线
        productLineRepository.deleteById(id);
    }

    @Override
    @Cacheable(value = "productLine", key = "#id")
    public ProductLineDetailResponse getProductLineById(Long id) {
        ProductLine productLine = productLineRepository.findById(id)
                .orElseThrow(() -> new BusinessException("产品线不存在"));

        return convertToDetailResponse(productLine);
    }

    @Override
    public ProductLineListResponse getProductLineList(int page, int size, String keyword, Integer type,
            Integer status) {
        List<ProductLine> productLines = productLineRepository.findByPage(page, size, keyword, type, status);
        long total = productLineRepository.count(keyword, type, status);

        List<ProductLineDetailResponse> productLineResponses = productLines.stream()
                .map(this::convertToDetailResponse)
                .collect(Collectors.toList());

        return new ProductLineListResponse(productLineResponses, total, page, size);
    }

    @Override
    @Cacheable(value = "productLineList", key = "'enabled'")
    public List<ProductLineSimpleResponse> getAllEnabledProductLines() {
        List<ProductLine> productLines = productLineRepository.findAllEnabled();
        return productLines.stream()
                .map(this::convertToSimpleResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "productLineList", key = "'type:' + #type")
    public List<ProductLineSimpleResponse> getProductLinesByType(Integer type) {
        List<ProductLine> productLines = productLineRepository.findByType(type);
        return productLines.stream()
                .map(this::convertToSimpleResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductLineSimpleResponse> getProductLinesByOwnerId(Long ownerId) {
        List<ProductLine> productLines = productLineRepository.findByOwnerId(ownerId);
        return productLines.stream()
                .map(this::convertToSimpleResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductLineSimpleResponse> getProductLinesByUserId(Long userId) {
        List<ProductLine> productLines = productLineRepository.findByUserId(userId);
        return productLines.stream()
                .map(this::convertToSimpleResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    @CacheEvict(value = { "productLine", "productLineList" }, key = "#id")
    public void enableProductLine(Long id) {
        ProductLine productLine = productLineRepository.findById(id)
                .orElseThrow(() -> new BusinessException("产品线不存在"));

        productLine.enable();
        productLineRepository.save(productLine);
    }

    @Override
    @Transactional
    @CacheEvict(value = { "productLine", "productLineList" }, key = "#id")
    public void disableProductLine(Long id) {
        ProductLine productLine = productLineRepository.findById(id)
                .orElseThrow(() -> new BusinessException("产品线不存在"));

        productLine.disable();
        productLineRepository.save(productLine);
    }

    @Override
    @Transactional
    @CacheEvict(value = { "productLine", "productLineList" }, allEntries = true)
    public void enableProductLinesBatch(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        productLineRepository.updateStatusBatch(ids, 1);
    }

    @Override
    @Transactional
    @CacheEvict(value = { "productLine", "productLineList" }, allEntries = true)
    public void disableProductLinesBatch(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        productLineRepository.updateStatusBatch(ids, 0);
    }

    @Override
    @Transactional
    public void updateDataSourceConfig(Long id, String dataSourceConfig) {
        ProductLine productLine = productLineRepository.findById(id)
                .orElseThrow(() -> new BusinessException("产品线不存在"));

        productLine.setDataSourceConfig(dataSourceConfig);
        productLineRepository.save(productLine);
    }

    @Override
    public String getDataSourceConfig(Long id) {
        ProductLine productLine = productLineRepository.findById(id)
                .orElseThrow(() -> new BusinessException("产品线不存在"));

        return productLine.getDataSourceConfig();
    }

    @Override
    public boolean isCodeAvailable(String code) {
        return !productLineRepository.existsByCode(code);
    }

    @Override
    public boolean isCodeAvailable(String code, Long excludeId) {
        return !productLineRepository.existsByCodeAndIdNot(code, excludeId);
    }

    @Override
    public boolean isNameAvailable(String name) {
        return !productLineRepository.existsByName(name);
    }

    @Override
    public boolean isNameAvailable(String name, Long excludeId) {
        return !productLineRepository.existsByNameAndIdNot(name, excludeId);
    }

    @Override
    public List<ProductLineDetailResponse> getProductLineTypeStatistics() {
        List<ProductLine> productLines = productLineRepository.findAllWithTypeCount();
        return productLines.stream()
                .map(this::convertToDetailResponse)
                .collect(Collectors.toList());
    }

    /**
     * 转换为详情响应对象
     */
    private ProductLineDetailResponse convertToDetailResponse(ProductLine productLine) {
        ProductLineDetailResponse response = new ProductLineDetailResponse();
        BeanUtils.copyProperties(productLine, response);
        response.setType(productLine.getType()); // 触发类型文本设置
        response.setStatus(productLine.getStatus()); // 触发状态文本设置
        return response;
    }

    /**
     * 转换为简单响应对象
     */
    private ProductLineSimpleResponse convertToSimpleResponse(ProductLine productLine) {
        ProductLineSimpleResponse response = new ProductLineSimpleResponse();
        BeanUtils.copyProperties(productLine, response);
        response.setType(productLine.getType()); // 触发类型文本设置
        response.setStatus(productLine.getStatus()); // 触发状态文本设置
        return response;
    }

}
