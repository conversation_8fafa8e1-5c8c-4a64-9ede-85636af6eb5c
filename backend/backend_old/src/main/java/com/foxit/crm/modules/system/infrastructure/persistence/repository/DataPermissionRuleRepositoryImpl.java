package com.foxit.crm.modules.system.infrastructure.persistence.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.foxit.crm.modules.system.domain.model.aggregate.DataPermissionRule;
import com.foxit.crm.modules.system.domain.repository.DataPermissionRuleRepository;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.DataPermissionRulePO;
import com.foxit.crm.modules.system.infrastructure.persistence.mapper.DataPermissionRuleMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 数据权限规则仓储实现
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Repository
@RequiredArgsConstructor
public class DataPermissionRuleRepositoryImpl implements DataPermissionRuleRepository {

    private final DataPermissionRuleMapper dataPermissionRuleMapper;

    @Override
    public DataPermissionRule save(DataPermissionRule dataPermissionRule) {
        DataPermissionRulePO dataPermissionRulePO = new DataPermissionRulePO();
        BeanUtils.copyProperties(dataPermissionRule, dataPermissionRulePO);
        
        if (dataPermissionRule.getId() == null) {
            dataPermissionRuleMapper.insert(dataPermissionRulePO);
            dataPermissionRule.setId(dataPermissionRulePO.getId());
        } else {
            dataPermissionRuleMapper.updateById(dataPermissionRulePO);
        }
        
        return dataPermissionRule;
    }

    @Override
    public Optional<DataPermissionRule> findById(Long id) {
        DataPermissionRulePO dataPermissionRulePO = dataPermissionRuleMapper.selectById(id);
        if (dataPermissionRulePO == null) {
            return Optional.empty();
        }
        
        DataPermissionRule dataPermissionRule = new DataPermissionRule();
        BeanUtils.copyProperties(dataPermissionRulePO, dataPermissionRule);
        return Optional.of(dataPermissionRule);
    }

    @Override
    public Optional<DataPermissionRule> findByRuleCode(String ruleCode) {
        QueryWrapper<DataPermissionRulePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rule_code", ruleCode);
        
        DataPermissionRulePO dataPermissionRulePO = dataPermissionRuleMapper.selectOne(queryWrapper);
        if (dataPermissionRulePO == null) {
            return Optional.empty();
        }
        
        DataPermissionRule dataPermissionRule = new DataPermissionRule();
        BeanUtils.copyProperties(dataPermissionRulePO, dataPermissionRule);
        return Optional.of(dataPermissionRule);
    }

    @Override
    public List<DataPermissionRule> findAllEnabled() {
        QueryWrapper<DataPermissionRulePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1)
                   .orderByAsc("priority", "sort_order");
        
        List<DataPermissionRulePO> dataPermissionRulePOs = dataPermissionRuleMapper.selectList(queryWrapper);
        return dataPermissionRulePOs.stream()
                                   .map(this::convertToDataPermissionRule)
                                   .collect(Collectors.toList());
    }

    @Override
    public List<DataPermissionRule> findByPermissionType(Integer permissionType) {
        QueryWrapper<DataPermissionRulePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("permission_type", permissionType)
                   .eq("status", 1)
                   .orderByAsc("priority", "sort_order");
        
        List<DataPermissionRulePO> dataPermissionRulePOs = dataPermissionRuleMapper.selectList(queryWrapper);
        return dataPermissionRulePOs.stream()
                                   .map(this::convertToDataPermissionRule)
                                   .collect(Collectors.toList());
    }

    @Override
    public List<DataPermissionRule> findByTableName(String tableName) {
        QueryWrapper<DataPermissionRulePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("table_name", tableName)
                   .eq("status", 1)
                   .orderByAsc("priority", "sort_order");
        
        List<DataPermissionRulePO> dataPermissionRulePOs = dataPermissionRuleMapper.selectList(queryWrapper);
        return dataPermissionRulePOs.stream()
                                   .map(this::convertToDataPermissionRule)
                                   .collect(Collectors.toList());
    }

    @Override
    public List<DataPermissionRule> findByUserId(Long userId) {
        QueryWrapper<DataPermissionRulePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("status", 1)
                   .orderByAsc("priority", "sort_order");
        
        List<DataPermissionRulePO> dataPermissionRulePOs = dataPermissionRuleMapper.selectList(queryWrapper);
        return dataPermissionRulePOs.stream()
                                   .map(this::convertToDataPermissionRule)
                                   .collect(Collectors.toList());
    }

    @Override
    public List<DataPermissionRule> findByRoleId(Long roleId) {
        QueryWrapper<DataPermissionRulePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId)
                   .eq("status", 1)
                   .orderByAsc("priority", "sort_order");
        
        List<DataPermissionRulePO> dataPermissionRulePOs = dataPermissionRuleMapper.selectList(queryWrapper);
        return dataPermissionRulePOs.stream()
                                   .map(this::convertToDataPermissionRule)
                                   .collect(Collectors.toList());
    }

    @Override
    public List<DataPermissionRule> findByProductLineId(Long productLineId) {
        QueryWrapper<DataPermissionRulePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("product_line_id", productLineId)
                   .eq("status", 1)
                   .orderByAsc("priority", "sort_order");
        
        List<DataPermissionRulePO> dataPermissionRulePOs = dataPermissionRuleMapper.selectList(queryWrapper);
        return dataPermissionRulePOs.stream()
                                   .map(this::convertToDataPermissionRule)
                                   .collect(Collectors.toList());
    }

    @Override
    public List<DataPermissionRule> findApplicableRules(Long userId, String tableName) {
        QueryWrapper<DataPermissionRulePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("table_name", tableName)
                   .eq("status", 1)
                   .orderByAsc("priority", "sort_order");
        
        List<DataPermissionRulePO> dataPermissionRulePOs = dataPermissionRuleMapper.selectList(queryWrapper);
        return dataPermissionRulePOs.stream()
                                   .map(this::convertToDataPermissionRule)
                                   .collect(Collectors.toList());
    }

    @Override
    public List<DataPermissionRule> findApplicableRulesWithRoles(Long userId, List<Long> roleIds, String tableName) {
        QueryWrapper<DataPermissionRulePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("table_name", tableName)
                   .eq("status", 1)
                   .and(wrapper -> wrapper
                       .eq("user_id", userId)
                       .or()
                       .in(!roleIds.isEmpty(), "role_id", roleIds)
                   )
                   .orderByAsc("priority", "sort_order");
        
        List<DataPermissionRulePO> dataPermissionRulePOs = dataPermissionRuleMapper.selectList(queryWrapper);
        return dataPermissionRulePOs.stream()
                                   .map(this::convertToDataPermissionRule)
                                   .collect(Collectors.toList());
    }

    @Override
    public List<DataPermissionRule> findByPage(int page, int size, String keyword, Integer permissionType,
                                              Integer permissionScope, Long roleId, Long userId, Integer status) {
        Page<DataPermissionRulePO> pageParam = new Page<>(page, size);
        QueryWrapper<DataPermissionRulePO> queryWrapper = buildQueryWrapper(keyword, permissionType, permissionScope, roleId, userId, status);
        queryWrapper.orderByAsc("priority", "sort_order");
        
        Page<DataPermissionRulePO> result = dataPermissionRuleMapper.selectPage(pageParam, queryWrapper);
        return result.getRecords().stream()
                    .map(this::convertToDataPermissionRule)
                    .collect(Collectors.toList());
    }

    @Override
    public long count(String keyword, Integer permissionType, Integer permissionScope, 
                     Long roleId, Long userId, Integer status) {
        QueryWrapper<DataPermissionRulePO> queryWrapper = buildQueryWrapper(keyword, permissionType, permissionScope, roleId, userId, status);
        return dataPermissionRuleMapper.selectCount(queryWrapper);
    }

    @Override
    public void deleteById(Long id) {
        dataPermissionRuleMapper.deleteById(id);
    }

    @Override
    public boolean existsByRuleCode(String ruleCode) {
        QueryWrapper<DataPermissionRulePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rule_code", ruleCode);
        return dataPermissionRuleMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean existsByRuleCodeAndIdNot(String ruleCode, Long id) {
        QueryWrapper<DataPermissionRulePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rule_code", ruleCode)
                   .ne("id", id);
        return dataPermissionRuleMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public List<DataPermissionRule> findByStatus(Integer status) {
        QueryWrapper<DataPermissionRulePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status)
                   .orderByAsc("priority", "sort_order");
        
        List<DataPermissionRulePO> dataPermissionRulePOs = dataPermissionRuleMapper.selectList(queryWrapper);
        return dataPermissionRulePOs.stream()
                                   .map(this::convertToDataPermissionRule)
                                   .collect(Collectors.toList());
    }

    @Override
    public void updateStatusBatch(List<Long> ids, Integer status) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        
        UpdateWrapper<DataPermissionRulePO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", ids)
                    .set("status", status);
        
        dataPermissionRuleMapper.update(null, updateWrapper);
    }

    @Override
    public List<DataPermissionRule> findByTableNameOrderByPriority(String tableName) {
        QueryWrapper<DataPermissionRulePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("table_name", tableName)
                   .eq("status", 1)
                   .orderByAsc("priority", "sort_order");
        
        List<DataPermissionRulePO> dataPermissionRulePOs = dataPermissionRuleMapper.selectList(queryWrapper);
        return dataPermissionRulePOs.stream()
                                   .map(this::convertToDataPermissionRule)
                                   .collect(Collectors.toList());
    }

    @Override
    public List<DataPermissionRule> findByUserIdAndPermissionType(Long userId, Integer permissionType) {
        QueryWrapper<DataPermissionRulePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("permission_type", permissionType)
                   .eq("status", 1)
                   .orderByAsc("priority", "sort_order");
        
        List<DataPermissionRulePO> dataPermissionRulePOs = dataPermissionRuleMapper.selectList(queryWrapper);
        return dataPermissionRulePOs.stream()
                                   .map(this::convertToDataPermissionRule)
                                   .collect(Collectors.toList());
    }

    @Override
    public List<DataPermissionRule> findByRoleIdAndPermissionType(Long roleId, Integer permissionType) {
        QueryWrapper<DataPermissionRulePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId)
                   .eq("permission_type", permissionType)
                   .eq("status", 1)
                   .orderByAsc("priority", "sort_order");
        
        List<DataPermissionRulePO> dataPermissionRulePOs = dataPermissionRuleMapper.selectList(queryWrapper);
        return dataPermissionRulePOs.stream()
                                   .map(this::convertToDataPermissionRule)
                                   .collect(Collectors.toList());
    }

    @Override
    public List<DataPermissionRule> findRowLevelRules(Long userId, List<Long> roleIds, String tableName) {
        return findRulesByTypeAndContext(userId, roleIds, tableName, 1);
    }

    @Override
    public List<DataPermissionRule> findColumnLevelRules(Long userId, List<Long> roleIds, String tableName) {
        return findRulesByTypeAndContext(userId, roleIds, tableName, 2);
    }

    @Override
    public List<DataPermissionRule> findQueryLevelRules(Long userId, List<Long> roleIds, String tableName) {
        return findRulesByTypeAndContext(userId, roleIds, tableName, 3);
    }

    /**
     * 根据类型和上下文查找规则
     */
    private List<DataPermissionRule> findRulesByTypeAndContext(Long userId, List<Long> roleIds, String tableName, Integer permissionType) {
        QueryWrapper<DataPermissionRulePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("table_name", tableName)
                   .eq("permission_type", permissionType)
                   .eq("status", 1)
                   .and(wrapper -> wrapper
                       .eq("user_id", userId)
                       .or()
                       .in(!roleIds.isEmpty(), "role_id", roleIds)
                   )
                   .orderByAsc("priority", "sort_order");
        
        List<DataPermissionRulePO> dataPermissionRulePOs = dataPermissionRuleMapper.selectList(queryWrapper);
        return dataPermissionRulePOs.stream()
                                   .map(this::convertToDataPermissionRule)
                                   .collect(Collectors.toList());
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper<DataPermissionRulePO> buildQueryWrapper(String keyword, Integer permissionType,
                                                               Integer permissionScope, Long roleId, Long userId, Integer status) {
        QueryWrapper<DataPermissionRulePO> queryWrapper = new QueryWrapper<>();
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                .like("rule_name", keyword)
                .or().like("rule_code", keyword)
                .or().like("description", keyword)
                .or().like("table_name", keyword)
            );
        }
        
        if (permissionType != null) {
            queryWrapper.eq("permission_type", permissionType);
        }
        
        if (permissionScope != null) {
            queryWrapper.eq("permission_scope", permissionScope);
        }
        
        if (roleId != null) {
            queryWrapper.eq("role_id", roleId);
        }
        
        if (userId != null) {
            queryWrapper.eq("user_id", userId);
        }
        
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        
        return queryWrapper;
    }

    /**
     * 转换PO为领域对象
     */
    private DataPermissionRule convertToDataPermissionRule(DataPermissionRulePO dataPermissionRulePO) {
        DataPermissionRule dataPermissionRule = new DataPermissionRule();
        BeanUtils.copyProperties(dataPermissionRulePO, dataPermissionRule);
        return dataPermissionRule;
    }
}
