package com.foxit.crm.modules.system.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.modules.system.api.dto.request.ProductLineCreateRequest;
import com.foxit.crm.modules.system.api.dto.request.ProductLineUpdateRequest;
import com.foxit.crm.modules.system.api.dto.response.ProductLineDetailResponse;
import com.foxit.crm.modules.system.api.dto.response.ProductLineListResponse;
import com.foxit.crm.modules.system.api.dto.response.ProductLineSimpleResponse;
import com.foxit.crm.modules.system.application.service.ProductLineService;
import com.foxit.crm.shared.domain.event.OperationLog;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品线管理控制器
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@RestController
@RequestMapping("/admin/product-lines")
@Validated
@RequiredArgsConstructor
@PreAuthorize("hasAuthority('ADMIN')") // 整个控制器只允许管理员访问
public class ProductLineController {

    private final ProductLineService productLineService;

    /**
     * 创建产品线
     */
    @PostMapping
    @OperationLog(value = "创建产品线", operation = "CREATE_PRODUCT_LINE", saveParams = true)
    public Result<Long> createProductLine(@Valid @RequestBody ProductLineCreateRequest request) {
        Long productLineId = productLineService.createProductLine(request);
        return Result.success(productLineId, "产品线创建成功");
    }

    /**
     * 更新产品线
     */
    @PutMapping("/{id}")
    @OperationLog(value = "更新产品线", operation = "UPDATE_PRODUCT_LINE", saveParams = true)
    public Result<String> updateProductLine(@PathVariable Long id, @Valid @RequestBody ProductLineUpdateRequest request) {
        productLineService.updateProductLine(id, request);
        return Result.success("产品线更新成功");
    }

    /**
     * 删除产品线
     */
    @DeleteMapping("/{id}")
    @OperationLog(value = "删除产品线", operation = "DELETE_PRODUCT_LINE")
    public Result<String> deleteProductLine(@PathVariable Long id) {
        productLineService.deleteProductLine(id);
        return Result.success("产品线删除成功");
    }

    /**
     * 获取产品线详情
     */
    @GetMapping("/{id}")
    @OperationLog(value = "查看产品线详情", operation = "VIEW_PRODUCT_LINE")
    public Result<ProductLineDetailResponse> getProductLineById(@PathVariable Long id) {
        ProductLineDetailResponse response = productLineService.getProductLineById(id);
        return Result.success(response);
    }

    /**
     * 分页查询产品线列表
     */
    @GetMapping
    @OperationLog(value = "查看产品线列表", operation = "LIST_PRODUCT_LINES")
    public Result<ProductLineListResponse> getProductLineList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer type,
            @RequestParam(required = false) Integer status) {
        ProductLineListResponse response = productLineService.getProductLineList(page, size, keyword, type, status);
        return Result.success(response);
    }

    /**
     * 获取所有启用的产品线（简单信息）
     */
    @GetMapping("/enabled")
    @OperationLog(value = "获取启用产品线列表", operation = "LIST_ENABLED_PRODUCT_LINES")
    public Result<List<ProductLineSimpleResponse>> getAllEnabledProductLines() {
        List<ProductLineSimpleResponse> response = productLineService.getAllEnabledProductLines();
        return Result.success(response);
    }

    /**
     * 根据产品线类型获取产品线列表
     */
    @GetMapping("/type/{type}")
    @OperationLog(value = "按类型获取产品线列表", operation = "GET_PRODUCT_LINES_BY_TYPE")
    public Result<List<ProductLineSimpleResponse>> getProductLinesByType(@PathVariable Integer type) {
        List<ProductLineSimpleResponse> response = productLineService.getProductLinesByType(type);
        return Result.success(response);
    }

    /**
     * 根据负责人ID获取产品线列表
     */
    @GetMapping("/owner/{ownerId}")
    @OperationLog(value = "获取负责人产品线列表", operation = "GET_PRODUCT_LINES_BY_OWNER")
    public Result<List<ProductLineSimpleResponse>> getProductLinesByOwnerId(@PathVariable Long ownerId) {
        List<ProductLineSimpleResponse> response = productLineService.getProductLinesByOwnerId(ownerId);
        return Result.success(response);
    }

    /**
     * 根据用户ID获取有权限的产品线列表
     */
    @GetMapping("/user/{userId}")
    @OperationLog(value = "获取用户产品线列表", operation = "GET_USER_PRODUCT_LINES")
    public Result<List<ProductLineSimpleResponse>> getProductLinesByUserId(@PathVariable Long userId) {
        List<ProductLineSimpleResponse> response = productLineService.getProductLinesByUserId(userId);
        return Result.success(response);
    }

    /**
     * 启用产品线
     */
    @PutMapping("/{id}/enable")
    @OperationLog(value = "启用产品线", operation = "ENABLE_PRODUCT_LINE")
    public Result<String> enableProductLine(@PathVariable Long id) {
        productLineService.enableProductLine(id);
        return Result.success("产品线启用成功");
    }

    /**
     * 禁用产品线
     */
    @PutMapping("/{id}/disable")
    @OperationLog(value = "禁用产品线", operation = "DISABLE_PRODUCT_LINE")
    public Result<String> disableProductLine(@PathVariable Long id) {
        productLineService.disableProductLine(id);
        return Result.success("产品线禁用成功");
    }

    /**
     * 批量启用产品线
     */
    @PutMapping("/batch/enable")
    @OperationLog(value = "批量启用产品线", operation = "BATCH_ENABLE_PRODUCT_LINES", saveParams = true)
    public Result<String> enableProductLinesBatch(@RequestBody List<Long> ids) {
        productLineService.enableProductLinesBatch(ids);
        return Result.success("产品线批量启用成功");
    }

    /**
     * 批量禁用产品线
     */
    @PutMapping("/batch/disable")
    @OperationLog(value = "批量禁用产品线", operation = "BATCH_DISABLE_PRODUCT_LINES", saveParams = true)
    public Result<String> disableProductLinesBatch(@RequestBody List<Long> ids) {
        productLineService.disableProductLinesBatch(ids);
        return Result.success("产品线批量禁用成功");
    }

    /**
     * 更新产品线数据源配置
     */
    @PutMapping("/{id}/data-source")
    @OperationLog(value = "更新产品线数据源配置", operation = "UPDATE_PRODUCT_LINE_DATA_SOURCE", saveParams = true)
    public Result<String> updateDataSourceConfig(@PathVariable Long id, @RequestBody String dataSourceConfig) {
        productLineService.updateDataSourceConfig(id, dataSourceConfig);
        return Result.success("数据源配置更新成功");
    }

    /**
     * 获取产品线数据源配置
     */
    @GetMapping("/{id}/data-source")
    @OperationLog(value = "获取产品线数据源配置", operation = "GET_PRODUCT_LINE_DATA_SOURCE")
    public Result<String> getDataSourceConfig(@PathVariable Long id) {
        String dataSourceConfig = productLineService.getDataSourceConfig(id);
        return Result.success(dataSourceConfig);
    }

    /**
     * 检查产品线编码是否可用
     */
    @GetMapping("/check-code")
    @OperationLog(value = "检查产品线编码可用性", operation = "CHECK_PRODUCT_LINE_CODE")
    public Result<Boolean> checkCodeAvailability(@RequestParam String code, @RequestParam(required = false) Long excludeId) {
        boolean available = excludeId != null ? 
            productLineService.isCodeAvailable(code, excludeId) : 
            productLineService.isCodeAvailable(code);
        return Result.success(available);
    }

    /**
     * 检查产品线名称是否可用
     */
    @GetMapping("/check-name")
    @OperationLog(value = "检查产品线名称可用性", operation = "CHECK_PRODUCT_LINE_NAME")
    public Result<Boolean> checkNameAvailability(@RequestParam String name, @RequestParam(required = false) Long excludeId) {
        boolean available = excludeId != null ? 
            productLineService.isNameAvailable(name, excludeId) : 
            productLineService.isNameAvailable(name);
        return Result.success(available);
    }

    /**
     * 获取产品线类型统计信息
     */
    @GetMapping("/statistics")
    @OperationLog(value = "获取产品线统计信息", operation = "GET_PRODUCT_LINE_STATISTICS")
    public Result<List<ProductLineDetailResponse>> getProductLineTypeStatistics() {
        List<ProductLineDetailResponse> response = productLineService.getProductLineTypeStatistics();
        return Result.success(response);
    }
}
