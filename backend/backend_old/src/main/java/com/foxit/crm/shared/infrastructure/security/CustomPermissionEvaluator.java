package com.foxit.crm.shared.infrastructure.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Collection;

/**
 * 自定义权限评估器
 * 实现Spring Security的PermissionEvaluator接口，用于处理@PreAuthorize中的hasPermission表达式
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Component
public class CustomPermissionEvaluator implements PermissionEvaluator {

    /**
     * 检查用户是否有指定资源的权限
     *
     * @param authentication     认证信息
     * @param targetDomainObject 目标对象
     * @param permission         权限标识
     * @return 是否有权限
     */
    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        if (authentication == null || !authentication.isAuthenticated()) {
            log.debug("用户未认证，拒绝访问");
            return false;
        }

        String permissionStr = permission.toString();
        log.debug("检查权限: user={}, permission={}, target={}",
                authentication.getName(), permissionStr, targetDomainObject);

        // 检查用户权限
        return hasAuthority(authentication, permissionStr);
    }

    /**
     * 检查用户是否有指定资源ID的权限
     *
     * @param authentication 认证信息
     * @param targetId       目标资源ID
     * @param targetType     目标资源类型
     * @param permission     权限标识
     * @return 是否有权限
     */
    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType,
            Object permission) {
        if (authentication == null || !authentication.isAuthenticated()) {
            log.debug("用户未认证，拒绝访问");
            return false;
        }

        String permissionStr = permission.toString();
        log.debug("检查权限: user={}, permission={}, targetType={}, targetId={}",
                authentication.getName(), permissionStr, targetType, targetId);

        // 构建完整的权限标识
        String fullPermission = targetType + ":" + permissionStr;

        // 检查用户权限
        return hasAuthority(authentication, fullPermission) || hasAuthority(authentication, permissionStr);
    }

    /**
     * 检查用户是否有指定权限
     *
     * @param authentication 认证信息
     * @param permission     权限标识
     * @return 是否有权限
     */
    private boolean hasAuthority(Authentication authentication, String permission) {
        Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();

        // 临时允许用户分析模块的访问（开发阶段）
        if (permission.contains("user:active") || permission.equals("READ")) {
            log.debug("临时允许用户分析模块访问: user={}, permission={}", authentication.getName(), permission);
            return true;
        }

        // 管理员拥有所有权限
        if (authorities.stream().anyMatch(auth -> "ADMIN".equals(auth.getAuthority()))) {
            log.debug("管理员用户，允许访问: {}", permission);
            return true;
        }

        // 检查具体权限
        boolean hasPermission = authorities.stream()
                .anyMatch(auth -> {
                    String authority = auth.getAuthority();
                    // 精确匹配
                    if (authority.equals(permission)) {
                        return true;
                    }
                    // 模块权限匹配（如：user:active 匹配 user:active:READ）
                    if (permission.contains(":")
                            && authority.equals(permission.substring(0, permission.lastIndexOf(":")))) {
                        return true;
                    }
                    return false;
                });

        log.debug("权限检查结果: user={}, permission={}, hasPermission={}",
                authentication.getName(), permission, hasPermission);

        return hasPermission;
    }
}
