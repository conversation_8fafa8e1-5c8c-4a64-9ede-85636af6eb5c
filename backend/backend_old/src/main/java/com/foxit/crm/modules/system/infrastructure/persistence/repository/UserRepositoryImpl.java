package com.foxit.crm.modules.system.infrastructure.persistence.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.foxit.crm.common.util.PageResult;
import com.foxit.crm.modules.system.domain.model.aggregate.User;
import com.foxit.crm.modules.system.domain.model.valueobject.UserId;
import com.foxit.crm.modules.system.domain.model.valueobject.Username;
import com.foxit.crm.modules.system.domain.repository.UserRepository;
import com.foxit.crm.modules.system.infrastructure.persistence.converter.UserConverter;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.UserPO;
import com.foxit.crm.modules.system.infrastructure.persistence.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户仓储实现
 * 使用 MyBatis-Plus QueryWrapper 替代 XML 映射
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Repository
@RequiredArgsConstructor
public class UserRepositoryImpl implements UserRepository {

    private final UserMapper userMapper;
    private final UserConverter userConverter;

    @Override
    public Optional<User> findById(UserId userId) {
        if (userId == null) {
            return Optional.empty();
        }

        UserPO userPO = userMapper.selectById(userId.getValue());
        if (userPO == null) {
            return Optional.empty();
        }

        User user = userConverter.toDomain(userPO);
        return Optional.of(user);
    }

    @Override
    public Optional<User> findByUsername(Username username) {
        if (username == null) {
            return Optional.empty();
        }

        // 使用 MyBatis-Plus LambdaQueryWrapper 替代 XML
        // @TableLogic 注解会自动处理逻辑删除条件，无需手动添加 deleted = 0
        LambdaQueryWrapper<UserPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserPO::getUsername, username.getValue());

        UserPO userPO = userMapper.selectOne(queryWrapper);
        if (userPO == null) {
            return Optional.empty();
        }

        User user = userConverter.toDomain(userPO);
        return Optional.of(user);
    }

    @Override
    public User save(User user) {
        if (user == null) {
            throw new IllegalArgumentException("用户不能为空");
        }

        UserPO userPO = userConverter.toPO(user);

        if (userPO.getId() == null) {
            // 新增
            userMapper.insert(userPO);
        } else {
            // 更新时，确保 version 字段正确传递
            if (userPO.getVersion() == null) {
                // 如果版本号为空，先查询当前版本号
                UserPO currentPO = userMapper.selectById(userPO.getId());
                if (currentPO != null) {
                    userPO.setVersion(currentPO.getVersion());
                }
            }
            int updateResult = userMapper.updateById(userPO);
            if (updateResult == 0) {
                throw new RuntimeException("更新失败，可能是数据已被其他用户修改，请刷新后重试");
            }
        }

        // 将持久化后的 PO 转换回领域对象并返回（包含更新后的版本号）
        return userConverter.toDomain(userPO);
    }

    @Override
    public void deleteById(UserId userId) {
        if (userId != null) {
            userMapper.deleteById(userId.getValue());
        }
    }

    @Override
    public boolean existsByUsername(Username username) {
        if (username == null) {
            return false;
        }

        // 使用 MyBatis-Plus LambdaQueryWrapper 进行计数查询
        // @TableLogic 注解会自动处理逻辑删除条件，无需手动添加 deleted = 0
        LambdaQueryWrapper<UserPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserPO::getUsername, username.getValue());

        return userMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean existsByEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }

        // 使用 MyBatis-Plus LambdaQueryWrapper 进行计数查询
        // @TableLogic 注解会自动处理逻辑删除条件，无需手动添加 deleted = 0
        LambdaQueryWrapper<UserPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserPO::getEmail, email);

        return userMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public PageResult<User> findByPage(int page, int size, String keyword) {
        Page<UserPO> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<UserPO> queryWrapper = new LambdaQueryWrapper<>();

        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                    .like(UserPO::getUsername, keyword)
                    .or().like(UserPO::getRealName, keyword)
                    .or().like(UserPO::getEmail, keyword)
                    .or().like(UserPO::getPhone, keyword));
        }

        queryWrapper.orderByDesc(UserPO::getCreateTime);

        Page<UserPO> result = userMapper.selectPage(pageParam, queryWrapper);
        List<User> users = result.getRecords().stream()
                .map(userConverter::toDomain)
                .collect(Collectors.toList());

        return PageResult.of(users, result.getTotal(), (int) result.getCurrent(), (int) result.getSize());
    }
}
