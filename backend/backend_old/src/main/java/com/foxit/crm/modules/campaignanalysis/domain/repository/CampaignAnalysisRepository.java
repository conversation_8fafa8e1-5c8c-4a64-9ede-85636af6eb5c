package com.foxit.crm.modules.campaignanalysis.domain.repository;

import com.foxit.crm.modules.campaignanalysis.domain.entity.CampaignAggregate;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;

import java.util.List;
import java.util.Optional;

/**
 * 活动分析仓储接口
 * 定义活动分析数据的查询和存储操作
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface CampaignAnalysisRepository {

    /**
     * 获取活动总览数据
     *
     * @param timeRange 时间范围
     * @param campaignIds 活动ID列表，为空时查询所有活动
     * @param dataScope 数据权限范围
     * @return 活动总览聚合根
     */
    Optional<CampaignAggregate> getCampaignOverview(TimeRange timeRange, List<Long> campaignIds, String dataScope);

    /**
     * 获取活动效果分析数据
     *
     * @param timeRange 时间范围
     * @param campaignIds 活动ID列表
     * @param dataScope 数据权限范围
     * @return 活动效果聚合根
     */
    Optional<CampaignAggregate> getCampaignPerformance(TimeRange timeRange, List<Long> campaignIds, String dataScope);

    /**
     * 获取渠道分析数据
     *
     * @param timeRange 时间范围
     * @param campaignIds 活动ID列表
     * @param dataScope 数据权限范围
     * @return 渠道分析聚合根
     */
    Optional<CampaignAggregate> getChannelAnalysis(TimeRange timeRange, List<Long> campaignIds, String dataScope);

    /**
     * 获取ROI分析数据
     *
     * @param timeRange 时间范围
     * @param campaignIds 活动ID列表
     * @param dataScope 数据权限范围
     * @return ROI分析聚合根
     */
    Optional<CampaignAggregate> getROIAnalysis(TimeRange timeRange, List<Long> campaignIds, String dataScope);

    /**
     * 获取趋势分析数据
     *
     * @param timeRange 时间范围
     * @param campaignIds 活动ID列表
     * @param dataScope 数据权限范围
     * @return 趋势分析聚合根
     */
    Optional<CampaignAggregate> getTrendAnalysis(TimeRange timeRange, List<Long> campaignIds, String dataScope);

    /**
     * 保存活动分析结果
     *
     * @param aggregate 活动分析聚合根
     */
    void save(CampaignAggregate aggregate);

    /**
     * 根据ID查找活动分析
     *
     * @param id 聚合根ID
     * @return 活动分析聚合根
     */
    Optional<CampaignAggregate> findById(String id);

    /**
     * 活动实时统计数据
     */
    record CampaignRealTimeStats(
            Long totalCampaigns,
            Long activeCampaigns,
            Long totalImpressions,
            Long totalClicks,
            Long totalConversions,
            Double averageClickRate,
            Double averageConversionRate,
            Double averageROI
    ) {}

    /**
     * 获取实时活动统计
     *
     * @param campaignIds 活动ID列表
     * @param dataScope 数据权限范围
     * @return 实时统计数据
     */
    CampaignRealTimeStats getRealTimeStats(List<Long> campaignIds, String dataScope);

    /**
     * 活动分析摘要数据
     */
    record CampaignAnalysisSummary(
            String analysisId,
            String analysisType,
            TimeRange timeRange,
            Long totalCampaigns,
            Long activeCampaigns,
            Double totalROI,
            String bestCampaign,
            String summary
    ) {}

    /**
     * 获取活动分析摘要
     *
     * @param timeRange 时间范围
     * @param dataScope 数据权限范围
     * @return 分析摘要
     */
    CampaignAnalysisSummary getAnalysisSummary(TimeRange timeRange, String dataScope);
}
