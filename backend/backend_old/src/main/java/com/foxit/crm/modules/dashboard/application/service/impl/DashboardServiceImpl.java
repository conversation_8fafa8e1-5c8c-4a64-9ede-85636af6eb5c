package com.foxit.crm.modules.dashboard.application.service.impl;

import com.foxit.crm.modules.dashboard.application.dto.request.DashboardConfigRequest;
import com.foxit.crm.modules.dashboard.application.dto.request.DashboardQueryRequest;
import com.foxit.crm.modules.dashboard.application.dto.response.*;
import com.foxit.crm.modules.dashboard.application.service.DashboardService;
import com.foxit.crm.modules.dashboard.domain.entity.DashboardAggregate;
import com.foxit.crm.modules.dashboard.domain.repository.DashboardRepository;
import com.foxit.crm.modules.dashboard.domain.valueobject.MetricValue;
import com.foxit.crm.modules.dashboard.domain.valueobject.TimeRange;
import com.foxit.crm.shared.infrastructure.cache.StatisticsCacheManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Dashboard应用服务实现
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DashboardServiceImpl implements DashboardService {

    private final DashboardRepository dashboardRepository;
    private final StatisticsCacheManager cacheManager;

    @Override
    @Cacheable(value = "dashboardOverview", key = "#request.dataScope + ':' + #request.startDate + ':' + #request.endDate")
    public OverviewDashboardResponse getOverviewDashboard(DashboardQueryRequest request) {
        log.info("获取总览仪表盘数据: {}", request);

        // 构建时间范围
        TimeRange timeRange = buildTimeRange(request);

        // 创建Dashboard聚合根
        DashboardAggregate dashboard = DashboardAggregate.createOverview(timeRange, request.getDataScope());

        if (!dashboard.isValid()) {
            throw new IllegalArgumentException("Dashboard配置无效");
        }

        // 获取核心指标
        Map<String, MetricValue> coreMetrics = dashboardRepository.getCoreMetrics(
                timeRange, request.getProductLineIds(), request.getDataScope());

        // 获取图表数据
        DashboardAggregate.ChartData userGrowthChart = dashboardRepository.getUserGrowthData(
                timeRange, request.getProductLineIds(), request.getDataScope());

        DashboardAggregate.ChartData revenueChart = dashboardRepository.getRevenueTrendData(
                timeRange, request.getProductLineIds(), request.getDataScope());

        DashboardAggregate.ChartData productLineChart = dashboardRepository.getProductLineDistributionData(
                timeRange, request.getDataScope());

        DashboardAggregate.ChartData userTypeChart = dashboardRepository.getUserTypeDistributionData(
                timeRange, request.getProductLineIds(), request.getDataScope());

        DashboardAggregate.ChartData realTimeChart = dashboardRepository.getRealTimeOnlineData(
                request.getDataScope());

        // 获取实时统计
        DashboardAggregate.RealTimeStats realTimeStats = dashboardRepository.getRealTimeStats(
                request.getDataScope());

        // 获取可访问的产品线
        List<DashboardRepository.ProductLineSummary> accessibleProductLines = dashboardRepository
                .getAccessibleProductLines(request.getDataScope(), getCurrentUserId());

        return OverviewDashboardResponse.builder()
                .coreMetrics(convertToCoreMetricsResponse(coreMetrics))
                .userGrowthChart(convertToChartDataResponse(userGrowthChart))
                .revenueChart(convertToChartDataResponse(revenueChart))
                .productLineChart(convertToChartDataResponse(productLineChart))
                .userTypeChart(convertToChartDataResponse(userTypeChart))
                .realTimeChart(convertToChartDataResponse(realTimeChart))
                .realTimeStats(convertToRealTimeStatsResponse(realTimeStats))
                .updateTime(LocalDateTime.now())
                .dataScope(request.getDataScope())
                .accessibleProductLines(convertToProductLineSummaryList(accessibleProductLines))
                .build();
    }

    @Override
    @Cacheable(value = "dashboardProductLine", key = "#request.productLineIds + ':' + #request.dataScope + ':' + #request.startDate")
    public ProductLineDashboardResponse getProductLineDashboard(DashboardQueryRequest request) {
        log.info("获取产品线仪表盘数据: {}", request);

        if (request.getProductLineIds() == null || request.getProductLineIds().isEmpty()) {
            throw new IllegalArgumentException("产品线ID不能为空");
        }

        TimeRange timeRange = buildTimeRange(request);

        // 创建产品线Dashboard聚合根
        DashboardAggregate dashboard = DashboardAggregate.createProductLine(
                request.getProductLineIds(), timeRange, request.getDataScope());

        // 获取产品线基本信息（这里简化处理，实际应该从产品线服务获取）
        ProductLineDashboardResponse.ProductLineInfo productLineInfo = ProductLineDashboardResponse.ProductLineInfo
                .builder()
                .id(request.getProductLineIds().get(0))
                .name("产品线名称") // 实际应该查询数据库
                .type("阅读器")
                .description("产品线描述")
                .owner("负责人")
                .status("启用")
                .build();

        // 获取产品线指标
        Map<String, MetricValue> metrics = dashboardRepository.getCoreMetrics(
                timeRange, request.getProductLineIds(), request.getDataScope());

        // 获取图表数据
        DashboardAggregate.ChartData userGrowthChart = dashboardRepository.getUserGrowthData(
                timeRange, request.getProductLineIds(), request.getDataScope());

        DashboardAggregate.ChartData revenueTrendChart = dashboardRepository.getRevenueTrendData(
                timeRange, request.getProductLineIds(), request.getDataScope());

        DashboardAggregate.ChartData featureUsageChart = dashboardRepository.getFeatureUsageData(
                timeRange, request.getProductLineIds(), request.getDataScope());

        DashboardAggregate.ChartData userDistributionChart = dashboardRepository.getUserTypeDistributionData(
                timeRange, request.getProductLineIds(), request.getDataScope());

        return ProductLineDashboardResponse.builder()
                .productLineInfo(productLineInfo)
                .metrics(convertToProductLineMetrics(metrics))
                .userGrowthChart(convertToChartDataResponse(userGrowthChart))
                .revenueTrendChart(convertToChartDataResponse(revenueTrendChart))
                .featureUsageChart(convertToChartDataResponse(featureUsageChart))
                .userDistributionChart(convertToChartDataResponse(userDistributionChart))
                .updateTime(LocalDateTime.now())
                .build();
    }

    @Override
    @Cacheable(value = "dashboardRealTime", key = "#dataScope", unless = "#result == null")
    public RealTimeStatsResponse getRealTimeStats(String dataScope) {
        log.info("获取实时统计数据: dataScope={}", dataScope);

        DashboardAggregate.RealTimeStats realTimeStats = dashboardRepository.getRealTimeStats(dataScope);
        return convertToRealTimeStatsResponse(realTimeStats);
    }

    @Override
    public CoreMetricsResponse getCoreMetrics(DashboardQueryRequest request) {
        log.info("获取核心指标数据: {}", request);

        TimeRange timeRange = buildTimeRange(request);
        Map<String, MetricValue> coreMetrics = dashboardRepository.getCoreMetrics(
                timeRange, request.getProductLineIds(), request.getDataScope());

        return convertToCoreMetricsResponse(coreMetrics);
    }

    @Override
    public ChartDataResponse getChartData(String chartType, DashboardQueryRequest request) {
        log.info("获取图表数据: chartType={}, request={}", chartType, request);

        TimeRange timeRange = buildTimeRange(request);
        DashboardAggregate.ChartData chartData;

        switch (chartType) {
            case "userGrowth":
                chartData = dashboardRepository.getUserGrowthData(timeRange, request.getProductLineIds(),
                        request.getDataScope());
                break;
            case "revenue":
                chartData = dashboardRepository.getRevenueTrendData(timeRange, request.getProductLineIds(),
                        request.getDataScope());
                break;
            case "productLine":
                chartData = dashboardRepository.getProductLineDistributionData(timeRange, request.getDataScope());
                break;
            case "userType":
                chartData = dashboardRepository.getUserTypeDistributionData(timeRange, request.getProductLineIds(),
                        request.getDataScope());
                break;
            case "realTime":
                chartData = dashboardRepository.getRealTimeOnlineData(request.getDataScope());
                break;
            default:
                throw new IllegalArgumentException("不支持的图表类型: " + chartType);
        }

        return convertToChartDataResponse(chartData);
    }

    @Override
    @CacheEvict(value = { "dashboardOverview", "dashboardProductLine",
            "dashboardRealTime" }, key = "#dataScope", condition = "#dashboardType == 'all' or #dashboardType == null")
    public void refreshDashboardCache(String dashboardType, String dataScope) {
        log.info("刷新Dashboard缓存: dashboardType={}, dataScope={}", dashboardType, dataScope);

        // 根据类型清除特定缓存
        if ("overview".equals(dashboardType)) {
            // 清除总览缓存
        } else if ("product".equals(dashboardType)) {
            // 清除产品线缓存
        } else if ("realtime".equals(dashboardType)) {
            // 清除实时缓存
        }
        // 如果是all或null，则清除所有缓存（通过@CacheEvict注解处理）
    }

    @Override
    public DashboardConfigResponse getDashboardConfig(Long userId) {
        log.info("获取Dashboard配置: userId={}", userId);

        // 这里应该从数据库或配置服务获取用户的Dashboard配置
        // 暂时返回默认配置
        return DashboardConfigResponse.builder()
                .userId(userId)
                .defaultTimeRange("last30days")
                .defaultTimeGranularity("day")
                .refreshInterval(5)
                .enableRealTimeUpdate(true)
                .dataScope("all")
                .build();
    }

    @Override
    public void updateDashboardConfig(Long userId, DashboardConfigRequest request) {
        log.info("更新Dashboard配置: userId={}, request={}", userId, request);

        // 这里应该保存用户的Dashboard配置到数据库
        // 暂时只记录日志
    }

    // 私有辅助方法
    private TimeRange buildTimeRange(DashboardQueryRequest request) {
        if (request.getStartDate() != null && request.getEndDate() != null) {
            return TimeRange.custom(request.getStartDate(), request.getEndDate(),
                    request.getTimeGranularity() != null ? request.getTimeGranularity() : "day");
        } else {
            return TimeRange.lastDays(30); // 默认最近30天
        }
    }

    private Long getCurrentUserId() {
        // 这里应该从安全上下文获取当前用户ID
        // 暂时返回固定值
        return 1L;
    }

    // 转换方法
    private CoreMetricsResponse convertToCoreMetricsResponse(Map<String, MetricValue> coreMetrics) {
        return CoreMetricsResponse.builder()
                .totalUsers(convertToMetricData(coreMetrics.get("totalUsers")))
                .activeUsers(convertToMetricData(coreMetrics.get("activeUsers")))
                .newUsers(convertToMetricData(coreMetrics.get("newUsers")))
                .revenue(convertToMetricData(coreMetrics.get("revenue")))
                .downloads(convertToMetricData(coreMetrics.get("downloads")))
                .retention(convertToMetricData(coreMetrics.get("retention")))
                .updateTime(LocalDateTime.now())
                .build();
    }

    private CoreMetricsResponse.MetricData convertToMetricData(MetricValue metricValue) {
        if (metricValue == null) {
            return null;
        }

        return CoreMetricsResponse.MetricData.builder()
                .value(metricValue.getValue())
                .unit(metricValue.getUnit())
                .changeRate(metricValue.getChangeRate())
                .trend(metricValue.getTrend())
                .comparisonText("环比上期")
                .comparisonValue(metricValue.getComparisonValue())
                .isAbnormal(metricValue.isAbnormal(java.math.BigDecimal.valueOf(20))) // 20%阈值
                .build();
    }

    private ChartDataResponse convertToChartDataResponse(DashboardAggregate.ChartData chartData) {
        if (chartData == null) {
            return null;
        }

        List<ChartDataResponse.SeriesData> series = chartData.getSeries().stream()
                .map(this::convertToSeriesData)
                .collect(Collectors.toList());

        return ChartDataResponse.builder()
                .chartType(chartData.getChartType())
                .title(chartData.getTitle())
                .categories(chartData.getCategories())
                .series(series)
                .options(ChartDataResponse.ChartOptions.builder()
                        .showLegend(true)
                        .legendPosition("top")
                        .showDataLabels(false)
                        .smooth(true)
                        .height(300)
                        .build())
                .build();
    }

    private ChartDataResponse.SeriesData convertToSeriesData(DashboardAggregate.ChartData.SeriesData seriesData) {
        return ChartDataResponse.SeriesData.builder()
                .name(seriesData.getName())
                .data(seriesData.getData())
                .color(seriesData.getColor())
                .type(seriesData.getType())
                .visible(true)
                .build();
    }

    private RealTimeStatsResponse convertToRealTimeStatsResponse(DashboardAggregate.RealTimeStats realTimeStats) {
        if (realTimeStats == null) {
            return null;
        }

        return RealTimeStatsResponse.builder()
                .currentOnline(realTimeStats.getCurrentOnline())
                .peakOnline(realTimeStats.getPeakOnline())
                .totalDownloadsToday(realTimeStats.getTotalDownloadsToday())
                .systemLoad(realTimeStats.getSystemLoad())
                .updateTime(realTimeStats.getUpdateTime())
                .build();
    }

    private ProductLineDashboardResponse.ProductLineMetrics convertToProductLineMetrics(
            Map<String, MetricValue> metrics) {
        return ProductLineDashboardResponse.ProductLineMetrics.builder()
                .totalUsers(getMetricLongValue(metrics, "totalUsers"))
                .activeUsers(getMetricLongValue(metrics, "activeUsers"))
                .newUsers(getMetricLongValue(metrics, "newUsers"))
                .totalRevenue(getMetricBigDecimalValue(metrics, "revenue"))
                .downloads(getMetricLongValue(metrics, "downloads"))
                .retentionRate(getMetricBigDecimalValue(metrics, "retention"))
                .userGrowthRate(getMetricBigDecimalValue(metrics, "userGrowthRate"))
                .revenueGrowthRate(getMetricBigDecimalValue(metrics, "revenueGrowthRate"))
                .marketShare(getMetricBigDecimalValue(metrics, "marketShare"))
                .build();
    }

    private List<OverviewDashboardResponse.ProductLineSummary> convertToProductLineSummaryList(
            List<DashboardRepository.ProductLineSummary> summaries) {
        return summaries.stream()
                .map(summary -> OverviewDashboardResponse.ProductLineSummary.builder()
                        .id(summary.getId())
                        .name(summary.getName())
                        .type(summary.getType())
                        .userCount(summary.getUserCount())
                        .revenue(summary.getRevenue())
                        .growthRate(summary.getGrowthRate())
                        .build())
                .collect(Collectors.toList());
    }

    private Long getMetricLongValue(Map<String, MetricValue> metrics, String key) {
        MetricValue metric = metrics.get(key);
        return metric != null ? metric.getValue().longValue() : 0L;
    }

    private java.math.BigDecimal getMetricBigDecimalValue(Map<String, MetricValue> metrics, String key) {
        MetricValue metric = metrics.get(key);
        return metric != null ? metric.getValue() : java.math.BigDecimal.ZERO;
    }
}
