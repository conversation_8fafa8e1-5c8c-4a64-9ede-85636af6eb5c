package com.foxit.crm.modules.useranalysis.domain.repository;

import com.foxit.crm.modules.useranalysis.domain.entity.UserGrowthAggregate;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;

import java.util.List;
import java.util.Optional;

/**
 * 用户增长仓储接口
 * 定义用户增长数据访问的领域契约
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface UserGrowthRepository {

    /**
     * 获取用户增长总览数据
     *
     * @param timeRange 时间范围
     * @param dataScope 数据权限范围
     * @return 用户增长聚合根
     */
    Optional<UserGrowthAggregate> getUserGrowthOverview(TimeRange timeRange, String dataScope);

    /**
     * 获取新增用户分析数据
     *
     * @param timeRange 时间范围
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 用户增长聚合根
     */
    Optional<UserGrowthAggregate> getNewUserAnalysis(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 获取用户留存分析数据
     *
     * @param timeRange 时间范围
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 用户增长聚合根
     */
    Optional<UserGrowthAggregate> getUserRetentionAnalysis(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 获取增长趋势分析数据
     *
     * @param timeRange 时间范围
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 用户增长聚合根
     */
    Optional<UserGrowthAggregate> getGrowthTrendAnalysis(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 获取用户来源分析数据
     *
     * @param timeRange 时间范围
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 用户增长聚合根
     */
    Optional<UserGrowthAggregate> getUserSourceAnalysis(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 获取队列留存分析数据
     *
     * @param timeRange 时间范围
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 用户增长聚合根
     */
    Optional<UserGrowthAggregate> getCohortRetentionAnalysis(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 获取产品线对比数据
     *
     * @param timeRange 时间范围
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 用户增长聚合根列表
     */
    List<UserGrowthAggregate> getProductLineComparison(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 保存用户增长分析结果
     *
     * @param aggregate 用户增长聚合根
     * @return 是否保存成功
     */
    boolean saveUserGrowthAnalysis(UserGrowthAggregate aggregate);

    /**
     * 删除用户增长分析数据
     *
     * @param aggregateId 聚合根ID
     * @return 是否删除成功
     */
    boolean deleteUserGrowthAnalysis(String aggregateId);

    /**
     * 批量获取用户增长数据
     *
     * @param timeRanges 时间范围列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 用户增长聚合根列表
     */
    List<UserGrowthAggregate> batchGetUserGrowthData(List<TimeRange> timeRanges, List<Long> productLineIds, String dataScope);

    /**
     * 获取用户增长统计摘要
     *
     * @param timeRange 时间范围
     * @param dataScope 数据权限范围
     * @return 统计摘要数据
     */
    UserGrowthSummary getUserGrowthSummary(TimeRange timeRange, String dataScope);

    /**
     * 用户增长统计摘要
     */
    record UserGrowthSummary(
            Long totalNewUsers,
            Long totalUsers,
            Double growthRate,
            Double retention1d,
            Double retention7d,
            Double retention30d,
            String primarySource,
            java.time.LocalDateTime calculatedAt
    ) {}
}
