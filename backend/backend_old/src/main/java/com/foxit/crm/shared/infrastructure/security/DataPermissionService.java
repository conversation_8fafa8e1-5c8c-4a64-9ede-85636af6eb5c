package com.foxit.crm.shared.infrastructure.security;

import com.foxit.crm.shared.infrastructure.cache.DataPermissionCacheManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据权限服务
 * 提供数据权限检查、过滤等功能，集成缓存机制
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataPermissionService {

    private final DataPermissionCacheManager cacheManager;

    /**
     * 检查用户是否有访问指定数据的权限
     */
    public boolean hasDataPermission(Long userId, String tableName, Long dataId) {
        // 获取用户数据权限规则（从缓存）
        List<String> rules = cacheManager.getUserDataPermissionRules(userId, tableName, 
            () -> loadUserDataPermissionRulesFromDB(userId, tableName));
        
        // 检查权限规则
        return checkDataPermission(rules, dataId);
    }

    /**
     * 获取用户可访问的数据ID列表
     */
    public List<Long> getUserAccessibleDataIds(Long userId, String resourceType) {
        return cacheManager.getUserDataScope(userId, resourceType,
            () -> loadUserDataScopeFromDB(userId, resourceType));
    }

    /**
     * 获取用户行级权限过滤条件
     */
    public Map<String, Object> getUserRowLevelFilter(Long userId, String tableName) {
        return cacheManager.getUserRowLevelFilter(userId, tableName,
            () -> buildUserRowLevelFilterFromDB(userId, tableName));
    }

    /**
     * 获取用户可访问的列
     */
    public List<String> getUserAccessibleColumns(Long userId, String tableName) {
        return cacheManager.getUserColumnPermissions(userId, tableName,
            () -> loadUserColumnPermissionsFromDB(userId, tableName));
    }

    /**
     * 过滤用户可访问的数据列表
     */
    public <T> List<T> filterAccessibleData(Long userId, String tableName, List<T> dataList,
                                           java.util.function.Function<T, Long> idExtractor) {
        List<Long> accessibleIds = getUserAccessibleDataIds(userId, tableName);
        
        if (accessibleIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        return dataList.stream()
                .filter(data -> accessibleIds.contains(idExtractor.apply(data)))
                .collect(Collectors.toList());
    }

    /**
     * 构建SQL WHERE条件
     */
    public String buildSqlWhereCondition(Long userId, String tableName) {
        Map<String, Object> filter = getUserRowLevelFilter(userId, tableName);
        
        if (filter.isEmpty()) {
            return "1=1"; // 无限制
        }
        
        StringBuilder whereClause = new StringBuilder();
        filter.forEach((column, value) -> {
            if (whereClause.length() > 0) {
                whereClause.append(" AND ");
            }
            
            if (value instanceof List) {
                List<?> values = (List<?>) value;
                String inClause = values.stream()
                        .map(v -> "'" + v + "'")
                        .collect(Collectors.joining(","));
                whereClause.append(column).append(" IN (").append(inClause).append(")");
            } else {
                whereClause.append(column).append(" = '").append(value).append("'");
            }
        });
        
        return whereClause.toString();
    }

    /**
     * 检查用户是否有部门数据权限
     */
    public boolean hasDepartmentDataPermission(Long userId, Long deptId) {
        // 获取用户所属部门的数据权限范围
        List<Long> userDeptScope = getUserDepartmentScope(userId);
        return userDeptScope.contains(deptId);
    }

    /**
     * 获取用户部门数据权限范围
     */
    public List<Long> getUserDepartmentScope(Long userId) {
        return cacheManager.getUserDataScope(userId, "department",
            () -> loadUserDepartmentScopeFromDB(userId));
    }

    /**
     * 获取部门数据权限范围
     */
    public List<Long> getDepartmentDataScope(Long deptId) {
        return cacheManager.getDepartmentDataScope(deptId,
            () -> loadDepartmentDataScopeFromDB(deptId));
    }

    /**
     * 应用数据权限模板
     */
    public Map<String, Object> applyDataPermissionTemplate(Long templateId, Map<String, Object> context) {
        Map<String, Object> template = cacheManager.getDataPermissionTemplate(templateId,
            () -> loadDataPermissionTemplateFromDB(templateId));
        
        // 应用模板逻辑，替换占位符等
        return processTemplate(template, context);
    }

    /**
     * 批量检查数据权限
     */
    public Map<Long, Boolean> batchCheckDataPermission(Long userId, String tableName, List<Long> dataIds) {
        List<String> rules = cacheManager.getUserDataPermissionRules(userId, tableName,
            () -> loadUserDataPermissionRulesFromDB(userId, tableName));
        
        Map<Long, Boolean> result = new HashMap<>();
        for (Long dataId : dataIds) {
            result.put(dataId, checkDataPermission(rules, dataId));
        }
        
        return result;
    }

    /**
     * 清除用户数据权限缓存
     */
    public void clearUserDataPermissionCache(Long userId) {
        cacheManager.evictUserDataPermissions(userId);
        log.info("已清除用户数据权限缓存: userId={}", userId);
    }

    /**
     * 清除角色数据权限缓存
     */
    public void clearRoleDataPermissionCache(Long roleId) {
        cacheManager.evictRoleDataPermissions(roleId);
        log.info("已清除角色数据权限缓存: roleId={}", roleId);
    }

    /**
     * 预热用户数据权限缓存
     */
    public void warmUpUserDataPermissions(Long userId) {
        List<String> commonTables = Arrays.asList(
            "sys_user", "sys_role", "sys_permission", 
            "product_line", "operation_log"
        );
        
        cacheManager.warmUpUserDataPermissions(userId, commonTables,
            this::loadUserDataPermissionRulesFromDB);
        
        log.info("已预热用户数据权限缓存: userId={}", userId);
    }

    // ============================
    // 私有方法 - 数据库查询
    // ============================

    private List<String> loadUserDataPermissionRulesFromDB(Long userId, String tableName) {
        // 模拟从数据库加载用户数据权限规则
        log.debug("从数据库加载用户数据权限规则: userId={}, tableName={}", userId, tableName);
        
        // 这里应该实现实际的数据库查询逻辑
        // 返回权限规则列表，例如：["dept_id IN (1,2,3)", "status = 1"]
        return Arrays.asList("status = 1", "deleted = 0");
    }

    private List<Long> loadUserDataScopeFromDB(Long userId, String resourceType) {
        // 模拟从数据库加载用户数据范围
        log.debug("从数据库加载用户数据范围: userId={}, resourceType={}", userId, resourceType);
        
        // 这里应该实现实际的数据库查询逻辑
        return Arrays.asList(1L, 2L, 3L);
    }

    private Map<String, Object> buildUserRowLevelFilterFromDB(Long userId, String tableName) {
        // 模拟构建用户行级权限过滤条件
        log.debug("构建用户行级权限过滤条件: userId={}, tableName={}", userId, tableName);
        
        Map<String, Object> filter = new HashMap<>();
        filter.put("created_by", userId);
        filter.put("status", Arrays.asList(1, 2));
        
        return filter;
    }

    private List<String> loadUserColumnPermissionsFromDB(Long userId, String tableName) {
        // 模拟从数据库加载用户列级权限
        log.debug("从数据库加载用户列级权限: userId={}, tableName={}", userId, tableName);
        
        // 返回用户可访问的列名列表
        return Arrays.asList("id", "name", "status", "created_time");
    }

    private List<Long> loadUserDepartmentScopeFromDB(Long userId) {
        // 模拟从数据库加载用户部门权限范围
        log.debug("从数据库加载用户部门权限范围: userId={}", userId);
        
        return Arrays.asList(1L, 2L, 3L);
    }

    private List<Long> loadDepartmentDataScopeFromDB(Long deptId) {
        // 模拟从数据库加载部门数据权限范围
        log.debug("从数据库加载部门数据权限范围: deptId={}", deptId);
        
        return Arrays.asList(1L, 2L, 3L);
    }

    private Map<String, Object> loadDataPermissionTemplateFromDB(Long templateId) {
        // 模拟从数据库加载数据权限模板
        log.debug("从数据库加载数据权限模板: templateId={}", templateId);
        
        Map<String, Object> template = new HashMap<>();
        template.put("rules", Arrays.asList("dept_id = ${user.deptId}", "status = 1"));
        template.put("columns", Arrays.asList("id", "name", "status"));
        
        return template;
    }

    private boolean checkDataPermission(List<String> rules, Long dataId) {
        // 模拟权限检查逻辑
        // 实际实现应该根据规则和数据ID进行权限验证
        return !rules.isEmpty();
    }

    private Map<String, Object> processTemplate(Map<String, Object> template, Map<String, Object> context) {
        // 模拟模板处理逻辑
        // 实际实现应该替换模板中的占位符
        return new HashMap<>(template);
    }
}
