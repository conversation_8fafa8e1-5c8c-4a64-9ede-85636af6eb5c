package com.foxit.crm.modules.system.infrastructure.persistence.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.foxit.crm.modules.system.domain.model.aggregate.ProductLine;
import com.foxit.crm.modules.system.domain.repository.ProductLineRepository;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.ProductLinePO;
import com.foxit.crm.modules.system.infrastructure.persistence.mapper.ProductLineMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 产品线仓储实现
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Repository
@RequiredArgsConstructor
public class ProductLineRepositoryImpl implements ProductLineRepository {

    private final ProductLineMapper productLineMapper;

    @Override
    public ProductLine save(ProductLine productLine) {
        ProductLinePO productLinePO = new ProductLinePO();
        BeanUtils.copyProperties(productLine, productLinePO);
        
        if (productLine.getId() == null) {
            productLineMapper.insert(productLinePO);
            productLine.setId(productLinePO.getId());
        } else {
            productLineMapper.updateById(productLinePO);
        }
        
        return productLine;
    }

    @Override
    public Optional<ProductLine> findById(Long id) {
        ProductLinePO productLinePO = productLineMapper.selectById(id);
        if (productLinePO == null) {
            return Optional.empty();
        }
        
        ProductLine productLine = new ProductLine();
        BeanUtils.copyProperties(productLinePO, productLine);
        return Optional.of(productLine);
    }

    @Override
    public Optional<ProductLine> findByCode(String code) {
        QueryWrapper<ProductLinePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code);
        
        ProductLinePO productLinePO = productLineMapper.selectOne(queryWrapper);
        if (productLinePO == null) {
            return Optional.empty();
        }
        
        ProductLine productLine = new ProductLine();
        BeanUtils.copyProperties(productLinePO, productLine);
        return Optional.of(productLine);
    }

    @Override
    public List<ProductLine> findAllEnabled() {
        QueryWrapper<ProductLinePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1)
                   .orderByAsc("sort_order");
        
        List<ProductLinePO> productLinePOs = productLineMapper.selectList(queryWrapper);
        return productLinePOs.stream()
                            .map(this::convertToProductLine)
                            .collect(Collectors.toList());
    }

    @Override
    public List<ProductLine> findByType(Integer type) {
        QueryWrapper<ProductLinePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type", type)
                   .eq("status", 1)
                   .orderByAsc("sort_order");
        
        List<ProductLinePO> productLinePOs = productLineMapper.selectList(queryWrapper);
        return productLinePOs.stream()
                            .map(this::convertToProductLine)
                            .collect(Collectors.toList());
    }

    @Override
    public List<ProductLine> findByOwnerId(Long ownerId) {
        QueryWrapper<ProductLinePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("owner_id", ownerId)
                   .orderByAsc("sort_order");
        
        List<ProductLinePO> productLinePOs = productLineMapper.selectList(queryWrapper);
        return productLinePOs.stream()
                            .map(this::convertToProductLine)
                            .collect(Collectors.toList());
    }

    @Override
    public List<ProductLine> findByPage(int page, int size, String keyword, Integer type, Integer status) {
        Page<ProductLinePO> pageParam = new Page<>(page, size);
        QueryWrapper<ProductLinePO> queryWrapper = new QueryWrapper<>();
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                .like("name", keyword)
                .or()
                .like("code", keyword)
                .or()
                .like("description", keyword)
            );
        }
        
        if (type != null) {
            queryWrapper.eq("type", type);
        }
        
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        
        queryWrapper.orderByAsc("sort_order");
        
        Page<ProductLinePO> result = productLineMapper.selectPage(pageParam, queryWrapper);
        return result.getRecords().stream()
                    .map(this::convertToProductLine)
                    .collect(Collectors.toList());
    }

    @Override
    public long count(String keyword, Integer type, Integer status) {
        QueryWrapper<ProductLinePO> queryWrapper = new QueryWrapper<>();
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                .like("name", keyword)
                .or()
                .like("code", keyword)
                .or()
                .like("description", keyword)
            );
        }
        
        if (type != null) {
            queryWrapper.eq("type", type);
        }
        
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        
        return productLineMapper.selectCount(queryWrapper);
    }

    @Override
    public void deleteById(Long id) {
        productLineMapper.deleteById(id);
    }

    @Override
    public boolean existsByCode(String code) {
        QueryWrapper<ProductLinePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code);
        return productLineMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean existsByCodeAndIdNot(String code, Long id) {
        QueryWrapper<ProductLinePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code)
                   .ne("id", id);
        return productLineMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean existsByName(String name) {
        QueryWrapper<ProductLinePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", name);
        return productLineMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean existsByNameAndIdNot(String name, Long id) {
        QueryWrapper<ProductLinePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", name)
                   .ne("id", id);
        return productLineMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public List<ProductLine> findByUserId(Long userId) {
        // TODO: 实现基于用户权限的产品线查询
        // 这里需要结合用户角色权限来查询用户有权限访问的产品线
        // 暂时返回所有启用的产品线
        return findAllEnabled();
    }

    @Override
    public List<ProductLine> findAllWithTypeCount() {
        QueryWrapper<ProductLinePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("type", "sort_order");
        
        List<ProductLinePO> productLinePOs = productLineMapper.selectList(queryWrapper);
        return productLinePOs.stream()
                            .map(this::convertToProductLine)
                            .collect(Collectors.toList());
    }

    @Override
    public List<ProductLine> findByStatus(Integer status) {
        QueryWrapper<ProductLinePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status)
                   .orderByAsc("sort_order");
        
        List<ProductLinePO> productLinePOs = productLineMapper.selectList(queryWrapper);
        return productLinePOs.stream()
                            .map(this::convertToProductLine)
                            .collect(Collectors.toList());
    }

    @Override
    public void updateStatusBatch(List<Long> ids, Integer status) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        
        UpdateWrapper<ProductLinePO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", ids)
                    .set("status", status);
        
        productLineMapper.update(null, updateWrapper);
    }

    /**
     * 转换PO为领域对象
     */
    private ProductLine convertToProductLine(ProductLinePO productLinePO) {
        ProductLine productLine = new ProductLine();
        BeanUtils.copyProperties(productLinePO, productLine);
        return productLine;
    }
}
