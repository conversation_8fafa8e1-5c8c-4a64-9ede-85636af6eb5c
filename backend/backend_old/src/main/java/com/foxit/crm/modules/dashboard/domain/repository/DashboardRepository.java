package com.foxit.crm.modules.dashboard.domain.repository;

import com.foxit.crm.modules.dashboard.domain.entity.DashboardAggregate;
import com.foxit.crm.modules.dashboard.domain.valueobject.MetricValue;
import com.foxit.crm.modules.dashboard.domain.valueobject.TimeRange;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Dashboard仓储接口
 * 定义Dashboard数据访问的抽象接口
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
public interface DashboardRepository {

    /**
     * 获取核心指标数据
     */
    Map<String, MetricValue> getCoreMetrics(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 获取用户增长数据
     */
    DashboardAggregate.ChartData getUserGrowthData(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 获取收入趋势数据
     */
    DashboardAggregate.ChartData getRevenueTrendData(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 获取产品线分布数据
     */
    DashboardAggregate.ChartData getProductLineDistributionData(TimeRange timeRange, String dataScope);

    /**
     * 获取用户类型分布数据
     */
    DashboardAggregate.ChartData getUserTypeDistributionData(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 获取实时在线用户数据
     */
    DashboardAggregate.ChartData getRealTimeOnlineData(String dataScope);

    /**
     * 获取实时统计数据
     */
    DashboardAggregate.RealTimeStats getRealTimeStats(String dataScope);

    /**
     * 获取功能使用分析数据
     */
    DashboardAggregate.ChartData getFeatureUsageData(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 获取下载统计数据
     */
    DashboardAggregate.ChartData getDownloadStatsData(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 获取用户活跃度热力图数据
     */
    DashboardAggregate.ChartData getActivityHeatmapData(TimeRange timeRange, List<Long> productLineIds, String dataScope);

    /**
     * 获取产品线对比数据
     */
    Map<String, BigDecimal> getProductLineComparisonData(List<Long> productLineIds, TimeRange timeRange, String dataScope);

    /**
     * 获取异常检测数据
     */
    List<AnomalyData> getAnomalyData(TimeRange timeRange, String dataScope);

    /**
     * 获取用户权限可访问的产品线列表
     */
    List<ProductLineSummary> getAccessibleProductLines(String dataScope, Long userId);

    /**
     * 异常数据内部类
     */
    class AnomalyData {
        private final String metricName;
        private final BigDecimal currentValue;
        private final BigDecimal expectedValue;
        private final BigDecimal deviation;
        private final String severity;
        private final String description;

        public AnomalyData(String metricName, BigDecimal currentValue, BigDecimal expectedValue, 
                          BigDecimal deviation, String severity, String description) {
            this.metricName = metricName;
            this.currentValue = currentValue;
            this.expectedValue = expectedValue;
            this.deviation = deviation;
            this.severity = severity;
            this.description = description;
        }

        // Getters
        public String getMetricName() { return metricName; }
        public BigDecimal getCurrentValue() { return currentValue; }
        public BigDecimal getExpectedValue() { return expectedValue; }
        public BigDecimal getDeviation() { return deviation; }
        public String getSeverity() { return severity; }
        public String getDescription() { return description; }
    }

    /**
     * 产品线摘要内部类
     */
    class ProductLineSummary {
        private final Long id;
        private final String name;
        private final String type;
        private final Long userCount;
        private final BigDecimal revenue;
        private final BigDecimal growthRate;

        public ProductLineSummary(Long id, String name, String type, Long userCount, 
                                 BigDecimal revenue, BigDecimal growthRate) {
            this.id = id;
            this.name = name;
            this.type = type;
            this.userCount = userCount;
            this.revenue = revenue;
            this.growthRate = growthRate;
        }

        // Getters
        public Long getId() { return id; }
        public String getName() { return name; }
        public String getType() { return type; }
        public Long getUserCount() { return userCount; }
        public BigDecimal getRevenue() { return revenue; }
        public BigDecimal getGrowthRate() { return growthRate; }
    }
}
