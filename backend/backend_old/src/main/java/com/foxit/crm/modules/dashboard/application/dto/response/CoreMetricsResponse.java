package com.foxit.crm.modules.dashboard.application.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 核心指标响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CoreMetricsResponse {

    /**
     * 总用户数
     */
    private MetricData totalUsers;

    /**
     * 活跃用户数
     */
    private MetricData activeUsers;

    /**
     * 新增用户数
     */
    private MetricData newUsers;

    /**
     * 总收入
     */
    private MetricData revenue;

    /**
     * 下载量
     */
    private MetricData downloads;

    /**
     * 留存率
     */
    private MetricData retention;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 指标数据内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class MetricData {
        /**
         * 指标值
         */
        private BigDecimal value;

        /**
         * 单位
         */
        private String unit;

        /**
         * 变化率（百分比）
         */
        private BigDecimal changeRate;

        /**
         * 变化趋势：up-上升, down-下降, stable-稳定
         */
        private String trend;

        /**
         * 对比文本
         */
        private String comparisonText;

        /**
         * 对比值
         */
        private BigDecimal comparisonValue;

        /**
         * 是否异常
         */
        private Boolean isAbnormal;

        /**
         * 异常原因
         */
        private String abnormalReason;
    }
}
