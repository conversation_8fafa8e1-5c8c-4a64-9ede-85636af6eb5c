{"serverUrl": "http://localhost:8080", "serverEnv": "dev", "projectName": "SCRM-Next数据分析平台API文档", "openUrl": "http://localhost:8080/api", "packageFilters": "com.foxit.crm.modules.*.api.controller.*", "md5EncryptedHtmlName": false, "style": "xt256", "createDebugPage": true, "allInOne": true, "outPath": "src/main/resources/static/doc", "coverOld": true, "packageExcludeFilters": "", "skipTransientField": true, "sortByTitle": false, "showAuthor": true, "requestFieldToUnderline": true, "responseFieldToUnderline": true, "inlineEnum": true, "recursionLimit": 7, "allInOneDocFileName": "index.html", "requestExample": true, "responseExample": true, "requestIgnoreParam": ["org.springframework.ui.ModelMap"], "dataDictionaries": [], "errorCodeDictionaries": [], "revisionLogs": [{"version": "1.0.0", "revisionTime": "2025-06-19 10:00:00", "status": "创建", "author": "veikin", "remarks": "初始版本，包含用户认证、基础管理等核心功能"}], "customResponseFields": [{"name": "code", "desc": "响应码", "ownerClassName": "com.foxit.crm.common.exception.Result", "value": "200"}, {"name": "message", "desc": "响应消息", "ownerClassName": "com.foxit.crm.common.exception.Result", "value": "操作成功"}, {"name": "data", "desc": "响应数据", "ownerClassName": "com.foxit.crm.common.exception.Result"}, {"name": "timestamp", "desc": "时间戳", "ownerClassName": "com.foxit.crm.common.exception.Result", "value": "1640995200000"}], "customRequestFields": [], "apiConstants": [], "responseBodyAdvice": {"className": "com.foxit.crm.common.exception.Result"}, "requestHeaders": [{"name": "Authorization", "type": "string", "desc": "认证token，格式：Bearer {token}", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "required": false, "since": "-", "pathPatterns": "/api/**", "excludePathPatterns": "/api/auth/login,/api/auth/register"}], "rpcApiDependencies": [], "rpcConsumerConfig": "src/main/resources/consumer-example.conf", "apiObjectReplacements": [], "groups": [{"name": "系统管理", "apis": "com.foxit.crm.modules.system.api.controller.*"}, {"name": "产品版本管理", "apis": "com.foxit.crm.modules.system.api.controller.ProductVersionController"}]}