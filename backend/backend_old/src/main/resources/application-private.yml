# 私有配置文件 - 使用 Jasypt 加密敏感信息
# 此文件包含所有环境相关的连接信息和敏感数据
# 通过统一的变量名实现配置复用，主配置文件可直接引用
# 使用方法：java -Djasypt.encryptor.password=your_master_password -jar app.jar

# ============================
# Jasypt 加密配置
# ============================
jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES # 加密算法（可升级为更安全的算法）
    key-obtention-iterations: 1000 # 密钥获取迭代次数
    pool-size: 1 # 加密器池大小
    provider-name: SunJCE # 加密提供者
    salt-generator-classname: org.jasypt.salt.RandomSaltGenerator # 盐值生成器
    iv-generator-classname: org.jasypt.iv.NoIvGenerator # IV 生成器
    string-output-type: base64 # 字符串输出类型
    password: ${FX_MASTER_KEY:2d8810f4c0e3dd74124f49950c97979e} # 主密钥

# ============================
# 开发环境私有配置
# ============================
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    jdbc-url: ********************************************************************************************************************************************************************************
    username: ENC(vgxJEjmOrk89thcSt7p3jg==)
    password: ENC(vgxJEjmOrk89thcSt7p3jg==)
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 10 # 开发环境连接池大小
      minimum-idle: 2
      connection-timeout: 30000
      idle-timeout: 600000
    # ClickHouse 数据源
    clickhouse:
      jdbc-url: *****************************************************
      driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
      username: ENC(eGmDzWC7WJjdROjcXs4aIw==)
      password: ENC(eGmDzWC7WJjdROjcXs4aIw==)
      connection-timeout: 30000
      socket-timeout: 60000
      hikari:
        maximum-pool-size: 5
        minimum-idle: 1
        connection-timeout: 30000
        idle-timeout: 600000
  # Redis 配置 - 开发环境
  data:
    redis:
      host: localhost # Redis 主机地址
      port: 6379 # Redis 端口
      database: 0 # Redis 数据库索引
      timeout: 10000ms # 连接超时时间
      password: # 开发环境无密码

# SCRM 业务配置 - 开发环境（使用统一变量名）
scrm:
  # JWT 配置
  jwt:
    secret: ENC(52yqzeZGRNFHTDzYe/F5wXTSZ4mlBIX7aCEMphBun3OxaHRLJ1B1tsQKxwVq7aQl) # JWT 签名密钥（加密）
  # 跨域配置
  cors:
    allowed-origins: # 开发环境允许的域名
      - http://localhost:3000
      - http://localhost:5173
      - http://127.0.0.1:3000
      - http://127.0.0.1:5173

---
# ============================
# 测试环境私有配置
# ============================
spring:
  config:
    activate:
      on-profile: test
  datasource:
    jdbc-url: ************************************************************************************************************************************************************
    username: ENC(encrypted_test_username) # 数据库用户名（加密）
    password: ENC(encrypted_test_password) # 数据库密码（加密）
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 15 # 测试环境连接池大小
      minimum-idle: 2 # 最小空闲连接数
    # ClickHouse 数据源
    clickhouse:
      jdbc-url: ************************************************************
      driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
      username: ENC(encrypted_test_clickhouse_username)
      password: ENC(encrypted_test_clickhouse_password)
      connection-timeout: 30000
      socket-timeout: 60000
      hikari:
        maximum-pool-size: 8
        minimum-idle: 2
  # Redis 配置 - 测试环境
  data:
    redis:
      host: test-redis-server # Redis 主机地址
      port: 6379 # Redis 端口
      database: 1 # Redis 数据库索引（独立）
      timeout: 10000ms # 连接超时时间
      password: ENC(encrypted_test_redis_password) # Redis 密码（加密）
      lettuce:
        pool:
          max-active: 10 # 测试环境覆盖默认配置
          max-idle: 10 # 测试环境覆盖默认配置
          min-idle: 2 # 测试环境覆盖默认配置

# SCRM 业务配置 - 测试环境（使用统一变量名）
scrm:
  # JWT 配置
  jwt:
    secret: ENC(encrypted_test_jwt_secret) # JWT 签名密钥（加密）
  # 跨域配置
  cors:
    allowed-origins: # 测试环境允许的域名
      - http://test-frontend.example.com

---
# ============================
# 生产环境私有配置
# ============================
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    jdbc-url: ${DB_URL:************************************************************************************************************************************************************}
    username: ENC(${DB_USERNAME_ENCRYPTED:encrypted_prod_username}) # 数据库用户名（加密，支持环境变量）
    password: ENC(${DB_PASSWORD_ENCRYPTED:encrypted_prod_password}) # 数据库密码（加密，支持环境变量）
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20 # 生产环境连接池大小
      minimum-idle: 10 # 生产环境最小空闲连接
    # ClickHouse 数据源
    clickhouse:
      jdbc-url: ${CLICKHOUSE_URL:************************************************************}
      driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
      username: ENC(${CLICKHOUSE_USERNAME_ENCRYPTED:encrypted_prod_clickhouse_username})
      password: ENC(${CLICKHOUSE_PASSWORD_ENCRYPTED:encrypted_prod_clickhouse_password})
      connection-timeout: 30000
      socket-timeout: 60000
      hikari:
        maximum-pool-size: 10
        minimum-idle: 3
  # Redis 配置 - 生产环境
  data:
    redis:
      host: ${REDIS_HOST:prod-redis-server} # Redis 主机地址（支持环境变量）
      port: 6379 # Redis 端口
      database: 0 # Redis 主数据库
      timeout: 5000ms # 生产环境较短超时时间
      password: ENC(${REDIS_PASSWORD_ENCRYPTED:encrypted_prod_redis_password}) # Redis 密码（加密，支持环境变量）
      lettuce:
        pool:
          max-active: 20 # 生产环境覆盖默认配置
          max-idle: 20 # 生产环境覆盖默认配置
          min-idle: 5 # 生产环境覆盖默认配置

# SCRM 业务配置 - 生产环境（使用统一变量名）
scrm:
  # JWT 配置
  jwt:
    secret: ENC(${JWT_SECRET_ENCRYPTED:encrypted_prod_jwt_secret}) # JWT 签名密钥（加密，支持环境变量）
  # 跨域配置
  cors:
    allowed-origins: # 生产环境允许的域名（HTTPS）
      - https://your-frontend-domain.com

# ============================
# 其他敏感配置示例
# ============================
# 第三方服务配置（使用 Jasypt 加密）
# 注意：不同环境可以使用相同的配置结构，主配置文件可直接引用
