-- =============================================
-- SCRM-Next MySQL 模拟数据文件
-- 创建时间：2025-07-02
-- 说明：包含所有MySQL业务表的测试数据
-- 版本：v1.0
-- 注意：执行前请确保表结构已创建
-- =============================================

-- 数据库的选择由执行脚本处理

-- =============================================
-- 核心系统数据
-- =============================================

-- =============================================
-- 1. 系统用户基础数据
-- =============================================
INSERT INTO `sys_user` (`id`, `username`, `password`, `real_name`, `email`, `phone`, `status`, `user_type`, `create_by`)
VALUES (2, 'veikin', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMqcpAp232grMhveYXpjlasf.d7F6', '开发者', '<EMAIL>',
        '13800138001', 1, 2, 1),
       (3, 'analyst', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMqcpAp232grMhveYXpjlasf.d7F6', '数据分析师', '<EMAIL>',
        '13800138002', 1, 2, 1),
       (4, 'operator', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMqcpAp232grMhveYXpjlasf.d7F6', '运营人员', '<EMAIL>',
        '13800138003', 1, 2, 1),
       (5, 'manager', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMqcpAp232grMhveYXpjlasf.d7F6', '产品经理', '<EMAIL>',
        '13800138004', 1, 2, 1),
       (6, 'sales', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMqcpAp232grMhveYXpjlasf.d7F6', '销售经理', '<EMAIL>',
        '13800138005', 1, 2, 1),
       (7, 'support', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMqcpAp232grMhveYXpjlasf.d7F6', '客服专员', '<EMAIL>',
        '13800138006', 1, 2, 1),
       (8, 'marketing', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMqcpAp232grMhveYXpjlasf.d7F6', '市场专员', '<EMAIL>',
        '13800138007', 1, 2, 1),
       (9, 'tester', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMqcpAp232grMhveYXpjlasf.d7F6', '测试工程师', '<EMAIL>',
        '13800138008', 1, 2, 1),
       (10, 'designer', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMqcpAp232grMhveYXpjlasf.d7F6', 'UI设计师', '<EMAIL>',
        '13800138009', 1, 2, 1),
       (11, 'finance', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMqcpAp232grMhveYXpjlasf.d7F6', '财务专员', '<EMAIL>',
        '13800138010', 1, 2, 1);

-- =============================================
-- 2. 系统角色基础数据
-- =============================================
INSERT INTO `sys_role` (`id`, `role_name`, `role_code`, `description`, `status`, `sort_order`, `create_by`)
VALUES (1, '超级管理员', 'SUPER_ADMIN', '拥有系统所有权限', 1, 1, 1),
       (2, '系统管理员', 'ADMIN', '拥有系统管理权限', 1, 2, 1),
       (3, '数据分析师', 'ANALYST', '拥有数据分析权限', 1, 3, 1),
       (4, '运营人员', 'OPERATOR', '拥有运营管理权限', 1, 4, 1),
       (5, '普通用户', 'USER', '基础用户权限', 1, 5, 1);

-- =============================================
-- 3. 系统权限基础数据
-- =============================================
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `permission_type`, `parent_id`, `path`,
                              `component`, `icon`, `sort_order`, `status`, `create_by`)
VALUES (1, '系统管理', 'system', 1, 0, '/system', 'Layout', 'system', 1, 1, 1),
       (2, '用户管理', 'system:user', 1, 1, '/system/user', 'system/user/index', 'user', 1, 1, 1),
       (3, '角色管理', 'system:role', 1, 1, '/system/role', 'system/role/index', 'role', 2, 1, 1),
       (4, '权限管理', 'system:permission', 1, 1, '/system/permission', 'system/permission/index', 'permission', 3, 1,
        1),
       (5, '数据分析', 'analytics', 1, 0, '/analytics', 'Layout', 'chart', 2, 1, 1),
       (6, '仪表盘', 'analytics:dashboard', 1, 5, '/analytics/dashboard', 'analytics/dashboard/index', 'dashboard', 1,
        1, 1),
       (7, '用户分析', 'analytics:user', 1, 5, '/analytics/user', 'analytics/user/index', 'user-analysis', 2, 1, 1),
       (8, '产品分析', 'analytics:product', 1, 5, '/analytics/product', 'analytics/product/index', 'product-analysis',
        3, 1, 1);

-- =============================================
-- 4. 用户角色关联数据
-- =============================================
INSERT INTO `sys_user_role` (`user_id`, `role_id`, `create_by`)
VALUES (1, 1, 1), -- admin -> 超级管理员
       (2, 2, 1), -- veikin -> 系统管理员
       (3, 3, 1), -- analyst -> 数据分析师
       (4, 4, 1);
-- operator -> 运营人员

-- =============================================
-- 5. 角色权限关联数据
-- =============================================
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `create_by`)
VALUES
-- 超级管理员拥有所有权限
(1, 1, 1),
(1, 2, 1),
(1, 3, 1),
(1, 4, 1),
(1, 5, 1),
(1, 6, 1),
(1, 7, 1),
(1, 8, 1),
-- 系统管理员拥有系统管理权限
(2, 1, 1),
(2, 2, 1),
(2, 3, 1),
(2, 4, 1),
-- 数据分析师拥有数据分析权限
(3, 5, 1),
(3, 6, 1),
(3, 7, 1),
(3, 8, 1),
-- 运营人员拥有部分分析权限
(4, 5, 1),
(4, 6, 1),
(4, 7, 1);

-- =============================================
-- 6. 系统配置基础数据
-- =============================================
INSERT INTO `sys_config` (`config_name`, `config_key`, `config_value`, `config_type`, `description`, `is_system`,
                          `status`, `create_by`)
VALUES ('系统名称', 'system.name', 'SCRM-Next', 1, '系统名称配置', 1, 1, 1),
       ('系统版本', 'system.version', '1.0.0', 1, '系统版本号', 1, 1, 1),
       ('登录失败锁定次数', 'login.fail.max.count', '5', 2, '登录失败最大次数', 1, 1, 1),
       ('会话超时时间', 'session.timeout', '30', 2, '会话超时时间(分钟)', 1, 1, 1),
       ('文件上传大小限制', 'upload.max.size', '10485760', 2, '文件上传大小限制(字节)', 1, 1, 1);

-- =============================================
-- 7. 产品线基础数据
-- =============================================
INSERT INTO `product_line` (`id`, `name`, `code`, `type`, `description`, `status`, `sort_order`, `create_by`)
VALUES (1, 'PDF阅读器', 'PDF_READER', 1, '专业的PDF阅读和查看工具', 1, 1, 1),
       (2, 'PDF编辑器', 'PDF_EDITOR', 1, '强大的PDF编辑和创建工具', 1, 2, 1),
       (3, 'PDF创建器', 'PDF_CREATOR', 3, '在线PDF创建和转换工具', 1, 3, 1),
       (4, 'Foxit Cloud', 'FOXIT_CLOUD', 2, '云端PDF协作平台', 1, 4, 1),
       (5, 'PhantomPDF', 'PHANTOM_PDF', 1, '企业级PDF解决方案', 1, 5, 1),
       (6, 'PDF365', 'PDF365', 3, '在线PDF工具集合', 1, 6, 1),
       (7, '智慧打印', 'SMART_PRINT', 2, '智能打印管理系统', 1, 7, 1),
       (8, 'PDF SDK', 'PDF_SDK', 4, 'PDF开发工具包', 1, 8, 1),
       (9, 'eSign', 'ESIGN', 2, '电子签名解决方案', 1, 9, 1),
       (10, 'PDF Compressor', 'PDF_COMPRESSOR', 3, 'PDF压缩工具', 1, 10, 1);

-- =============================================
-- 8. 产品版本基础数据
-- =============================================
INSERT INTO `product_version` (`product_line_id`, `version_number`, `version_name`, `description`, `is_latest`,
                               `is_stable`, `release_time`, `status`, `create_by`)
VALUES
-- PDF阅读器版本
(1, '12.1.0', 'PDF阅读器 v12.1', '新增批注功能，优化阅读体验', 1, 1, '2025-06-01 10:00:00', 1, 1),
(1, '12.0.5', 'PDF阅读器 v12.0.5', '修复已知问题，提升稳定性', 0, 1, '2025-05-15 14:30:00', 1, 1),
(1, '12.0.0', 'PDF阅读器 v12.0', '全新界面设计，性能大幅提升', 0, 1, '2025-04-20 09:00:00', 1, 1),
(1, '11.9.8', 'PDF阅读器 v11.9.8', '修复安全漏洞', 0, 1, '2025-04-01 16:30:00', 1, 1),

-- PDF编辑器版本
(2, '11.2.0', 'PDF编辑器 v11.2', '新增表单编辑功能', 1, 1, '2025-06-10 16:00:00', 1, 1),
(2, '11.1.8', 'PDF编辑器 v11.1.8', '优化编辑性能', 0, 1, '2025-05-20 11:20:00', 1, 1),
(2, '11.1.0', 'PDF编辑器 v11.1', '新增OCR识别功能', 0, 1, '2025-04-15 14:00:00', 1, 1),
(2, '11.0.5', 'PDF编辑器 v11.0.5', '修复文本编辑问题', 0, 1, '2025-03-25 10:45:00', 1, 1),

-- PDF创建器版本
(3, '3.5.0', 'PDF创建器 v3.5', '支持更多文档格式转换', 1, 1, '2025-06-05 09:15:00', 1, 1),
(3, '3.4.2', 'PDF创建器 v3.4.2', '优化转换速度', 0, 1, '2025-05-10 13:20:00', 1, 1),
(3, '3.4.0', 'PDF创建器 v3.4', '新增批量转换功能', 0, 1, '2025-04-08 11:30:00', 1, 1),

-- Foxit Cloud版本
(4, '2.8.0', 'Foxit Cloud v2.8', '新增团队协作功能', 1, 1, '2025-06-15 15:00:00', 1, 1),
(4, '2.7.5', 'Foxit Cloud v2.7.5', '优化云端同步', 0, 1, '2025-05-25 12:00:00', 1, 1),
(4, '2.7.0', 'Foxit Cloud v2.7', '新增版本控制', 0, 1, '2025-04-30 10:15:00', 1, 1),

-- PhantomPDF版本
(5, '12.0.0', 'PhantomPDF v12.0', '企业级安全增强', 1, 1, '2025-06-20 14:30:00', 1, 1),
(5, '11.8.5', 'PhantomPDF v11.8.5', '修复企业部署问题', 0, 1, '2025-05-30 16:45:00', 1, 1),

-- PDF365版本
(6, '1.2.0', 'PDF365 v1.2', '新增在线编辑器', 1, 1, '2025-06-12 11:00:00', 1, 1),
(6, '1.1.8', 'PDF365 v1.1.8', '优化用户体验', 0, 1, '2025-05-18 09:30:00', 1, 1),

-- 智慧打印版本
(7, '4.5.0', '智慧打印 v4.5', '新增移动端支持', 1, 1, '2025-06-08 13:15:00', 1, 1),
(7, '4.4.2', '智慧打印 v4.4.2', '修复打印队列问题', 0, 1, '2025-05-22 15:20:00', 1, 1),

-- PDF SDK版本
(8, '8.3.0', 'PDF SDK v8.3', '新增JavaScript API', 1, 1, '2025-06-18 10:45:00', 1, 1),
(8, '8.2.5', 'PDF SDK v8.2.5', '修复内存泄漏', 0, 1, '2025-05-28 14:10:00', 1, 1),

-- eSign版本
(9, '3.1.0', 'eSign v3.1', '新增生物识别签名', 1, 1, '2025-06-22 16:20:00', 1, 1),
(9, '3.0.8', 'eSign v3.0.8', '优化签名验证', 0, 1, '2025-06-01 12:40:00', 1, 1),

-- PDF Compressor版本
(10, '2.3.0', 'PDF Compressor v2.3', '新增智能压缩算法', 1, 1, '2025-06-25 09:50:00', 1, 1),
(10, '2.2.5', 'PDF Compressor v2.2.5', '提升压缩效率', 0, 1, '2025-06-05 11:25:00', 1, 1);

-- =============================================
-- 业务扩展数据
-- =============================================

-- =============================================
-- 9. 用户分群基础数据
-- =============================================
INSERT INTO `user_segments` (`id`, `segment_name`, `segment_code`, `description`, `segment_type`, `segment_rules`,
                             `user_count`, `status`, `create_by`)
VALUES (1, '全部用户', 'ALL_USERS', '包含所有注册用户', 1,
        '{"conditions": [{"field": "status", "operator": "eq", "value": 1}]}', 125000, 1, 1),
       (2, '活跃用户', 'ACTIVE_USERS', '最近30天内有登录行为的用户', 2,
        '{"conditions": [{"field": "last_login_time", "operator": "gte", "value": "30_days_ago"}]}', 68000, 1, 1),
       (3, '新用户', 'NEW_USERS', '最近7天内注册的用户', 2,
        '{"conditions": [{"field": "create_time", "operator": "gte", "value": "7_days_ago"}]}', 4500, 1, 1),
       (4, '高价值用户', 'HIGH_VALUE_USERS', '付费金额超过1000元的用户', 3,
        '{"conditions": [{"field": "total_payment", "operator": "gt", "value": 1000}]}', 12500, 1, 1),
       (5, '流失用户', 'CHURN_USERS', '超过90天未登录的用户', 2,
        '{"conditions": [{"field": "last_login_time", "operator": "lt", "value": "90_days_ago"}]}', 25000, 1, 1),
       (6, '企业用户', 'ENTERPRISE_USERS', '企业版产品用户', 3,
        '{"conditions": [{"field": "user_type", "operator": "eq", "value": "enterprise"}]}', 8900, 1, 1),
       (7, '免费用户', 'FREE_USERS', '仅使用免费功能的用户', 3,
        '{"conditions": [{"field": "subscription_type", "operator": "eq", "value": "free"}]}', 89000, 1, 1),
       (8, '试用用户', 'TRIAL_USERS', '正在试用付费功能的用户', 3,
        '{"conditions": [{"field": "subscription_type", "operator": "eq", "value": "trial"}]}', 15600, 1, 1),
       (9, '重度用户', 'HEAVY_USERS', '每日使用时长超过2小时的用户', 2,
        '{"conditions": [{"field": "daily_usage_hours", "operator": "gt", "value": 2}]}', 23400, 1, 1),
       (10, '移动端用户', 'MOBILE_USERS', '主要使用移动端的用户', 2,
        '{"conditions": [{"field": "primary_platform", "operator": "eq", "value": "mobile"}]}', 45600, 1, 1),
       (11, 'PDF编辑用户', 'PDF_EDITOR_USERS', '使用PDF编辑功能的用户', 3,
        '{"conditions": [{"field": "used_features", "operator": "contains", "value": "pdf_edit"}]}', 34500, 1, 1),
       (12, '云端用户', 'CLOUD_USERS', '使用云端功能的用户', 3,
        '{"conditions": [{"field": "used_features", "operator": "contains", "value": "cloud"}]}', 28900, 1, 1),
       (13, '国际用户', 'INTERNATIONAL_USERS', '海外地区用户', 2,
        '{"conditions": [{"field": "region", "operator": "not_in", "value": ["CN", "HK", "TW"]}]}', 18700, 1, 1),
       (14, 'VIP用户', 'VIP_USERS', '年付费超过5000元的VIP用户', 3,
        '{"conditions": [{"field": "annual_payment", "operator": "gt", "value": 5000}]}', 3200, 1, 1),
       (15, '学生用户', 'STUDENT_USERS', '教育版用户群体', 3,
        '{"conditions": [{"field": "user_category", "operator": "eq", "value": "education"}]}', 16800, 1, 1);

-- =============================================
-- 10. 用户分群成员数据（模拟部分用户）
-- =============================================
INSERT INTO `user_segment_members` (`segment_id`, `user_id`, `join_time`, `status`)
VALUES
-- 活跃用户分群成员
(2, 2, '2025-06-01 10:00:00', 1),
(2, 3, '2025-06-02 11:30:00', 1),
(2, 4, '2025-06-03 14:15:00', 1),
(2, 5, '2025-06-04 09:45:00', 1),
(2, 6, '2025-06-05 16:20:00', 1),

-- 新用户分群成员
(3, 8, '2025-06-28 10:30:00', 1),
(3, 9, '2025-06-29 15:45:00', 1),
(3, 10, '2025-06-30 12:20:00', 1),
(3, 11, '2025-07-01 09:15:00', 1),

-- 高价值用户分群成员
(4, 2, '2025-05-15 10:00:00', 1),
(4, 5, '2025-05-20 14:30:00', 1),
(4, 6, '2025-05-25 11:45:00', 1),

-- 企业用户分群成员
(6, 5, '2025-04-10 09:00:00', 1),
(6, 6, '2025-04-15 13:20:00', 1),

-- 试用用户分群成员
(8, 7, '2025-06-20 10:30:00', 1),
(8, 8, '2025-06-22 14:45:00', 1),
(8, 9, '2025-06-25 16:10:00', 1),

-- 重度用户分群成员
(9, 2, '2025-05-01 08:00:00', 1),
(9, 3, '2025-05-05 10:30:00', 1),
(9, 4, '2025-05-10 12:15:00', 1),

-- VIP用户分群成员
(14, 2, '2025-03-01 10:00:00', 1),
(14, 5, '2025-03-15 14:30:00', 1);

-- =============================================
-- 2. 功能配置基础数据
-- =============================================
INSERT INTO `feature_config` (`id`, `feature_code`, `feature_name`, `feature_type`, `product_line_id`, `is_enabled`, `config_params`,
                              `access_roles`, `description`, `create_by`)
VALUES (1, 'DASHBOARD_VIEW', '数据看板查看', 'analysis', NULL, 1, '{"refresh_interval": 30, "cache_duration": 300}',
        '["ADMIN", "ANALYST", "VIEWER"]', '查看数据看板功能', 1),
       (2, 'USER_ANALYSIS', '用户分析', 'analysis', NULL, 1, '{"max_date_range": 365, "export_limit": 10000}',
        '["ADMIN", "ANALYST"]', '用户行为分析功能', 1),
       (3, 'BEHAVIOR_ANALYSIS', '行为分析', 'analysis', NULL, 1, '{"max_events": 10000, "realtime_enabled": true}',
        '["ADMIN", "ANALYST"]', '用户行为分析功能', 1),
       (4, 'DATA_EXPORT', '数据导出', 'export', NULL, 1, '{"max_records": 50000, "formats": ["csv", "excel"]}', '["ADMIN"]',
        '数据导出功能', 1),
       (5, 'REPORT_GENERATE', '报告生成', 'report', NULL, 1, '{"max_reports_per_day": 10, "auto_schedule": true}',
        '["ADMIN", "ANALYST"]', '自动报告生成功能', 1),
       (6, 'REALTIME_MONITOR', '实时监控', 'monitor', NULL, 1, '{"alert_threshold": 1000, "notification_enabled": true}',
        '["ADMIN", "OPERATOR"]', '实时系统监控功能', 1),

       -- PDF阅读器功能 (产品线1)
       (10, 'pdf_read', 'PDF阅读', 'core', 1, 1, '{"max_file_size": 100, "supported_versions": ["1.4", "1.7", "2.0"]}',
        '["USER", "PREMIUM"]', 'PDF文档阅读功能', 1),
       (11, 'pdf_zoom', 'PDF缩放', 'core', 1, 1, '{"min_zoom": 25, "max_zoom": 800, "default_zoom": 100}',
        '["USER", "PREMIUM"]', 'PDF文档缩放功能', 1),
       (12, 'pdf_search', 'PDF搜索', 'core', 1, 1, '{"case_sensitive": true, "regex_support": false}',
        '["USER", "PREMIUM"]', 'PDF文档搜索功能', 1),
       (13, 'pdf_bookmark', 'PDF书签', 'advanced', 1, 1, '{"max_bookmarks": 1000, "nested_levels": 10}',
        '["PREMIUM"]', 'PDF书签管理功能', 1),
       (14, 'pdf_annotation', 'PDF注释', 'advanced', 1, 1, '{"annotation_types": ["text", "highlight", "stamp"], "collaborative": true}',
        '["PREMIUM"]', 'PDF注释功能', 1),

       -- PDF编辑器功能 (产品线2)
       (15, 'pdf_edit_text', 'PDF文本编辑', 'edit', 2, 1, '{"font_support": true, "unicode_support": true}',
        '["PREMIUM", "ENTERPRISE"]', 'PDF文本编辑功能', 1),
       (16, 'pdf_edit_image', 'PDF图像编辑', 'edit', 2, 1, '{"image_formats": ["jpg", "png", "gif"], "resize_support": true}',
        '["PREMIUM", "ENTERPRISE"]', 'PDF图像编辑功能', 1),
       (17, 'pdf_page_manage', 'PDF页面管理', 'edit', 2, 1, '{"insert_pages": true, "delete_pages": true, "reorder_pages": true}',
        '["PREMIUM", "ENTERPRISE"]', 'PDF页面管理功能', 1),

       -- PDF转换器功能 (产品线3)
       (18, 'pdf_to_word', 'PDF转Word', 'convert', 3, 1, '{"preserve_layout": true, "ocr_support": true}',
        '["PREMIUM", "ENTERPRISE"]', 'PDF转Word文档功能', 1),
       (19, 'pdf_to_excel', 'PDF转Excel', 'convert', 3, 1, '{"table_detection": true, "data_validation": true}',
        '["PREMIUM", "ENTERPRISE"]', 'PDF转Excel表格功能', 1),
       (20, 'pdf_to_image', 'PDF转图片', 'convert', 3, 1, '{"output_formats": ["jpg", "png", "tiff"], "dpi_options": [150, 300, 600]}',
        '["USER", "PREMIUM", "ENTERPRISE"]', 'PDF转图片功能', 1),

       -- 电子签名功能 (产品线4)
       (21, 'pdf_sign_digital', '数字签名', 'sign', 4, 1, '{"certificate_support": true, "timestamp_support": true}',
        '["ENTERPRISE"]', 'PDF数字签名功能', 1),
       (22, 'pdf_sign_handwrite', '手写签名', 'sign', 4, 1, '{"touch_support": true, "stylus_support": true}',
        '["PREMIUM", "ENTERPRISE"]', 'PDF手写签名功能', 1),

       -- PDF压缩器功能 (产品线5)
       (23, 'pdf_compress_size', 'PDF压缩', 'optimize', 5, 1, '{"compression_levels": ["low", "medium", "high"], "quality_preserve": true}',
        '["USER", "PREMIUM", "ENTERPRISE"]', 'PDF文件压缩功能', 1),
       (24, 'pdf_optimize_web', 'Web优化', 'optimize', 5, 1, '{"fast_web_view": true, "linearization": true}',
        '["PREMIUM", "ENTERPRISE"]', 'PDF Web优化功能', 1);

-- =============================================
-- 3. 用户登录记录模拟数据（最近30天）
-- =============================================
INSERT INTO `user_login_records` (`user_id`, `username`, `login_time`, `logout_time`, `session_duration`, `ip_address`,
                                  `location`, `user_agent`, `device_type`, `platform`, `browser`, `session_id`,
                                  `login_status`, `login_type`)
VALUES
-- 管理员用户登录记录
(1, 'admin', '2025-07-02 09:00:00', '2025-07-02 17:30:00', 30600000, '*************', '北京市',
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'desktop', 'windows', 'Chrome', 'sess_001', 1, 1),
(1, 'admin', '2025-07-01 08:30:00', '2025-07-01 18:00:00', 34200000, '*************', '北京市',
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'desktop', 'windows', 'Chrome', 'sess_002', 1, 1),
(1, 'admin', '2025-06-30 09:15:00', '2025-06-30 17:45:00', 30600000, '*************', '北京市',
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'desktop', 'windows', 'Chrome', 'sess_003', 1, 1),

-- 普通用户登录记录
(2, 'user001', '2025-07-02 10:00:00', '2025-07-02 16:30:00', 23400000, '*************', '上海市',
 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', 'desktop', 'macos', 'Safari', 'sess_004', 1, 1),
(2, 'user001', '2025-07-01 09:30:00', '2025-07-01 17:00:00', 27000000, '*************', '上海市',
 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', 'desktop', 'macos', 'Safari', 'sess_005', 1, 1),

(3, 'user002', '2025-07-02 11:00:00', '2025-07-02 15:30:00', 16200000, '*************', '广州市',
 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15', 'mobile', 'ios', 'Safari', 'sess_006',
 1, 1),
(3, 'user002', '2025-07-01 10:30:00', '2025-07-01 16:00:00', 19800000, '*************', '广州市',
 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15', 'mobile', 'ios', 'Safari', 'sess_007',
 1, 1),

-- 登录失败记录
(4, 'user003', '2025-07-02 12:00:00', NULL, NULL, '*************', '深圳市',
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'desktop', 'windows', 'Chrome', 'sess_008', 0, 1),
(4, 'user003', '2025-07-02 12:05:00', '2025-07-02 18:00:00', 21300000, '*************', '深圳市',
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'desktop', 'windows', 'Chrome', 'sess_009', 1, 1);

-- =============================================
-- 4. 产品使用统计模拟数据（最近30天）
-- =============================================
INSERT INTO `product_usage_stats` (`product_line_id`, `stat_date`, `total_users`, `active_users`, `new_users`,
                                   `returning_users`, `sessions`, `page_views`, `avg_session_duration`, `bounce_rate`,
                                   `conversion_rate`, `revenue`)
VALUES
-- 产品线1：PDF阅读器
(1, '2025-07-02', 125680, 89420, 1250, 88170, 156780, 892340, 25.5, 15.2, 8.5, 285678.50),
(1, '2025-07-01', 124430, 87650, 1180, 86470, 152340, 876540, 24.8, 16.1, 8.2, 278945.30),
(1, '2025-06-30', 123250, 85920, 1320, 84600, 148920, 865430, 26.2, 14.8, 8.8, 292156.80),

-- 产品线2：PDF编辑器
(2, '2025-07-02', 89420, 67890, 890, 67000, 98760, 567890, 32.1, 12.5, 12.3, 195678.90),
(2, '2025-07-01', 88530, 66780, 920, 65860, 96540, 554320, 31.8, 13.2, 11.9, 189456.70),
(2, '2025-06-30', 87610, 65430, 850, 64580, 94320, 542180, 33.5, 11.8, 12.8, 203789.40),

-- 产品线3：PDF创建器
(3, '2025-07-02', 67890, 45670, 670, 45000, 67890, 389450, 28.9, 18.3, 5.8, 145678.20),
(3, '2025-07-01', 67220, 44890, 720, 44170, 66540, 382190, 27.6, 19.1, 5.5, 138945.60),
(3, '2025-06-30', 66500, 43980, 650, 43330, 65120, 375680, 29.3, 17.8, 6.2, 152340.80);

-- =============================================
-- 5. 用户分群成员关系数据（补充数据）
-- =============================================
-- 注意：主要的用户分群成员数据已在上面的第10节中插入
-- 这里只添加一些补充的全部用户分群数据
INSERT INTO `user_segment_members` (`segment_id`, `user_id`, `join_time`, `status`)
VALUES
-- 全部用户分群（补充用户1）
(1, 1, '2025-06-01 00:00:00', 1);

-- =============================================
-- 6. 数据权限配置示例数据
-- =============================================
INSERT INTO `data_permission_config` (`user_id`, `resource_type`, `resource_id`, `permission_type`, `permission_scope`,
                                      `scope_conditions`, `status`, `create_by`)
VALUES
-- 管理员全权限
(1, 'product_line', '*', 'read', 'all', '{}', 1, 1),
(1, 'product_line', '*', 'write', 'all', '{}', 1, 1),
(1, 'user_data', '*', 'read', 'all', '{}', 1, 1),
(1, 'report', '*', 'export', 'all', '{}', 1, 1),

-- 普通用户限制权限
(2, 'product_line', '1', 'read', 'own', '{"department": "sales"}', 1, 1),
(2, 'product_line', '2', 'read', 'own', '{"department": "sales"}', 1, 1),
(2, 'user_data', 'own', 'read', 'own', '{"user_id": 2}', 1, 1),

(3, 'product_line', '1', 'read', 'department', '{"department": "marketing"}', 1, 1),
(3, 'report', 'basic', 'read', 'department', '{"department": "marketing"}', 1, 1);

-- =============================================
-- 更新用户分群的用户数量
-- =============================================
UPDATE `user_segments`
SET `user_count` = (SELECT COUNT(*)
                    FROM `user_segment_members`
                    WHERE `segment_id` = `user_segments`.`id`
                      AND `status` = 1);

-- =============================================
-- 活动分析模拟数据
-- =============================================

-- 插入营销活动数据
INSERT INTO `marketing_campaigns` (`id`, `campaign_name`, `campaign_type`, `campaign_description`, `status`, `budget`, `actual_cost`, `target_audience`, `channels`, `start_time`, `end_time`, `created_by`, `create_time`) VALUES
(1, '夏季促销活动', 'promotion', '夏季大促销，全场商品8折优惠', 'completed', 50000.00, 45000.00, '全体用户', '["email", "sms", "push", "social"]', '2025-07-01 00:00:00', '2025-07-31 23:59:59', 1, '2025-06-25 10:00:00'),
(2, '新产品发布会', 'product', '新版本产品发布推广活动', 'active', 80000.00, 35000.00, '潜在客户和现有用户', '["social", "ads", "email"]', '2025-07-15 00:00:00', '2025-08-15 23:59:59', 1, '2025-07-10 14:30:00'),
(3, '品牌形象推广', 'brand', '提升品牌知名度和影响力', 'active', 120000.00, 60000.00, '目标市场用户', '["ads", "social", "content"]', '2025-07-01 00:00:00', '2025-09-30 23:59:59', 1, '2025-06-20 09:15:00'),
(4, '用户回馈活动', 'user', '感谢老用户的支持，专属优惠', 'paused', 30000.00, 15000.00, '老用户', '["email", "push"]', '2025-07-20 00:00:00', '2025-08-20 23:59:59', 1, '2025-07-15 16:45:00'),
(5, '中秋节营销', 'holiday', '中秋节主题营销活动', 'draft', 40000.00, 0.00, '全体用户', '["email", "sms", "social"]', '2025-09-01 00:00:00', '2025-09-30 23:59:59', 1, '2025-07-25 11:20:00'),
(6, '双十一预热', 'promotion', '双十一购物节预热活动', 'draft', 200000.00, 0.00, '购物用户', '["ads", "social", "email", "sms"]', '2025-10-20 00:00:00', '2025-11-11 23:59:59', 1, '2025-07-30 13:10:00');

-- 插入活动效果统计数据
INSERT INTO `campaign_statistics` (`campaign_id`, `stat_date`, `impressions`, `clicks`, `conversions`, `participants`, `new_users`, `revenue`, `cost`) VALUES
-- 夏季促销活动数据
(1, '2025-07-01', 50000, 2500, 125, 800, 50, 12500.00, 1500.00),
(1, '2025-07-02', 52000, 2600, 130, 850, 55, 13000.00, 1550.00),
(1, '2025-07-03', 48000, 2400, 120, 780, 45, 12000.00, 1450.00),
(1, '2025-07-04', 55000, 2750, 138, 900, 60, 13800.00, 1600.00),
(1, '2025-07-05', 60000, 3000, 150, 950, 65, 15000.00, 1700.00),
-- 新产品发布会数据
(2, '2025-07-15', 80000, 4000, 200, 1200, 80, 20000.00, 2000.00),
(2, '2025-07-16', 85000, 4250, 213, 1300, 85, 21300.00, 2100.00),
(2, '2025-07-17', 78000, 3900, 195, 1150, 75, 19500.00, 1950.00),
(2, '2025-07-18', 90000, 4500, 225, 1400, 90, 22500.00, 2200.00),
(2, '2025-07-19', 95000, 4750, 238, 1500, 95, 23800.00, 2300.00),
-- 品牌形象推广数据
(3, '2025-07-01', 120000, 3600, 180, 2000, 100, 18000.00, 3000.00),
(3, '2025-07-02', 125000, 3750, 188, 2100, 105, 18800.00, 3100.00),
(3, '2025-07-03', 118000, 3540, 177, 1950, 98, 17700.00, 2950.00),
(3, '2025-07-04', 130000, 3900, 195, 2200, 110, 19500.00, 3200.00),
(3, '2025-07-05', 135000, 4050, 203, 2300, 115, 20300.00, 3300.00),
-- 用户回馈活动数据
(4, '2025-07-20', 25000, 1250, 63, 500, 25, 6300.00, 800.00),
(4, '2025-07-21', 28000, 1400, 70, 550, 28, 7000.00, 850.00),
(4, '2025-07-22', 22000, 1100, 55, 450, 22, 5500.00, 750.00),
(4, '2025-07-23', 30000, 1500, 75, 600, 30, 7500.00, 900.00);

-- =============================================
-- 粉丝分析模拟数据
-- =============================================

-- 插入粉丝基础信息数据
INSERT IGNORE INTO `fans` (`id`, `fan_id`, `nickname`, `platform`, `platform_user_id`, `gender`, `age_range`, `region`, `city`, `follow_time`, `status`, `source`, `fan_type`, `fan_value`, `activity_level`, `interaction_count`, `last_interaction_time`, `tags`, `created_by`, `create_time`) VALUES
(1, 'FAN001', '张小明', 'wechat', 'wx_001', 1, '26-35', '北京市', '北京', '2025-06-15 10:30:00', 'active', 'search', 'loyal', 'high', 85, 156, '2025-08-04 15:30:00', '["科技爱好者", "高消费"]', 1, '2025-06-15 10:30:00'),
(2, 'FAN002', '李小红', 'weibo', 'wb_002', 2, '18-25', '上海市', '上海', '2025-06-20 14:20:00', 'active', 'recommend', 'potential', 'medium', 45, 89, '2025-08-03 12:15:00', '["时尚", "美妆"]', 1, '2025-06-20 14:20:00'),
(3, 'FAN003', '王大力', 'wechat', 'wx_003', 1, '36-45', '广东省', '广州', '2025-06-25 09:15:00', 'active', 'share', 'loyal', 'high', 72, 134, '2025-08-04 09:45:00', '["商务", "投资"]', 1, '2025-06-25 09:15:00'),
(4, 'FAN004', '陈小美', 'douyin', 'dy_004', 2, '18-25', '浙江省', '杭州', '2025-07-01 16:45:00', 'active', 'ads', 'ordinary', 'medium', 58, 67, '2025-08-02 20:30:00', '["娱乐", "音乐"]', 1, '2025-07-01 16:45:00'),
(5, 'FAN005', '刘小强', 'xiaohongshu', 'xhs_005', 1, '26-35', '四川省', '成都', '2025-07-05 11:20:00', 'active', 'direct', 'vip', 'high', 91, 203, '2025-08-04 18:20:00', '["美食", "旅游", "VIP客户"]', 1, '2025-07-05 11:20:00'),
(6, 'FAN006', '赵小芳', 'official', 'of_006', 2, '26-35', '江苏省', '南京', '2025-07-08 13:10:00', 'active', 'search', 'loyal', 'medium', 63, 98, '2025-08-03 16:45:00', '["教育", "亲子"]', 1, '2025-07-08 13:10:00'),
(7, 'FAN007', '孙小龙', 'wechat', 'wx_007', 1, '46-55', '山东省', '青岛', '2025-07-10 08:30:00', 'inactive', 'recommend', 'ordinary', 'low', 23, 34, '2025-07-25 10:15:00', '["传统文化"]', 1, '2025-07-10 08:30:00'),
(8, 'FAN008', '周小花', 'weibo', 'wb_008', 2, '18-25', '湖北省', '武汉', '2025-07-12 19:25:00', 'active', 'share', 'potential', 'medium', 67, 87, '2025-08-04 11:30:00', '["健身", "运动"]', 1, '2025-07-12 19:25:00'),
(9, 'FAN009', '吴小军', 'douyin', 'dy_009', 1, '36-45', '河南省', '郑州', '2025-07-15 14:50:00', 'active', 'ads', 'loyal', 'high', 78, 145, '2025-08-04 14:20:00', '["汽车", "科技"]', 1, '2025-07-15 14:50:00'),
(10, 'FAN010', '郑小玉', 'xiaohongshu', 'xhs_010', 2, '26-35', '福建省', '厦门', '2025-07-18 10:15:00', 'active', 'direct', 'vip', 'high', 88, 178, '2025-08-04 17:10:00', '["奢侈品", "时尚", "VIP客户"]', 1, '2025-07-18 10:15:00');

-- 插入粉丝互动记录数据
INSERT IGNORE INTO `fan_interactions` (`fan_id`, `interaction_type`, `interaction_date`, `interaction_time`, `content_id`, `content_type`, `interaction_value`, `platform`, `device_type`) VALUES
-- 张小明的互动记录
(1, 'like', '2025-08-04', '2025-08-04 15:30:00', 'post_001', 'post', 1, 'wechat', 'mobile'),
(1, 'comment', '2025-08-04', '2025-08-04 15:32:00', 'post_001', 'post', 3, 'wechat', 'mobile'),
(1, 'share', '2025-08-03', '2025-08-03 10:15:00', 'article_001', 'article', 5, 'wechat', 'mobile'),
-- 李小红的互动记录
(2, 'like', '2025-08-03', '2025-08-03 12:15:00', 'video_001', 'video', 1, 'weibo', 'mobile'),
(2, 'view', '2025-08-03', '2025-08-03 12:10:00', 'video_001', 'video', 1, 'weibo', 'mobile'),
-- 王大力的互动记录
(3, 'like', '2025-08-04', '2025-08-04 09:45:00', 'product_001', 'product', 2, 'wechat', 'desktop'),
(3, 'comment', '2025-08-04', '2025-08-04 09:50:00', 'product_001', 'product', 3, 'wechat', 'desktop'),
-- 陈小美的互动记录
(4, 'like', '2025-08-02', '2025-08-02 20:30:00', 'video_002', 'video', 1, 'douyin', 'mobile'),
(4, 'share', '2025-08-02', '2025-08-02 20:35:00', 'video_002', 'video', 5, 'douyin', 'mobile'),
-- 刘小强的互动记录
(5, 'like', '2025-08-04', '2025-08-04 18:20:00', 'post_002', 'post', 1, 'xiaohongshu', 'mobile'),
(5, 'comment', '2025-08-04', '2025-08-04 18:25:00', 'post_002', 'post', 3, 'xiaohongshu', 'mobile'),
(5, 'message', '2025-08-04', '2025-08-04 18:30:00', 'msg_001', 'message', 10, 'xiaohongshu', 'mobile');

-- 插入粉丝统计数据
INSERT IGNORE INTO `fan_statistics` (`stat_date`, `platform`, `total_fans`, `new_fans`, `unfollowed_fans`, `active_fans`, `high_value_fans`, `medium_value_fans`, `low_value_fans`, `total_interactions`, `avg_activity_level`, `retention_rate`) VALUES
('2025-08-01', 'wechat', 3, 1, 0, 2, 2, 0, 1, 45, 60.00, 95.50),
('2025-08-01', 'weibo', 2, 1, 0, 2, 0, 2, 0, 23, 56.00, 92.30),
('2025-08-01', 'douyin', 2, 1, 0, 2, 1, 1, 0, 34, 62.50, 94.20),
('2025-08-01', 'xiaohongshu', 2, 1, 0, 2, 2, 0, 0, 67, 89.50, 98.10),
('2025-08-01', 'official', 1, 1, 0, 1, 0, 1, 0, 12, 63.00, 96.00),
('2025-08-02', 'wechat', 3, 0, 0, 2, 2, 0, 1, 52, 61.33, 95.80),
('2025-08-02', 'weibo', 2, 0, 0, 2, 0, 2, 0, 28, 57.50, 92.60),
('2025-08-02', 'douyin', 2, 0, 0, 2, 1, 1, 0, 41, 63.00, 94.50),
('2025-08-02', 'xiaohongshu', 2, 0, 0, 2, 2, 0, 0, 73, 90.00, 98.30),
('2025-08-02', 'official', 1, 0, 0, 1, 0, 1, 0, 15, 64.00, 96.20),
('2025-08-03', 'wechat', 3, 0, 0, 3, 2, 0, 1, 58, 62.00, 96.10),
('2025-08-03', 'weibo', 2, 0, 0, 2, 0, 2, 0, 32, 58.00, 93.00),
('2025-08-03', 'douyin', 2, 0, 0, 2, 1, 1, 0, 45, 63.50, 94.80),
('2025-08-03', 'xiaohongshu', 2, 0, 0, 2, 2, 0, 0, 78, 90.50, 98.50),
('2025-08-03', 'official', 1, 0, 0, 1, 0, 1, 0, 18, 65.00, 96.50),
('2025-08-04', 'wechat', 3, 0, 0, 3, 2, 0, 1, 65, 62.67, 96.40),
('2025-08-04', 'weibo', 2, 0, 0, 2, 0, 2, 0, 35, 58.50, 93.20),
('2025-08-04', 'douyin', 2, 0, 0, 2, 1, 1, 0, 48, 64.00, 95.00),
('2025-08-04', 'xiaohongshu', 2, 0, 0, 2, 2, 0, 0, 82, 91.00, 98.70),
('2025-08-04', 'official', 1, 0, 0, 1, 0, 1, 0, 20, 66.00, 96.80);





-- 完成模拟数据插入
SELECT 'MySQL mock data inserted successfully!' as status;
