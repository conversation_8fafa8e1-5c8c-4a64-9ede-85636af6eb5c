# 在运行此脚本之前，请确保已安装所需的依赖库。
# 打开终端或命令行，导航到此文件所在目录，然后运行:
# pip install -r requirements.txt
#
# 如果您仍然看到 "ModuleNotFoundError: No module named 'mysql'" 错误,
# 这通常意味着您安装了错误的库 (例如 'mysql' 或 'mysqlclient')。
#
# 请运行以下命令来安装正确的库:
# pip uninstall mysql mysqlclient
# pip install mysql-connector-python
#

import mysql.connector
from mysql.connector import errorcode
import os

# --- 配置 ---
DB_CONFIG = {
    'user': 'root',
    'password': 'root',
    'host': '127.0.0.1',
    # 'database' 键被移除，因为脚本会动态创建和选择数据库
    'auth_plugin': 'mysql_native_password'
}

# 要按顺序执行的SQL文件
SQL_FILES = [
    'mysql_tables_schema.sql',
    'mysql_mock_data.sql'
]
# --- 配置结束 ---

def split_sql_script(sql_script):
    """
    智能分割SQL脚本，处理字符串中的分号和注释。
    """
    # 移除单行注释
    sql_script = '\n'.join(line for line in sql_script.splitlines() if not line.strip().startswith('--'))

    statements = []
    in_string_char = None
    current_statement = ""

    for char in sql_script:
        current_statement += char
        if in_string_char:
            if char == in_string_char:
                in_string_char = None
        else:
            if char == "'" or char == '"':
                in_string_char = char
            elif char == ';':
                if current_statement.strip():
                    statements.append(current_statement.strip())
                current_statement = ""

    if current_statement.strip():
        # 添加最后一个没有分号的语句
        statements.append(current_statement.strip())

    return [s for s in statements if s] # 过滤掉空语句

def execute_sql_from_file(cursor, filepath):
    """从文件中读取并执行SQL脚本"""
    print(f"--- 正在执行脚本: {os.path.basename(filepath)} ---")
    with open(filepath, 'r', encoding='utf-8') as f:
        sql_script = f.read()

    # 使用智能分割器获取所有SQL语句
    statements = split_sql_script(sql_script)

    try:
        for i, statement in enumerate(statements):
            # 注释已在分割器中处理，直接执行
            cursor.execute(statement)

            # 如果语句返回了结果集，需要读取并清空
            if cursor.with_rows:
                results = cursor.fetchall()
                # 可选：打印 SELECT 语句的结果
                if statement.strip().upper().startswith('SELECT'):
                    for row in results:
                        print(f"查询结果: {row}")

        print(f"--- 脚本 {os.path.basename(filepath)} 执行成功 ---\n")
    except mysql.connector.Error as err:
        print(f"执行脚本 {os.path.basename(filepath)} 时发生错误: {err}")
        raise

def main():
    """主函数，连接数据库并执行所有SQL脚本"""
    cnx = None
    cursor = None
    db_name = 'scrm_next'
    try:
        # 初始连接时不指定数据库
        connect_config = DB_CONFIG.copy()
        connect_config.pop('database', None)

        print(f"正在连接到 MySQL 服务器: {connect_config['host']}...")
        cnx = mysql.connector.connect(**connect_config)
        # 启用自动提交，确保每条语句都立即生效
        cnx.autocommit = True
        cursor = cnx.cursor()
        print("数据库连接成功 (自动提交已启用)。")

        # 为了确保幂等性，先删除并重新创建数据库
        print(f"正在删除旧数据库 (如果存在): {db_name}...")
        cursor.execute(f"DROP DATABASE IF EXISTS `{db_name}`")
        print(f"数据库 {db_name} 已删除。")

        print(f"正在创建新数据库: {db_name}...")
        cursor.execute(f"CREATE DATABASE `{db_name}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print(f"数据库 {db_name} 已创建。")

        # 选择数据库以用于后续操作
        cursor.execute(f"USE `{db_name}`")
        print(f"已选择数据库: {db_name}")

        script_dir = os.path.dirname(os.path.abspath(__file__))

        # 按顺序执行所有SQL文件
        for sql_file in SQL_FILES:
            file_path = os.path.join(script_dir, sql_file)
            if not os.path.exists(file_path):
                print(f"错误: SQL文件未找到 -> {file_path}")
                continue
            execute_sql_from_file(cursor, file_path)

        # 由于启用了自动提交，无需手动提交
        print("所有SQL脚本已成功执行。")

    except mysql.connector.Error as err:
        if err.errno == errorcode.ER_ACCESS_DENIED_ERROR:
            print("访问被拒绝。请检查您的用户名和密码。")
        elif err.errno == errorcode.ER_BAD_DB_ERROR:
            print("数据库不存在。")
        else:
            print(f"发生错误: {err}")
    finally:
        if cnx and cnx.is_connected():
            cursor.close()
            cnx.close()
            print("数据库连接已关闭。")

if __name__ == '__main__':
    main()
