package com.foxit.crm.shared.common.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.springframework.util.StringUtils;

import java.util.Date;

/**
 * JWT工具类
 * 
 * <AUTHOR>
 * @since 2025-08-05
 */
public final class JwtUtil {

    private JwtUtil() {
        throw new UnsupportedOperationException("Utility class");
    }

    /**
     * 生成JWT Token
     * 
     * @param userId   用户ID
     * @param username 用户名
     * @param secret   密钥
     * @param expire   过期时间(秒)
     * @return JWT Token
     */
    public static String generateToken(Long userId, String username, String secret, Long expire) {
        return generateToken(userId, username, null, secret, expire);
    }

    /**
     * 生成JWT Token（包含用户类型）
     * 
     * @param userId   用户ID
     * @param username 用户名
     * @param userType 用户类型：1-管理员，2-普通用户
     * @param secret   密钥
     * @param expire   过期时间(秒)
     * @return JWT Token
     */
    public static String generateToken(Long userId, String username, Integer userType, String secret, Long expire) {
        if (userId == null || !StringUtils.hasText(username) || !StringUtils.hasText(secret) || expire == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        try {
            Algorithm algorithm = Algorithm.HMAC256(secret);
            Date expireDate = new Date(System.currentTimeMillis() + expire * 1000);
            
            var builder = JWT.create()
                    .withClaim("userId", userId)
                    .withClaim("username", username)
                    .withIssuedAt(new Date())
                    .withExpiresAt(expireDate);
            
            if (userType != null) {
                builder.withClaim("userType", userType);
            }
            
            return builder.sign(algorithm);
        } catch (Exception e) {
            throw new RuntimeException("生成JWT Token失败", e);
        }
    }

    /**
     * 验证JWT Token
     * 
     * @param token  JWT Token
     * @param secret 密钥
     * @return 是否有效
     */
    public static boolean verifyToken(String token, String secret) {
        if (!StringUtils.hasText(token) || !StringUtils.hasText(secret)) {
            return false;
        }

        try {
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm).build();
            verifier.verify(token);
            return true;
        } catch (JWTVerificationException e) {
            return false;
        }
    }

    /**
     * 从Token中获取用户ID
     * 
     * @param token JWT Token
     * @return 用户ID
     */
    public static Long getUserId(String token) {
        if (!StringUtils.hasText(token)) {
            return null;
        }

        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim("userId").asLong();
        } catch (JWTDecodeException e) {
            return null;
        }
    }

    /**
     * 从Token中获取用户名
     * 
     * @param token JWT Token
     * @return 用户名
     */
    public static String getUsername(String token) {
        if (!StringUtils.hasText(token)) {
            return null;
        }

        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim("username").asString();
        } catch (JWTDecodeException e) {
            return null;
        }
    }

    /**
     * 从Token中获取用户类型
     * 
     * @param token JWT Token
     * @return 用户类型
     */
    public static Integer getUserType(String token) {
        if (!StringUtils.hasText(token)) {
            return null;
        }

        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim("userType").asInt();
        } catch (JWTDecodeException e) {
            return null;
        }
    }

    /**
     * 检查Token是否过期
     * 
     * @param token JWT Token
     * @return 是否过期
     */
    public static boolean isTokenExpired(String token) {
        if (!StringUtils.hasText(token)) {
            return true;
        }

        try {
            DecodedJWT jwt = JWT.decode(token);
            Date expiration = jwt.getExpiresAt();
            return expiration.before(new Date());
        } catch (JWTDecodeException e) {
            return true;
        }
    }

    /**
     * 获取Token的过期时间
     * 
     * @param token JWT Token
     * @return 过期时间
     */
    public static Date getExpiration(String token) {
        if (!StringUtils.hasText(token)) {
            return null;
        }

        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getExpiresAt();
        } catch (JWTDecodeException e) {
            return null;
        }
    }

    /**
     * 获取Token的签发时间
     * 
     * @param token JWT Token
     * @return 签发时间
     */
    public static Date getIssuedAt(String token) {
        if (!StringUtils.hasText(token)) {
            return null;
        }

        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getIssuedAt();
        } catch (JWTDecodeException e) {
            return null;
        }
    }

    /**
     * 刷新Token
     * 
     * @param token  原Token
     * @param secret 密钥
     * @param expire 新的过期时间(秒)
     * @return 新Token
     */
    public static String refreshToken(String token, String secret, Long expire) {
        if (!StringUtils.hasText(token) || !StringUtils.hasText(secret) || expire == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        try {
            DecodedJWT jwt = JWT.decode(token);
            Long userId = jwt.getClaim("userId").asLong();
            String username = jwt.getClaim("username").asString();
            Integer userType = jwt.getClaim("userType").asInt();
            
            return generateToken(userId, username, userType, secret, expire);
        } catch (JWTDecodeException e) {
            throw new RuntimeException("刷新Token失败", e);
        }
    }
}
