package com.foxit.crm.shared.domain.model;

import com.foxit.crm.shared.domain.event.DomainEvent;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 聚合根基类
 * 提供领域事件管理功能
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Getter
public abstract class AggregateRoot extends BaseEntity {

    /**
     * 领域事件列表
     */
    private final List<DomainEvent> domainEvents = new ArrayList<>();

    /**
     * 添加领域事件
     */
    protected void addDomainEvent(DomainEvent event) {
        this.domainEvents.add(event);
    }

    /**
     * 获取所有领域事件（只读）
     */
    public List<DomainEvent> getDomainEvents() {
        return Collections.unmodifiableList(domainEvents);
    }

    /**
     * 清除所有领域事件
     */
    public void clearDomainEvents() {
        this.domainEvents.clear();
    }

    /**
     * 检查是否有领域事件
     */
    public boolean hasDomainEvents() {
        return !this.domainEvents.isEmpty();
    }

    /**
     * 获取领域事件数量
     */
    public int getDomainEventCount() {
        return this.domainEvents.size();
    }
}
