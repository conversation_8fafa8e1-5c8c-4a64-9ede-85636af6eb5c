package com.foxit.crm.shared.domain.event;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 领域事件基类
 * 所有领域事件都应该继承此类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public abstract class DomainEvent {

    /**
     * 事件ID
     */
    private final String eventId;

    /**
     * 事件发生时间
     */
    private final LocalDateTime occurredOn;

    /**
     * 事件版本
     */
    private final Integer version;

    /**
     * 聚合根ID
     */
    private final Long aggregateId;

    /**
     * 事件类型
     */
    private final String eventType;

    /**
     * 构造函数
     */
    protected DomainEvent(Long aggregateId) {
        this.eventId = UUID.randomUUID().toString();
        this.occurredOn = LocalDateTime.now();
        this.version = 1;
        this.aggregateId = aggregateId;
        this.eventType = this.getClass().getSimpleName();
    }

    /**
     * 获取事件名称
     */
    public abstract String getEventName();

    /**
     * 获取事件描述
     */
    public abstract String getEventDescription();

    /**
     * 获取事件数据
     */
    public abstract Object getEventData();
}
