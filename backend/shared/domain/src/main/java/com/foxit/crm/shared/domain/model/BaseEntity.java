package com.foxit.crm.shared.domain.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 基础实体类
 * 提供所有领域实体的公共属性和行为
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public abstract class BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 更新者ID
     */
    private Long updateBy;

    /**
     * 逻辑删除标志（0：未删除，1：已删除）
     */
    private Integer deleted;

    /**
     * 版本号（用于乐观锁）
     */
    private Integer version;

    /**
     * 备注
     */
    private String remark;

    /**
     * 检查实体是否为新实体
     */
    public boolean isNew() {
        return this.id == null;
    }

    /**
     * 检查实体是否已删除
     */
    public boolean isDeleted() {
        return this.deleted != null && this.deleted == 1;
    }

    /**
     * 标记实体为已删除
     */
    public void markAsDeleted() {
        this.deleted = 1;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 设置创建信息
     */
    public void setCreateInfo(Long createBy) {
        this.createBy = createBy;
        this.createTime = LocalDateTime.now();
        this.updateBy = createBy;
        this.updateTime = this.createTime;
        this.deleted = 0;
        this.version = 1;
    }

    /**
     * 设置更新信息
     */
    public void setUpdateInfo(Long updateBy) {
        this.updateBy = updateBy;
        this.updateTime = LocalDateTime.now();
    }
}
