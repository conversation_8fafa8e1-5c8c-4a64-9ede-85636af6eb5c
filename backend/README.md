# SCRM-Next 后端服务

> 基于 Maven多模块 + DDD 架构的企业级 CRM 数据分析平台后端服务

## 📋 项目概述

SCRM-Next 后端是一个现代化的企业级 CRM 数据分析平台后端服务，采用**Maven多模块DDD架构**，实现了技术与业务关注点的完全分离，为福昕公司 15+ 产品线提供统一的数据处理和业务逻辑支持。

## 🎉 重构完成 (v2.0.0 - 2025年8月5日)

### ✅ 架构重构成果

本项目已完成从单体架构到**Maven多模块DDD架构**的重大重构：

- **✅ 完全清理了旧代码**：删除了原有的单体架构代码
- **✅ 建立了13个模块**：shared(3) + system(4) + analytics(4) + gateway(1) + starter(1)
- **✅ 实现了技术与业务分离**：shared模块处理技术关注点，业务模块专注业务逻辑
- **✅ 编译构建成功**：新架构已通过Maven编译验证
- **✅ 清理了所有临时实现**：所有TODO和临时代码已替换为正式方案
- **✅ 为微服务化做好准备**：模块化设计便于后续拆分

## 🏗️ Maven多模块DDD架构

### 📦 模块架构概览

```
backend/
├── shared/                 # 共享模块（技术关注点）
│   ├── common/            # 通用工具和基础类
│   ├── domain/            # 共享领域对象
│   └── infrastructure/    # 基础设施配置
├── system/                # 系统管理模块（业务关注点）
│   ├── domain/           # 用户、权限领域模型
│   ├── application/      # 应用服务层
│   ├── infrastructure/   # 数据访问实现
│   └── api/             # REST API接口
├── analytics/             # 数据分析模块（业务关注点）
│   └── user/            # 用户分析子模块
│       ├── domain/      # 用户分析领域模型
│       ├── application/ # 分析应用服务
│       ├── infrastructure/ # 数据访问实现
│       └── api/        # 分析API接口
├── gateway/              # API网关模块
└── starter/              # 应用启动模块
```

### 🎯 架构优势

- **模块独立性**：每个模块都有独立的领域、应用、基础设施和API层
- **技术分离**：shared模块统一管理技术关注点，业务模块专注业务逻辑
- **可扩展性**：新增业务模块无需修改现有代码
- **团队协作**：不同团队可以并行开发不同的业务模块
- **微服务就绪**：模块化设计便于后续拆分为微服务

### 📊 模块详情

| 模块 | 子模块 | 职责 | 状态 |
|------|--------|------|------|
| **shared** | common | 通用工具类、常量、枚举 | ✅ |
| | domain | 共享值对象、基础实体 | ✅ |
| | infrastructure | 数据库配置、Redis配置 | ✅ |
| **system** | domain | 用户、角色、权限领域模型 | ✅ |
| | application | 系统管理应用服务 | ✅ |
| | infrastructure | 系统数据访问实现 | ✅ |
| | api | 系统管理REST接口 | ✅ |
| **analytics** | user | 用户分析完整模块 | ✅ |
| **gateway** | - | API网关和认证过滤器 | ✅ |
| **starter** | - | 应用启动和配置聚合 | ✅ |

## � 技术栈

### 核心框架

- **Spring Boot 3.4.6** - 企业级 Java 应用框架
- **JDK 21** - Java 开发工具包
- **Maven 3.9+** - 项目构建和依赖管理

### 数据库

- **MySQL 8.0.33** - 关系型数据库，存储业务数据
- **Redis 7.x** - 内存数据库，用于缓存和会话管理
- **ClickHouse 8.x** - 列式数据库，用于行为分析和大数据查询

### 数据访问

- **MyBatis Plus 3.5.12** - MyBatis 增强工具
- **HikariCP** - 高性能数据库连接池

### 安全认证

- **Spring Security 6.x** - 安全框架
- **JWT (Auth0)** - JSON Web Token 认证
- **BCrypt** - 密码加密

### 开发工具

- **Lombok** - Java 代码简化工具
- **Smart-doc 3.x** - API 文档生成工具

### 配置加密

- **Jasypt** - 配置文件加密工具

### 监控工具

- **Spring Boot Actuator** - 应用监控和管理
- **Micrometer** - 应用指标收集

## � 快速开始

### 环境要求

- **JDK 21+**
- **Maven 3.9+**
- **MySQL 8.0+**
- **Redis 7.x+**

### 构建和运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd scrm-next/backend
   ```

2. **配置数据库**
   ```bash
   # 创建数据库
   mysql -u root -p
   CREATE DATABASE scrm_next CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **编译项目**
   ```bash
   # Maven多模块编译
   mvn clean compile

   # 打包项目
   mvn clean package -DskipTests
   ```

4. **启动服务**
   ```bash
   # 方式一：使用Maven启动
   cd starter
   mvn spring-boot:run
   
   # 方式二：在根目录maven中启动
   mvn spring-boot:run -pl starter

   # 方式三：直接运行jar包
   java -jar starter/target/scrm-next-starter-1.0.0.jar
   ```

5. **验证服务**
   ```bash
   # 健康检查
   curl http://localhost:9090/actuator/health

   # API文档
   http://localhost:9090/doc.html
   ```

## 🏛️ DDD 架构详解

### 架构原则

本项目采用**Maven多模块DDD架构**，实现了技术与业务关注点的完全分离：

#### 分层职责

- **API 层（api）**：处理 HTTP 请求，数据传输对象（DTO）
- **应用层（application）**：编排业务流程，事务管理
- **领域层（domain）**：核心业务逻辑，领域规则
- **基础设施层（infrastructure）**：技术实现，外部依赖

#### 模块化设计

- **技术模块（shared）**：统一管理技术关注点，如配置、工具类、基础设施
- **业务模块（system、analytics）**：专注业务逻辑，每个模块内部完整的DDD分层
- **网关模块（gateway）**：统一的API网关和认证过滤
- **启动模块（starter）**：应用启动和配置聚合

### 📁 详细模块结构

#### shared模块 - 技术关注点
```
shared/
├── common/                    # 通用工具模块
│   ├── annotation/           # 自定义注解
│   ├── constant/            # 常量定义
│   ├── enums/              # 枚举类型
│   ├── exception/          # 异常处理
│   ├── result/             # 统一响应格式
│   └── utils/              # 工具类
├── domain/                   # 共享领域模块
│   ├── entity/             # 基础实体类
│   └── valueobject/        # 共享值对象
└── infrastructure/           # 基础设施模块
    ├── config/             # 配置类
    ├── security/           # 安全配置
    └── persistence/        # 数据持久化配置
```

#### system模块 - 系统管理业务
```
system/
├── domain/                   # 领域层
│   ├── model/               # 领域模型
│   │   ├── aggregate/       # 聚合根
│   │   ├── entity/          # 实体
│   │   └── valueobject/     # 值对象
│   ├── repository/          # 仓储接口
│   └── service/            # 领域服务
├── application/             # 应用层
│   ├── service/            # 应用服务
│   ├── dto/               # 数据传输对象
│   └── converter/         # 对象转换器
├── infrastructure/          # 基础设施层
│   ├── repository/         # 仓储实现
│   ├── mapper/            # MyBatis映射器
│   └── config/           # 模块配置
└── api/                    # API层
    ├── controller/        # REST控制器
    └── dto/              # API数据传输对象
```

#### analytics模块 - 数据分析业务
```
analytics/
└── user/                   # 用户分析子模块
    ├── domain/            # 用户分析领域层
    │   ├── model/         # 分析模型
    │   ├── repository/    # 分析仓储接口
    │   └── service/      # 分析领域服务
    ├── application/       # 用户分析应用层
    │   ├── service/      # 分析应用服务
    │   ├── dto/         # 分析DTO
    │   └── converter/   # 分析转换器
    ├── infrastructure/    # 用户分析基础设施层
    │   └── repository/   # 分析仓储实现
    └── api/              # 用户分析API层
        └── controller/   # 分析控制器
## 📚 开发指南

### 新增业务模块

1. **创建模块目录结构**
   ```bash
   mkdir -p new-module/{domain,application,infrastructure,api}/src/main/java
   ```

2. **创建模块POM文件**
   ```xml
   <parent>
       <groupId>com.foxit.crm</groupId>
       <artifactId>scrm-next-parent</artifactId>
       <version>1.0.0</version>
   </parent>
   <artifactId>new-module-domain</artifactId>
   ```

3. **实现DDD分层**
   - **Domain层**：定义聚合根、实体、值对象、仓储接口
   - **Application层**：实现应用服务、DTO转换
   - **Infrastructure层**：实现仓储、数据访问
   - **API层**：实现REST控制器

### 代码规范

- **包命名**：`com.foxit.crm.{module}.{layer}`
- **类命名**：使用领域术语，避免技术术语
- **方法命名**：动词+名词，表达业务意图
- **注释规范**：使用JavaDoc，说明业务逻辑

### 测试策略

- **单元测试**：每个模块的domain和application层
- **集成测试**：infrastructure层的数据访问
- **API测试**：REST接口的功能验证

## 📈 项目统计

### 代码规模
- **后端架构**：13个Maven模块，完整的DDD四层架构
- **模块化设计**：shared(3) + system(4) + analytics(4) + gateway(1) + starter(1)
- **数据库**：MySQL + Redis + ClickHouse 三重数据架构
- **文档**：自动生成的 API 文档，完善的项目文档

### 技术特色
- **架构先进**：Maven多模块DDD架构，技术与业务完全分离
- **微服务就绪**：模块化设计便于后续拆分为微服务
- **技术栈新**：Java 21 + Spring Boot 3.4.6 最新技术栈
- **可扩展性强**：新增业务模块无需修改现有代码
- **质量保障**：完善的代码规范、测试框架、安全机制

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

---

**SCRM-Next Backend** - 企业级CRM数据分析平台后端服务 🚀
