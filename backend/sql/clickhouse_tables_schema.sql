-- =============================================
-- SCRM-Next ClickHouse 数据库表结构文件
-- 创建时间：2025-07-02
-- 说明：包含所有ClickHouse分析表结构（不含数据）
-- 版本：v1.0
-- 主机：ck_dev，数据库：scrm_next
-- =============================================

-- 创建数据库
CREATE
DATABASE IF NOT EXISTS scrm_next;

USE
scrm_next;

-- 清理残留的物化视图，防止因旧视图导致错误
DROP VIEW IF EXISTS mv_realtime_online_users;

-- =============================================
-- 1. 用户行为事件表
-- 用途：存储用户在各产品线中的行为事件数据
-- 数据来源：前端埋点、后端日志
-- =============================================
CREATE TABLE IF NOT EXISTS user_behavior_events
(
    event_id         String COMMENT '事件唯一标识',
    user_id          UInt64 COMMENT '用户ID',
    product_line_id  UInt64 COMMENT '产品线ID',
    event_type       String COMMENT '事件类型：click, view, action等',
    event_name       String COMMENT '事件名称',
    event_category   String COMMENT '事件分类',
    event_properties String COMMENT '事件属性JSON格式',
    session_id       String COMMENT '会话ID',
    device_type      String COMMENT '设备类型：desktop, mobile, tablet',
    platform         String COMMENT '平台：windows, macos, android, ios',
    browser          String COMMENT '浏览器类型',
    ip_address       String COMMENT 'IP地址',
    user_agent       String COMMENT '用户代理字符串',
    referrer         String COMMENT '来源页面',
    page_url         String COMMENT '当前页面URL',
    source_channel   String COMMENT '用户来源渠道：direct, search, social, email, ads等',
    event_time       DateTime COMMENT '事件发生时间',
    create_time      DateTime DEFAULT now() COMMENT '记录创建时间'
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(event_time)
ORDER BY (product_line_id, event_time, user_id)
TTL event_time + INTERVAL 2 YEAR
SETTINGS index_granularity = 8192;

-- =============================================
-- 2. 功能使用统计表
-- 用途：存储各功能的使用统计数据
-- 数据来源：用户行为事件聚合
-- =============================================
CREATE TABLE IF NOT EXISTS feature_usage_stats
(
    stat_id          String COMMENT '统计记录ID',
    product_line_id  UInt64 COMMENT '产品线ID',
    feature_id       String COMMENT '功能ID',
    feature_name     String COMMENT '功能名称',
    feature_category String COMMENT '功能分类',
    feature_type     String COMMENT '功能类型：core, premium, addon',
    usage_count      UInt64 COMMENT '使用次数',
    unique_users     UInt64 COMMENT '独立用户数',
    total_duration   UInt64 COMMENT '总使用时长(毫秒)',
    avg_duration     Float64 COMMENT '平均使用时长(毫秒)',
    stat_date        Date COMMENT '统计日期',
    stat_hour        UInt8 COMMENT '统计小时(0-23)',
    create_time      DateTime DEFAULT now() COMMENT '记录创建时间'
) ENGINE = SummingMergeTree()
PARTITION BY toYYYYMM(stat_date)
ORDER BY (product_line_id, stat_date, feature_id)
TTL stat_date + INTERVAL 1 YEAR
SETTINGS index_granularity = 8192;

-- =============================================
-- 3. 用户路径分析表
-- 用途：存储用户在产品中的行为路径数据
-- 数据来源：用户行为事件序列分析
-- =============================================
CREATE TABLE IF NOT EXISTS user_path_analysis
(
    path_id         String COMMENT '路径唯一标识',
    user_id         UInt64 COMMENT '用户ID',
    product_line_id UInt64 COMMENT '产品线ID',
    session_id      String COMMENT '会话ID',
    path_sequence   Array(String) COMMENT '路径序列',
    path_timestamps Array(DateTime) COMMENT '路径时间戳序列',
    path_duration   UInt64 COMMENT '路径总时长(毫秒)',
    path_length     UInt32 COMMENT '路径长度',
    conversion_goal String COMMENT '转化目标',
    is_converted    UInt8 COMMENT '是否转化：0-否，1-是',
    conversion_step UInt32 COMMENT '转化步骤',
    bounce_step     UInt32 COMMENT '跳出步骤',
    path_date       Date COMMENT '路径日期',
    create_time     DateTime DEFAULT now() COMMENT '记录创建时间'
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(path_date)
ORDER BY (product_line_id, path_date, user_id)
TTL path_date + INTERVAL 1 YEAR
SETTINGS index_granularity = 8192;

-- =============================================
-- 4. 漏斗转化数据表
-- 用途：存储漏斗分析的转化数据
-- 数据来源：用户行为路径分析
-- =============================================
CREATE TABLE IF NOT EXISTS funnel_conversion_data
(
    funnel_id       String COMMENT '漏斗ID',
    user_id         UInt64 COMMENT '用户ID',
    product_line_id UInt64 COMMENT '产品线ID',
    session_id      String COMMENT '会话ID',
    funnel_name     String COMMENT '漏斗名称',
    funnel_category String COMMENT '漏斗分类',
    step_sequence   UInt8 COMMENT '步骤序号',
    step_name       String COMMENT '步骤名称',
    step_timestamp  DateTime COMMENT '步骤时间戳',
    is_completed    UInt8 COMMENT '是否完成：0-否，1-是',
    conversion_time UInt64 COMMENT '转化耗时(毫秒)',
    drop_reason     String COMMENT '流失原因',
    funnel_date     Date COMMENT '漏斗日期',
    create_time     DateTime DEFAULT now() COMMENT '记录创建时间'
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(funnel_date)
ORDER BY (product_line_id, funnel_date, funnel_id, user_id, step_sequence)
TTL funnel_date + INTERVAL 1 YEAR
SETTINGS index_granularity = 8192;

-- =============================================
-- 5. 实时统计汇总表
-- 用途：存储实时统计数据的汇总信息
-- 数据来源：实时计算引擎
-- =============================================
CREATE TABLE IF NOT EXISTS realtime_stats_summary
(
    stat_type       String COMMENT '统计类型：online_users, active_sessions, events_count等',
    product_line_id UInt64 COMMENT '产品线ID',
    stat_value      UInt64 COMMENT '统计值',
    stat_metadata   String COMMENT '统计元数据JSON',
    stat_timestamp  DateTime COMMENT '统计时间戳',
    stat_minute     DateTime COMMENT '分钟级别聚合时间',
    create_time     DateTime DEFAULT now() COMMENT '记录创建时间'
) ENGINE = ReplacingMergeTree(create_time)
PARTITION BY toYYYYMMDD(stat_timestamp)
ORDER BY (stat_type, product_line_id, stat_minute)
TTL stat_timestamp + INTERVAL 30 DAY
SETTINGS index_granularity = 8192;

-- =============================================
-- 6. 用户活跃度统计表
-- 用途：存储用户活跃度的统计数据
-- 数据来源：用户行为事件聚合
-- =============================================
CREATE TABLE IF NOT EXISTS user_activity_stats
(
    user_id             UInt64 COMMENT '用户ID',
    product_line_id     UInt64 COMMENT '产品线ID',
    activity_date       Date COMMENT '活跃日期',
    session_count       UInt32 COMMENT '会话数量',
    event_count         UInt64 COMMENT '事件数量',
    active_duration     UInt64 COMMENT '活跃时长(毫秒)',
    page_views          UInt64 COMMENT '页面浏览数',
    feature_usage_count UInt64 COMMENT '功能使用次数',
    is_new_user         UInt8 COMMENT '是否新用户：0-否，1-是',
    user_type           String COMMENT '用户类型：free, premium, enterprise',
    create_time         DateTime DEFAULT now() COMMENT '记录创建时间'
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(activity_date)
ORDER BY (product_line_id, activity_date, user_id)
SETTINGS index_granularity = 8192;

-- =============================================
-- 7. 产品使用统计表
-- 用途：存储产品级别的使用统计数据
-- 数据来源：用户行为数据聚合
-- =============================================
CREATE TABLE IF NOT EXISTS product_usage_stats
(
    product_line_id      UInt64 COMMENT '产品线ID',
    stat_date            Date COMMENT '统计日期',
    stat_hour            UInt8 COMMENT '统计小时',
    total_users          UInt64 COMMENT '总用户数',
    active_users         UInt64 COMMENT '活跃用户数',
    new_users            UInt64 COMMENT '新增用户数',
    sessions             UInt64 COMMENT '会话数',
    page_views           UInt64 COMMENT '页面浏览数',
    events               UInt64 COMMENT '事件数',
    avg_session_duration Float64 COMMENT '平均会话时长(毫秒)',
    bounce_rate          Float64 COMMENT '跳出率',
    create_time          DateTime DEFAULT now() COMMENT '记录创建时间'
) ENGINE = SummingMergeTree()
PARTITION BY toYYYYMM(stat_date)
ORDER BY (product_line_id, stat_date, stat_hour)
TTL stat_date + INTERVAL 1 YEAR
SETTINGS index_granularity = 8192;

-- =============================================
-- 8. 用户登录行为表
-- 用途：存储用户登录和会话行为数据
-- 数据来源：用户登录日志
-- =============================================
CREATE TABLE IF NOT EXISTS user_login_behavior
(
    user_id       UInt64 COMMENT '用户ID',
    product_line_id UInt32 COMMENT '产品线ID',
    login_time    DateTime COMMENT '登录时间',
    logout_time   Nullable(DateTime) COMMENT '登出时间',
    session_duration UInt64 COMMENT '会话时长（毫秒）',
    device_type   String COMMENT '设备类型：desktop, mobile, tablet',
    os_type       String COMMENT '操作系统：windows, macos, ios, android, linux',
    browser       String COMMENT '浏览器：chrome, safari, firefox, edge',
    ip_address    String COMMENT 'IP地址',
    location      String COMMENT '登录城市',
    country_code  String COMMENT '国家代码',
    login_status  UInt8 COMMENT '登录状态：1=成功, 0=失败',
    created_at    DateTime DEFAULT now() COMMENT '记录创建时间'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(login_time)
ORDER BY (user_id, login_time)
TTL toDate(login_time) + INTERVAL 2 YEAR
SETTINGS index_granularity = 8192;

-- =============================================
-- 索引优化 - 已在表创建时包含，无需额外添加
-- =============================================

-- =============================================
-- 物化视图 - 实时统计
-- =============================================

-- 以下物化视图及目标表与 user_behavior_events 字段不符，建议移除
-- CREATE TABLE IF NOT EXISTS scrm_next.realtime_online_users_mv_target
-- (
--     user_id UInt64,
--     online_count UInt32,
--     region String,
--     event_time DateTime
-- )
-- ENGINE = MergeTree()
-- ORDER BY (user_id, event_time);

-- CREATE MATERIALIZED VIEW IF NOT EXISTS mv_realtime_online_users
-- TO scrm_next.realtime_online_users_mv_target
-- AS
-- SELECT user_id,
--        online_count,
--        region,
--        event_time
-- FROM user_behavior_events
-- WHERE event_time >= now() - INTERVAL 1 DAY
-- GROUP BY user_id, online_count, region, event_time;

-- =============================================
-- 数据保留策略
-- =============================================

-- 注意：TTL已在表创建时设置，此处仅作为文档说明
-- 用户行为事件保留2年：TTL event_time + INTERVAL 2 YEAR
-- 功能使用统计保留1年：TTL stat_date + INTERVAL 1 YEAR  
-- 用户路径分析保留1年：TTL path_date + INTERVAL 1 YEAR
-- 漏斗转化数据保留1年：TTL funnel_date + INTERVAL 1 YEAR
-- 实时统计保留30天：TTL stat_timestamp + INTERVAL 30 DAY

-- 完成初始化
SELECT 'ClickHouse tables schema created successfully!' as status;
