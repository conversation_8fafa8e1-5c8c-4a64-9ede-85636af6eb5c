import os
from clickhouse_driver import Client
from clickhouse_driver.errors import Error

# 连接 ClickHouse 服务器
def connect_to_clickhouse(host, user, password, database='default'):
    try:
        # 创建 ClickHouse 客户端连接
        client = Client(host=host, user=user, password=password, database=database)
        print(f"Connected to ClickHouse database '{database}' successfully.")
        return client
    except Error as e:
        print(f"Error connecting to ClickHouse database: {e}")
        return None

# 执行 SQL 文件中的语句
def execute_sql_file(client, sql_file_path, target_database):
    try:
        with open(sql_file_path, 'r', encoding='utf-8') as sql_file:
            sql_script = sql_file.read()

        # 新分割逻辑：只以分号结尾作为完整SQL，避免拆分多行INSERT
        statements = []
        buffer = []
        for line in sql_script.splitlines():
            striped = line.strip()
            if not striped or striped.startswith('--'):
                continue
            buffer.append(line)
            if striped.endswith(';'):
                statement = '\n'.join(buffer)
                statements.append(statement)
                buffer = []
        # 处理文件末尾无分号的情况
        if buffer:
            statements.append('\n'.join(buffer))

        for statement in statements:
            statement = statement.strip().rstrip(';')
            if not statement:
                continue
            try:
                client.execute(f'USE {target_database}')
                client.execute(statement)
                print(f"SQL statement executed successfully: {statement[:50]}...")
            except Error as e:
                # 防止 ClickHouse 返回非utf-8内容导致脚本崩溃
                err_msg = str(e)
                try:
                    print(f"Error executing SQL statement: {statement[:100]}...")
                    print(f"Error: {err_msg}")
                except UnicodeDecodeError:
                    print(f"Error executing SQL statement: {statement[:100]}...")
                    print("Error: (UnicodeDecodeError in error message)")
        print(f"All SQL statements in '{sql_file_path}' executed successfully.")
    except FileNotFoundError:
        print(f"File not found: {sql_file_path}")
    except Error as e:
        try:
            print(f"Error reading or executing SQL file: {sql_file_path}")
            print(f"Error: {str(e)}")
        except UnicodeDecodeError:
            print(f"Error reading or executing SQL file: {sql_file_path}")
            print("Error: (UnicodeDecodeError in error message)")

def main():
    # ClickHouse 服务器配置
    host = '127.0.0.1'
    user = 'admin'
    password = 'admin'
    database = 'scrm_next'  # 替换为实际的数据库名

    # SQL 文件路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    schema_file_path = os.path.join(script_dir, 'clickhouse_tables_schema.sql')  # 建表脚本路径
    data_file_path = os.path.join(script_dir, 'clickhouse_mock_data.sql')      # 插入数据脚本路径

    # 连接到 ClickHouse
    client = connect_to_clickhouse(host, user, password)
    if client:
        # 执行建表语句文件
        execute_sql_file(client, schema_file_path, database)

        # 执行数据插入语句文件
        execute_sql_file(client, data_file_path, database)

        # 关闭连接
        print("Closing ClickHouse connection.")
        del client

if __name__ == "__main__":
    main()