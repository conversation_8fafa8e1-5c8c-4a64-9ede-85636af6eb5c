-- =============================================
-- SCRM-Next ClickHouse 模拟数据文件
-- 创建时间：2025-07-02
-- 说明：包含所有ClickHouse分析表的测试数据
-- 版本：v1.0
-- 主机：ck_dev，数据库：scrm_next
-- 注意：执行前请确保表结构已创建
-- =============================================

USE scrm_next;

-- =============================================
-- 1. 实时统计汇总表数据
-- =============================================
INSERT INTO realtime_stats_summary VALUES
-- 在线用户统计
('online_users', 1, 1250, '{"region": "beijing"}', now() - INTERVAL 1 MINUTE, toStartOfMinute(now() - INTERVAL 1 MINUTE), now()),
('online_users', 2, 890, '{"region": "shanghai"}', now() - INTERVAL 1 MINUTE, toStartOfMinute(now() - INTERVAL 1 MINUTE), now()),
('online_users', 3, 650, '{"region": "guangzhou"}', now() - INTERVAL 1 MINUTE, toStartOfMinute(now() - INTERVAL 1 MINUTE), now()),

-- 活跃会话统计
('active_sessions', 1, 2100, '{"concurrent": true}', now() - INTERVAL 1 MINUTE, toStartOfMinute(now() - INTERVAL 1 MINUTE), now()),
('active_sessions', 2, 1560, '{"concurrent": true}', now() - INTERVAL 1 MINUTE, toStartOfMinute(now() - INTERVAL 1 MINUTE), now()),
('active_sessions', 3, 1120, '{"concurrent": true}', now() - INTERVAL 1 MINUTE, toStartOfMinute(now() - INTERVAL 1 MINUTE), now()),

-- 今日下载统计
('downloads_today', 1, 15680, '{"type": "installer"}', now() - INTERVAL 1 MINUTE, toStartOfMinute(now() - INTERVAL 1 MINUTE), now()),
('downloads_today', 2, 12340, '{"type": "installer"}', now() - INTERVAL 1 MINUTE, toStartOfMinute(now() - INTERVAL 1 MINUTE), now()),
('downloads_today', 3, 8950, '{"type": "installer"}', now() - INTERVAL 1 MINUTE, toStartOfMinute(now() - INTERVAL 1 MINUTE), now()),

-- 系统负载统计
('system_load', 0, 75, '{"cpu": 75, "memory": 68, "disk": 45}', now() - INTERVAL 1 MINUTE, toStartOfMinute(now() - INTERVAL 1 MINUTE), now()),

-- 峰值在线用户
('peak_online', 1, 1680, '{"time": "14:30"}', now() - INTERVAL 1 MINUTE, toStartOfMinute(now() - INTERVAL 1 MINUTE), now()),
('peak_online', 2, 1250, '{"time": "15:15"}', now() - INTERVAL 1 MINUTE, toStartOfMinute(now() - INTERVAL 1 MINUTE), now()),
('peak_online', 3, 890, '{"time": "16:00"}', now() - INTERVAL 1 MINUTE, toStartOfMinute(now() - INTERVAL 1 MINUTE), now());

-- =============================================
-- 2. 功能使用统计数据（最近30天）
-- =============================================
INSERT INTO feature_usage_stats VALUES
-- PDF阅读器功能使用统计（最近7天）
('stat_001', 1, 'pdf_read', 'PDF阅读', 'core', 'core', 15680, 8950, 45600000, 2908.9, '2025-07-02', 14, now()),
('stat_002', 1, 'pdf_zoom', 'PDF缩放', 'core', 'core', 12340, 7890, 18900000, 1531.2, '2025-07-02', 14, now()),
('stat_003', 1, 'pdf_search', 'PDF搜索', 'core', 'core', 8950, 5670, 12600000, 1407.9, '2025-07-02', 14, now()),
('stat_004', 1, 'pdf_bookmark', 'PDF书签', 'advanced', 'premium', 5670, 3450, 8900000, 1570.3, '2025-07-02', 14, now()),
('stat_005', 1, 'pdf_annotation', 'PDF注释', 'advanced', 'premium', 3450, 2890, 6700000, 1942.2, '2025-07-02', 14, now()),
('stat_006', 1, 'pdf_print', 'PDF打印', 'core', 'core', 9800, 6200, 15400000, 1571.0, '2025-07-02', 14, now()),
('stat_007', 1, 'pdf_export', 'PDF导出', 'advanced', 'premium', 4200, 2800, 8900000, 2119.0, '2025-07-02', 14, now()),
('stat_008', 1, 'pdf_share', 'PDF分享', 'core', 'core', 6700, 4100, 11200000, 1671.2, '2025-07-02', 14, now()),

-- PDF阅读器功能使用统计（昨天）
('stat_011', 1, 'pdf_read', 'PDF阅读', 'core', 'core', 14200, 8100, 42300000, 2980.2, '2025-07-01', 14, now()),
('stat_012', 1, 'pdf_zoom', 'PDF缩放', 'core', 'core', 11800, 7200, 17800000, 1508.5, '2025-07-01', 14, now()),
('stat_013', 1, 'pdf_search', 'PDF搜索', 'core', 'core', 8200, 5200, 11900000, 1451.2, '2025-07-01', 14, now()),
('stat_014', 1, 'pdf_bookmark', 'PDF书签', 'advanced', 'premium', 5200, 3100, 8200000, 1576.9, '2025-07-01', 14, now()),
('stat_015', 1, 'pdf_annotation', 'PDF注释', 'advanced', 'premium', 3100, 2600, 6200000, 2000.0, '2025-07-01', 14, now()),

-- PDF编辑器功能使用统计（今天）
('stat_021', 2, 'pdf_edit_text', 'PDF文本编辑', 'core', 'core', 12340, 6780, 38900000, 3152.4, '2025-07-02', 14, now()),
('stat_022', 2, 'pdf_edit_image', 'PDF图片编辑', 'core', 'core', 8950, 4560, 25600000, 2861.8, '2025-07-02', 14, now()),
('stat_023', 2, 'pdf_form_fill', 'PDF表单填写', 'advanced', 'premium', 6780, 3890, 19800000, 2920.1, '2025-07-02', 14, now()),
('stat_024', 2, 'pdf_signature', 'PDF签名', 'advanced', 'premium', 4560, 2340, 15600000, 3421.1, '2025-07-02', 14, now()),
('stat_025', 2, 'pdf_comment', 'PDF评论', 'advanced', 'premium', 3200, 1800, 9800000, 3062.5, '2025-07-02', 14, now()),
('stat_026', 2, 'pdf_redact', 'PDF编辑', 'advanced', 'premium', 2100, 1200, 7200000, 3428.6, '2025-07-02', 14, now()),

-- PDF编辑器功能使用统计（昨天）
('stat_031', 2, 'pdf_edit_text', 'PDF文本编辑', 'core', 'core', 11800, 6200, 36200000, 3067.7, '2025-07-01', 14, now()),
('stat_032', 2, 'pdf_edit_image', 'PDF图片编辑', 'core', 'core', 8200, 4100, 23800000, 2902.4, '2025-07-01', 14, now()),
('stat_033', 2, 'pdf_form_fill', 'PDF表单填写', 'advanced', 'premium', 6200, 3500, 18200000, 2935.5, '2025-07-01', 14, now()),

-- PDF创建器功能使用统计（今天）
('stat_041', 3, 'pdf_create', 'PDF创建', 'core', 'core', 8950, 4560, 28900000, 3229.5, '2025-07-02', 14, now()),
('stat_042', 3, 'pdf_convert', 'PDF转换', 'core', 'core', 6780, 3450, 19800000, 2920.3, '2025-07-02', 14, now()),
('stat_043', 3, 'pdf_merge', 'PDF合并', 'advanced', 'premium', 4560, 2890, 12600000, 2763.2, '2025-07-02', 14, now()),
('stat_044', 3, 'pdf_split', 'PDF拆分', 'advanced', 'premium', 3450, 1890, 8900000, 2580.9, '2025-07-02', 14, now()),
('stat_045', 3, 'pdf_compress', 'PDF压缩', 'core', 'core', 5200, 2800, 14600000, 2807.7, '2025-07-02', 14, now()),
('stat_046', 3, 'pdf_watermark', 'PDF水印', 'advanced', 'premium', 2800, 1500, 8200000, 2928.6, '2025-07-02', 14, now()),

-- Foxit Cloud功能使用统计（今天）
('stat_051', 4, 'cloud_sync', '云端同步', 'core', 'core', 7800, 4200, 22400000, 2871.8, '2025-07-02', 14, now()),
('stat_052', 4, 'cloud_share', '云端分享', 'core', 'core', 5600, 3100, 16800000, 3000.0, '2025-07-02', 14, now()),
('stat_053', 4, 'cloud_collaborate', '云端协作', 'advanced', 'premium', 3800, 2200, 12600000, 3315.8, '2025-07-02', 14, now()),
('stat_054', 4, 'cloud_backup', '云端备份', 'advanced', 'premium', 2900, 1600, 9200000, 3172.4, '2025-07-02', 14, now()),

-- PhantomPDF功能使用统计（今天）
('stat_061', 5, 'phantom_security', '安全功能', 'enterprise', 'enterprise', 4200, 2800, 18900000, 4500.0, '2025-07-02', 14, now()),
('stat_062', 5, 'phantom_batch', '批量处理', 'enterprise', 'enterprise', 3100, 2100, 14200000, 4580.6, '2025-07-02', 14, now()),
('stat_063', 5, 'phantom_ocr', 'OCR识别', 'enterprise', 'enterprise', 2600, 1800, 11800000, 4538.5, '2025-07-02', 14, now()),

-- PDF365功能使用统计（今天）
('stat_071', 6, 'online_edit', '在线编辑', 'core', 'core', 9200, 5800, 31200000, 3391.3, '2025-07-02', 14, now()),
('stat_072', 6, 'online_convert', '在线转换', 'core', 'core', 12800, 7200, 28900000, 2257.8, '2025-07-02', 14, now()),
('stat_073', 6, 'online_compress', '在线压缩', 'core', 'core', 8900, 5100, 18600000, 2089.9, '2025-07-02', 14, now()),

-- 智慧打印功能使用统计（今天）
('stat_081', 7, 'smart_print', '智能打印', 'core', 'core', 6700, 3900, 19800000, 2955.2, '2025-07-02', 14, now()),
('stat_082', 7, 'print_queue', '打印队列', 'core', 'core', 4800, 2800, 14200000, 2958.3, '2025-07-02', 14, now()),
('stat_083', 7, 'print_monitor', '打印监控', 'advanced', 'premium', 3200, 1900, 9800000, 3062.5, '2025-07-02', 14, now());

-- =============================================
-- 3. 用户行为事件数据（最近24小时）
-- =============================================
INSERT INTO user_behavior_events VALUES
-- 用户1的行为事件（PDF阅读器）
('evt_001', 1, 1, 'click', 'open_file', 'file_operation', '{"file_type": "pdf", "file_size": 2048}', 'sess_001', 'desktop', 'windows', 'Chrome', '192.168.1.100', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '', '/dashboard', now() - INTERVAL 2 HOUR, now()),
('evt_002', 1, 1, 'view', 'page_view', 'navigation', '{"page": "dashboard", "duration": 15000}', 'sess_001', 'desktop', 'windows', 'Chrome', '192.168.1.100', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '', '/dashboard', now() - INTERVAL 2 HOUR + INTERVAL 30 SECOND, now()),
('evt_003', 1, 1, 'action', 'zoom_in', 'pdf_operation', '{"zoom_level": 150, "page_number": 1}', 'sess_001', 'desktop', 'windows', 'Chrome', '192.168.1.100', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '', '/reader', now() - INTERVAL 1 HOUR + INTERVAL 45 MINUTE, now()),
('evt_004', 1, 1, 'action', 'search_text', 'pdf_operation', '{"search_term": "contract", "results_count": 5}', 'sess_001', 'desktop', 'windows', 'Chrome', '192.168.1.100', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '', '/reader', now() - INTERVAL 1 HOUR + INTERVAL 30 MINUTE, now()),
('evt_005', 1, 1, 'action', 'add_bookmark', 'pdf_operation', '{"page_number": 5, "title": "Important Section"}', 'sess_001', 'desktop', 'windows', 'Chrome', '192.168.1.100', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '', '/reader', now() - INTERVAL 1 HOUR + INTERVAL 15 MINUTE, now()),
('evt_006', 1, 1, 'action', 'print_document', 'file_operation', '{"pages": "1-10", "copies": 1, "printer": "HP LaserJet"}', 'sess_001', 'desktop', 'windows', 'Chrome', '192.168.1.100', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '', '/reader', now() - INTERVAL 1 HOUR, now()),

-- 用户2的行为事件（PDF编辑器）
('evt_011', 2, 2, 'click', 'edit_text', 'edit_operation', '{"text_length": 256, "position": {"x": 100, "y": 200}}', 'sess_004', 'desktop', 'macos', 'Safari', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '', '/editor', now() - INTERVAL 3 HOUR, now()),
('evt_012', 2, 2, 'action', 'insert_image', 'edit_operation', '{"image_type": "png", "image_size": 1024, "position": {"x": 200, "y": 300}}', 'sess_004', 'desktop', 'macos', 'Safari', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '', '/editor', now() - INTERVAL 2 HOUR + INTERVAL 45 MINUTE, now()),
('evt_013', 2, 2, 'action', 'add_signature', 'edit_operation', '{"signature_type": "digital", "position": {"x": 400, "y": 500}}', 'sess_004', 'desktop', 'macos', 'Safari', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '', '/editor', now() - INTERVAL 2 HOUR + INTERVAL 35 MINUTE, now()),
('evt_014', 2, 2, 'action', 'save_file', 'file_operation', '{"file_name": "document.pdf", "save_type": "auto"}', 'sess_004', 'desktop', 'macos', 'Safari', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '', '/editor', now() - INTERVAL 2 HOUR + INTERVAL 30 MINUTE, now()),
('evt_015', 2, 2, 'action', 'export_pdf', 'file_operation', '{"format": "pdf", "quality": "high", "file_size": 3072}', 'sess_004', 'desktop', 'macos', 'Safari', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '', '/editor', now() - INTERVAL 2 HOUR + INTERVAL 20 MINUTE, now()),

-- 用户3的行为事件（PDF创建器）
('evt_021', 3, 3, 'click', 'create_pdf', 'create_operation', '{"template": "blank", "page_size": "A4"}', 'sess_006', 'mobile', 'ios', 'Safari', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15', '', '/creator', now() - INTERVAL 4 HOUR, now()),
('evt_022', 3, 3, 'action', 'add_text', 'content_operation', '{"text": "Hello World", "font": "Arial", "size": 12}', 'sess_006', 'mobile', 'ios', 'Safari', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15', '', '/creator', now() - INTERVAL 3 HOUR + INTERVAL 45 MINUTE, now()),
('evt_023', 3, 3, 'action', 'convert_document', 'create_operation', '{"source_format": "docx", "target_format": "pdf", "file_size": 1536}', 'sess_006', 'mobile', 'ios', 'Safari', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15', '', '/creator', now() - INTERVAL 3 HOUR + INTERVAL 30 MINUTE, now()),
('evt_024', 3, 3, 'action', 'merge_pdfs', 'create_operation', '{"file_count": 3, "total_pages": 25}', 'sess_006', 'mobile', 'ios', 'Safari', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15', '', '/creator', now() - INTERVAL 3 HOUR + INTERVAL 15 MINUTE, now()),

-- 用户4的行为事件（Foxit Cloud）
('evt_031', 4, 4, 'action', 'upload_file', 'cloud_operation', '{"file_name": "report.pdf", "file_size": 2048, "folder": "documents"}', 'sess_008', 'desktop', 'windows', 'Edge', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '', '/cloud', now() - INTERVAL 5 HOUR, now()),
('evt_032', 4, 4, 'action', 'share_document', 'cloud_operation', '{"document_id": "doc_123", "share_type": "link", "permissions": "view"}', 'sess_008', 'desktop', 'windows', 'Edge', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '', '/cloud', now() - INTERVAL 4 HOUR + INTERVAL 45 MINUTE, now()),
('evt_033', 4, 4, 'action', 'sync_files', 'cloud_operation', '{"sync_count": 15, "sync_size": 25600}', 'sess_008', 'desktop', 'windows', 'Edge', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '', '/cloud', now() - INTERVAL 4 HOUR + INTERVAL 30 MINUTE, now()),

-- 用户5的行为事件（PhantomPDF企业版）
('evt_041', 5, 5, 'action', 'batch_process', 'enterprise_operation', '{"operation": "watermark", "file_count": 50, "processing_time": 120}', 'sess_010', 'desktop', 'windows', 'Chrome', '192.168.1.104', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '', '/phantom', now() - INTERVAL 6 HOUR, now()),
('evt_042', 5, 5, 'action', 'apply_security', 'enterprise_operation', '{"security_type": "password", "encryption_level": "256bit"}', 'sess_010', 'desktop', 'windows', 'Chrome', '192.168.1.104', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '', '/phantom', now() - INTERVAL 5 HOUR + INTERVAL 45 MINUTE, now()),
('evt_043', 5, 5, 'action', 'ocr_process', 'enterprise_operation', '{"pages_processed": 25, "accuracy": 98.5, "language": "en"}', 'sess_010', 'desktop', 'windows', 'Chrome', '192.168.1.104', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '', '/phantom', now() - INTERVAL 5 HOUR + INTERVAL 30 MINUTE, now());

-- =============================================
-- 4. 用户活跃度统计数据（最近30天）
-- =============================================
INSERT INTO user_activity_stats VALUES
-- 用户1活跃度统计（PDF阅读器用户）
(1, 1, '2025-07-02', 3, 15, 7200000, 25, 8, 0, 'premium', now()),
(1, 1, '2025-07-01', 2, 12, 5400000, 18, 6, 0, 'premium', now()),
(1, 1, '2025-06-30', 4, 18, 8100000, 32, 10, 0, 'premium', now()),
(1, 1, '2025-06-29', 3, 14, 6900000, 28, 9, 0, 'premium', now()),
(1, 1, '2025-06-28', 2, 11, 5100000, 20, 7, 0, 'premium', now()),

-- 用户2活跃度统计（PDF编辑器用户）
(2, 2, '2025-07-02', 2, 10, 4800000, 15, 5, 0, 'free', now()),
(2, 2, '2025-07-01', 1, 8, 3600000, 12, 4, 0, 'free', now()),
(2, 2, '2025-06-30', 3, 12, 5400000, 18, 6, 0, 'free', now()),
(2, 2, '2025-06-29', 2, 9, 4200000, 14, 5, 0, 'free', now()),
(2, 2, '2025-06-28', 1, 6, 2700000, 10, 3, 0, 'free', now()),

-- 用户3活跃度统计（PDF创建器用户）
(3, 3, '2025-07-02', 1, 6, 2400000, 8, 3, 0, 'trial', now()),
(3, 3, '2025-07-01', 2, 8, 3300000, 12, 4, 0, 'trial', now()),
(3, 3, '2025-06-30', 1, 5, 2100000, 7, 2, 0, 'trial', now()),
(3, 3, '2025-06-29', 3, 10, 4500000, 16, 5, 0, 'trial', now()),

-- 用户4活跃度统计（Foxit Cloud用户）
(4, 4, '2025-07-02', 2, 9, 3900000, 14, 4, 0, 'premium', now()),
(4, 4, '2025-07-01', 3, 11, 4800000, 18, 6, 0, 'premium', now()),
(4, 4, '2025-06-30', 2, 8, 3600000, 12, 4, 0, 'premium', now()),
(4, 4, '2025-06-29', 1, 7, 3000000, 10, 3, 0, 'premium', now()),

-- 用户5活跃度统计（PhantomPDF企业用户）
(5, 5, '2025-07-02', 4, 20, 9600000, 35, 12, 0, 'enterprise', now()),
(5, 5, '2025-07-01', 3, 16, 7800000, 28, 9, 0, 'enterprise', now()),
(5, 5, '2025-06-30', 5, 22, 10800000, 40, 15, 0, 'enterprise', now()),
(5, 5, '2025-06-29', 4, 18, 8700000, 32, 11, 0, 'enterprise', now()),

-- 用户6活跃度统计（PDF365用户）
(6, 6, '2025-07-02', 1, 4, 1800000, 6, 2, 0, 'free', now()),
(6, 6, '2025-07-01', 2, 6, 2700000, 9, 3, 0, 'free', now()),
(6, 6, '2025-06-30', 1, 3, 1500000, 5, 2, 0, 'free', now()),

-- 用户7活跃度统计（智慧打印用户）
(7, 7, '2025-07-02', 2, 7, 3000000, 11, 4, 0, 'premium', now()),
(7, 7, '2025-07-01', 1, 5, 2100000, 8, 3, 0, 'premium', now()),
(7, 7, '2025-06-30', 3, 9, 3900000, 15, 5, 0, 'premium', now()),

-- 用户8活跃度统计（新用户）
(8, 8, '2025-07-02', 1, 3, 1200000, 4, 1, 0, 'trial', now()),
(8, 8, '2025-07-01', 2, 5, 2100000, 7, 2, 0, 'trial', now()),

-- 用户9活跃度统计（新用户）
(9, 9, '2025-07-02', 1, 2, 900000, 3, 1, 0, 'free', now()),
(9, 9, '2025-07-01', 1, 4, 1800000, 6, 2, 0, 'free', now()),

-- 用户10活跃度统计（设计师用户）
(10, 10, '2025-07-02', 2, 8, 3600000, 13, 4, 0, 'premium', now()),
(10, 10, '2025-07-01', 3, 10, 4500000, 16, 5, 0, 'premium', now()),
(10, 10, '2025-06-30', 2, 7, 3000000, 11, 4, 0, 'premium', now()),

-- 用户11活跃度统计（财务用户）
(11, 11, '2025-07-02', 1, 5, 2100000, 8, 3, 0, 'enterprise', now()),
(11, 11, '2025-07-01', 2, 7, 3000000, 11, 4, 0, 'enterprise', now()),
(11, 11, '2025-06-30', 1, 4, 1800000, 6, 2, 0, 'enterprise', now());

-- =============================================
-- 5. 用户登录行为数据（最近30天）
-- =============================================
INSERT INTO user_login_behavior VALUES
-- 用户1登录行为（活跃用户）
(1, 1, '2025-07-02 09:15:00', '2025-07-02 17:30:00', 29700000, 'desktop', 'windows', 'Chrome', '192.168.1.100', 'Beijing', 'CN', 1, now()),
(1, 1, '2025-07-01 08:45:00', '2025-07-01 16:20:00', 27300000, 'desktop', 'windows', 'Chrome', '192.168.1.100', 'Beijing', 'CN', 1, now()),
(1, 1, '2025-06-30 10:20:00', '2025-06-30 18:45:00', 30300000, 'desktop', 'windows', 'Chrome', '192.168.1.100', 'Beijing', 'CN', 1, now()),
(1, 1, '2025-06-29 09:30:00', '2025-06-29 17:15:00', 27900000, 'desktop', 'windows', 'Chrome', '192.168.1.100', 'Beijing', 'CN', 1, now()),
(1, 1, '2025-06-28 08:50:00', '2025-06-28 15:30:00', 24000000, 'desktop', 'windows', 'Chrome', '192.168.1.100', 'Beijing', 'CN', 1, now()),

-- 用户2登录行为（编辑器用户）
(2, 2, '2025-07-02 10:30:00', '2025-07-02 15:45:00', 18900000, 'desktop', 'macos', 'Safari', '*************', 'Shanghai', 'CN', 1, now()),
(2, 2, '2025-07-01 11:15:00', '2025-07-01 14:30:00', 11700000, 'desktop', 'macos', 'Safari', '*************', 'Shanghai', 'CN', 1, now()),
(2, 2, '2025-06-30 09:45:00', '2025-06-30 16:20:00', 23700000, 'desktop', 'macos', 'Safari', '*************', 'Shanghai', 'CN', 1, now()),
(2, 2, '2025-06-29 10:20:00', '2025-06-29 15:10:00', 17400000, 'desktop', 'macos', 'Safari', '*************', 'Shanghai', 'CN', 1, now()),

-- 用户3登录行为（移动端用户）
(3, 3, '2025-07-02 12:20:00', '2025-07-02 13:45:00', 5100000, 'mobile', 'ios', 'Safari', '*************', 'Guangzhou', 'CN', 1, now()),
(3, 3, '2025-07-01 14:30:00', '2025-07-01 16:15:00', 6300000, 'mobile', 'ios', 'Safari', '*************', 'Guangzhou', 'CN', 1, now()),
(3, 3, '2025-06-30 11:45:00', '2025-06-30 12:30:00', 2700000, 'mobile', 'ios', 'Safari', '*************', 'Guangzhou', 'CN', 1, now()),
(3, 3, '2025-06-29 13:20:00', '2025-06-29 15:45:00', 8700000, 'mobile', 'ios', 'Safari', '*************', 'Guangzhou', 'CN', 1, now()),

-- 用户4登录行为（云端用户）
(4, 4, '2025-07-02 08:30:00', '2025-07-02 12:45:00', 15300000, 'desktop', 'windows', 'Edge', '*************', 'Shenzhen', 'CN', 1, now()),
(4, 4, '2025-07-01 09:15:00', '2025-07-01 13:30:00', 15300000, 'desktop', 'windows', 'Edge', '*************', 'Shenzhen', 'CN', 1, now()),
(4, 4, '2025-06-30 10:00:00', '2025-06-30 14:20:00', 15600000, 'desktop', 'windows', 'Edge', '*************', 'Shenzhen', 'CN', 1, now()),

-- 用户5登录行为（企业用户）
(5, 5, '2025-07-02 07:45:00', '2025-07-02 19:30:00', 42300000, 'desktop', 'windows', 'Chrome', '192.168.1.104', 'Hangzhou', 'CN', 1, now()),
(5, 5, '2025-07-01 08:00:00', '2025-07-01 18:45:00', 38700000, 'desktop', 'windows', 'Chrome', '192.168.1.104', 'Hangzhou', 'CN', 1, now()),
(5, 5, '2025-06-30 07:30:00', '2025-06-30 19:15:00', 42300000, 'desktop', 'windows', 'Chrome', '192.168.1.104', 'Hangzhou', 'CN', 1, now()),

-- 用户6-11登录行为（其他用户）
(6, 6, '2025-07-02 15:20:00', '2025-07-02 16:10:00', 3000000, 'desktop', 'windows', 'Firefox', '192.168.1.105', 'Nanjing', 'CN', 1, now()),
(7, 7, '2025-07-02 09:00:00', '2025-07-02 12:30:00', 12600000, 'desktop', 'windows', 'Chrome', '192.168.1.106', 'Wuhan', 'CN', 1, now()),
(8, 8, '2025-07-02 14:45:00', '2025-07-02 15:30:00', 2700000, 'mobile', 'android', 'Chrome', '192.168.1.107', 'Chengdu', 'CN', 1, now()),
(9, 9, '2025-07-02 16:30:00', '2025-07-02 17:00:00', 1800000, 'tablet', 'ios', 'Safari', '192.168.1.108', 'Xian', 'CN', 1, now()),
(10, 10, '2025-07-02 10:45:00', '2025-07-02 14:20:00', 12900000, 'desktop', 'macos', 'Chrome', '192.168.1.109', 'Qingdao', 'CN', 1, now()),
(11, 11, '2025-07-02 08:15:00', '2025-07-02 11:30:00', 11700000, 'desktop', 'windows', 'Edge', '192.168.1.110', 'Tianjin', 'CN', 1, now());

-- =============================================
-- 5. 用户路径分析数据
-- =============================================
INSERT INTO user_path_analysis VALUES
-- 用户1的路径分析
('path_001', 1, 1, 'sess_001', ['login', 'dashboard', 'file_list', 'open_file', 'reader'], 
 [now() - INTERVAL 2 HOUR, now() - INTERVAL 2 HOUR + INTERVAL 5 MINUTE, now() - INTERVAL 2 HOUR + INTERVAL 8 MINUTE, now() - INTERVAL 2 HOUR + INTERVAL 12 MINUTE, now() - INTERVAL 2 HOUR + INTERVAL 15 MINUTE], 
 900000, 5, 'file_read', 1, 5, 0, '2025-07-02', now()),

-- 用户2的路径分析
('path_002', 2, 2, 'sess_004', ['login', 'dashboard', 'editor', 'edit_text', 'save'], 
 [now() - INTERVAL 3 HOUR, now() - INTERVAL 3 HOUR + INTERVAL 3 MINUTE, now() - INTERVAL 3 HOUR + INTERVAL 6 MINUTE, now() - INTERVAL 3 HOUR + INTERVAL 20 MINUTE, now() - INTERVAL 3 HOUR + INTERVAL 35 MINUTE], 
 2100000, 5, 'file_edit', 1, 5, 0, '2025-07-02', now()),

-- 用户3的路径分析
('path_003', 3, 3, 'sess_006', ['login', 'creator', 'create_pdf'],
 [now() - INTERVAL 4 HOUR, now() - INTERVAL 4 HOUR + INTERVAL 2 MINUTE, now() - INTERVAL 4 HOUR + INTERVAL 5 MINUTE],
 300000, 3, 'file_create', 0, 0, 3, '2025-07-02', now()),

-- 用户4的路径分析 - 首页到功能页到编辑页
('path_004', 4, 1, 'sess_007', ['home', 'feature', 'editor', 'save'],
 [now() - INTERVAL 1 DAY, now() - INTERVAL 1 DAY + INTERVAL 2 MINUTE, now() - INTERVAL 1 DAY + INTERVAL 8 MINUTE, now() - INTERVAL 1 DAY + INTERVAL 15 MINUTE],
 900000, 4, 'feature_usage', 1, 4, 0, '2025-07-01', now()),

-- 用户5的路径分析 - 首页到帮助页到功能页
('path_005', 5, 1, 'sess_008', ['home', 'help', 'feature', 'editor'],
 [now() - INTERVAL 1 DAY, now() - INTERVAL 1 DAY + INTERVAL 3 MINUTE, now() - INTERVAL 1 DAY + INTERVAL 10 MINUTE, now() - INTERVAL 1 DAY + INTERVAL 18 MINUTE],
 1080000, 4, 'feature_usage', 1, 4, 0, '2025-07-01', now()),

-- 用户6的路径分析 - 首页到设置页
('path_006', 6, 2, 'sess_009', ['home', 'settings'],
 [now() - INTERVAL 1 DAY, now() - INTERVAL 1 DAY + INTERVAL 5 MINUTE],
 300000, 2, 'settings_config', 0, 0, 2, '2025-07-01', now()),

-- 用户7的路径分析 - 登录页到首页到功能页
('path_007', 7, 1, 'sess_010', ['login', 'home', 'feature', 'editor', 'save'],
 [now() - INTERVAL 2 DAY, now() - INTERVAL 2 DAY + INTERVAL 1 MINUTE, now() - INTERVAL 2 DAY + INTERVAL 3 MINUTE, now() - INTERVAL 2 DAY + INTERVAL 12 MINUTE, now() - INTERVAL 2 DAY + INTERVAL 20 MINUTE],
 1200000, 5, 'complete_workflow', 1, 5, 0, '2025-06-30', now()),

-- 用户8的路径分析 - 功能页到编辑页到保存
('path_008', 8, 2, 'sess_011', ['feature', 'editor', 'save'],
 [now() - INTERVAL 2 DAY, now() - INTERVAL 2 DAY + INTERVAL 5 MINUTE, now() - INTERVAL 2 DAY + INTERVAL 15 MINUTE],
 900000, 3, 'quick_edit', 1, 3, 0, '2025-06-30', now()),

-- 用户9的路径分析 - 首页到功能页（跳出）
('path_009', 9, 1, 'sess_012', ['home', 'feature'],
 [now() - INTERVAL 3 DAY, now() - INTERVAL 3 DAY + INTERVAL 2 MINUTE],
 120000, 2, 'feature_browse', 0, 0, 2, '2025-06-29', now()),

-- 用户10的路径分析 - 完整的PDF处理流程
('path_010', 10, 3, 'sess_013', ['home', 'converter', 'upload', 'convert', 'download'],
 [now() - INTERVAL 3 DAY, now() - INTERVAL 3 DAY + INTERVAL 1 MINUTE, now() - INTERVAL 3 DAY + INTERVAL 3 MINUTE, now() - INTERVAL 3 DAY + INTERVAL 8 MINUTE, now() - INTERVAL 3 DAY + INTERVAL 12 MINUTE],
 720000, 5, 'pdf_conversion', 1, 5, 0, '2025-06-29', now()),

-- 用户11的路径分析 - 签名流程
('path_011', 11, 4, 'sess_014', ['home', 'sign', 'upload', 'signature', 'download'],
 [now() - INTERVAL 4 DAY, now() - INTERVAL 4 DAY + INTERVAL 2 MINUTE, now() - INTERVAL 4 DAY + INTERVAL 4 MINUTE, now() - INTERVAL 4 DAY + INTERVAL 10 MINUTE, now() - INTERVAL 4 DAY + INTERVAL 15 MINUTE],
 900000, 5, 'digital_signature', 1, 5, 0, '2025-06-28', now()),

-- 用户12的路径分析 - 压缩流程
('path_012', 12, 5, 'sess_015', ['home', 'compress', 'upload', 'optimize', 'download'],
 [now() - INTERVAL 5 DAY, now() - INTERVAL 5 DAY + INTERVAL 1 MINUTE, now() - INTERVAL 5 DAY + INTERVAL 2 MINUTE, now() - INTERVAL 5 DAY + INTERVAL 5 MINUTE, now() - INTERVAL 5 DAY + INTERVAL 8 MINUTE],
 480000, 5, 'file_compression', 1, 5, 0, '2025-06-27', now());

-- =============================================
-- 6. 漏斗转化数据
-- =============================================
INSERT INTO funnel_conversion_data VALUES
-- PDF阅读漏斗
('funnel_001', 1, 1, 'sess_001', 'PDF阅读转化', 'reader', 1, '访问首页', now() - INTERVAL 2 HOUR, 1, 0, '', '2025-07-02', now()),
('funnel_001', 1, 1, 'sess_001', 'PDF阅读转化', 'reader', 2, '打开文件列表', now() - INTERVAL 2 HOUR + INTERVAL 8 MINUTE, 1, 480000, '', '2025-07-02', now()),
('funnel_001', 1, 1, 'sess_001', 'PDF阅读转化', 'reader', 3, '选择文件', now() - INTERVAL 2 HOUR + INTERVAL 12 MINUTE, 1, 720000, '', '2025-07-02', now()),
('funnel_001', 1, 1, 'sess_001', 'PDF阅读转化', 'reader', 4, '开始阅读', now() - INTERVAL 2 HOUR + INTERVAL 15 MINUTE, 1, 900000, '', '2025-07-02', now()),

-- PDF编辑漏斗
('funnel_002', 2, 2, 'sess_004', 'PDF编辑转化', 'editor', 1, '访问编辑器', now() - INTERVAL 3 HOUR, 1, 0, '', '2025-07-02', now()),
('funnel_002', 2, 2, 'sess_004', 'PDF编辑转化', 'editor', 2, '开始编辑', now() - INTERVAL 3 HOUR + INTERVAL 6 MINUTE, 1, 360000, '', '2025-07-02', now()),
('funnel_002', 2, 2, 'sess_004', 'PDF编辑转化', 'editor', 3, '保存文件', now() - INTERVAL 3 HOUR + INTERVAL 35 MINUTE, 1, 2100000, '', '2025-07-02', now()),

-- PDF创建漏斗（未完成转化）
('funnel_003', 3, 3, 'sess_006', 'PDF创建转化', 'creator', 1, '访问创建器', now() - INTERVAL 4 HOUR, 1, 0, '', '2025-07-02', now()),
('funnel_003', 3, 3, 'sess_006', 'PDF创建转化', 'creator', 2, '选择模板', now() - INTERVAL 4 HOUR + INTERVAL 2 MINUTE, 1, 120000, '', '2025-07-02', now()),
('funnel_003', 3, 3, 'sess_006', 'PDF创建转化', 'creator', 3, '添加内容', now() - INTERVAL 4 HOUR + INTERVAL 5 MINUTE, 0, 300000, '用户离开', '2025-07-02', now());

-- =============================================
-- 7. 产品使用统计数据（ClickHouse版本）
-- =============================================
INSERT INTO product_usage_stats VALUES
-- 产品线1统计
(1, '2025-07-02', 14, 125680, 89420, 1250, 156780, 892340, 8950, 1530000.5, 15.2, now()),
(1, '2025-07-02', 15, 126890, 91250, 1570, 162340, 915680, 9250, 1580000.8, 14.8, now()),
(1, '2025-07-02', 16, 128150, 93180, 1890, 168920, 938450, 9580, 1620000.3, 14.5, now()),

-- 产品线2统计
(2, '2025-07-02', 14, 89420, 67890, 890, 98760, 567890, 6780, 1920000.2, 12.5, now()),
(2, '2025-07-02', 15, 90310, 69120, 1120, 102340, 582340, 7120, 1950000.6, 12.1, now()),
(2, '2025-07-02', 16, 91250, 70450, 1340, 105890, 596780, 7450, 1980000.9, 11.8, now()),

-- 产品线3统计
(3, '2025-07-02', 14, 67890, 45670, 670, 67890, 389450, 4560, 1740000.8, 18.3, now()),
(3, '2025-07-02', 15, 68560, 46890, 890, 70120, 402180, 4890, 1780000.4, 17.9, now()),
(3, '2025-07-02', 16, 69230, 48120, 1120, 72450, 414920, 5120, 1820000.7, 17.5, now());

-- 完成模拟数据插入
SELECT 'ClickHouse mock data inserted successfully!' as status;
