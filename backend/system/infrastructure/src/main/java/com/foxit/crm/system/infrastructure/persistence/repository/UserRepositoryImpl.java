package com.foxit.crm.system.infrastructure.persistence.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.foxit.crm.system.domain.model.aggregate.User;
import com.foxit.crm.system.domain.model.valueobject.UserId;
import com.foxit.crm.system.domain.model.valueobject.Username;
import com.foxit.crm.system.domain.model.valueobject.Email;
import com.foxit.crm.system.domain.repository.UserRepository;
import com.foxit.crm.system.infrastructure.persistence.entity.UserPO;
import com.foxit.crm.system.infrastructure.persistence.mapper.UserMapper;
import com.foxit.crm.system.infrastructure.persistence.converter.UserPOConverter;
import com.foxit.crm.shared.common.util.PageResult;

import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户仓储实现
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Repository
public class UserRepositoryImpl implements UserRepository {

    private final UserMapper userMapper;
    private final UserPOConverter userPOConverter;

    public UserRepositoryImpl(UserMapper userMapper, UserPOConverter userPOConverter) {
        this.userMapper = userMapper;
        this.userPOConverter = userPOConverter;
    }

    @Override
    public User save(User user) {
        UserPO po = userPOConverter.toPO(user);
        
        if (user.isNew()) {
            // 新增
            userMapper.insert(po);
            user.setId(po.getId());
        } else {
            // 更新
            userMapper.updateById(po);
        }
        
        return user;
    }

    @Override
    public Optional<User> findById(Long id) {
        UserPO po = userMapper.selectById(id);
        return Optional.ofNullable(userPOConverter.toDomain(po));
    }

    @Override
    public Optional<User> findByUserId(UserId userId) {
        UserPO po = userMapper.findByUserId(userId.getValue());
        return Optional.ofNullable(userPOConverter.toDomain(po));
    }

    @Override
    public Optional<User> findByUsername(Username username) {
        UserPO po = userMapper.findByUsername(username.getValue());
        return Optional.ofNullable(userPOConverter.toDomain(po));
    }

    @Override
    public Optional<User> findByEmail(Email email) {
        if (email.isEmpty()) {
            return Optional.empty();
        }
        UserPO po = userMapper.findByEmail(email.getValue());
        return Optional.ofNullable(userPOConverter.toDomain(po));
    }

    @Override
    public boolean existsByUsername(Username username) {
        return userMapper.existsByUsername(username.getValue());
    }

    @Override
    public boolean existsByEmail(Email email) {
        if (email.isEmpty()) {
            return false;
        }
        return userMapper.existsByEmail(email.getValue());
    }

    @Override
    public void delete(User user) {
        userMapper.deleteById(user.getId());
    }

    @Override
    public void deleteById(Long id) {
        userMapper.deleteById(id);
    }

    @Override
    public PageResult<User> findPage(int pageNum, int pageSize, UserQuery query) {
        // 构建查询参数
        UserMapper.UserQueryParam queryParam = new UserMapper.UserQueryParam();
        queryParam.setUsername(query.getUsername());
        queryParam.setRealName(query.getRealName());
        queryParam.setEmail(query.getEmail());
        queryParam.setStatus(query.getStatus());
        queryParam.setUserType(query.getUserType());
        queryParam.setDeptId(query.getDeptId());

        // 执行分页查询
        Page<UserPO> page = new Page<>(pageNum, pageSize);
        IPage<UserPO> result = userMapper.selectUserPage(page, queryParam);

        // 转换结果
        List<User> users = userPOConverter.toDomain(result.getRecords());
        
        return PageResult.of(users, result.getTotal(), 
            (int) result.getCurrent(), (int) result.getSize());
    }

    @Override
    public List<User> findAllEnabled() {
        List<UserPO> poList = userMapper.selectAllEnabled();
        return userPOConverter.toDomain(poList);
    }

    @Override
    public List<User> findByDeptId(Long deptId) {
        List<UserPO> poList = userMapper.selectByDeptId(deptId);
        return userPOConverter.toDomain(poList);
    }

    @Override
    public List<User> findByUserType(Integer userType) {
        List<UserPO> poList = userMapper.selectByUserType(userType);
        return userPOConverter.toDomain(poList);
    }
}
