package com.foxit.crm.system.infrastructure.persistence.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.foxit.crm.system.domain.model.aggregate.Role;
import com.foxit.crm.system.domain.model.valueobject.RoleId;
import com.foxit.crm.system.domain.model.valueobject.RoleCode;
import com.foxit.crm.system.domain.repository.RoleRepository;
import com.foxit.crm.system.infrastructure.persistence.mapper.RoleMapper;
import com.foxit.crm.system.infrastructure.persistence.po.RolePO;
import com.foxit.crm.system.infrastructure.persistence.converter.RolePOConverter;
import com.foxit.crm.shared.common.util.PageResult;

import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 角色仓储实现
 * 实现领域层定义的RoleRepository接口
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Repository
public class RoleRepositoryImpl implements RoleRepository {

    private final RoleMapper roleMapper;
    private final RolePOConverter rolePOConverter;

    public RoleRepositoryImpl(RoleMapper roleMapper, RolePOConverter rolePOConverter) {
        this.roleMapper = roleMapper;
        this.rolePOConverter = rolePOConverter;
    }

    @Override
    public Role save(Role role) {
        RolePO rolePO = rolePOConverter.toPO(role);
        if (rolePO.getId() == null) {
            roleMapper.insert(rolePO);
            // 更新聚合根的ID
            role.setId(rolePO.getId());
        } else {
            roleMapper.updateById(rolePO);
        }
        return role;
    }

    @Override
    public Optional<Role> findById(Long id) {
        RolePO rolePO = roleMapper.selectById(id);
        return Optional.ofNullable(rolePO)
                .map(rolePOConverter::toDomain);
    }

    @Override
    public Optional<Role> findByRoleId(RoleId roleId) {
        RolePO rolePO = roleMapper.selectByRoleId(roleId.getValue().toString());
        return Optional.ofNullable(rolePO)
                .map(rolePOConverter::toDomain);
    }

    @Override
    public Optional<Role> findByRoleCode(RoleCode roleCode) {
        RolePO rolePO = roleMapper.selectByCode(roleCode.getValue());
        return Optional.ofNullable(rolePO)
                .map(rolePOConverter::toDomain);
    }

    @Override
    public boolean existsByRoleCode(RoleCode roleCode) {
        return roleMapper.selectByCode(roleCode.getValue()) != null;
    }

    @Override
    public boolean existsByRoleName(String roleName) {
        QueryWrapper<RolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_name", roleName);
        return roleMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public void delete(Role role) {
        if (role.getId() != null) {
            roleMapper.deleteById(role.getId());
        }
    }

    @Override
    public void deleteById(Long id) {
        roleMapper.deleteById(id);
    }

    @Override
    public PageResult<Role> findPage(int pageNum, int pageSize, RoleQuery query) {
        Page<RolePO> page = new Page<>(pageNum, pageSize);
        QueryWrapper<RolePO> queryWrapper = new QueryWrapper<>();

        if (query != null) {
            if (StringUtils.hasText(query.getRoleName())) {
                queryWrapper.like("role_name", query.getRoleName());
            }
            if (StringUtils.hasText(query.getRoleCode())) {
                queryWrapper.like("role_code", query.getRoleCode());
            }
            if (query.getStatus() != null) {
                queryWrapper.eq("status", query.getStatus());
            }
            if (query.getRoleType() != null) {
                queryWrapper.eq("role_type", query.getRoleType());
            }
        }

        queryWrapper.orderByAsc("sort_order", "create_time");
        Page<RolePO> result = roleMapper.selectPage(page, queryWrapper);

        List<Role> roles = result.getRecords().stream()
                .map(rolePOConverter::toDomain)
                .collect(Collectors.toList());

        return PageResult.of(roles, result.getTotal(), pageNum, pageSize);
    }

    @Override
    public List<Role> findAllEnabled() {
        QueryWrapper<RolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.orderByAsc("sort_order", "create_time");

        List<RolePO> rolePOs = roleMapper.selectList(queryWrapper);
        return rolePOs.stream()
                .map(rolePOConverter::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<Role> findByRoleType(Integer roleType) {
        QueryWrapper<RolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_type", roleType);
        queryWrapper.orderByAsc("sort_order", "create_time");

        List<RolePO> rolePOs = roleMapper.selectList(queryWrapper);
        return rolePOs.stream()
                .map(rolePOConverter::toDomain)
                .collect(Collectors.toList());
    }
}
