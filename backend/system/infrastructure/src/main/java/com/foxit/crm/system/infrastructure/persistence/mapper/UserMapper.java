package com.foxit.crm.system.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.foxit.crm.system.infrastructure.persistence.entity.UserPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户Mapper接口
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Mapper
public interface UserMapper extends BaseMapper<UserPO> {

    /**
     * 根据用户名查找用户
     */
    UserPO findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查找用户
     */
    UserPO findByEmail(@Param("email") String email);

    /**
     * 根据用户ID查找用户
     */
    UserPO findByUserId(@Param("userId") Long userId);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(@Param("email") String email);

    /**
     * 分页查询用户
     */
    IPage<UserPO> selectUserPage(Page<UserPO> page, @Param("query") UserQueryParam query);

    /**
     * 查询所有启用的用户
     */
    List<UserPO> selectAllEnabled();

    /**
     * 根据部门ID查询用户
     */
    List<UserPO> selectByDeptId(@Param("deptId") Long deptId);

    /**
     * 根据用户类型查询用户
     */
    List<UserPO> selectByUserType(@Param("userType") Integer userType);

    /**
     * 用户查询参数
     */
    class UserQueryParam {
        private String username;
        private String realName;
        private String email;
        private Integer status;
        private Integer userType;
        private Long deptId;

        // getters and setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }

        public String getRealName() { return realName; }
        public void setRealName(String realName) { this.realName = realName; }

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }

        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }

        public Integer getUserType() { return userType; }
        public void setUserType(Integer userType) { this.userType = userType; }

        public Long getDeptId() { return deptId; }
        public void setDeptId(Long deptId) { this.deptId = deptId; }
    }
}
