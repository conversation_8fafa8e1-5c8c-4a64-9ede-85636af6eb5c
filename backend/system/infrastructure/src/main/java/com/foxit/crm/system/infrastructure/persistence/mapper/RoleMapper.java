package com.foxit.crm.system.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.foxit.crm.system.infrastructure.persistence.po.RolePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 角色数据访问映射器
 * 基于MyBatis-Plus的数据访问层
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Mapper
public interface RoleMapper extends BaseMapper<RolePO> {

    /**
     * 根据角色编码查询角色
     *
     * @param roleCode 角色编码
     * @return 角色PO对象
     */
    @Select("SELECT * FROM sys_role WHERE role_code = #{roleCode} AND deleted = 0")
    RolePO selectByCode(@Param("roleCode") String roleCode);

    /**
     * 根据角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色PO对象
     */
    @Select("SELECT * FROM sys_role WHERE role_id = #{roleId} AND deleted = 0")
    RolePO selectByRoleId(@Param("roleId") String roleId);
}
