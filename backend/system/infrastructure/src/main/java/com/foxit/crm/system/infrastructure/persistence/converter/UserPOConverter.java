package com.foxit.crm.system.infrastructure.persistence.converter;

import com.foxit.crm.system.domain.model.aggregate.User;
import com.foxit.crm.system.domain.model.valueobject.UserId;
import com.foxit.crm.system.domain.model.valueobject.Username;
import com.foxit.crm.system.domain.model.valueobject.Email;
import com.foxit.crm.system.domain.model.valueobject.Password;
import com.foxit.crm.system.infrastructure.persistence.entity.UserPO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户领域对象与PO转换器
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Component
public class UserPOConverter {

    /**
     * 领域对象转PO
     */
    public UserPO toPO(User user) {
        if (user == null) {
            return null;
        }

        UserPO po = new UserPO();
        po.setId(user.getId());
        po.setUserId(user.getUserId() != null ? user.getUserId().getValue() : null);
        po.setUsername(user.getUsername() != null ? user.getUsername().getValue() : null);
        po.setPassword(user.getPassword() != null ? user.getPassword().getEncodedValue() : null);
        po.setRealName(user.getRealName());
        po.setEmail(user.getEmail() != null ? user.getEmail().getValue() : null);
        po.setPhone(user.getPhone());
        po.setAvatar(user.getAvatar());
        po.setStatus(user.getStatus());
        po.setUserType(user.getUserType());
        po.setDeptId(user.getDeptId());
        po.setLastLoginTime(user.getLastLoginTime());
        po.setLastLoginIp(user.getLastLoginIp());
        po.setRemark(user.getRemark());
        po.setCreateTime(user.getCreateTime());
        po.setUpdateTime(user.getUpdateTime());
        po.setCreateBy(user.getCreateBy());
        po.setUpdateBy(user.getUpdateBy());
        po.setDeleted(user.getDeleted());
        po.setVersion(user.getVersion());

        return po;
    }

    /**
     * PO转领域对象
     */
    public User toDomain(UserPO po) {
        if (po == null) {
            return null;
        }

        User user = new User();
        
        // 设置基础实体属性
        user.setId(po.getId());
        user.setCreateTime(po.getCreateTime());
        user.setUpdateTime(po.getUpdateTime());
        user.setCreateBy(po.getCreateBy());
        user.setUpdateBy(po.getUpdateBy());
        user.setDeleted(po.getDeleted());
        user.setVersion(po.getVersion());

        // 设置值对象
        if (po.getUserId() != null) {
            user.setUserId(new UserId(po.getUserId()));
        }
        if (po.getUsername() != null) {
            user.setUsername(new Username(po.getUsername()));
        }
        if (po.getPassword() != null) {
            user.setPassword(Password.fromEncoded(po.getPassword()));
        }
        if (po.getEmail() != null) {
            user.setEmail(new Email(po.getEmail()));
        }

        // 设置其他属性
        user.setRealName(po.getRealName());
        user.setPhone(po.getPhone());
        user.setAvatar(po.getAvatar());
        user.setStatus(po.getStatus());
        user.setUserType(po.getUserType());
        user.setDeptId(po.getDeptId());
        user.setLastLoginTime(po.getLastLoginTime());
        user.setLastLoginIp(po.getLastLoginIp());
        user.setRemark(po.getRemark());

        return user;
    }

    /**
     * 批量PO转领域对象
     */
    public List<User> toDomain(List<UserPO> poList) {
        if (poList == null) {
            return null;
        }
        return poList.stream()
                .map(this::toDomain)
                .collect(Collectors.toList());
    }

    /**
     * 批量领域对象转PO
     */
    public List<UserPO> toPO(List<User> userList) {
        if (userList == null) {
            return null;
        }
        return userList.stream()
                .map(this::toPO)
                .collect(Collectors.toList());
    }
}
