package com.foxit.crm.system.infrastructure.persistence.converter;

import com.foxit.crm.system.domain.model.aggregate.Role;
import com.foxit.crm.system.domain.model.valueobject.RoleId;
import com.foxit.crm.system.domain.model.valueobject.RoleCode;
import com.foxit.crm.system.infrastructure.persistence.po.RolePO;

import org.springframework.stereotype.Component;

/**
 * 角色PO转换器
 * 负责领域对象与持久化对象之间的转换
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Component
public class RolePOConverter {

    /**
     * 将角色PO转换为领域对象
     *
     * @param rolePO 角色PO
     * @return 角色领域对象
     */
    public Role toDomain(RolePO rolePO) {
        if (rolePO == null) {
            return null;
        }

        // 创建角色聚合根 - 使用数据库ID作为RoleId
        Role role = new Role(
                new RoleId(rolePO.getId()),
                rolePO.getRoleName(),
                new RoleCode(rolePO.getRoleCode()),
                rolePO.getDescription()
        );

        // 设置数据库ID
        if (rolePO.getId() != null) {
            role.setId(rolePO.getId());
        }

        // 设置状态
        if (rolePO.getStatus() != null) {
            role.setStatus(rolePO.getStatus());
        }

        // 设置排序号
        if (rolePO.getSortOrder() != null) {
            role.setSortOrder(rolePO.getSortOrder());
        }

        // 设置角色类型
        if (rolePO.getRoleType() != null) {
            role.setRoleType(rolePO.getRoleType());
        }

        // 设置审计字段
        role.setCreateTime(rolePO.getCreateTime());
        role.setUpdateTime(rolePO.getUpdateTime());
        role.setCreateBy(rolePO.getCreateBy());
        role.setUpdateBy(rolePO.getUpdateBy());
        role.setVersion(rolePO.getVersion());

        return role;
    }

    /**
     * 将角色领域对象转换为PO
     *
     * @param role 角色领域对象
     * @return 角色PO
     */
    public RolePO toPO(Role role) {
        if (role == null) {
            return null;
        }

        RolePO rolePO = new RolePO();

        // 设置数据库ID（如果存在）
        if (role.getId() != null) {
            rolePO.setId(role.getId());
        }

        // 设置角色业务ID - 使用数据库ID的字符串形式
        if (role.getRoleId() != null) {
            rolePO.setRoleId(role.getRoleId().getValue().toString());
        }

        // 设置基本信息
        if (role.getRoleCode() != null) {
            rolePO.setRoleCode(role.getRoleCode().getValue());
        }
        rolePO.setRoleName(role.getRoleName());
        rolePO.setDescription(role.getDescription());

        // 设置状态
        rolePO.setStatus(role.getStatus());

        // 设置排序号
        rolePO.setSortOrder(role.getSortOrder());

        // 设置角色类型
        rolePO.setRoleType(role.getRoleType());

        // 设置审计字段
        rolePO.setCreateTime(role.getCreateTime());
        rolePO.setUpdateTime(role.getUpdateTime());
        rolePO.setCreateBy(role.getCreateBy());
        rolePO.setUpdateBy(role.getUpdateBy());
        rolePO.setVersion(role.getVersion());

        return rolePO;
    }
}
