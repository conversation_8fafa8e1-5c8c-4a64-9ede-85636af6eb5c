package com.foxit.crm.system.api.controller;

import com.foxit.crm.system.application.service.UserApplicationService;
import com.foxit.crm.system.application.dto.request.UserCreateRequest;
import com.foxit.crm.system.application.dto.request.UserUpdateRequest;
import com.foxit.crm.system.application.dto.request.UserQueryRequest;
import com.foxit.crm.system.application.dto.response.UserDetailResponse;
import com.foxit.crm.system.application.dto.response.UserListResponse;
import com.foxit.crm.shared.common.result.Result;
import com.foxit.crm.shared.common.util.PageResult;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户管理控制器
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Tag(name = "用户管理", description = "用户管理相关接口")
@RestController
@RequestMapping("/api/system/users")
@Validated
public class UserController {

    private final UserApplicationService userApplicationService;

    public UserController(UserApplicationService userApplicationService) {
        this.userApplicationService = userApplicationService;
    }

    @Operation(summary = "创建用户", description = "创建新用户")
    @PostMapping
    @PreAuthorize("hasAuthority('ADMIN')")
    public Result<Long> createUser(@Valid @RequestBody UserCreateRequest request) {
        Long userId = userApplicationService.createUser(request);
        return Result.success(userId, "用户创建成功");
    }

    @Operation(summary = "更新用户信息", description = "更新指定用户的信息")
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('ADMIN') or #id == authentication.principal.id")
    public Result<String> updateUser(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long id,
            @Valid @RequestBody UserUpdateRequest request) {
        userApplicationService.updateUser(id, request);
        return Result.success("用户信息更新成功");
    }

    @Operation(summary = "启用用户", description = "启用指定用户")
    @PostMapping("/{id}/enable")
    @PreAuthorize("hasAuthority('ADMIN')")
    public Result<String> enableUser(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long id) {
        userApplicationService.enableUser(id);
        return Result.success("用户启用成功");
    }

    @Operation(summary = "禁用用户", description = "禁用指定用户")
    @PostMapping("/{id}/disable")
    @PreAuthorize("hasAuthority('ADMIN')")
    public Result<String> disableUser(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long id) {
        userApplicationService.disableUser(id);
        return Result.success("用户禁用成功");
    }

    @Operation(summary = "删除用户", description = "删除指定用户")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('ADMIN')")
    public Result<String> deleteUser(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long id) {
        userApplicationService.deleteUser(id);
        return Result.success("用户删除成功");
    }

    @Operation(summary = "修改密码", description = "修改用户密码")
    @PostMapping("/{id}/change-password")
    @PreAuthorize("hasAuthority('ADMIN') or #id == authentication.principal.id")
    public Result<String> changePassword(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long id,
            @Parameter(description = "新密码") @RequestParam @NotNull String newPassword) {
        userApplicationService.changePassword(id, newPassword);
        return Result.success("密码修改成功");
    }

    @Operation(summary = "获取用户详情", description = "根据ID获取用户详细信息")
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('ADMIN') or #id == authentication.principal.id")
    public Result<UserDetailResponse> getUserById(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long id) {
        UserDetailResponse user = userApplicationService.getUserById(id);
        return Result.success(user);
    }

    @Operation(summary = "根据用户名获取用户", description = "根据用户名获取用户信息")
    @GetMapping("/username/{username}")
    @PreAuthorize("hasAuthority('ADMIN')")
    public Result<UserDetailResponse> getUserByUsername(
            @Parameter(description = "用户名") @PathVariable @NotNull String username) {
        UserDetailResponse user = userApplicationService.getUserByUsername(username);
        return Result.success(user);
    }

    @Operation(summary = "分页查询用户", description = "分页查询用户列表")
    @GetMapping
    @PreAuthorize("hasAuthority('ADMIN')")
    public Result<PageResult<UserListResponse>> getUserPage(@Valid UserQueryRequest request) {
        PageResult<UserListResponse> result = userApplicationService.getUserPage(request);
        return Result.success(result);
    }

    @Operation(summary = "获取所有启用用户", description = "获取所有启用状态的用户")
    @GetMapping("/enabled")
    @PreAuthorize("hasAuthority('ADMIN')")
    public Result<List<UserListResponse>> getAllEnabledUsers() {
        List<UserListResponse> users = userApplicationService.getAllEnabledUsers();
        return Result.success(users);
    }

    @Operation(summary = "检查用户名可用性", description = "检查用户名是否可用")
    @GetMapping("/check-username")
    public Result<Boolean> checkUsernameAvailability(
            @Parameter(description = "用户名") @RequestParam @NotNull String username) {
        boolean available = userApplicationService.isUsernameAvailable(username);
        return Result.success(available);
    }

    @Operation(summary = "检查邮箱可用性", description = "检查邮箱是否可用")
    @GetMapping("/check-email")
    public Result<Boolean> checkEmailAvailability(
            @Parameter(description = "邮箱") @RequestParam @NotNull String email) {
        boolean available = userApplicationService.isEmailAvailable(email);
        return Result.success(available);
    }
}
