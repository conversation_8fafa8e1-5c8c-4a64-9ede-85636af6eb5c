package com.foxit.crm.system.domain.model.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

/**
 * 角色ID值对象
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Getter
@EqualsAndHashCode
@ToString
public class RoleId {

    private final Long value;

    public RoleId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("角色ID不能为空或小于等于0");
        }
        this.value = value;
    }

    /**
     * 静态工厂方法
     */
    public static RoleId of(Long value) {
        return new RoleId(value);
    }
}
