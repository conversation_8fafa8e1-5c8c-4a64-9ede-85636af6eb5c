package com.foxit.crm.system.domain.repository;

import com.foxit.crm.system.domain.model.aggregate.Role;
import com.foxit.crm.system.domain.model.valueobject.RoleId;
import com.foxit.crm.system.domain.model.valueobject.RoleCode;
import com.foxit.crm.shared.common.util.PageResult;

import java.util.List;
import java.util.Optional;

/**
 * 角色仓储接口
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface RoleRepository {

    /**
     * 保存角色
     */
    Role save(Role role);

    /**
     * 根据ID查找角色
     */
    Optional<Role> findById(Long id);

    /**
     * 根据角色ID查找角色
     */
    Optional<Role> findByRoleId(RoleId roleId);

    /**
     * 根据角色编码查找角色
     */
    Optional<Role> findByRoleCode(RoleCode roleCode);

    /**
     * 检查角色编码是否存在
     */
    boolean existsByRoleCode(RoleCode roleCode);

    /**
     * 检查角色名称是否存在
     */
    boolean existsByRoleName(String roleName);

    /**
     * 删除角色
     */
    void delete(Role role);

    /**
     * 根据ID删除角色
     */
    void deleteById(Long id);

    /**
     * 分页查询角色
     */
    PageResult<Role> findPage(int pageNum, int pageSize, RoleQuery query);

    /**
     * 查询所有启用的角色
     */
    List<Role> findAllEnabled();

    /**
     * 根据角色类型查询角色
     */
    List<Role> findByRoleType(Integer roleType);

    /**
     * 角色查询条件
     */
    class RoleQuery {
        private String roleName;
        private String roleCode;
        private Integer status;
        private Integer roleType;

        // getters and setters
        public String getRoleName() { return roleName; }
        public void setRoleName(String roleName) { this.roleName = roleName; }

        public String getRoleCode() { return roleCode; }
        public void setRoleCode(String roleCode) { this.roleCode = roleCode; }

        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }

        public Integer getRoleType() { return roleType; }
        public void setRoleType(Integer roleType) { this.roleType = roleType; }
    }
}
