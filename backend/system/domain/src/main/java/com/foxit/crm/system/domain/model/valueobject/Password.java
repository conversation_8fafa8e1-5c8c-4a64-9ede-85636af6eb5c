package com.foxit.crm.system.domain.model.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 密码值对象
 * 注意：这里不直接依赖Spring Security，而是通过领域服务来处理密码加密
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Getter
@EqualsAndHashCode
public class Password {

    private final String encodedValue;

    /**
     * 从已编码的密码创建（用于从数据库加载）
     */
    public static Password fromEncoded(String encodedPassword) {
        if (encodedPassword == null || encodedPassword.trim().isEmpty()) {
            throw new IllegalArgumentException("编码后的密码不能为空");
        }
        return new Password(encodedPassword);
    }

    /**
     * 从原始密码创建（需要通过领域服务加密）
     */
    public static Password fromRaw(String rawPassword, PasswordEncoder encoder) {
        if (rawPassword == null || rawPassword.trim().isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }

        if (rawPassword.length() < 6 || rawPassword.length() > 20) {
            throw new IllegalArgumentException("密码长度必须在6-20个字符之间");
        }

        String encoded = encoder.encode(rawPassword);
        return new Password(encoded);
    }

    private Password(String encodedValue) {
        this.encodedValue = encodedValue;
    }

    /**
     * 验证原始密码是否匹配（需要通过领域服务验证）
     */
    public boolean matches(String rawPassword, PasswordEncoder encoder) {
        if (rawPassword == null) {
            return false;
        }
        return encoder.matches(rawPassword, this.encodedValue);
    }

    /**
     * 简化的匹配方法（向后兼容）
     */
    public boolean matches(String rawPassword) {
        // 这里需要注入密码编码器，暂时抛出异常提醒使用正确的方法
        throw new UnsupportedOperationException("请使用 matches(rawPassword, encoder) 方法");
    }

    @Override
    public String toString() {
        return "Password{encodedValue='[PROTECTED]'}";
    }

    /**
     * 密码编码器接口（领域层定义，基础设施层实现）
     */
    public interface PasswordEncoder {
        String encode(String rawPassword);
        boolean matches(String rawPassword, String encodedPassword);
    }
}
