package com.foxit.crm.system.domain.model.valueobject;

import java.util.Objects;

/**
 * 角色名称值对象
 * 封装角色名称的业务规则和验证逻辑
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public class RoleName {

    private final String value;

    /**
     * 构造函数
     *
     * @param value 角色名称
     */
    public RoleName(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("角色名称不能为空");
        }
        if (value.length() > 50) {
            throw new IllegalArgumentException("角色名称长度不能超过50个字符");
        }
        this.value = value.trim();
    }

    /**
     * 获取角色名称值
     *
     * @return 角色名称
     */
    public String getValue() {
        return value;
    }

    /**
     * 验证角色名称格式
     *
     * @return 是否有效
     */
    public boolean isValid() {
        return value != null && !value.trim().isEmpty() && value.length() <= 50;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RoleName roleName = (RoleName) o;
        return Objects.equals(value, roleName.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(value);
    }

    @Override
    public String toString() {
        return "RoleName{" +
                "value='" + value + '\'' +
                '}';
    }
}
