package com.foxit.crm.system.domain.event;

import com.foxit.crm.shared.domain.event.DomainEvent;
import lombok.Getter;

/**
 * 用户登录事件
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Getter
public class UserLoginEvent extends DomainEvent {

    private final Long userId;
    private final String loginIp;

    public UserLoginEvent(Long aggregateId, Long userId, String loginIp) {
        super(aggregateId);
        this.userId = userId;
        this.loginIp = loginIp;
    }

    @Override
    public String getEventName() {
        return "用户登录";
    }

    @Override
    public String getEventDescription() {
        return String.format("用户 (ID: %d) 从 IP %s 登录", userId, loginIp);
    }

    @Override
    public Object getEventData() {
        return new UserLoginEventData(userId, loginIp);
    }

    @Getter
    public static class UserLoginEventData {
        private final Long userId;
        private final String loginIp;

        public UserLoginEventData(Long userId, String loginIp) {
            this.userId = userId;
            this.loginIp = loginIp;
        }
    }
}
