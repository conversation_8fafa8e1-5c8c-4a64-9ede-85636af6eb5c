package com.foxit.crm.system.domain.event;

import com.foxit.crm.shared.domain.event.DomainEvent;
import lombok.Getter;

/**
 * 角色创建事件
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Getter
public class RoleCreatedEvent extends DomainEvent {

    private final Long roleId;
    private final String roleName;
    private final String roleCode;

    public RoleCreatedEvent(Long aggregateId, Long roleId, String roleName, String roleCode) {
        super(aggregateId);
        this.roleId = roleId;
        this.roleName = roleName;
        this.roleCode = roleCode;
    }

    @Override
    public String getEventName() {
        return "角色创建";
    }

    @Override
    public String getEventDescription() {
        return String.format("角色 %s (%s) 创建成功", roleName, roleCode);
    }

    @Override
    public Object getEventData() {
        return new RoleCreatedEventData(roleId, roleName, roleCode);
    }

    @Getter
    public static class RoleCreatedEventData {
        private final Long roleId;
        private final String roleName;
        private final String roleCode;

        public RoleCreatedEventData(Long roleId, String roleName, String roleCode) {
            this.roleId = roleId;
            this.roleName = roleName;
            this.roleCode = roleCode;
        }
    }
}
