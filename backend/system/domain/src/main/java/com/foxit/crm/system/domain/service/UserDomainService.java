package com.foxit.crm.system.domain.service;

import com.foxit.crm.system.domain.model.aggregate.User;
import com.foxit.crm.system.domain.model.valueobject.Username;
import com.foxit.crm.system.domain.model.valueobject.Email;
import com.foxit.crm.system.domain.model.valueobject.Password;
import com.foxit.crm.system.domain.repository.UserRepository;

import org.springframework.stereotype.Service;

/**
 * 用户领域服务
 * 处理跨聚合的业务逻辑和复杂的业务规则
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Service
public class UserDomainService {

    private final UserRepository userRepository;
    private final Password.PasswordEncoder passwordEncoder;

    public UserDomainService(UserRepository userRepository, Password.PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
    }

    /**
     * 检查用户名是否可用
     */
    public boolean isUsernameAvailable(Username username) {
        return !userRepository.existsByUsername(username);
    }

    /**
     * 检查邮箱是否可用
     */
    public boolean isEmailAvailable(Email email) {
        if (email.isEmpty()) {
            return true; // 邮箱可以为空
        }
        return !userRepository.existsByEmail(email);
    }

    /**
     * 验证用户注册信息
     */
    public void validateUserRegistration(Username username, Email email) {
        if (!isUsernameAvailable(username)) {
            throw new IllegalArgumentException("用户名已存在");
        }

        if (!email.isEmpty() && !isEmailAvailable(email)) {
            throw new IllegalArgumentException("邮箱已被使用");
        }
    }

    /**
     * 创建密码
     */
    public Password createPassword(String rawPassword) {
        return Password.fromRaw(rawPassword, passwordEncoder);
    }

    /**
     * 验证密码
     */
    public boolean validatePassword(Password password, String rawPassword) {
        return password.matches(rawPassword, passwordEncoder);
    }

    /**
     * 检查用户是否可以被删除
     */
    public boolean canDeleteUser(User user) {
        // 管理员不能删除
        if (user.isAdmin()) {
            return false;
        }

        // 可以添加其他业务规则，比如：
        // - 检查用户是否有未完成的任务
        // - 检查用户是否是某些数据的创建者
        // - 等等

        return true;
    }

    /**
     * 检查用户是否可以被禁用
     */
    public boolean canDisableUser(User user) {
        // 超级管理员不能被禁用
        if (user.getUserId().getValue().equals(1L)) {
            return false;
        }

        return true;
    }

    /**
     * 生成默认头像URL
     */
    public String generateDefaultAvatar(Username username) {
        // 可以根据用户名生成默认头像
        // 这里简单返回一个默认值
        return "/default-avatar.png";
    }

    /**
     * 检查密码强度
     */
    public boolean isPasswordStrong(String password) {
        if (password == null || password.length() < 8) {
            return false;
        }

        // 检查是否包含大小写字母、数字和特殊字符
        boolean hasUpper = password.chars().anyMatch(Character::isUpperCase);
        boolean hasLower = password.chars().anyMatch(Character::isLowerCase);
        boolean hasDigit = password.chars().anyMatch(Character::isDigit);
        boolean hasSpecial = password.chars().anyMatch(ch -> "!@#$%^&*()_+-=[]{}|;:,.<>?".indexOf(ch) >= 0);

        return hasUpper && hasLower && hasDigit && hasSpecial;
    }
}
