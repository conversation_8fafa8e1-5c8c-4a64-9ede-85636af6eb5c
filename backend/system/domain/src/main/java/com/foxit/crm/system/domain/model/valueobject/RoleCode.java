package com.foxit.crm.system.domain.model.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.util.regex.Pattern;

/**
 * 角色编码值对象
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Getter
@EqualsAndHashCode
@ToString
public class RoleCode {

    private static final Pattern ROLE_CODE_PATTERN = Pattern.compile("^[A-Z][A-Z0-9_]{2,49}$");

    private final String value;

    public RoleCode(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("角色编码不能为空");
        }

        String trimmedValue = value.trim();
        if (!ROLE_CODE_PATTERN.matcher(trimmedValue).matches()) {
            throw new IllegalArgumentException("角色编码格式不正确，必须以大写字母开头，只能包含大写字母、数字和下划线，长度为3-50个字符");
        }

        this.value = trimmedValue;
    }

    /**
     * 静态工厂方法
     */
    public static RoleCode of(String value) {
        return new RoleCode(value);
    }

    /**
     * 预定义的系统角色编码
     */
    public static class System {
        public static final RoleCode SUPER_ADMIN = new RoleCode("SUPER_ADMIN");
        public static final RoleCode ADMIN = new RoleCode("ADMIN");
        public static final RoleCode USER = new RoleCode("USER");
        public static final RoleCode GUEST = new RoleCode("GUEST");
    }
}
