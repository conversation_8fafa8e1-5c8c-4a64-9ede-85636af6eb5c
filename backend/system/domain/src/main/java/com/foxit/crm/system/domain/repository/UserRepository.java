package com.foxit.crm.system.domain.repository;

import com.foxit.crm.system.domain.model.aggregate.User;
import com.foxit.crm.system.domain.model.valueobject.UserId;
import com.foxit.crm.system.domain.model.valueobject.Username;
import com.foxit.crm.system.domain.model.valueobject.Email;
import com.foxit.crm.shared.common.util.PageResult;

import java.util.List;
import java.util.Optional;

/**
 * 用户仓储接口
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface UserRepository {

    /**
     * 保存用户
     */
    User save(User user);

    /**
     * 根据ID查找用户
     */
    Optional<User> findById(Long id);

    /**
     * 根据用户ID查找用户
     */
    Optional<User> findByUserId(UserId userId);

    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(Username username);

    /**
     * 根据邮箱查找用户
     */
    Optional<User> findByEmail(Email email);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(Username username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(Email email);

    /**
     * 删除用户
     */
    void delete(User user);

    /**
     * 根据ID删除用户
     */
    void deleteById(Long id);

    /**
     * 分页查询用户
     */
    PageResult<User> findPage(int pageNum, int pageSize, UserQuery query);

    /**
     * 查询所有启用的用户
     */
    List<User> findAllEnabled();

    /**
     * 根据部门ID查询用户
     */
    List<User> findByDeptId(Long deptId);

    /**
     * 根据用户类型查询用户
     */
    List<User> findByUserType(Integer userType);

    /**
     * 用户查询条件
     */
    class UserQuery {
        private String username;
        private String realName;
        private String email;
        private Integer status;
        private Integer userType;
        private Long deptId;

        // getters and setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }

        public String getRealName() { return realName; }
        public void setRealName(String realName) { this.realName = realName; }

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }

        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }

        public Integer getUserType() { return userType; }
        public void setUserType(Integer userType) { this.userType = userType; }

        public Long getDeptId() { return deptId; }
        public void setDeptId(Long deptId) { this.deptId = deptId; }
    }
}
