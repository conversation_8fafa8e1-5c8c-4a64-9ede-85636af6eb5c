package com.foxit.crm.system.domain.event;

import com.foxit.crm.shared.domain.event.DomainEvent;
import lombok.Getter;

import java.util.List;

/**
 * 角色权限变更事件
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Getter
public class RolePermissionChangedEvent extends DomainEvent {

    private final Long roleId;
    private final List<Long> oldPermissionIds;
    private final List<Long> newPermissionIds;

    public RolePermissionChangedEvent(Long aggregateId, Long roleId, 
                                    List<Long> oldPermissionIds, List<Long> newPermissionIds) {
        super(aggregateId);
        this.roleId = roleId;
        this.oldPermissionIds = oldPermissionIds;
        this.newPermissionIds = newPermissionIds;
    }

    @Override
    public String getEventName() {
        return "角色权限变更";
    }

    @Override
    public String getEventDescription() {
        return String.format("角色 (ID: %d) 权限从 %d 个变更为 %d 个", 
                roleId, oldPermissionIds.size(), newPermissionIds.size());
    }

    @Override
    public Object getEventData() {
        return new RolePermissionChangedEventData(roleId, oldPermissionIds, newPermissionIds);
    }

    @Getter
    public static class RolePermissionChangedEventData {
        private final Long roleId;
        private final List<Long> oldPermissionIds;
        private final List<Long> newPermissionIds;

        public RolePermissionChangedEventData(Long roleId, List<Long> oldPermissionIds, List<Long> newPermissionIds) {
            this.roleId = roleId;
            this.oldPermissionIds = oldPermissionIds;
            this.newPermissionIds = newPermissionIds;
        }
    }
}
