package com.foxit.crm.system.domain.model.aggregate;

import com.foxit.crm.shared.domain.model.AggregateRoot;
import com.foxit.crm.system.domain.model.valueobject.UserId;
import com.foxit.crm.system.domain.model.valueobject.Username;
import com.foxit.crm.system.domain.model.valueobject.Password;
import com.foxit.crm.system.domain.model.valueobject.Email;
import com.foxit.crm.system.domain.event.UserRegisteredEvent;
import com.foxit.crm.system.domain.event.UserLoginEvent;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 用户聚合根
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ToString(exclude = { "password" }, callSuper = true) // 排除密码字段，避免日志泄露
public class User extends AggregateRoot {

    /**
     * 用户ID（业务标识）
     */
    private UserId userId;

    /**
     * 用户名
     */
    private Username username;

    /**
     * 密码
     */
    private Password password;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 邮箱
     */
    private Email email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 用户状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 用户类型：1-管理员，2-普通用户
     */
    private Integer userType;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    // 业务构造函数
    public User(UserId userId, Username username, Password password, String realName, Email email) {
        this.userId = userId;
        this.username = username;
        this.password = password;
        this.realName = realName;
        this.email = email;
        this.status = 1; // 默认启用
        this.userType = 2; // 默认普通用户
        
        // 设置基础实体信息
        setCreateInfo(null); // 创建者ID稍后设置
        
        // 发布用户注册事件
        addDomainEvent(new UserRegisteredEvent(this.getId(), userId.getValue(), username.getValue(), email.getValue()));
    }

    /**
     * 验证密码
     */
    public boolean validatePassword(String rawPassword) {
        return this.password.matches(rawPassword);
    }

    /**
     * 更新最后登录信息
     */
    public void updateLastLogin(String loginIp) {
        this.lastLoginTime = LocalDateTime.now();
        this.lastLoginIp = loginIp;
        setUpdateInfo(this.getId());
        
        // 发布用户登录事件
        addDomainEvent(new UserLoginEvent(this.getId(), userId.getValue(), loginIp));
    }

    /**
     * 启用用户
     */
    public void enable() {
        this.status = 1;
        setUpdateInfo(this.getId());
    }

    /**
     * 禁用用户
     */
    public void disable() {
        this.status = 0;
        setUpdateInfo(this.getId());
    }

    /**
     * 检查用户是否启用
     */
    public boolean isEnabled() {
        return this.status != null && this.status == 1;
    }

    /**
     * 检查用户是否为管理员
     */
    public boolean isAdmin() {
        return this.userType != null && this.userType == 1;
    }

    /**
     * 更新用户信息
     */
    public void updateProfile(String realName, Email email, String phone, String avatar) {
        this.realName = realName;
        this.email = email;
        this.phone = phone;
        this.avatar = avatar;
        setUpdateInfo(this.getId());
    }

    /**
     * 修改密码
     */
    public void changePassword(Password newPassword) {
        this.password = newPassword;
        setUpdateInfo(this.getId());
    }

    /**
     * 检查是否可以删除
     */
    public boolean canBeDeleted() {
        // 管理员不能删除
        return !isAdmin();
    }
}
