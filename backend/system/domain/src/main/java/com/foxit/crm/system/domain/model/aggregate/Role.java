package com.foxit.crm.system.domain.model.aggregate;

import com.foxit.crm.shared.domain.model.AggregateRoot;
import com.foxit.crm.system.domain.model.valueobject.RoleId;
import com.foxit.crm.system.domain.model.valueobject.RoleCode;
import com.foxit.crm.system.domain.event.RoleCreatedEvent;
import com.foxit.crm.system.domain.event.RolePermissionChangedEvent;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.ArrayList;

/**
 * 角色聚合根
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class Role extends AggregateRoot {

    /**
     * 角色ID（业务标识）
     */
    private RoleId roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色编码
     */
    private RoleCode roleCode;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 角色状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 备注
     */
    private String remark;

    /**
     * 权限ID列表
     */
    private List<Long> permissionIds;

    /**
     * 角色类型：1-系统角色，2-自定义角色
     */
    private Integer roleType;

    /**
     * 业务构造函数
     */
    public Role(RoleId roleId, String roleName, RoleCode roleCode, String description) {
        this.roleId = roleId;
        this.roleName = roleName;
        this.roleCode = roleCode;
        this.description = description;
        this.status = 1; // 默认启用
        this.roleType = 2; // 默认自定义角色
        this.permissionIds = new ArrayList<>();
        
        // 设置基础实体信息
        setCreateInfo(null); // 创建者ID稍后设置
        
        // 发布角色创建事件
        addDomainEvent(new RoleCreatedEvent(this.getId(), roleId.getValue(), roleName, roleCode.getValue()));
    }

    /**
     * 启用角色
     */
    public void enable() {
        this.status = 1;
        setUpdateInfo(this.getId());
    }

    /**
     * 禁用角色
     */
    public void disable() {
        if (isSystemRole()) {
            throw new IllegalStateException("系统角色不能被禁用");
        }
        this.status = 0;
        setUpdateInfo(this.getId());
    }

    /**
     * 检查角色是否启用
     */
    public boolean isEnabled() {
        return this.status != null && this.status == 1;
    }

    /**
     * 检查是否为系统角色
     */
    public boolean isSystemRole() {
        return this.roleType != null && this.roleType == 1;
    }

    /**
     * 更新角色信息
     */
    public void updateInfo(String roleName, String description, Integer sortOrder) {
        this.roleName = roleName;
        this.description = description;
        this.sortOrder = sortOrder;
        setUpdateInfo(this.getId());
    }

    /**
     * 分配权限
     */
    public void assignPermissions(List<Long> newPermissionIds) {
        if (newPermissionIds == null) {
            newPermissionIds = new ArrayList<>();
        }
        
        List<Long> oldPermissionIds = new ArrayList<>(this.permissionIds);
        this.permissionIds = new ArrayList<>(newPermissionIds);
        setUpdateInfo(this.getId());
        
        // 发布权限变更事件
        addDomainEvent(new RolePermissionChangedEvent(this.getId(), roleId.getValue(), 
                oldPermissionIds, newPermissionIds));
    }

    /**
     * 添加权限
     */
    public void addPermission(Long permissionId) {
        if (permissionId != null && !this.permissionIds.contains(permissionId)) {
            this.permissionIds.add(permissionId);
            setUpdateInfo(this.getId());
        }
    }

    /**
     * 移除权限
     */
    public void removePermission(Long permissionId) {
        if (this.permissionIds.remove(permissionId)) {
            setUpdateInfo(this.getId());
        }
    }

    /**
     * 检查是否拥有指定权限
     */
    public boolean hasPermission(Long permissionId) {
        return this.permissionIds.contains(permissionId);
    }

    /**
     * 检查是否可以删除
     */
    public boolean canBeDeleted() {
        // 系统角色不能删除
        return !isSystemRole();
    }

    /**
     * 获取权限数量
     */
    public int getPermissionCount() {
        return this.permissionIds.size();
    }
}
