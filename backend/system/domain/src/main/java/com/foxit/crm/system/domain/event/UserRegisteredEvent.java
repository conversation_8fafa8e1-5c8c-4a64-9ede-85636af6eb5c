package com.foxit.crm.system.domain.event;

import com.foxit.crm.shared.domain.event.DomainEvent;
import lombok.Getter;

/**
 * 用户注册事件
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Getter
public class UserRegisteredEvent extends DomainEvent {

    private final Long userId;
    private final String username;
    private final String email;

    public UserRegisteredEvent(Long aggregateId, Long userId, String username, String email) {
        super(aggregateId);
        this.userId = userId;
        this.username = username;
        this.email = email;
    }

    @Override
    public String getEventName() {
        return "用户注册";
    }

    @Override
    public String getEventDescription() {
        return String.format("用户 %s (ID: %d) 注册成功", username, userId);
    }

    @Override
    public Object getEventData() {
        return new UserRegisteredEventData(userId, username, email);
    }

    @Getter
    public static class UserRegisteredEventData {
        private final Long userId;
        private final String username;
        private final String email;

        public UserRegisteredEventData(Long userId, String username, String email) {
            this.userId = userId;
            this.username = username;
            this.email = email;
        }
    }
}
