package com.foxit.crm.system.application.dto.request;

import lombok.Data;

/**
 * 用户查询请求DTO
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class UserQueryRequest {

    /**
     * 用户名（模糊查询）
     */
    private String username;

    /**
     * 真实姓名（模糊查询）
     */
    private String realName;

    /**
     * 邮箱（模糊查询）
     */
    private String email;

    /**
     * 用户状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 用户类型：1-管理员，2-普通用户
     */
    private Integer userType;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;
}
