package com.foxit.crm.system.application.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户详情响应DTO
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class UserDetailResponse {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID（业务标识）
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 用户状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 用户状态描述
     */
    private String statusDesc;

    /**
     * 用户类型：1-管理员，2-普通用户
     */
    private Integer userType;

    /**
     * 用户类型描述
     */
    private String userTypeDesc;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 创建者姓名
     */
    private String createByName;

    /**
     * 更新者ID
     */
    private Long updateBy;

    /**
     * 更新者姓名
     */
    private String updateByName;
}
