package com.foxit.crm.system.application.dto.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 角色创建请求DTO
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class RoleCreateRequest {

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 50, message = "角色名称长度不能超过50个字符")
    private String roleName;

    /**
     * 角色编码
     */
    @NotBlank(message = "角色编码不能为空")
    @Pattern(regexp = "^[A-Z][A-Z0-9_]{2,49}$", message = "角色编码格式不正确，必须以大写字母开头，只能包含大写字母、数字和下划线，长度为3-50个字符")
    private String roleCode;

    /**
     * 角色描述
     */
    @Size(max = 200, message = "角色描述长度不能超过200个字符")
    private String description;

    /**
     * 排序号
     */
    private Integer sortOrder = 0;

    /**
     * 权限ID列表
     */
    private List<Long> permissionIds;
}
