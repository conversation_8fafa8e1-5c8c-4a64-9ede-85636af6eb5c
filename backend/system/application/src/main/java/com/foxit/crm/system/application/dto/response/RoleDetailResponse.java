package com.foxit.crm.system.application.dto.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色详情响应DTO
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class RoleDetailResponse {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 角色ID（业务标识）
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 角色状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 角色状态描述
     */
    private String statusDesc;

    /**
     * 角色类型：1-系统角色，2-自定义角色
     */
    private Integer roleType;

    /**
     * 角色类型描述
     */
    private String roleTypeDesc;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 权限ID列表
     */
    private List<Long> permissionIds;

    /**
     * 权限数量
     */
    private Integer permissionCount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 创建者姓名
     */
    private String createByName;

    /**
     * 更新者ID
     */
    private Long updateBy;

    /**
     * 更新者姓名
     */
    private String updateByName;

    /**
     * 备注
     */
    private String remark;
}
