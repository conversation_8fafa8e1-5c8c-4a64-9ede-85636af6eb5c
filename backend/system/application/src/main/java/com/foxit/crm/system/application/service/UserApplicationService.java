package com.foxit.crm.system.application.service;

import com.foxit.crm.system.domain.model.aggregate.User;
import com.foxit.crm.system.domain.model.valueobject.UserId;
import com.foxit.crm.system.domain.model.valueobject.Username;
import com.foxit.crm.system.domain.model.valueobject.Email;
import com.foxit.crm.system.domain.model.valueobject.Password;
import com.foxit.crm.system.domain.repository.UserRepository;
import com.foxit.crm.system.domain.service.UserDomainService;
import com.foxit.crm.system.application.dto.request.UserCreateRequest;
import com.foxit.crm.system.application.dto.request.UserUpdateRequest;
import com.foxit.crm.system.application.dto.request.UserQueryRequest;
import com.foxit.crm.system.application.dto.response.UserDetailResponse;
import com.foxit.crm.system.application.dto.response.UserListResponse;
import com.foxit.crm.system.application.converter.UserConverter;
import com.foxit.crm.shared.common.util.PageResult;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 用户应用服务
 * 负责编排业务流程，协调领域对象完成业务用例
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Service
@Transactional
public class UserApplicationService {

    private final UserRepository userRepository;
    private final UserDomainService userDomainService;
    private final UserConverter userConverter;

    public UserApplicationService(UserRepository userRepository, 
                                UserDomainService userDomainService,
                                UserConverter userConverter) {
        this.userRepository = userRepository;
        this.userDomainService = userDomainService;
        this.userConverter = userConverter;
    }

    /**
     * 创建用户
     */
    public Long createUser(UserCreateRequest request) {
        // 1. 验证业务规则
        Username username = new Username(request.getUsername());
        Email email = new Email(request.getEmail());
        userDomainService.validateUserRegistration(username, email);

        // 2. 生成用户ID
        UserId userId = generateUserId();

        // 3. 创建密码
        Password password = userDomainService.createPassword(request.getPassword());

        // 4. 创建用户聚合根
        User user = userConverter.fromCreateRequest(request, userId);
        user.changePassword(password);

        // 5. 保存用户
        User savedUser = userRepository.save(user);

        // 6. 发布领域事件（由基础设施层处理）
        
        return savedUser.getId();
    }

    /**
     * 更新用户信息
     */
    public void updateUser(Long id, UserUpdateRequest request) {
        // 1. 查找用户
        User user = findUserById(id);

        // 2. 验证邮箱唯一性（如果邮箱有变化）
        Email newEmail = new Email(request.getEmail());
        if (!newEmail.isEmpty() && 
            (user.getEmail() == null || !user.getEmail().equals(newEmail))) {
            if (!userDomainService.isEmailAvailable(newEmail)) {
                throw new IllegalArgumentException("邮箱已被使用");
            }
        }

        // 3. 更新用户信息
        userConverter.updateFromRequest(user, request);

        // 4. 保存更新
        userRepository.save(user);
    }

    /**
     * 启用用户
     */
    public void enableUser(Long id) {
        User user = findUserById(id);
        user.enable();
        userRepository.save(user);
    }

    /**
     * 禁用用户
     */
    public void disableUser(Long id) {
        User user = findUserById(id);
        
        // 检查是否可以禁用
        if (!userDomainService.canDisableUser(user)) {
            throw new IllegalArgumentException("该用户不能被禁用");
        }
        
        user.disable();
        userRepository.save(user);
    }

    /**
     * 删除用户
     */
    public void deleteUser(Long id) {
        User user = findUserById(id);
        
        // 检查是否可以删除
        if (!userDomainService.canDeleteUser(user)) {
            throw new IllegalArgumentException("该用户不能被删除");
        }
        
        userRepository.delete(user);
    }

    /**
     * 修改密码
     */
    public void changePassword(Long id, String newPassword) {
        User user = findUserById(id);
        Password password = userDomainService.createPassword(newPassword);
        user.changePassword(password);
        userRepository.save(user);
    }

    /**
     * 根据ID查询用户详情
     */
    @Transactional(readOnly = true)
    public UserDetailResponse getUserById(Long id) {
        User user = findUserById(id);
        return userConverter.toDetailResponse(user);
    }

    /**
     * 根据用户名查询用户
     */
    @Transactional(readOnly = true)
    public UserDetailResponse getUserByUsername(String username) {
        Username usernameVO = new Username(username);
        Optional<User> userOpt = userRepository.findByUsername(usernameVO);
        if (userOpt.isEmpty()) {
            throw new IllegalArgumentException("用户不存在");
        }
        return userConverter.toDetailResponse(userOpt.get());
    }

    /**
     * 分页查询用户列表
     */
    @Transactional(readOnly = true)
    public PageResult<UserListResponse> getUserPage(UserQueryRequest request) {
        // 构建查询条件
        UserRepository.UserQuery query = new UserRepository.UserQuery();
        query.setUsername(request.getUsername());
        query.setRealName(request.getRealName());
        query.setEmail(request.getEmail());
        query.setStatus(request.getStatus());
        query.setUserType(request.getUserType());
        query.setDeptId(request.getDeptId());

        // 执行查询
        PageResult<User> userPage = userRepository.findPage(
            request.getPageNum(), request.getPageSize(), query);

        // 转换DTO
        List<UserListResponse> responseList = userConverter.toListResponse(userPage.getRecords());
        
        return PageResult.of(responseList, userPage.getTotal(), 
            userPage.getPageNum(), userPage.getPageSize());
    }

    /**
     * 查询所有启用的用户
     */
    @Transactional(readOnly = true)
    public List<UserListResponse> getAllEnabledUsers() {
        List<User> users = userRepository.findAllEnabled();
        return userConverter.toListResponse(users);
    }

    /**
     * 检查用户名是否可用
     */
    @Transactional(readOnly = true)
    public boolean isUsernameAvailable(String username) {
        Username usernameVO = new Username(username);
        return userDomainService.isUsernameAvailable(usernameVO);
    }

    /**
     * 检查邮箱是否可用
     */
    @Transactional(readOnly = true)
    public boolean isEmailAvailable(String email) {
        Email emailVO = new Email(email);
        return userDomainService.isEmailAvailable(emailVO);
    }

    // 私有辅助方法

    private User findUserById(Long id) {
        Optional<User> userOpt = userRepository.findById(id);
        if (userOpt.isEmpty()) {
            throw new IllegalArgumentException("用户不存在");
        }
        return userOpt.get();
    }

    private UserId generateUserId() {
        // 这里可以实现自定义的ID生成策略
        // 暂时使用简单的时间戳方式
        return new UserId(System.currentTimeMillis());
    }
}
