package com.foxit.crm.system.application.service;

import com.foxit.crm.system.domain.model.aggregate.Role;
import com.foxit.crm.system.domain.model.valueobject.RoleId;
import com.foxit.crm.system.domain.model.valueobject.RoleCode;
import com.foxit.crm.system.domain.repository.RoleRepository;
import com.foxit.crm.system.domain.service.RoleDomainService;
import com.foxit.crm.system.application.dto.request.RoleCreateRequest;
import com.foxit.crm.system.application.dto.request.RoleUpdateRequest;
import com.foxit.crm.system.application.dto.response.RoleDetailResponse;
import com.foxit.crm.system.application.converter.RoleConverter;
import com.foxit.crm.shared.common.util.PageResult;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 角色应用服务
 * 负责编排角色管理业务流程
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Service
@Transactional
public class RoleApplicationService {

    private final RoleRepository roleRepository;
    private final RoleDomainService roleDomainService;
    private final RoleConverter roleConverter;

    public RoleApplicationService(RoleRepository roleRepository,
                                RoleDomainService roleDomainService,
                                RoleConverter roleConverter) {
        this.roleRepository = roleRepository;
        this.roleDomainService = roleDomainService;
        this.roleConverter = roleConverter;
    }

    /**
     * 创建角色
     */
    public Long createRole(RoleCreateRequest request) {
        // 1. 验证业务规则
        RoleCode roleCode = new RoleCode(request.getRoleCode());
        roleDomainService.validateRoleCreation(request.getRoleName(), roleCode);

        // 2. 生成角色ID
        RoleId roleId = generateRoleId();

        // 3. 创建角色聚合根
        Role role = roleConverter.fromCreateRequest(request, roleId);

        // 4. 分配权限
        if (request.getPermissionIds() != null && !request.getPermissionIds().isEmpty()) {
            roleDomainService.validatePermissions(request.getPermissionIds());
            role.assignPermissions(request.getPermissionIds());
        }

        // 5. 保存角色
        Role savedRole = roleRepository.save(role);

        return savedRole.getId();
    }

    /**
     * 更新角色信息
     */
    public void updateRole(Long id, RoleUpdateRequest request) {
        // 1. 查找角色
        Role role = findRoleById(id);

        // 2. 检查是否可以修改
        if (role.isSystemRole()) {
            throw new IllegalArgumentException("系统角色不能修改");
        }

        // 3. 更新角色信息
        roleConverter.updateFromRequest(role, request);

        // 4. 验证并分配权限
        if (request.getPermissionIds() != null) {
            roleDomainService.validatePermissions(request.getPermissionIds());
            role.assignPermissions(request.getPermissionIds());
        }

        // 5. 保存更新
        roleRepository.save(role);
    }

    /**
     * 启用角色
     */
    public void enableRole(Long id) {
        Role role = findRoleById(id);
        role.enable();
        roleRepository.save(role);
    }

    /**
     * 禁用角色
     */
    public void disableRole(Long id) {
        Role role = findRoleById(id);
        role.disable();
        roleRepository.save(role);
    }

    /**
     * 删除角色
     */
    public void deleteRole(Long id) {
        Role role = findRoleById(id);
        
        // 检查是否可以删除
        if (!role.canBeDeleted()) {
            throw new IllegalArgumentException("该角色不能被删除");
        }
        
        // 检查是否有用户使用该角色
        if (roleDomainService.hasUsersAssigned(role.getRoleId())) {
            throw new IllegalArgumentException("该角色已分配给用户，不能删除");
        }
        
        roleRepository.delete(role);
    }

    /**
     * 分配权限
     */
    public void assignPermissions(Long id, List<Long> permissionIds) {
        Role role = findRoleById(id);
        
        // 验证权限
        roleDomainService.validatePermissions(permissionIds);
        
        // 分配权限
        role.assignPermissions(permissionIds);
        
        roleRepository.save(role);
    }

    /**
     * 根据ID查询角色详情
     */
    @Transactional(readOnly = true)
    public RoleDetailResponse getRoleById(Long id) {
        Role role = findRoleById(id);
        return roleConverter.toDetailResponse(role);
    }

    /**
     * 分页查询角色列表
     */
    @Transactional(readOnly = true)
    public PageResult<RoleDetailResponse> getRolePage(int pageNum, int pageSize, String roleName, Integer status) {
        // 构建查询条件
        RoleRepository.RoleQuery query = new RoleRepository.RoleQuery();
        query.setRoleName(roleName);
        query.setStatus(status);

        // 执行查询
        PageResult<Role> rolePage = roleRepository.findPage(pageNum, pageSize, query);

        // 转换DTO
        List<RoleDetailResponse> responseList = roleConverter.toDetailResponseList(rolePage.getRecords());
        
        return PageResult.of(responseList, rolePage.getTotal(), 
            rolePage.getPageNum(), rolePage.getPageSize());
    }

    /**
     * 查询所有启用的角色
     */
    @Transactional(readOnly = true)
    public List<RoleDetailResponse> getAllEnabledRoles() {
        List<Role> roles = roleRepository.findAllEnabled();
        return roleConverter.toDetailResponseList(roles);
    }

    /**
     * 检查角色编码是否可用
     */
    @Transactional(readOnly = true)
    public boolean isRoleCodeAvailable(String roleCode) {
        RoleCode code = new RoleCode(roleCode);
        return roleDomainService.isRoleCodeAvailable(code);
    }

    // 私有辅助方法

    private Role findRoleById(Long id) {
        Optional<Role> roleOpt = roleRepository.findById(id);
        if (roleOpt.isEmpty()) {
            throw new IllegalArgumentException("角色不存在");
        }
        return roleOpt.get();
    }

    private RoleId generateRoleId() {
        // 这里可以实现自定义的ID生成策略
        // 暂时使用简单的时间戳方式
        return new RoleId(System.currentTimeMillis());
    }
}
