package com.foxit.crm.system.application.converter;

import com.foxit.crm.system.domain.model.aggregate.Role;
import com.foxit.crm.system.domain.model.valueobject.RoleId;
import com.foxit.crm.system.domain.model.valueobject.RoleCode;
import com.foxit.crm.system.application.dto.request.RoleCreateRequest;
import com.foxit.crm.system.application.dto.request.RoleUpdateRequest;
import com.foxit.crm.system.application.dto.response.RoleDetailResponse;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色DTO转换器
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Component
public class RoleConverter {

    /**
     * 转换为详情响应DTO
     */
    public RoleDetailResponse toDetailResponse(Role role) {
        if (role == null) {
            return null;
        }

        RoleDetailResponse response = new RoleDetailResponse();
        response.setId(role.getId());
        response.setRoleId(role.getRoleId() != null ? role.getRoleId().getValue() : null);
        response.setRoleName(role.getRoleName());
        response.setRoleCode(role.getRoleCode() != null ? role.getRoleCode().getValue() : null);
        response.setDescription(role.getDescription());
        response.setStatus(role.getStatus());
        response.setStatusDesc(getStatusDesc(role.getStatus()));
        response.setRoleType(role.getRoleType());
        response.setRoleTypeDesc(getRoleTypeDesc(role.getRoleType()));
        response.setSortOrder(role.getSortOrder());
        response.setPermissionIds(role.getPermissionIds());
        response.setPermissionCount(role.getPermissionCount());
        response.setCreateTime(role.getCreateTime());
        response.setUpdateTime(role.getUpdateTime());
        response.setCreateBy(role.getCreateBy());
        response.setUpdateBy(role.getUpdateBy());
        response.setRemark(role.getRemark());

        return response;
    }

    /**
     * 批量转换为详情响应DTO
     */
    public List<RoleDetailResponse> toDetailResponseList(List<Role> roles) {
        if (roles == null) {
            return null;
        }
        return roles.stream()
                .map(this::toDetailResponse)
                .collect(Collectors.toList());
    }

    /**
     * 从创建请求转换为领域对象
     */
    public Role fromCreateRequest(RoleCreateRequest request, RoleId roleId) {
        if (request == null) {
            return null;
        }

        RoleCode roleCode = new RoleCode(request.getRoleCode());
        
        Role role = new Role(roleId, request.getRoleName(), roleCode, request.getDescription());
        role.setSortOrder(request.getSortOrder());

        return role;
    }

    /**
     * 更新领域对象
     */
    public void updateFromRequest(Role role, RoleUpdateRequest request) {
        if (role == null || request == null) {
            return;
        }

        role.updateInfo(request.getRoleName(), request.getDescription(), request.getSortOrder());
        role.setRemark(request.getRemark());
    }

    /**
     * 获取状态描述
     */
    private String getStatusDesc(Integer status) {
        if (status == null) {
            return null;
        }
        return status == 1 ? "启用" : "禁用";
    }

    /**
     * 获取角色类型描述
     */
    private String getRoleTypeDesc(Integer roleType) {
        if (roleType == null) {
            return null;
        }
        return roleType == 1 ? "系统角色" : "自定义角色";
    }
}
