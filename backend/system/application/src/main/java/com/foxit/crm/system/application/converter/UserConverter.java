package com.foxit.crm.system.application.converter;

import com.foxit.crm.system.domain.model.aggregate.User;
import com.foxit.crm.system.domain.model.valueobject.UserId;
import com.foxit.crm.system.domain.model.valueobject.Username;
import com.foxit.crm.system.domain.model.valueobject.Email;
import com.foxit.crm.system.application.dto.request.UserCreateRequest;
import com.foxit.crm.system.application.dto.request.UserUpdateRequest;
import com.foxit.crm.system.application.dto.response.UserDetailResponse;
import com.foxit.crm.system.application.dto.response.UserListResponse;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户DTO转换器
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Component
public class UserConverter {

    /**
     * 转换为详情响应DTO
     */
    public UserDetailResponse toDetailResponse(User user) {
        if (user == null) {
            return null;
        }

        UserDetailResponse response = new UserDetailResponse();
        response.setId(user.getId());
        response.setUserId(user.getUserId() != null ? user.getUserId().getValue() : null);
        response.setUsername(user.getUsername() != null ? user.getUsername().getValue() : null);
        response.setRealName(user.getRealName());
        response.setEmail(user.getEmail() != null ? user.getEmail().getValue() : null);
        response.setPhone(user.getPhone());
        response.setAvatar(user.getAvatar());
        response.setStatus(user.getStatus());
        response.setStatusDesc(getStatusDesc(user.getStatus()));
        response.setUserType(user.getUserType());
        response.setUserTypeDesc(getUserTypeDesc(user.getUserType()));
        response.setDeptId(user.getDeptId());
        response.setLastLoginTime(user.getLastLoginTime());
        response.setLastLoginIp(user.getLastLoginIp());
        response.setRemark(user.getRemark());
        response.setCreateTime(user.getCreateTime());
        response.setUpdateTime(user.getUpdateTime());
        response.setCreateBy(user.getCreateBy());
        response.setUpdateBy(user.getUpdateBy());

        return response;
    }

    /**
     * 转换为列表响应DTO
     */
    public UserListResponse toListResponse(User user) {
        if (user == null) {
            return null;
        }

        UserListResponse response = new UserListResponse();
        response.setId(user.getId());
        response.setUserId(user.getUserId() != null ? user.getUserId().getValue() : null);
        response.setUsername(user.getUsername() != null ? user.getUsername().getValue() : null);
        response.setRealName(user.getRealName());
        response.setEmail(user.getEmail() != null ? user.getEmail().getValue() : null);
        response.setPhone(user.getPhone());
        response.setAvatar(user.getAvatar());
        response.setStatus(user.getStatus());
        response.setStatusDesc(getStatusDesc(user.getStatus()));
        response.setUserType(user.getUserType());
        response.setUserTypeDesc(getUserTypeDesc(user.getUserType()));
        response.setLastLoginTime(user.getLastLoginTime());
        response.setCreateTime(user.getCreateTime());

        return response;
    }

    /**
     * 批量转换为列表响应DTO
     */
    public List<UserListResponse> toListResponse(List<User> users) {
        if (users == null) {
            return null;
        }
        return users.stream()
                .map(this::toListResponse)
                .collect(Collectors.toList());
    }

    /**
     * 从创建请求转换为领域对象
     */
    public User fromCreateRequest(UserCreateRequest request, UserId userId) {
        if (request == null) {
            return null;
        }

        Username username = new Username(request.getUsername());
        Email email = new Email(request.getEmail());
        
        User user = new User(userId, username, null, request.getRealName(), email);
        user.setPhone(request.getPhone());
        user.setUserType(request.getUserType());
        user.setDeptId(request.getDeptId());
        user.setRemark(request.getRemark());

        return user;
    }

    /**
     * 更新领域对象
     */
    public void updateFromRequest(User user, UserUpdateRequest request) {
        if (user == null || request == null) {
            return;
        }

        Email email = new Email(request.getEmail());
        user.updateProfile(request.getRealName(), email, request.getPhone(), request.getAvatar());
        user.setDeptId(request.getDeptId());
        user.setRemark(request.getRemark());
    }

    /**
     * 获取状态描述
     */
    private String getStatusDesc(Integer status) {
        if (status == null) {
            return null;
        }
        return status == 1 ? "启用" : "禁用";
    }

    /**
     * 获取用户类型描述
     */
    private String getUserTypeDesc(Integer userType) {
        if (userType == null) {
            return null;
        }
        return userType == 1 ? "管理员" : "普通用户";
    }
}
