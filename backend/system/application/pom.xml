<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.foxit.crm</groupId>
        <artifactId>system</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>system-application</artifactId>
    <name>SCRM-Next System Application</name>
    <description>SCRM-Next系统管理应用服务层</description>

    <dependencies>
        <!-- 依赖系统领域模块 -->
        <dependency>
            <groupId>com.foxit.crm</groupId>
            <artifactId>system-domain</artifactId>
        </dependency>

        <!-- 依赖共享模块 -->
        <dependency>
            <groupId>com.foxit.crm</groupId>
            <artifactId>shared-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.foxit.crm</groupId>
            <artifactId>shared-common</artifactId>
        </dependency>

        <!-- Spring Framework -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>

        <!-- Validation -->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>

        <!-- Utilities -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>
