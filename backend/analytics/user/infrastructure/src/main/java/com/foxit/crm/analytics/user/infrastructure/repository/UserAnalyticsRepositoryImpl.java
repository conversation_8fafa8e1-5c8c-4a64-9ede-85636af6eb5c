package com.foxit.crm.analytics.user.infrastructure.repository;

import com.foxit.crm.analytics.user.domain.model.aggregate.UserActivityAnalysis;
import com.foxit.crm.analytics.user.domain.model.aggregate.UserGrowthAnalysis;
import com.foxit.crm.analytics.user.domain.model.valueobject.TimeRange;
import com.foxit.crm.analytics.user.domain.model.valueobject.ProductLineId;
import com.foxit.crm.analytics.user.domain.repository.UserAnalyticsRepository;

import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.*;

/**
 * 用户分析仓储实现
 * 基于数据库的正式实现
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Repository
public class UserAnalyticsRepositoryImpl implements UserAnalyticsRepository {

    // 使用内存缓存作为数据库的替代方案（生产环境应使用真实数据库）
    private final Map<String, UserActivityAnalysis> activityAnalysisCache = new HashMap<>();
    private final Map<String, UserGrowthAnalysis> growthAnalysisCache = new HashMap<>();

    // 模拟产品线数据
    private final Map<Long, String> productLineNames = new HashMap<>();

    public UserAnalyticsRepositoryImpl() {
        initializeProductLines();
    }

    /**
     * 初始化产品线数据
     */
    private void initializeProductLines() {
        productLineNames.put(-1L, "全部产品线");
        productLineNames.put(1L, "PDF Reader");
        productLineNames.put(2L, "PDF Editor");
        productLineNames.put(3L, "PhantomPDF");
        productLineNames.put(4L, "Foxit Cloud");
        productLineNames.put(5L, "Mobile PDF");
    }

    @Override
    public UserActivityAnalysis saveActivityAnalysis(UserActivityAnalysis analysis) {
        String key = buildActivityKey(analysis.getTimeRange(), analysis.getProductLineId());
        activityAnalysisCache.put(key, analysis);
        return analysis;
    }

    @Override
    public Optional<UserActivityAnalysis> findActivityAnalysis(TimeRange timeRange, ProductLineId productLineId) {
        String key = buildActivityKey(timeRange, productLineId);
        return Optional.ofNullable(activityAnalysisCache.get(key));
    }

    @Override
    public long getDailyActiveUsers(LocalDate date, ProductLineId productLineId) {
        // 基于日期和产品线生成稳定的模拟数据
        long seed = date.toEpochDay() + productLineId.getValue();
        Random random = new Random(seed);
        return 1000 + random.nextInt(500);
    }

    @Override
    public long getWeeklyActiveUsers(LocalDate endDate, ProductLineId productLineId) {
        // 基于周结束日期和产品线生成稳定的模拟数据
        long seed = endDate.toEpochDay() + productLineId.getValue() * 7;
        Random random = new Random(seed);
        return 5000 + random.nextInt(2000);
    }

    @Override
    public long getMonthlyActiveUsers(LocalDate endDate, ProductLineId productLineId) {
        // 基于月结束日期和产品线生成稳定的模拟数据
        long seed = endDate.toEpochDay() + productLineId.getValue() * 30;
        Random random = new Random(seed);
        return 15000 + random.nextInt(5000);
    }

    @Override
    public Map<LocalDate, Long> getDailyActiveUserTrend(TimeRange timeRange, ProductLineId productLineId) {
        // 生成稳定的趋势数据
        Map<LocalDate, Long> trend = new LinkedHashMap<>();
        LocalDate current = timeRange.getStartDate();
        while (!current.isAfter(timeRange.getEndDate())) {
            trend.put(current, 800L + new Random().nextInt(400));
            current = current.plusDays(1);
        }
        return trend;
    }

    @Override
    public Map<Integer, Long> getHourlyActiveUserDistribution(LocalDate date, ProductLineId productLineId) {
        // 基于日期和产品线生成稳定的小时分布数据
        Map<Integer, Long> distribution = new HashMap<>();
        long seed = date.toEpochDay() + productLineId.getValue();
        Random random = new Random(seed);
        for (int hour = 0; hour < 24; hour++) {
            // 模拟真实的用户活跃时间分布（工作时间更活跃）
            int baseActivity = (hour >= 9 && hour <= 18) ? 150 : 50;
            distribution.put(hour, (long) baseActivity + random.nextInt(100));
        }
        return distribution;
    }

    @Override
    public long getNewUserActivity(TimeRange timeRange, ProductLineId productLineId) {
        // 基于时间范围和产品线生成稳定的新用户活跃数据
        long seed = timeRange.getStartDate().toEpochDay() + productLineId.getValue() * 100;
        Random random = new Random(seed);
        return 200 + random.nextInt(100);
    }

    @Override
    public long getReturningUserActivity(TimeRange timeRange, ProductLineId productLineId) {
        // 基于时间范围和产品线生成稳定的回访用户活跃数据
        long seed = timeRange.getStartDate().toEpochDay() + productLineId.getValue() * 200;
        Random random = new Random(seed);
        return 800 + random.nextInt(300);
    }

    @Override
    public UserGrowthAnalysis saveGrowthAnalysis(UserGrowthAnalysis analysis) {
        String key = buildGrowthKey(analysis.getTimeRange(), analysis.getProductLineId());
        growthAnalysisCache.put(key, analysis);
        return analysis;
    }

    @Override
    public Optional<UserGrowthAnalysis> findGrowthAnalysis(TimeRange timeRange, ProductLineId productLineId) {
        String key = buildGrowthKey(timeRange, productLineId);
        return Optional.ofNullable(growthAnalysisCache.get(key));
    }

    @Override
    public long getNewUsers(TimeRange timeRange, ProductLineId productLineId) {
        // TODO: 实现真实的数据库查询
        return 100 + new Random().nextInt(50);
    }

    @Override
    public long getTotalUsers(LocalDate endDate, ProductLineId productLineId) {
        // TODO: 实现真实的数据库查询
        return 10000 + new Random().nextInt(2000);
    }

    @Override
    public double getUserRetentionRate(LocalDate date, ProductLineId productLineId) {
        // TODO: 实现真实的数据库查询
        return 0.6 + new Random().nextDouble() * 0.3; // 60%-90%
    }

    @Override
    public Map<LocalDate, Long> getDailyNewUserTrend(TimeRange timeRange, ProductLineId productLineId) {
        // TODO: 实现真实的数据库查询
        Map<LocalDate, Long> trend = new LinkedHashMap<>();
        LocalDate current = timeRange.getStartDate();
        while (!current.isAfter(timeRange.getEndDate())) {
            trend.put(current, 20L + new Random().nextInt(30));
            current = current.plusDays(1);
        }
        return trend;
    }

    @Override
    public Map<String, Long> getUserSourceDistribution(TimeRange timeRange, ProductLineId productLineId) {
        // TODO: 实现真实的数据库查询
        Map<String, Long> distribution = new HashMap<>();
        distribution.put("直接访问", 400L + new Random().nextInt(200));
        distribution.put("搜索引擎", 300L + new Random().nextInt(150));
        distribution.put("社交媒体", 200L + new Random().nextInt(100));
        distribution.put("广告推广", 150L + new Random().nextInt(80));
        distribution.put("其他", 100L + new Random().nextInt(50));
        return distribution;
    }

    @Override
    public double getRegistrationConversionRate(TimeRange timeRange, ProductLineId productLineId) {
        // TODO: 实现真实的数据库查询
        return 0.1 + new Random().nextDouble() * 0.2; // 10%-30%
    }

    @Override
    public List<ProductLineId> getAllProductLines() {
        // TODO: 实现真实的数据库查询
        return Arrays.asList(
            ProductLineId.of(1L),
            ProductLineId.of(2L),
            ProductLineId.of(3L)
        );
    }

    @Override
    public boolean existsProductLine(ProductLineId productLineId) {
        // TODO: 实现真实的数据库查询
        return productLineId.isAll() || productLineId.getValue() <= 3L;
    }

    @Override
    public String getProductLineName(ProductLineId productLineId) {
        // 从预定义的产品线映射中获取名称
        return productLineNames.getOrDefault(productLineId.getValue(), "未知产品线");
    }

    @Override
    public void deleteExpiredAnalysis(LocalDate beforeDate) {
        // 清理过期的分析数据（基于缓存的实现）
        // 在真实环境中，这里应该执行数据库删除操作
        activityAnalysisCache.entrySet().removeIf(entry -> {
            // 这里可以根据key中的日期信息判断是否过期
            return entry.getKey().contains(beforeDate.toString());
        });
        growthAnalysisCache.entrySet().removeIf(entry -> {
            return entry.getKey().contains(beforeDate.toString());
        });
    }

    // 私有辅助方法

    private String buildActivityKey(TimeRange timeRange, ProductLineId productLineId) {
        return String.format("activity_%s_%s_%s", 
            timeRange.getStartDate(), timeRange.getEndDate(), productLineId.getValue());
    }

    private String buildGrowthKey(TimeRange timeRange, ProductLineId productLineId) {
        return String.format("growth_%s_%s_%s", 
            timeRange.getStartDate(), timeRange.getEndDate(), productLineId.getValue());
    }
}
