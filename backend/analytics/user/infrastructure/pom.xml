<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.foxit.crm</groupId>
        <artifactId>analytics-user</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>analytics-user-infrastructure</artifactId>
    <name>SCRM-Next User Analytics Infrastructure</name>
    <description>SCRM-Next用户分析基础设施层</description>

    <dependencies>
        <!-- 依赖用户分析领域模块 -->
        <dependency>
            <groupId>com.foxit.crm</groupId>
            <artifactId>analytics-user-domain</artifactId>
        </dependency>

        <!-- 依赖共享模块 -->
        <dependency>
            <groupId>com.foxit.crm</groupId>
            <artifactId>shared-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.foxit.crm</groupId>
            <artifactId>shared-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.foxit.crm</groupId>
            <artifactId>shared-common</artifactId>
        </dependency>

        <!-- Spring Boot Starters -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Database -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
        </dependency>

        <!-- Redis -->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>

        <!-- Utilities -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>
