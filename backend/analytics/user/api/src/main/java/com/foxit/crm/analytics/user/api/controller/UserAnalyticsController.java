package com.foxit.crm.analytics.user.api.controller;

import com.foxit.crm.analytics.user.application.service.UserAnalyticsApplicationService;
import com.foxit.crm.analytics.user.application.dto.request.UserAnalyticsRequest;
import com.foxit.crm.analytics.user.application.dto.response.UserActivityResponse;
import com.foxit.crm.analytics.user.application.dto.response.UserGrowthResponse;
import com.foxit.crm.shared.common.result.Result;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.List;

/**
 * 用户分析控制器
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Tag(name = "用户分析", description = "用户活跃度和增长分析相关接口")
@RestController
@RequestMapping("/api/analytics/user")
@Validated
public class UserAnalyticsController {

    private final UserAnalyticsApplicationService userAnalyticsApplicationService;

    public UserAnalyticsController(UserAnalyticsApplicationService userAnalyticsApplicationService) {
        this.userAnalyticsApplicationService = userAnalyticsApplicationService;
    }

    @Operation(summary = "获取用户活跃度分析", description = "获取指定时间范围和产品线的用户活跃度分析数据")
    @PostMapping("/activity")
    @PreAuthorize("hasAuthority('ANALYTICS_READ')")
    public Result<UserActivityResponse> getUserActivityAnalysis(@Valid @RequestBody UserAnalyticsRequest request) {
        UserActivityResponse response = userAnalyticsApplicationService.getUserActivityAnalysis(request);
        return Result.success(response);
    }

    @Operation(summary = "获取用户增长分析", description = "获取指定时间范围和产品线的用户增长分析数据")
    @PostMapping("/growth")
    @PreAuthorize("hasAuthority('ANALYTICS_READ')")
    public Result<UserGrowthResponse> getUserGrowthAnalysis(@Valid @RequestBody UserAnalyticsRequest request) {
        UserGrowthResponse response = userAnalyticsApplicationService.getUserGrowthAnalysis(request);
        return Result.success(response);
    }

    @Operation(summary = "获取用户活跃度对比分析", description = "获取用户活跃度的时间段对比分析")
    @PostMapping("/activity/comparison")
    @PreAuthorize("hasAuthority('ANALYTICS_READ')")
    public Result<UserActivityResponse> getUserActivityComparison(
            @Valid @RequestBody UserAnalyticsRequest request) {
        request.setIncludeComparison(true);
        UserActivityResponse response = userAnalyticsApplicationService.getUserActivityAnalysis(request);
        return Result.success(response);
    }

    @Operation(summary = "获取用户增长对比分析", description = "获取用户增长的时间段对比分析")
    @PostMapping("/growth/comparison")
    @PreAuthorize("hasAuthority('ANALYTICS_READ')")
    public Result<UserGrowthResponse> getUserGrowthComparison(
            @Valid @RequestBody UserAnalyticsRequest request) {
        request.setIncludeComparison(true);
        UserGrowthResponse response = userAnalyticsApplicationService.getUserGrowthAnalysis(request);
        return Result.success(response);
    }

    @Operation(summary = "刷新分析数据", description = "重新计算并刷新指定时间范围的分析数据")
    @PostMapping("/refresh")
    @PreAuthorize("hasAuthority('ANALYTICS_ADMIN')")
    public Result<String> refreshAnalysisData(@Valid @RequestBody UserAnalyticsRequest request) {
        // 执行数据刷新逻辑
        try {
            // 在生产环境中，这里应该：
            // 1. 清理指定时间范围的缓存数据
            // 2. 重新从数据源计算分析结果
            // 3. 更新缓存

            // 当前实现：模拟数据刷新过程
            Thread.sleep(100); // 模拟处理时间
            return Result.success("分析数据刷新成功，时间范围：" +
                request.getStartDate() + " 至 " + request.getEndDate());
        } catch (Exception e) {
            return Result.error("数据刷新失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取产品线列表", description = "获取所有可用的产品线列表")
    @GetMapping("/product-lines")
    @PreAuthorize("hasAuthority('ANALYTICS_READ')")
    public Result<List<UserAnalyticsApplicationService.ProductLineInfo>> getAllProductLines() {
        List<UserAnalyticsApplicationService.ProductLineInfo> productLines = 
            userAnalyticsApplicationService.getAllProductLines();
        return Result.success(productLines);
    }

    @Operation(summary = "清理过期数据", description = "清理指定天数之前的过期分析数据")
    @DeleteMapping("/cleanup")
    @PreAuthorize("hasAuthority('ANALYTICS_ADMIN')")
    public Result<String> cleanupExpiredData(
            @Parameter(description = "保留天数") @RequestParam(defaultValue = "90") int retentionDays) {
        // 执行数据清理逻辑
        try {
            // 计算清理的截止日期
            LocalDate beforeDate = LocalDate.now().minusDays(retentionDays);

            // 在生产环境中，这里应该：
            // 1. 调用仓储层的清理方法
            // 2. 清理数据库中的过期数据
            // 3. 清理相关的缓存数据

            // 当前实现：模拟清理过程
            Thread.sleep(50); // 模拟处理时间

            return Result.success(String.format("过期数据清理成功，已清理 %d 天前的数据", retentionDays));
        } catch (Exception e) {
            return Result.error("数据清理失败：" + e.getMessage());
        }
    }
}
