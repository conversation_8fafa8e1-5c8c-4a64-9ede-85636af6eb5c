package com.foxit.crm.analytics.user.application.converter;

import com.foxit.crm.analytics.user.domain.model.valueobject.TimeRange;
import com.foxit.crm.analytics.user.domain.model.valueobject.ProductLineId;
import com.foxit.crm.analytics.user.application.dto.request.UserAnalyticsRequest;
import com.foxit.crm.analytics.user.application.dto.response.UserActivityResponse;
import com.foxit.crm.analytics.user.application.dto.response.UserGrowthResponse;
import org.springframework.stereotype.Component;

/**
 * 用户分析DTO转换器（简化版本）
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Component
public class UserAnalyticsConverter {

    /**
     * 转换请求为TimeRange
     */
    public TimeRange toTimeRange(UserAnalyticsRequest request) {
        return new TimeRange(request.getStartDate(), request.getEndDate());
    }

    /**
     * 转换请求为ProductLineId
     */
    public ProductLineId toProductLineId(UserAnalyticsRequest request) {
        return request.getProductLineId() == -1L ? 
            ProductLineId.all() : ProductLineId.of(request.getProductLineId());
    }

    /**
     * 创建用户活跃度响应
     */
    public UserActivityResponse createActivityResponse(String timeRange, String productLineName) {
        UserActivityResponse response = new UserActivityResponse();
        response.setTimeRange(timeRange);
        response.setProductLineName(productLineName);

        // 创建示例指标数据
        response.setDailyActiveUsers(createSampleMetric(1200.0, 5.2));
        response.setWeeklyActiveUsers(createSampleMetric(6800.0, 3.8));
        response.setMonthlyActiveUsers(createSampleMetric(18500.0, 2.1));
        response.setStickinessRatio(createSampleMetric(0.65, 1.2));
        response.setNewUserActivity(createSampleMetric(320.0, 8.5));
        response.setReturningUserActivity(createSampleMetric(880.0, 4.2));

        // 设置分析结果
        response.setTrendAnalysis("用户活跃度呈稳定增长趋势，新用户活跃度表现良好");
        response.setPeakActiveHour(14); // 下午2点为活跃高峰
        response.setNeedsAlert(false);

        return response;
    }

    /**
     * 创建用户增长响应
     */
    public UserGrowthResponse createGrowthResponse(String timeRange, String productLineName) {
        UserGrowthResponse response = new UserGrowthResponse();
        response.setTimeRange(timeRange);
        response.setProductLineName(productLineName);

        // 创建示例指标数据
        response.setNewUsers(createGrowthMetric(150.0, 12.5));
        response.setTotalUsers(createGrowthMetric(12800.0, 8.2));
        response.setGrowthRate(createGrowthMetric(0.125, 15.3));
        response.setRetentionRate(createGrowthMetric(0.72, 3.1));
        response.setChurnRate(createGrowthMetric(0.08, -5.2));
        response.setRegistrationConversionRate(createGrowthMetric(0.18, 7.8));

        // 设置分析结果
        response.setGrowthTrend("用户增长保持健康态势，留存率稳步提升");
        response.setPrimaryUserSource("搜索引擎");
        response.setNeedsGrowthAlert(false);
        response.setGrowthHealthScore(85);

        return response;
    }

    /**
     * 创建示例活跃度指标
     */
    private UserActivityResponse.MetricDTO createSampleMetric(Double value, Double growthRate) {
        UserActivityResponse.MetricDTO metric = new UserActivityResponse.MetricDTO();
        metric.setValue(value);
        metric.setGrowthRate(growthRate);
        metric.setGrowthDescription(growthRate > 0 ? "增长" : "下降");
        metric.setTrendDirection(growthRate > 0 ? "上升" : "下降");
        return metric;
    }

    /**
     * 创建示例增长指标
     */
    private UserGrowthResponse.MetricDTO createGrowthMetric(Double value, Double growthRate) {
        UserGrowthResponse.MetricDTO metric = new UserGrowthResponse.MetricDTO();
        metric.setValue(value);
        metric.setGrowthRate(growthRate);
        metric.setGrowthDescription(growthRate > 0 ? "增长" : "下降");
        metric.setTrendDirection(growthRate > 0 ? "上升" : "下降");
        return metric;
    }
}
