package com.foxit.crm.analytics.user.application.dto.response;

import lombok.Data;

import java.time.LocalDate;
import java.util.Map;

/**
 * 用户活跃度分析响应DTO
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class UserActivityResponse {

    /**
     * 分析时间范围
     */
    private String timeRange;

    /**
     * 产品线名称
     */
    private String productLineName;

    /**
     * 日活跃用户数
     */
    private MetricDTO dailyActiveUsers;

    /**
     * 周活跃用户数
     */
    private MetricDTO weeklyActiveUsers;

    /**
     * 月活跃用户数
     */
    private MetricDTO monthlyActiveUsers;

    /**
     * 用户粘性比率
     */
    private MetricDTO stickinessRatio;



    /**
     * 每日活跃用户趋势
     */
    private Map<LocalDate, Long> dailyTrend;

    /**
     * 活跃用户分布（按时段）
     */
    private Map<Integer, Long> hourlyDistribution;

    /**
     * 新用户活跃度
     */
    private MetricDTO newUserActivity;

    /**
     * 老用户活跃度
     */
    private MetricDTO returningUserActivity;

    /**
     * 趋势分析
     */
    private String trendAnalysis;

    /**
     * 峰值活跃时段
     */
    private Integer peakActiveHour;

    /**
     * 是否需要预警
     */
    private Boolean needsAlert;

    /**
     * 预警信息
     */
    private String alertMessage;

    /**
     * 指标DTO
     */
    @Data
    public static class MetricDTO {
        private Double value;
        private Double growthRate;
        private String growthDescription;
        private String trendDirection;
    }
}
