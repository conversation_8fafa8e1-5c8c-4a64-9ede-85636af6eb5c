package com.foxit.crm.analytics.user.application.dto.request;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 用户分析请求DTO
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class UserAnalyticsRequest {

    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @NotNull(message = "结束日期不能为空")
    private LocalDate endDate;

    /**
     * 产品线ID（-1表示全部产品线）
     */
    private Long productLineId = -1L;

    /**
     * 分析类型：activity-活跃度分析，growth-增长分析
     */
    private String analysisType = "activity";

    /**
     * 是否包含对比数据
     */
    private Boolean includeComparison = false;

    /**
     * 对比时间范围类型：previous-上一周期，same_last_year-去年同期
     */
    private String comparisonType = "previous";
}
