package com.foxit.crm.analytics.user.application.service;

import com.foxit.crm.analytics.user.domain.model.valueobject.TimeRange;
import com.foxit.crm.analytics.user.domain.model.valueobject.ProductLineId;
import com.foxit.crm.analytics.user.domain.repository.UserAnalyticsRepository;
import com.foxit.crm.analytics.user.application.dto.request.UserAnalyticsRequest;
import com.foxit.crm.analytics.user.application.dto.response.UserActivityResponse;
import com.foxit.crm.analytics.user.application.dto.response.UserGrowthResponse;
import com.foxit.crm.analytics.user.application.converter.UserAnalyticsConverter;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;

/**
 * 用户分析应用服务（简化版本）
 * 负责编排用户分析业务流程
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Service
@Transactional
public class UserAnalyticsApplicationService {

    private final UserAnalyticsRepository userAnalyticsRepository;
    private final UserAnalyticsConverter userAnalyticsConverter;

    public UserAnalyticsApplicationService(UserAnalyticsRepository userAnalyticsRepository,
                                         UserAnalyticsConverter userAnalyticsConverter) {
        this.userAnalyticsRepository = userAnalyticsRepository;
        this.userAnalyticsConverter = userAnalyticsConverter;
    }

    /**
     * 获取用户活跃度分析（简化版本）
     */
    @Transactional(readOnly = true)
    public UserActivityResponse getUserActivityAnalysis(UserAnalyticsRequest request) {
        // 1. 转换请求参数
        TimeRange timeRange = userAnalyticsConverter.toTimeRange(request);
        ProductLineId productLineId = userAnalyticsConverter.toProductLineId(request);

        // 2. 获取产品线名称
        String productLineName = userAnalyticsRepository.getProductLineName(productLineId);

        // 3. 创建响应
        return userAnalyticsConverter.createActivityResponse(
            timeRange.toString(), productLineName);
    }

    /**
     * 获取用户增长分析（简化版本）
     */
    @Transactional(readOnly = true)
    public UserGrowthResponse getUserGrowthAnalysis(UserAnalyticsRequest request) {
        // 1. 转换请求参数
        TimeRange timeRange = userAnalyticsConverter.toTimeRange(request);
        ProductLineId productLineId = userAnalyticsConverter.toProductLineId(request);

        // 2. 获取产品线名称
        String productLineName = userAnalyticsRepository.getProductLineName(productLineId);

        // 3. 创建响应
        return userAnalyticsConverter.createGrowthResponse(
            timeRange.toString(), productLineName);
    }

    /**
     * 获取所有产品线列表
     */
    @Transactional(readOnly = true)
    public List<ProductLineInfo> getAllProductLines() {
        List<ProductLineId> productLineIds = userAnalyticsRepository.getAllProductLines();
        List<ProductLineInfo> result = new ArrayList<>();
        
        for (ProductLineId productLineId : productLineIds) {
            String name = userAnalyticsRepository.getProductLineName(productLineId);
            result.add(new ProductLineInfo(productLineId.getValue(), name));
        }
        
        return result;
    }

    /**
     * 产品线信息DTO
     */
    public static class ProductLineInfo {
        private final Long id;
        private final String name;

        public ProductLineInfo(Long id, String name) {
            this.id = id;
            this.name = name;
        }

        public Long getId() { return id; }
        public String getName() { return name; }
    }
}
