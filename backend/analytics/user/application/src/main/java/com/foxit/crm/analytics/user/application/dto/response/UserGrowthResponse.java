package com.foxit.crm.analytics.user.application.dto.response;

import lombok.Data;

import java.time.LocalDate;
import java.util.Map;

/**
 * 用户增长分析响应DTO
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class UserGrowthResponse {

    /**
     * 分析时间范围
     */
    private String timeRange;

    /**
     * 产品线名称
     */
    private String productLineName;

    /**
     * 新增用户数
     */
    private MetricDTO newUsers;

    /**
     * 总用户数
     */
    private MetricDTO totalUsers;

    /**
     * 用户增长率
     */
    private MetricDTO growthRate;

    /**
     * 用户留存率
     */
    private MetricDTO retentionRate;

    /**
     * 用户流失率
     */
    private MetricDTO churnRate;

    /**
     * 每日新增用户趋势
     */
    private Map<LocalDate, Long> dailyNewUserTrend;

    /**
     * 用户来源分布
     */
    private Map<String, Long> userSourceDistribution;

    /**
     * 用户注册转化率
     */
    private MetricDTO registrationConversionRate;

    /**
     * 增长趋势分析
     */
    private String growthTrend;

    /**
     * 主要用户来源
     */
    private String primaryUserSource;

    /**
     * 是否需要增长预警
     */
    private Boolean needsGrowthAlert;

    /**
     * 增长健康度评分（0-100）
     */
    private Integer growthHealthScore;

    /**
     * 预警信息
     */
    private String alertMessage;

    /**
     * 指标DTO
     */
    @Data
    public static class MetricDTO {
        private Double value;
        private Double growthRate;
        private String growthDescription;
        private String trendDirection;
    }
}
