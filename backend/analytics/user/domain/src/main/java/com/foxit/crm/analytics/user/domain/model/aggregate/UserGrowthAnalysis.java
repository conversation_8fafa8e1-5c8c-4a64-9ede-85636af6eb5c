package com.foxit.crm.analytics.user.domain.model.aggregate;

import com.foxit.crm.shared.domain.model.AggregateRoot;
import com.foxit.crm.analytics.user.domain.model.valueobject.TimeRange;
import com.foxit.crm.analytics.user.domain.model.valueobject.MetricValue;
import com.foxit.crm.analytics.user.domain.model.valueobject.ProductLineId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.Map;

/**
 * 用户增长分析聚合根
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserGrowthAnalysis extends AggregateRoot {

    /**
     * 分析时间范围
     */
    private TimeRange timeRange;

    /**
     * 产品线ID
     */
    private ProductLineId productLineId;

    /**
     * 新增用户数
     */
    private MetricValue newUsers;

    /**
     * 总用户数
     */
    private MetricValue totalUsers;

    /**
     * 用户增长率
     */
    private MetricValue growthRate;

    /**
     * 用户留存率（次日留存）
     */
    private MetricValue retentionRate;

    /**
     * 用户流失率
     */
    private MetricValue churnRate;

    /**
     * 每日新增用户趋势
     */
    private Map<LocalDate, Long> dailyNewUserTrend;

    /**
     * 用户来源分布
     */
    private Map<String, Long> userSourceDistribution;

    /**
     * 用户注册转化率
     */
    private MetricValue registrationConversionRate;

    /**
     * 构造函数
     */
    public UserGrowthAnalysis(TimeRange timeRange, ProductLineId productLineId) {
        this.timeRange = timeRange;
        this.productLineId = productLineId;
        setCreateInfo(null);
    }

    /**
     * 计算用户增长率
     */
    public void calculateGrowthRate() {
        if (newUsers != null && totalUsers != null && totalUsers.getValue() > 0) {
            double rate = newUsers.getValue() / totalUsers.getValue();
            this.growthRate = new MetricValue(rate, calculateGrowthRate(rate, 0.0));
        }
    }

    /**
     * 计算用户流失率
     */
    public void calculateChurnRate() {
        if (retentionRate != null) {
            double churn = 1.0 - retentionRate.getValue();
            this.churnRate = new MetricValue(churn, calculateGrowthRate(churn, 0.0));
        }
    }

    /**
     * 分析增长趋势
     */
    public GrowthTrend analyzeGrowthTrend() {
        if (dailyNewUserTrend == null || dailyNewUserTrend.size() < 7) {
            return GrowthTrend.INSUFFICIENT_DATA;
        }

        // 计算最近7天的平均增长
        var values = dailyNewUserTrend.values().stream().toList();
        int size = values.size();
        
        double recentAvg = values.subList(size - 7, size).stream()
                .mapToLong(Long::longValue).average().orElse(0.0);
        double previousAvg = values.subList(Math.max(0, size - 14), size - 7).stream()
                .mapToLong(Long::longValue).average().orElse(0.0);

        if (previousAvg == 0) {
            return recentAvg > 0 ? GrowthTrend.ACCELERATING : GrowthTrend.STAGNANT;
        }

        double changeRate = (recentAvg - previousAvg) / previousAvg;
        
        if (changeRate > 0.2) {
            return GrowthTrend.ACCELERATING;
        } else if (changeRate > 0.05) {
            return GrowthTrend.STEADY;
        } else if (changeRate > -0.05) {
            return GrowthTrend.STABLE;
        } else if (changeRate > -0.2) {
            return GrowthTrend.SLOWING;
        } else {
            return GrowthTrend.DECLINING;
        }
    }

    /**
     * 获取主要用户来源
     */
    public String getPrimaryUserSource() {
        if (userSourceDistribution == null || userSourceDistribution.isEmpty()) {
            return "未知";
        }

        return userSourceDistribution.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("未知");
    }

    /**
     * 检查是否需要增长预警
     */
    public boolean needsGrowthAlert() {
        // 如果新增用户下降超过30%，需要预警
        if (newUsers != null && newUsers.getGrowthRate() < -0.3) {
            return true;
        }

        // 如果用户流失率超过20%，需要预警
        if (churnRate != null && churnRate.getValue() > 0.2) {
            return true;
        }

        // 如果增长趋势为下降，需要预警
        if (analyzeGrowthTrend() == GrowthTrend.DECLINING) {
            return true;
        }

        return false;
    }

    /**
     * 获取增长健康度评分（0-100）
     */
    public int getGrowthHealthScore() {
        int score = 50; // 基础分

        // 新增用户增长率评分（30分）
        if (newUsers != null) {
            if (newUsers.getGrowthRate() > 0.2) {
                score += 30;
            } else if (newUsers.getGrowthRate() > 0.1) {
                score += 20;
            } else if (newUsers.getGrowthRate() > 0) {
                score += 10;
            } else if (newUsers.getGrowthRate() < -0.2) {
                score -= 20;
            }
        }

        // 留存率评分（20分）
        if (retentionRate != null) {
            if (retentionRate.getValue() > 0.8) {
                score += 20;
            } else if (retentionRate.getValue() > 0.6) {
                score += 15;
            } else if (retentionRate.getValue() > 0.4) {
                score += 10;
            } else {
                score -= 10;
            }
        }

        return Math.max(0, Math.min(100, score));
    }

    /**
     * 计算增长率
     */
    private double calculateGrowthRate(double current, double previous) {
        if (previous == 0) {
            return current > 0 ? 1.0 : 0.0;
        }
        return (current - previous) / previous;
    }

    /**
     * 增长趋势枚举
     */
    public enum GrowthTrend {
        ACCELERATING("加速增长"),
        STEADY("稳定增长"),
        STABLE("增长平稳"),
        SLOWING("增长放缓"),
        DECLINING("增长下降"),
        STAGNANT("增长停滞"),
        INSUFFICIENT_DATA("数据不足");

        private final String description;

        GrowthTrend(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
