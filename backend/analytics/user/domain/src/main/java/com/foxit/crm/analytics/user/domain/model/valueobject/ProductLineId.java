package com.foxit.crm.analytics.user.domain.model.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

/**
 * 产品线ID值对象
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Getter
@EqualsAndHashCode
@ToString
public class ProductLineId {

    private final Long value;

    public ProductLineId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("产品线ID不能为空或小于等于0");
        }
        this.value = value;
    }

    /**
     * 静态工厂方法
     */
    public static ProductLineId of(Long value) {
        return new ProductLineId(value);
    }

    /**
     * 全部产品线的特殊值
     */
    public static ProductLineId all() {
        return new ProductLineId(-1L);
    }

    /**
     * 检查是否为全部产品线
     */
    public boolean isAll() {
        return value.equals(-1L);
    }
}
