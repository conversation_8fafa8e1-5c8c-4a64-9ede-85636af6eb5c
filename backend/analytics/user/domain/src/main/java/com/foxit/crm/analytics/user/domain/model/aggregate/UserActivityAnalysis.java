package com.foxit.crm.analytics.user.domain.model.aggregate;

import com.foxit.crm.shared.domain.model.AggregateRoot;
import com.foxit.crm.analytics.user.domain.model.valueobject.TimeRange;
import com.foxit.crm.analytics.user.domain.model.valueobject.MetricValue;
import com.foxit.crm.analytics.user.domain.model.valueobject.ProductLineId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 用户活跃度分析聚合根
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserActivityAnalysis extends AggregateRoot {

    /**
     * 分析时间范围
     */
    private TimeRange timeRange;

    /**
     * 产品线ID
     */
    private ProductLineId productLineId;

    /**
     * 日活跃用户数（DAU）
     */
    private MetricValue dailyActiveUsers;

    /**
     * 周活跃用户数（WAU）
     */
    private MetricValue weeklyActiveUsers;

    /**
     * 月活跃用户数（MAU）
     */
    private MetricValue monthlyActiveUsers;

    /**
     * 用户粘性比率（DAU/MAU）
     */
    private MetricValue stickinessRatio;

    /**
     * 每日活跃用户趋势数据
     */
    private Map<LocalDate, Long> dailyTrend;

    /**
     * 活跃用户分布（按时段）
     */
    private Map<Integer, Long> hourlyDistribution;

    /**
     * 新用户活跃度
     */
    private MetricValue newUserActivity;

    /**
     * 老用户活跃度
     */
    private MetricValue returningUserActivity;

    /**
     * 构造函数
     */
    public UserActivityAnalysis(TimeRange timeRange, ProductLineId productLineId) {
        this.timeRange = timeRange;
        this.productLineId = productLineId;
        setCreateInfo(null);
    }

    /**
     * 计算用户粘性比率
     */
    public void calculateStickinessRatio() {
        if (dailyActiveUsers != null && monthlyActiveUsers != null && 
            monthlyActiveUsers.getValue() > 0) {
            double ratio = (double) dailyActiveUsers.getValue() / monthlyActiveUsers.getValue();
            this.stickinessRatio = new MetricValue(ratio, calculateGrowthRate(ratio, 0.0));
        }
    }

    /**
     * 分析活跃度趋势
     */
    public TrendAnalysis analyzeTrend() {
        if (dailyTrend == null || dailyTrend.isEmpty()) {
            return TrendAnalysis.STABLE;
        }

        List<Long> values = dailyTrend.values().stream().toList();
        if (values.size() < 2) {
            return TrendAnalysis.STABLE;
        }

        // 简单的趋势分析：比较前半段和后半段的平均值
        int midPoint = values.size() / 2;
        double firstHalfAvg = values.subList(0, midPoint).stream()
                .mapToLong(Long::longValue).average().orElse(0.0);
        double secondHalfAvg = values.subList(midPoint, values.size()).stream()
                .mapToLong(Long::longValue).average().orElse(0.0);

        double changeRate = (secondHalfAvg - firstHalfAvg) / firstHalfAvg;
        
        if (changeRate > 0.1) {
            return TrendAnalysis.INCREASING;
        } else if (changeRate < -0.1) {
            return TrendAnalysis.DECREASING;
        } else {
            return TrendAnalysis.STABLE;
        }
    }

    /**
     * 获取峰值活跃时段
     */
    public Integer getPeakActiveHour() {
        if (hourlyDistribution == null || hourlyDistribution.isEmpty()) {
            return null;
        }

        return hourlyDistribution.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);
    }

    /**
     * 检查是否需要预警
     */
    public boolean needsAlert() {
        // 如果DAU下降超过20%，需要预警
        if (dailyActiveUsers != null && dailyActiveUsers.getGrowthRate() < -0.2) {
            return true;
        }

        // 如果用户粘性低于10%，需要预警
        if (stickinessRatio != null && stickinessRatio.getValue() < 0.1) {
            return true;
        }

        return false;
    }

    /**
     * 计算增长率
     */
    private double calculateGrowthRate(double current, double previous) {
        if (previous == 0) {
            return current > 0 ? 1.0 : 0.0;
        }
        return (current - previous) / previous;
    }

    /**
     * 趋势分析枚举
     */
    public enum TrendAnalysis {
        INCREASING("上升"),
        DECREASING("下降"),
        STABLE("稳定");

        private final String description;

        TrendAnalysis(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
