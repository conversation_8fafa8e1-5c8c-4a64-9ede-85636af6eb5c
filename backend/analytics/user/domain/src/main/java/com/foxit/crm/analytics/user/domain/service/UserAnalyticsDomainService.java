package com.foxit.crm.analytics.user.domain.service;

import com.foxit.crm.analytics.user.domain.model.aggregate.UserActivityAnalysis;
import com.foxit.crm.analytics.user.domain.model.aggregate.UserGrowthAnalysis;
import com.foxit.crm.analytics.user.domain.model.valueobject.TimeRange;
import com.foxit.crm.analytics.user.domain.model.valueobject.MetricValue;
import com.foxit.crm.analytics.user.domain.model.valueobject.ProductLineId;
import com.foxit.crm.analytics.user.domain.repository.UserAnalyticsRepository;

import java.time.LocalDate;
import java.util.Map;

/**
 * 用户分析领域服务
 * 处理复杂的分析计算逻辑和业务规则
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public class UserAnalyticsDomainService {

    private final UserAnalyticsRepository userAnalyticsRepository;

    public UserAnalyticsDomainService(UserAnalyticsRepository userAnalyticsRepository) {
        this.userAnalyticsRepository = userAnalyticsRepository;
    }

    /**
     * 构建用户活跃度分析
     */
    public UserActivityAnalysis buildActivityAnalysis(TimeRange timeRange, ProductLineId productLineId) {
        UserActivityAnalysis analysis = new UserActivityAnalysis(timeRange, productLineId);

        // 获取基础数据
        LocalDate endDate = timeRange.getEndDate();
        long dau = userAnalyticsRepository.getDailyActiveUsers(endDate, productLineId);
        long wau = userAnalyticsRepository.getWeeklyActiveUsers(endDate, productLineId);
        long mau = userAnalyticsRepository.getMonthlyActiveUsers(endDate, productLineId);

        // 计算历史数据用于增长率计算
        LocalDate previousDate = endDate.minusDays(1);
        long previousDau = userAnalyticsRepository.getDailyActiveUsers(previousDate, productLineId);
        long previousWau = userAnalyticsRepository.getWeeklyActiveUsers(previousDate, productLineId);
        long previousMau = userAnalyticsRepository.getMonthlyActiveUsers(previousDate, productLineId);

        // 设置指标值
        analysis.setDailyActiveUsers(MetricValue.withGrowth(dau, previousDau));
        analysis.setWeeklyActiveUsers(MetricValue.withGrowth(wau, previousWau));
        analysis.setMonthlyActiveUsers(MetricValue.withGrowth(mau, previousMau));

        // 计算用户粘性比率
        analysis.calculateStickinessRatio();

        // 获取趋势数据
        Map<LocalDate, Long> dailyTrend = userAnalyticsRepository.getDailyActiveUserTrend(timeRange, productLineId);
        analysis.setDailyTrend(dailyTrend);

        // 获取小时分布
        Map<Integer, Long> hourlyDistribution = userAnalyticsRepository.getHourlyActiveUserDistribution(endDate, productLineId);
        analysis.setHourlyDistribution(hourlyDistribution);

        // 获取新老用户活跃度
        long newUserActivity = userAnalyticsRepository.getNewUserActivity(timeRange, productLineId);
        long returningUserActivity = userAnalyticsRepository.getReturningUserActivity(timeRange, productLineId);
        
        analysis.setNewUserActivity(MetricValue.of(newUserActivity));
        analysis.setReturningUserActivity(MetricValue.of(returningUserActivity));

        return analysis;
    }

    /**
     * 构建用户增长分析
     */
    public UserGrowthAnalysis buildGrowthAnalysis(TimeRange timeRange, ProductLineId productLineId) {
        UserGrowthAnalysis analysis = new UserGrowthAnalysis(timeRange, productLineId);

        // 获取基础数据
        LocalDate endDate = timeRange.getEndDate();
        long newUsers = userAnalyticsRepository.getNewUsers(timeRange, productLineId);
        long totalUsers = userAnalyticsRepository.getTotalUsers(endDate, productLineId);

        // 计算历史数据用于增长率计算
        TimeRange previousTimeRange = TimeRange.lastDays((int) timeRange.getDays());
        LocalDate previousEndDate = endDate.minusDays(timeRange.getDays());
        long previousNewUsers = userAnalyticsRepository.getNewUsers(previousTimeRange, productLineId);
        long previousTotalUsers = userAnalyticsRepository.getTotalUsers(previousEndDate, productLineId);

        // 设置指标值
        analysis.setNewUsers(MetricValue.withGrowth(newUsers, previousNewUsers));
        analysis.setTotalUsers(MetricValue.withGrowth(totalUsers, previousTotalUsers));

        // 计算增长率
        analysis.calculateGrowthRate();

        // 获取留存率和流失率
        double retentionRate = userAnalyticsRepository.getUserRetentionRate(endDate, productLineId);
        analysis.setRetentionRate(MetricValue.of(retentionRate));
        analysis.calculateChurnRate();

        // 获取趋势数据
        Map<LocalDate, Long> dailyNewUserTrend = userAnalyticsRepository.getDailyNewUserTrend(timeRange, productLineId);
        analysis.setDailyNewUserTrend(dailyNewUserTrend);

        // 获取用户来源分布
        Map<String, Long> userSourceDistribution = userAnalyticsRepository.getUserSourceDistribution(timeRange, productLineId);
        analysis.setUserSourceDistribution(userSourceDistribution);

        // 获取注册转化率
        double conversionRate = userAnalyticsRepository.getRegistrationConversionRate(timeRange, productLineId);
        analysis.setRegistrationConversionRate(MetricValue.of(conversionRate));

        return analysis;
    }

    /**
     * 比较两个时间段的活跃度
     */
    public ActivityComparison compareActivity(TimeRange currentPeriod, TimeRange previousPeriod, ProductLineId productLineId) {
        UserActivityAnalysis current = buildActivityAnalysis(currentPeriod, productLineId);
        UserActivityAnalysis previous = buildActivityAnalysis(previousPeriod, productLineId);

        return new ActivityComparison(current, previous);
    }

    /**
     * 比较两个时间段的增长情况
     */
    public GrowthComparison compareGrowth(TimeRange currentPeriod, TimeRange previousPeriod, ProductLineId productLineId) {
        UserGrowthAnalysis current = buildGrowthAnalysis(currentPeriod, productLineId);
        UserGrowthAnalysis previous = buildGrowthAnalysis(previousPeriod, productLineId);

        return new GrowthComparison(current, previous);
    }

    /**
     * 验证分析参数
     */
    public void validateAnalysisParameters(TimeRange timeRange, ProductLineId productLineId) {
        if (timeRange == null) {
            throw new IllegalArgumentException("时间范围不能为空");
        }

        if (productLineId == null) {
            throw new IllegalArgumentException("产品线ID不能为空");
        }

        if (!productLineId.isAll() && !userAnalyticsRepository.existsProductLine(productLineId)) {
            throw new IllegalArgumentException("产品线不存在");
        }

        if (timeRange.getDays() > 365) {
            throw new IllegalArgumentException("分析时间范围不能超过365天");
        }
    }

    /**
     * 活跃度比较结果
     */
    public static class ActivityComparison {
        private final UserActivityAnalysis current;
        private final UserActivityAnalysis previous;

        public ActivityComparison(UserActivityAnalysis current, UserActivityAnalysis previous) {
            this.current = current;
            this.previous = previous;
        }

        public UserActivityAnalysis getCurrent() { return current; }
        public UserActivityAnalysis getPrevious() { return previous; }

        public double getDauChangeRate() {
            if (previous.getDailyActiveUsers() == null || current.getDailyActiveUsers() == null) {
                return 0.0;
            }
            return calculateChangeRate(current.getDailyActiveUsers().getValue(), 
                                     previous.getDailyActiveUsers().getValue());
        }

        public double getMauChangeRate() {
            if (previous.getMonthlyActiveUsers() == null || current.getMonthlyActiveUsers() == null) {
                return 0.0;
            }
            return calculateChangeRate(current.getMonthlyActiveUsers().getValue(), 
                                     previous.getMonthlyActiveUsers().getValue());
        }

        private double calculateChangeRate(double current, double previous) {
            if (previous == 0) return current > 0 ? 1.0 : 0.0;
            return (current - previous) / previous;
        }
    }

    /**
     * 增长比较结果
     */
    public static class GrowthComparison {
        private final UserGrowthAnalysis current;
        private final UserGrowthAnalysis previous;

        public GrowthComparison(UserGrowthAnalysis current, UserGrowthAnalysis previous) {
            this.current = current;
            this.previous = previous;
        }

        public UserGrowthAnalysis getCurrent() { return current; }
        public UserGrowthAnalysis getPrevious() { return previous; }

        public double getNewUserChangeRate() {
            if (previous.getNewUsers() == null || current.getNewUsers() == null) {
                return 0.0;
            }
            return calculateChangeRate(current.getNewUsers().getValue(), 
                                     previous.getNewUsers().getValue());
        }

        public double getRetentionChangeRate() {
            if (previous.getRetentionRate() == null || current.getRetentionRate() == null) {
                return 0.0;
            }
            return calculateChangeRate(current.getRetentionRate().getValue(), 
                                     previous.getRetentionRate().getValue());
        }

        private double calculateChangeRate(double current, double previous) {
            if (previous == 0) return current > 0 ? 1.0 : 0.0;
            return (current - previous) / previous;
        }
    }
}
