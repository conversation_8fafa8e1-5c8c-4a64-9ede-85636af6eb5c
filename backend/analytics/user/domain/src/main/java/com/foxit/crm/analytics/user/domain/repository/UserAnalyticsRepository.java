package com.foxit.crm.analytics.user.domain.repository;

import com.foxit.crm.analytics.user.domain.model.aggregate.UserActivityAnalysis;
import com.foxit.crm.analytics.user.domain.model.aggregate.UserGrowthAnalysis;
import com.foxit.crm.analytics.user.domain.model.valueobject.TimeRange;
import com.foxit.crm.analytics.user.domain.model.valueobject.ProductLineId;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 用户分析仓储接口
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface UserAnalyticsRepository {

    // ============================
    // 用户活跃度分析
    // ============================

    /**
     * 保存用户活跃度分析
     */
    UserActivityAnalysis saveActivityAnalysis(UserActivityAnalysis analysis);

    /**
     * 查找用户活跃度分析
     */
    Optional<UserActivityAnalysis> findActivityAnalysis(TimeRange timeRange, ProductLineId productLineId);

    /**
     * 获取日活跃用户数
     */
    long getDailyActiveUsers(LocalDate date, ProductLineId productLineId);

    /**
     * 获取周活跃用户数
     */
    long getWeeklyActiveUsers(LocalDate endDate, ProductLineId productLineId);

    /**
     * 获取月活跃用户数
     */
    long getMonthlyActiveUsers(LocalDate endDate, ProductLineId productLineId);

    /**
     * 获取每日活跃用户趋势
     */
    Map<LocalDate, Long> getDailyActiveUserTrend(TimeRange timeRange, ProductLineId productLineId);

    /**
     * 获取活跃用户分布（按小时）
     */
    Map<Integer, Long> getHourlyActiveUserDistribution(LocalDate date, ProductLineId productLineId);

    /**
     * 获取新用户活跃度
     */
    long getNewUserActivity(TimeRange timeRange, ProductLineId productLineId);

    /**
     * 获取老用户活跃度
     */
    long getReturningUserActivity(TimeRange timeRange, ProductLineId productLineId);

    // ============================
    // 用户增长分析
    // ============================

    /**
     * 保存用户增长分析
     */
    UserGrowthAnalysis saveGrowthAnalysis(UserGrowthAnalysis analysis);

    /**
     * 查找用户增长分析
     */
    Optional<UserGrowthAnalysis> findGrowthAnalysis(TimeRange timeRange, ProductLineId productLineId);

    /**
     * 获取新增用户数
     */
    long getNewUsers(TimeRange timeRange, ProductLineId productLineId);

    /**
     * 获取总用户数
     */
    long getTotalUsers(LocalDate endDate, ProductLineId productLineId);

    /**
     * 获取用户留存率
     */
    double getUserRetentionRate(LocalDate date, ProductLineId productLineId);

    /**
     * 获取每日新增用户趋势
     */
    Map<LocalDate, Long> getDailyNewUserTrend(TimeRange timeRange, ProductLineId productLineId);

    /**
     * 获取用户来源分布
     */
    Map<String, Long> getUserSourceDistribution(TimeRange timeRange, ProductLineId productLineId);

    /**
     * 获取注册转化率
     */
    double getRegistrationConversionRate(TimeRange timeRange, ProductLineId productLineId);

    // ============================
    // 通用查询方法
    // ============================

    /**
     * 获取所有产品线列表
     */
    List<ProductLineId> getAllProductLines();

    /**
     * 检查产品线是否存在
     */
    boolean existsProductLine(ProductLineId productLineId);

    /**
     * 获取产品线名称
     */
    String getProductLineName(ProductLineId productLineId);

    /**
     * 批量删除过期的分析数据
     */
    void deleteExpiredAnalysis(LocalDate beforeDate);
}
