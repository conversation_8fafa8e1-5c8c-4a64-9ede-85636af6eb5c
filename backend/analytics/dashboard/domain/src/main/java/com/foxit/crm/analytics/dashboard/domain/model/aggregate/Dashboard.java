package com.foxit.crm.analytics.dashboard.domain.model.aggregate;

import com.foxit.crm.shared.domain.model.AggregateRoot;
import com.foxit.crm.analytics.dashboard.domain.model.valueobject.DashboardId;
import com.foxit.crm.analytics.dashboard.domain.model.valueobject.Widget;
import com.foxit.crm.analytics.dashboard.domain.model.valueobject.DashboardConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.ArrayList;
import java.util.Optional;

/**
 * 数据看板聚合根
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Dashboard extends AggregateRoot {

    /**
     * 看板ID（业务标识）
     */
    private DashboardId dashboardId;

    /**
     * 看板名称
     */
    private String name;

    /**
     * 看板描述
     */
    private String description;

    /**
     * 看板类型：overview-概览，user-用户分析，behavior-行为分析，business-业务分析
     */
    private String type;

    /**
     * 所属用户ID
     */
    private Long userId;

    /**
     * 是否为公共看板
     */
    private Boolean isPublic;

    /**
     * 看板配置
     */
    private DashboardConfig config;

    /**
     * 组件列表
     */
    private List<Widget> widgets;

    /**
     * 看板状态：active-活跃，inactive-非活跃，archived-已归档
     */
    private String status;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 构造函数
     */
    public Dashboard(DashboardId dashboardId, String name, String type, Long userId) {
        this.dashboardId = dashboardId;
        this.name = name;
        this.type = type;
        this.userId = userId;
        this.isPublic = false;
        this.status = "active";
        this.widgets = new ArrayList<>();
        this.config = DashboardConfig.defaultConfig();
        setCreateInfo(userId);
    }

    /**
     * 添加组件
     */
    public void addWidget(Widget widget) {
        if (widget == null) {
            throw new IllegalArgumentException("组件不能为空");
        }

        // 检查组件ID是否已存在
        if (widgets.stream().anyMatch(w -> w.getWidgetId().equals(widget.getWidgetId()))) {
            throw new IllegalArgumentException("组件ID已存在: " + widget.getWidgetId());
        }

        // 检查位置是否冲突
        if (hasPositionConflict(widget)) {
            throw new IllegalArgumentException("组件位置冲突");
        }

        widgets.add(widget);
        setUpdateInfo(userId);
    }

    /**
     * 移除组件
     */
    public void removeWidget(String widgetId) {
        boolean removed = widgets.removeIf(w -> w.getWidgetId().equals(widgetId));
        if (!removed) {
            throw new IllegalArgumentException("组件不存在: " + widgetId);
        }
        setUpdateInfo(userId);
    }

    /**
     * 更新组件
     */
    public void updateWidget(Widget updatedWidget) {
        Optional<Widget> existingWidget = widgets.stream()
                .filter(w -> w.getWidgetId().equals(updatedWidget.getWidgetId()))
                .findFirst();

        if (existingWidget.isEmpty()) {
            throw new IllegalArgumentException("组件不存在: " + updatedWidget.getWidgetId());
        }

        // 移除旧组件，添加新组件
        removeWidget(updatedWidget.getWidgetId());
        addWidget(updatedWidget);
    }

    /**
     * 移动组件位置
     */
    public void moveWidget(String widgetId, int newX, int newY) {
        Optional<Widget> widget = findWidget(widgetId);
        if (widget.isEmpty()) {
            throw new IllegalArgumentException("组件不存在: " + widgetId);
        }

        Widget movedWidget = widget.get().moveTo(newX, newY);
        
        // 检查新位置是否冲突
        if (hasPositionConflict(movedWidget, widgetId)) {
            throw new IllegalArgumentException("组件位置冲突");
        }

        updateWidget(movedWidget);
    }

    /**
     * 调整组件大小
     */
    public void resizeWidget(String widgetId, int newWidth, int newHeight) {
        Optional<Widget> widget = findWidget(widgetId);
        if (widget.isEmpty()) {
            throw new IllegalArgumentException("组件不存在: " + widgetId);
        }

        Widget resizedWidget = widget.get().resize(newWidth, newHeight);
        
        // 检查调整后是否冲突
        if (hasPositionConflict(resizedWidget, widgetId)) {
            throw new IllegalArgumentException("组件大小调整后位置冲突");
        }

        updateWidget(resizedWidget);
    }

    /**
     * 设置为公共看板
     */
    public void makePublic() {
        this.isPublic = true;
        setUpdateInfo(userId);
    }

    /**
     * 设置为私有看板
     */
    public void makePrivate() {
        this.isPublic = false;
        setUpdateInfo(userId);
    }

    /**
     * 归档看板
     */
    public void archive() {
        this.status = "archived";
        setUpdateInfo(userId);
    }

    /**
     * 激活看板
     */
    public void activate() {
        this.status = "active";
        setUpdateInfo(userId);
    }

    /**
     * 更新看板配置
     */
    public void updateConfig(DashboardConfig newConfig) {
        this.config = newConfig;
        setUpdateInfo(userId);
    }

    /**
     * 检查用户是否有访问权限
     */
    public boolean hasAccessPermission(Long requestUserId) {
        // 创建者总是有权限
        if (this.userId.equals(requestUserId)) {
            return true;
        }
        
        // 公共看板所有人都可以访问
        return this.isPublic;
    }

    /**
     * 检查用户是否有编辑权限
     */
    public boolean hasEditPermission(Long requestUserId) {
        // 只有创建者可以编辑
        return this.userId.equals(requestUserId);
    }

    /**
     * 获取组件数量
     */
    public int getWidgetCount() {
        return widgets.size();
    }

    /**
     * 检查是否为空看板
     */
    public boolean isEmpty() {
        return widgets.isEmpty();
    }

    /**
     * 检查是否为活跃状态
     */
    public boolean isActive() {
        return "active".equals(status);
    }

    /**
     * 检查是否已归档
     */
    public boolean isArchived() {
        return "archived".equals(status);
    }

    // 私有辅助方法

    private Optional<Widget> findWidget(String widgetId) {
        return widgets.stream()
                .filter(w -> w.getWidgetId().equals(widgetId))
                .findFirst();
    }

    private boolean hasPositionConflict(Widget widget) {
        return hasPositionConflict(widget, null);
    }

    private boolean hasPositionConflict(Widget widget, String excludeWidgetId) {
        return widgets.stream()
                .filter(w -> excludeWidgetId == null || !w.getWidgetId().equals(excludeWidgetId))
                .anyMatch(w -> w.overlaps(widget));
    }
}
