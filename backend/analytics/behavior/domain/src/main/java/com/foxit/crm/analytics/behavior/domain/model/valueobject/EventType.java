package com.foxit.crm.analytics.behavior.domain.model.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

/**
 * 事件类型值对象
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Getter
@EqualsAndHashCode
@ToString
public class EventType {

    private final String value;
    private final String displayName;
    private final EventCategory category;

    public EventType(String value, String displayName, EventCategory category) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("事件类型不能为空");
        }
        if (displayName == null || displayName.trim().isEmpty()) {
            throw new IllegalArgumentException("事件显示名称不能为空");
        }
        if (category == null) {
            throw new IllegalArgumentException("事件分类不能为空");
        }

        this.value = value.trim();
        this.displayName = displayName.trim();
        this.category = category;
    }

    /**
     * 静态工厂方法 - 用户行为事件
     */
    public static EventType userBehavior(String value, String displayName) {
        return new EventType(value, displayName, EventCategory.USER_BEHAVIOR);
    }

    /**
     * 静态工厂方法 - 业务事件
     */
    public static EventType business(String value, String displayName) {
        return new EventType(value, displayName, EventCategory.BUSINESS);
    }

    /**
     * 静态工厂方法 - 系统事件
     */
    public static EventType system(String value, String displayName) {
        return new EventType(value, displayName, EventCategory.SYSTEM);
    }

    /**
     * 预定义的常用事件类型
     */
    public static class Common {
        public static final EventType PAGE_VIEW = userBehavior("page_view", "页面浏览");
        public static final EventType CLICK = userBehavior("click", "点击");
        public static final EventType LOGIN = userBehavior("login", "登录");
        public static final EventType LOGOUT = userBehavior("logout", "登出");
        public static final EventType SEARCH = userBehavior("search", "搜索");
        public static final EventType DOWNLOAD = userBehavior("download", "下载");
        public static final EventType UPLOAD = userBehavior("upload", "上传");
        public static final EventType SHARE = userBehavior("share", "分享");
        public static final EventType PURCHASE = business("purchase", "购买");
        public static final EventType ORDER = business("order", "下单");
        public static final EventType PAYMENT = business("payment", "支付");
        public static final EventType REGISTER = business("register", "注册");
        public static final EventType ERROR = system("error", "错误");
        public static final EventType EXCEPTION = system("exception", "异常");
    }

    /**
     * 检查是否为用户行为事件
     */
    public boolean isUserBehavior() {
        return category == EventCategory.USER_BEHAVIOR;
    }

    /**
     * 检查是否为业务事件
     */
    public boolean isBusiness() {
        return category == EventCategory.BUSINESS;
    }

    /**
     * 检查是否为系统事件
     */
    public boolean isSystem() {
        return category == EventCategory.SYSTEM;
    }

    /**
     * 事件分类枚举
     */
    public enum EventCategory {
        USER_BEHAVIOR("用户行为"),
        BUSINESS("业务事件"),
        SYSTEM("系统事件");

        private final String description;

        EventCategory(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
