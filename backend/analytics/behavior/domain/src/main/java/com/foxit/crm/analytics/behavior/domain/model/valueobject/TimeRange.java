package com.foxit.crm.analytics.behavior.domain.model.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

/**
 * 时间范围值对象（行为分析模块）
 * 复用用户分析模块的TimeRange实现
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Getter
@EqualsAndHashCode
@ToString
public class TimeRange {

    private final LocalDate startDate;
    private final LocalDate endDate;

    public TimeRange(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            throw new IllegalArgumentException("开始时间和结束时间不能为空");
        }
        if (startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        this.startDate = startDate;
        this.endDate = endDate;
    }

    /**
     * 静态工厂方法 - 创建今天的时间范围
     */
    public static TimeRange today() {
        LocalDate today = LocalDate.now();
        return new TimeRange(today, today);
    }

    /**
     * 静态工厂方法 - 创建最近N天的时间范围
     */
    public static TimeRange lastDays(int days) {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(days - 1);
        return new TimeRange(startDate, endDate);
    }

    /**
     * 静态工厂方法 - 创建本周的时间范围
     */
    public static TimeRange thisWeek() {
        LocalDate today = LocalDate.now();
        LocalDate startOfWeek = today.minusDays(today.getDayOfWeek().getValue() - 1);
        return new TimeRange(startOfWeek, today);
    }

    /**
     * 静态工厂方法 - 创建本月的时间范围
     */
    public static TimeRange thisMonth() {
        LocalDate today = LocalDate.now();
        LocalDate startOfMonth = today.withDayOfMonth(1);
        return new TimeRange(startOfMonth, today);
    }

    /**
     * 获取天数差
     */
    public long getDays() {
        return ChronoUnit.DAYS.between(startDate, endDate) + 1;
    }

    /**
     * 检查是否包含指定日期
     */
    public boolean contains(LocalDate date) {
        return !date.isBefore(startDate) && !date.isAfter(endDate);
    }

    /**
     * 获取时间范围描述
     */
    public String getDescription() {
        if (startDate.equals(endDate)) {
            return startDate.toString();
        }
        return startDate + " 至 " + endDate;
    }
}
