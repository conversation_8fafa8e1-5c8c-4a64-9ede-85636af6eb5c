package com.foxit.crm.analytics.behavior.domain.model.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

/**
 * 指标值对象（行为分析模块）
 * 复用用户分析模块的MetricValue实现
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Getter
@EqualsAndHashCode
@ToString
public class MetricValue {

    private final double value;
    private final double growthRate;

    public MetricValue(double value, double growthRate) {
        this.value = value;
        this.growthRate = growthRate;
    }

    /**
     * 静态工厂方法 - 创建无增长率的指标值
     */
    public static MetricValue of(double value) {
        return new MetricValue(value, 0.0);
    }

    /**
     * 静态工厂方法 - 创建带增长率的指标值
     */
    public static MetricValue of(double value, double growthRate) {
        return new MetricValue(value, growthRate);
    }

    /**
     * 静态工厂方法 - 根据当前值和历史值计算增长率
     */
    public static MetricValue withGrowth(double currentValue, double previousValue) {
        double growthRate = calculateGrowthRate(currentValue, previousValue);
        return new MetricValue(currentValue, growthRate);
    }

    /**
     * 检查是否为正增长
     */
    public boolean isPositiveGrowth() {
        return growthRate > 0;
    }

    /**
     * 检查是否为负增长
     */
    public boolean isNegativeGrowth() {
        return growthRate < 0;
    }

    /**
     * 获取增长率百分比
     */
    public double getGrowthRatePercentage() {
        return growthRate * 100;
    }

    /**
     * 获取增长率描述
     */
    public String getGrowthDescription() {
        if (isPositiveGrowth()) {
            return String.format("增长 %.2f%%", getGrowthRatePercentage());
        } else if (isNegativeGrowth()) {
            return String.format("下降 %.2f%%", Math.abs(getGrowthRatePercentage()));
        } else {
            return "无变化";
        }
    }

    /**
     * 计算增长率
     */
    private static double calculateGrowthRate(double current, double previous) {
        if (previous == 0) {
            return current > 0 ? 1.0 : 0.0;
        }
        return (current - previous) / previous;
    }
}
