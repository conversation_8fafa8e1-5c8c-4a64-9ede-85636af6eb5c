package com.foxit.crm.analytics.behavior.domain.model.aggregate;

import com.foxit.crm.shared.domain.model.AggregateRoot;
import com.foxit.crm.analytics.behavior.domain.model.valueobject.EventType;
import com.foxit.crm.analytics.behavior.domain.model.valueobject.TimeRange;
import com.foxit.crm.analytics.behavior.domain.model.valueobject.MetricValue;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.List;

/**
 * 行为事件分析聚合根
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BehaviorEventAnalysis extends AggregateRoot {

    /**
     * 分析时间范围
     */
    private TimeRange timeRange;

    /**
     * 事件类型
     */
    private EventType eventType;

    /**
     * 产品线ID
     */
    private Long productLineId;

    /**
     * 事件总数
     */
    private MetricValue totalEvents;

    /**
     * 独立用户数（UV）
     */
    private MetricValue uniqueUsers;

    /**
     * 平均每用户事件数
     */
    private MetricValue avgEventsPerUser;

    /**
     * 事件转化率
     */
    private MetricValue conversionRate;

    /**
     * 每小时事件分布
     */
    private Map<Integer, Long> hourlyDistribution;

    /**
     * 每日事件趋势
     */
    private Map<String, Long> dailyTrend;

    /**
     * 事件来源分布
     */
    private Map<String, Long> sourceDistribution;

    /**
     * 设备类型分布
     */
    private Map<String, Long> deviceDistribution;

    /**
     * 地域分布（Top 10）
     */
    private Map<String, Long> regionDistribution;

    /**
     * 构造函数
     */
    public BehaviorEventAnalysis(TimeRange timeRange, EventType eventType, Long productLineId) {
        this.timeRange = timeRange;
        this.eventType = eventType;
        this.productLineId = productLineId;
        setCreateInfo(null);
    }

    /**
     * 计算平均每用户事件数
     */
    public void calculateAvgEventsPerUser() {
        if (totalEvents != null && uniqueUsers != null && uniqueUsers.getValue() > 0) {
            double avg = totalEvents.getValue() / uniqueUsers.getValue();
            this.avgEventsPerUser = new MetricValue(avg, calculateGrowthRate(avg, 0.0));
        }
    }

    /**
     * 分析事件趋势
     */
    public EventTrend analyzeEventTrend() {
        if (dailyTrend == null || dailyTrend.size() < 7) {
            return EventTrend.INSUFFICIENT_DATA;
        }

        List<Long> values = dailyTrend.values().stream().toList();
        int size = values.size();
        
        // 计算最近3天和前3天的平均值
        double recentAvg = values.subList(size - 3, size).stream()
                .mapToLong(Long::longValue).average().orElse(0.0);
        double previousAvg = values.subList(Math.max(0, size - 6), size - 3).stream()
                .mapToLong(Long::longValue).average().orElse(0.0);

        if (previousAvg == 0) {
            return recentAvg > 0 ? EventTrend.INCREASING : EventTrend.STABLE;
        }

        double changeRate = (recentAvg - previousAvg) / previousAvg;
        
        if (changeRate > 0.15) {
            return EventTrend.RAPIDLY_INCREASING;
        } else if (changeRate > 0.05) {
            return EventTrend.INCREASING;
        } else if (changeRate > -0.05) {
            return EventTrend.STABLE;
        } else if (changeRate > -0.15) {
            return EventTrend.DECREASING;
        } else {
            return EventTrend.RAPIDLY_DECREASING;
        }
    }

    /**
     * 获取峰值活跃时段
     */
    public Integer getPeakHour() {
        if (hourlyDistribution == null || hourlyDistribution.isEmpty()) {
            return null;
        }

        return hourlyDistribution.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);
    }

    /**
     * 获取主要事件来源
     */
    public String getPrimarySource() {
        if (sourceDistribution == null || sourceDistribution.isEmpty()) {
            return "未知";
        }

        return sourceDistribution.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("未知");
    }

    /**
     * 获取主要设备类型
     */
    public String getPrimaryDevice() {
        if (deviceDistribution == null || deviceDistribution.isEmpty()) {
            return "未知";
        }

        return deviceDistribution.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("未知");
    }

    /**
     * 检查是否需要预警
     */
    public boolean needsAlert() {
        // 如果事件数下降超过30%，需要预警
        if (totalEvents != null && totalEvents.getGrowthRate() < -0.3) {
            return true;
        }

        // 如果转化率下降超过20%，需要预警
        if (conversionRate != null && conversionRate.getGrowthRate() < -0.2) {
            return true;
        }

        // 如果趋势为快速下降，需要预警
        if (analyzeEventTrend() == EventTrend.RAPIDLY_DECREASING) {
            return true;
        }

        return false;
    }

    /**
     * 获取事件活跃度评分（0-100）
     */
    public int getActivityScore() {
        int score = 50; // 基础分

        // 事件增长率评分（30分）
        if (totalEvents != null) {
            if (totalEvents.getGrowthRate() > 0.2) {
                score += 30;
            } else if (totalEvents.getGrowthRate() > 0.1) {
                score += 20;
            } else if (totalEvents.getGrowthRate() > 0) {
                score += 10;
            } else if (totalEvents.getGrowthRate() < -0.2) {
                score -= 20;
            }
        }

        // 用户参与度评分（20分）
        if (avgEventsPerUser != null) {
            if (avgEventsPerUser.getValue() > 10) {
                score += 20;
            } else if (avgEventsPerUser.getValue() > 5) {
                score += 15;
            } else if (avgEventsPerUser.getValue() > 2) {
                score += 10;
            }
        }

        return Math.max(0, Math.min(100, score));
    }

    /**
     * 计算增长率
     */
    private double calculateGrowthRate(double current, double previous) {
        if (previous == 0) {
            return current > 0 ? 1.0 : 0.0;
        }
        return (current - previous) / previous;
    }

    /**
     * 事件趋势枚举
     */
    public enum EventTrend {
        RAPIDLY_INCREASING("快速上升"),
        INCREASING("上升"),
        STABLE("稳定"),
        DECREASING("下降"),
        RAPIDLY_DECREASING("快速下降"),
        INSUFFICIENT_DATA("数据不足");

        private final String description;

        EventTrend(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
