package com.foxit.crm.analytics.behavior.domain.repository;

import com.foxit.crm.analytics.behavior.domain.model.aggregate.BehaviorEventAnalysis;
import com.foxit.crm.analytics.behavior.domain.model.valueobject.EventType;
import com.foxit.crm.analytics.behavior.domain.model.valueobject.TimeRange;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 行为分析仓储接口
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface BehaviorAnalyticsRepository {

    // ============================
    // 行为事件分析
    // ============================

    /**
     * 保存行为事件分析
     */
    BehaviorEventAnalysis saveEventAnalysis(BehaviorEventAnalysis analysis);

    /**
     * 查找行为事件分析
     */
    Optional<BehaviorEventAnalysis> findEventAnalysis(TimeRange timeRange, EventType eventType, Long productLineId);

    /**
     * 获取事件总数
     */
    long getTotalEvents(TimeRange timeRange, EventType eventType, Long productLineId);

    /**
     * 获取独立用户数（UV）
     */
    long getUniqueUsers(TimeRange timeRange, EventType eventType, Long productLineId);

    /**
     * 获取事件转化率
     */
    double getConversionRate(TimeRange timeRange, EventType eventType, Long productLineId);

    /**
     * 获取每小时事件分布
     */
    Map<Integer, Long> getHourlyEventDistribution(LocalDate date, EventType eventType, Long productLineId);

    /**
     * 获取每日事件趋势
     */
    Map<String, Long> getDailyEventTrend(TimeRange timeRange, EventType eventType, Long productLineId);

    /**
     * 获取事件来源分布
     */
    Map<String, Long> getEventSourceDistribution(TimeRange timeRange, EventType eventType, Long productLineId);

    /**
     * 获取设备类型分布
     */
    Map<String, Long> getDeviceDistribution(TimeRange timeRange, EventType eventType, Long productLineId);

    /**
     * 获取地域分布（Top 10）
     */
    Map<String, Long> getRegionDistribution(TimeRange timeRange, EventType eventType, Long productLineId);

    // ============================
    // 事件类型管理
    // ============================

    /**
     * 获取所有事件类型
     */
    List<EventType> getAllEventTypes();

    /**
     * 获取指定分类的事件类型
     */
    List<EventType> getEventTypesByCategory(EventType.EventCategory category);

    /**
     * 检查事件类型是否存在
     */
    boolean existsEventType(EventType eventType);

    /**
     * 获取热门事件类型（按事件数量排序）
     */
    List<EventType> getPopularEventTypes(TimeRange timeRange, Long productLineId, int limit);

    // ============================
    // 漏斗分析
    // ============================

    /**
     * 获取漏斗转化数据
     */
    Map<String, Long> getFunnelConversion(List<EventType> funnelSteps, TimeRange timeRange, Long productLineId);

    /**
     * 获取漏斗各步骤的用户数
     */
    Map<String, Long> getFunnelUserCount(List<EventType> funnelSteps, TimeRange timeRange, Long productLineId);

    // ============================
    // 路径分析
    // ============================

    /**
     * 获取用户行为路径（最常见的N条路径）
     */
    List<UserPath> getCommonUserPaths(TimeRange timeRange, Long productLineId, int limit);

    /**
     * 获取指定起始事件的后续路径
     */
    Map<String, Long> getNextStepPaths(EventType startEvent, TimeRange timeRange, Long productLineId);

    // ============================
    // 留存分析
    // ============================

    /**
     * 获取事件留存率（N日留存）
     */
    Map<Integer, Double> getEventRetention(EventType eventType, LocalDate startDate, int days, Long productLineId);

    /**
     * 获取用户回访率
     */
    double getUserReturnRate(TimeRange timeRange, Long productLineId);

    // ============================
    // 通用查询方法
    // ============================

    /**
     * 批量删除过期的分析数据
     */
    void deleteExpiredAnalysis(LocalDate beforeDate);

    /**
     * 获取产品线名称
     */
    String getProductLineName(Long productLineId);

    /**
     * 用户路径数据类
     */
    class UserPath {
        private final List<String> steps;
        private final Long userCount;
        private final Double conversionRate;

        public UserPath(List<String> steps, Long userCount, Double conversionRate) {
            this.steps = steps;
            this.userCount = userCount;
            this.conversionRate = conversionRate;
        }

        public List<String> getSteps() { return steps; }
        public Long getUserCount() { return userCount; }
        public Double getConversionRate() { return conversionRate; }
    }
}
