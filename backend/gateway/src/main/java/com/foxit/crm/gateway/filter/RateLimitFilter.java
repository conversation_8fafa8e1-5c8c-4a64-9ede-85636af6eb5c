package com.foxit.crm.gateway.filter;

import com.foxit.crm.shared.common.result.Result;
import com.foxit.crm.shared.common.util.JsonUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 限流过滤器
 * 基于令牌桶算法实现API限流
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Component
public class RateLimitFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(RateLimitFilter.class);

    /**
     * 默认限流配置
     */
    private static final int DEFAULT_LIMIT = 100; // 每分钟100次请求
    private static final long WINDOW_SIZE = 60 * 1000; // 1分钟窗口

    /**
     * 用户请求计数器
     */
    private final ConcurrentHashMap<String, UserRateLimit> userLimits = new ConcurrentHashMap<>();

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        String clientId = getClientId(request);
        String requestURI = request.getRequestURI();
        
        // 检查是否需要限流
        if (shouldSkipRateLimit(requestURI)) {
            filterChain.doFilter(request, response);
            return;
        }

        // 获取限流配置
        int limit = getRateLimit(requestURI);
        
        // 检查是否超过限流
        if (isRateLimited(clientId, limit)) {
            handleRateLimitExceeded(response, clientId);
            return;
        }

        // 继续处理请求
        filterChain.doFilter(request, response);
    }

    /**
     * 获取客户端标识
     */
    private String getClientId(HttpServletRequest request) {
        // 优先使用用户ID
        Object userId = request.getAttribute("userId");
        if (userId != null) {
            return "user:" + userId;
        }

        // 使用IP地址作为备选
        String clientIp = getClientIp(request);
        return "ip:" + clientIp;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 检查是否需要跳过限流
     */
    private boolean shouldSkipRateLimit(String requestURI) {
        // 静态资源和健康检查接口跳过限流
        return requestURI.startsWith("/static/") ||
               requestURI.startsWith("/actuator/health") ||
               requestURI.equals("/favicon.ico");
    }

    /**
     * 获取接口限流配置
     */
    private int getRateLimit(String requestURI) {
        // 根据不同的接口设置不同的限流值
        if (requestURI.startsWith("/api/auth/")) {
            return 20; // 认证接口限制更严格
        }
        
        if (requestURI.startsWith("/api/analytics/")) {
            return 50; // 分析接口中等限制
        }
        
        return DEFAULT_LIMIT; // 默认限制
    }

    /**
     * 检查是否被限流
     */
    private boolean isRateLimited(String clientId, int limit) {
        long currentTime = System.currentTimeMillis();
        
        UserRateLimit userLimit = userLimits.computeIfAbsent(clientId, 
            k -> new UserRateLimit(currentTime));
        
        return !userLimit.allowRequest(currentTime, limit);
    }

    /**
     * 处理限流超出
     */
    private void handleRateLimitExceeded(HttpServletResponse response, String clientId) throws IOException {
        logger.warn("客户端 {} 触发限流", clientId);
        
        response.setStatus(429); // Too Many Requests
        response.setContentType("application/json;charset=UTF-8");
        response.setHeader("X-RateLimit-Limit", String.valueOf(DEFAULT_LIMIT));
        response.setHeader("X-RateLimit-Remaining", "0");
        response.setHeader("Retry-After", "60");
        
        Result<Void> result = Result.error(429, "请求过于频繁，请稍后再试");
        String jsonResponse = JsonUtils.toJson(result);
        
        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
    }

    /**
     * 用户限流状态
     */
    private static class UserRateLimit {
        private volatile long windowStart;
        private final AtomicInteger requestCount;

        public UserRateLimit(long windowStart) {
            this.windowStart = windowStart;
            this.requestCount = new AtomicInteger(0);
        }

        /**
         * 检查是否允许请求
         */
        public synchronized boolean allowRequest(long currentTime, int limit) {
            // 检查是否需要重置窗口
            if (currentTime - windowStart >= WINDOW_SIZE) {
                windowStart = currentTime;
                requestCount.set(0);
            }

            // 检查是否超过限制
            if (requestCount.get() >= limit) {
                return false;
            }

            // 增加请求计数
            requestCount.incrementAndGet();
            return true;
        }
    }
}
