package com.foxit.crm.gateway.filter;

import com.foxit.crm.shared.common.result.Result;
import com.foxit.crm.shared.common.util.JsonUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 认证过滤器
 * 负责JWT Token验证和用户身份认证
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Component
public class AuthenticationFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(AuthenticationFilter.class);

    /**
     * 不需要认证的路径（注意：由于context-path是/api，这里的路径不需要/api前缀）
     */
    private static final List<String> EXCLUDE_PATHS = Arrays.asList(
        "/public/**",
        "/auth/**",
        "/health",
        "/swagger-ui/**",
        "/v3/api-docs/**",
        "/actuator/**",
        "/favicon.ico",
        "/error"
    );

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        
        logger.debug("处理请求: {} {}", method, requestURI);

        // 检查是否需要跳过认证
        if (shouldSkipAuthentication(requestURI, method)) {
            filterChain.doFilter(request, response);
            return;
        }

        // 获取Authorization头
        String authHeader = request.getHeader("Authorization");
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            handleAuthenticationError(response, "缺少认证令牌");
            return;
        }

        try {
            // 提取Token
            String token = authHeader.substring(7);
            
            // 验证Token（这里应该调用JWT验证服务）
            if (!isValidToken(token)) {
                handleAuthenticationError(response, "认证令牌无效");
                return;
            }

            // 设置用户上下文（这里应该从Token中解析用户信息）
            setUserContext(request, token);

            // 继续处理请求
            filterChain.doFilter(request, response);

        } catch (Exception e) {
            logger.error("认证过程中发生错误", e);
            handleAuthenticationError(response, "认证失败");
        }
    }

    /**
     * 检查是否应该跳过认证
     */
    private boolean shouldSkipAuthentication(String requestURI, String method) {
        // OPTIONS请求跳过认证
        if ("OPTIONS".equals(method)) {
            return true;
        }

        // 由于context-path是/api，需要去掉前缀进行匹配
        final String pathToMatch;
        if (requestURI.startsWith("/api")) {
            pathToMatch = requestURI.substring(4); // 去掉 "/api" 前缀
        } else {
            pathToMatch = requestURI;
        }

        // 检查排除路径
        return EXCLUDE_PATHS.stream()
                .anyMatch(pattern -> matchesPattern(pathToMatch, pattern));
    }

    /**
     * 路径匹配
     */
    private boolean matchesPattern(String path, String pattern) {
        if (pattern.endsWith("/**")) {
            String prefix = pattern.substring(0, pattern.length() - 3);
            return path.startsWith(prefix);
        }
        return path.equals(pattern);
    }

    /**
     * 验证Token有效性
     */
    private boolean isValidToken(String token) {
        // 基本的Token格式验证
        if (token == null || token.trim().isEmpty()) {
            return false;
        }

        // 检查Token格式（JWT通常有三个部分，用.分隔）
        String[] parts = token.split("\\.");
        if (parts.length != 3) {
            return false;
        }

        // 在生产环境中，这里应该：
        // 1. 验证JWT签名
        // 2. 检查Token是否过期
        // 3. 验证Token是否在黑名单中
        // 4. 验证Token的issuer和audience

        // 当前实现：基本格式验证通过即认为有效
        return true;
    }

    /**
     * 设置用户上下文
     */
    private void setUserContext(HttpServletRequest request, String token) {
        // 解析Token并设置用户上下文
        try {
            // 在生产环境中，这里应该：
            // 1. 解析JWT Token获取payload
            // 2. 从payload中提取用户信息
            // 3. 验证用户信息的有效性

            // 当前实现：基于Token生成稳定的用户信息
            Long userId = Math.abs((long) token.hashCode()) % 1000 + 1;
            String username = "user_" + userId;

            // 设置到请求属性中，供后续业务逻辑使用
            request.setAttribute("userId", userId);
            request.setAttribute("username", username);
            request.setAttribute("token", token);

        } catch (Exception e) {
            // Token解析失败，设置默认用户信息
            request.setAttribute("userId", 1L);
            request.setAttribute("username", "guest");
        }
    }

    /**
     * 处理认证错误
     */
    private void handleAuthenticationError(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        
        Result<Void> result = Result.error(401, message);
        String jsonResponse = JsonUtils.toJson(result);
        
        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
    }
}
