*target/
*.settings/
*.classpath
*.project
*.log*
*_logs/
*qrcodelink/
*.zip
*gulpfile.js
.vscode/
.factorypath
.idea/
.serena/
.comate/
prompt/

# Spring Boot 配置文件（application-private.yml 现在可以提交，因为使用了 Jasypt 加密）
# 如果需要本地私有配置，可以创建 application-local.yml
**/application-local.yml

# Spring Boot 其他文件
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# IDE 文件
*.iws
*.iml
*.ipr
out/

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js (如果前端在同一个仓库)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
