{"name": "scrm-next-frontend", "version": "1.0.0", "description": "SCRM-Next数据分析平台前端", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.1.0", "axios": "^1.6.2", "dayjs": "^1.11.10", "echarts": "^5.6.0", "lodash-es": "^4.17.21", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-echarts": "^6.7.3", "vue-router": "^4.2.5"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.6.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "less": "^4.3.0", "prettier": "^3.1.1", "typescript": "~5.3.0", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.4.19", "vue-tsc": "^1.8.25"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}