/**
 * 系统枚举常量定义
 * 
 * 用于前端显示枚举值对应的文本描述，替代后端返回的冗余Text字段
 * 
 * <AUTHOR>
 * @since 2025-06-23
 */

// 通用状态枚举
export const STATUS_MAP = {
  0: '禁用',
  1: '启用'
} as const

export const STATUS_OPTIONS = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }
]

// 用户类型枚举
export const USER_TYPE_MAP = {
  1: '管理员',
  2: '普通用户'
} as const

export const USER_TYPE_OPTIONS = [
  { label: '管理员', value: 1 },
  { label: '普通用户', value: 2 }
]

// 系统配置类型枚举
export const CONFIG_TYPE_MAP = {
  1: '字符串',
  2: '数字',
  3: '布尔值',
  4: 'JSON'
} as const

export const CONFIG_TYPE_OPTIONS = [
  { label: '字符串', value: 1 },
  { label: '数字', value: 2 },
  { label: '布尔值', value: 3 },
  { label: 'JSON', value: 4 }
]

// 系统内置标识枚举
export const IS_SYSTEM_MAP = {
  0: '否',
  1: '是'
} as const

export const IS_SYSTEM_OPTIONS = [
  { label: '否', value: 0 },
  { label: '是', value: 1 }
]

// 权限类型枚举
export const PERMISSION_TYPE_MAP = {
  1: '菜单',
  2: '按钮',
  3: '接口'
} as const

export const PERMISSION_TYPE_OPTIONS = [
  { label: '菜单', value: 1 },
  { label: '按钮', value: 2 },
  { label: '接口', value: 3 }
]

// 产品线类型枚举
export const PRODUCT_LINE_TYPE_MAP = {
  1: '阅读器',
  2: '编辑器',
  3: '云服务',
  4: '工具类',
  5: '内容平台'
} as const

export const PRODUCT_LINE_TYPE_OPTIONS = [
  { label: '阅读器', value: 1 },
  { label: '编辑器', value: 2 },
  { label: '云服务', value: 3 },
  { label: '工具类', value: 4 },
  { label: '内容平台', value: 5 }
]

// 操作日志状态枚举
export const OPERATION_STATUS_MAP = {
  0: '失败',
  1: '成功'
} as const

export const OPERATION_STATUS_OPTIONS = [
  { label: '成功', value: 1 },
  { label: '失败', value: 0 }
]

// 产品版本状态枚举
export const VERSION_STATUS_MAP = {
  1: '开发中',
  2: '测试中',
  3: '预发布',
  4: '已发布',
  5: '已废弃'
} as const

export const VERSION_STATUS_OPTIONS = [
  { label: '开发中', value: 1 },
  { label: '测试中', value: 2 },
  { label: '预发布', value: 3 },
  { label: '已发布', value: 4 },
  { label: '已废弃', value: 5 }
]

// 工具函数：根据枚举值获取文本描述
export const getEnumText = <T extends Record<string | number, string>>(
  enumMap: T,
  value: string | number | null | undefined,
  defaultText = '未知'
): string => {
  if (value === null || value === undefined) {
    return defaultText
  }
  return enumMap[value] || defaultText
}

// 工具函数：根据枚举值获取对应的标签颜色
export const getStatusColor = (status: number | string): string => {
  const statusNum = Number(status)
  switch (statusNum) {
    case 1:
      return 'success'
    case 0:
      return 'error'
    default:
      return 'default'
  }
}

// 工具函数：根据用户类型获取对应的标签颜色
export const getUserTypeColor = (userType: number | string): string => {
  const typeNum = Number(userType)
  switch (typeNum) {
    case 1:
      return 'red'
    case 2:
      return 'blue'
    default:
      return 'default'
  }
}

// 工具函数：根据权限类型获取对应的标签颜色
export const getPermissionTypeColor = (permissionType: number | string): string => {
  const typeNum = Number(permissionType)
  switch (typeNum) {
    case 1:
      return 'blue'
    case 2:
      return 'green'
    case 3:
      return 'orange'
    default:
      return 'default'
  }
}
