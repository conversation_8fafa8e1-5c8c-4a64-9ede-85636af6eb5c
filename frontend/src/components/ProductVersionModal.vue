<template>
  <a-modal
    v-model:open="visible"
    :title="`版本管理 - ${productInfo?.name}`"
    width="1000px"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="version-management">
      <!-- 产品信息 -->
      <div class="product-info">
        <a-descriptions :column="3" size="small">
          <a-descriptions-item label="产品名称">{{ productInfo?.name }}</a-descriptions-item>
          <a-descriptions-item label="产品编码">{{ productInfo?.code }}</a-descriptions-item>
          <a-descriptions-item label="当前版本">
            <a-tag color="blue">v{{ productInfo?.currentVersion }}</a-tag>
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <a-divider />

      <!-- 操作工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <a-space>
            <a-button type="primary" @click="handleAddVersion">
              <template #icon><PlusOutlined /></template>
              新增版本
            </a-button>
            <a-button @click="handleBatchDelete" :disabled="!selectedRowKeys.length">
              <template #icon><DeleteOutlined /></template>
              批量删除 ({{ selectedRowKeys.length }})
            </a-button>
          </a-space>
        </div>
        <div class="toolbar-right">
          <a-space>
            <a-input-search
              v-model:value="searchText"
              placeholder="搜索版本号或描述"
              style="width: 250px"
              @search="handleSearch"
            />
            <a-button @click="loadVersionList">
              <template #icon><ReloadOutlined /></template>
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 版本列表 -->
      <a-table
        :columns="columns"
        :data-source="versionList"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        row-key="id"
        size="small"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'version'">
            <div class="version-info">
              <a-tag 
                :color="record.isCurrent ? 'blue' : 'default'"
                size="large"
              >
                v{{ record.version }}
                <a-badge v-if="record.isCurrent" status="success" text="当前" style="margin-left: 8px;" />
              </a-tag>
            </div>
          </template>
          <template v-else-if="column.key === 'status'">
            <a-tag 
              :color="getStatusColor(record.status)"
              class="status-tag"
            >
              <template #icon>
                <CheckCircleOutlined v-if="record.status === 'released'" />
                <ClockCircleOutlined v-else-if="record.status === 'beta'" />
                <ExperimentOutlined v-else-if="record.status === 'alpha'" />
                <StopOutlined v-else />
              </template>
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'releaseDate'">
            <div class="time-info">
              <div>{{ formatDate(record.releaseDate) }}</div>
              <div class="time-ago">{{ getTimeAgo(record.releaseDate) }}</div>
            </div>
          </template>
          <template v-else-if="column.key === 'downloads'">
            <a-statistic 
              :value="record.downloads" 
              :precision="0"
              :value-style="{ fontSize: '14px' }"
            />
          </template>
          <template v-else-if="column.key === 'action'">
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="edit" @click="handleEditVersion(record)">
                    <EditOutlined />
                    编辑版本
                  </a-menu-item>
                  <a-menu-item key="view" @click="handleViewDetails(record)">
                    <EyeOutlined />
                    查看详情
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item 
                    key="set-current" 
                    @click="handleSetCurrent(record)"
                    :disabled="record.isCurrent"
                  >
                    <StarOutlined />
                    设为当前版本
                  </a-menu-item>
                  <a-menu-item key="download" @click="handleDownload(record)">
                    <DownloadOutlined />
                    下载版本
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="delete" @click="handleDeleteVersion(record)" class="danger-item">
                    <DeleteOutlined />
                    删除版本
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="text" size="small">
                操作 <DownOutlined />
              </a-button>
            </a-dropdown>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 版本表单弹窗 -->
    <VersionFormModal
      v-model="versionFormVisible"
      :version-data="currentVersion"
      :product-info="productInfo"
      @submit="handleVersionSubmit"
    />

    <!-- 版本详情弹窗 -->
    <VersionDetailModal
      v-model="versionDetailVisible"
      :version-data="currentVersion"
    />
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  DeleteOutlined,
  ReloadOutlined,
  EditOutlined,
  EyeOutlined,
  StarOutlined,
  DownloadOutlined,
  DownOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExperimentOutlined,
  StopOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType } from 'ant-design-vue'
import type { TableRowSelection } from 'ant-design-vue/es/table/interface'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import VersionFormModal from './VersionFormModal.vue'
import VersionDetailModal from './VersionDetailModal.vue'

dayjs.extend(relativeTime)

interface ProductInfo {
  id: number
  name: string
  code: string
  currentVersion: string
}

interface VersionRecord {
  id: number
  version: string
  description: string
  status: 'alpha' | 'beta' | 'released' | 'deprecated'
  releaseDate: string
  downloads: number
  isCurrent: boolean
  features: string[]
  bugFixes: string[]
  size: string
  platforms: string[]
}

const props = defineProps<{
  modelValue: boolean
  productInfo: ProductInfo | null
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 状态管理
const loading = ref(false)
const searchText = ref('')
const selectedRowKeys = ref<(string | number)[]>([])
const versionList = ref<VersionRecord[]>([])
const versionFormVisible = ref(false)
const versionDetailVisible = ref(false)
const currentVersion = ref<VersionRecord | null>(null)

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '版本号',
    key: 'version',
    width: 150
  },
  {
    title: '版本描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  },
  {
    title: '状态',
    key: 'status',
    width: 120
  },
  {
    title: '发布时间',
    key: 'releaseDate',
    width: 160
  },
  {
    title: '下载次数',
    key: 'downloads',
    width: 120
  },
  {
    title: '文件大小',
    dataIndex: 'size',
    key: 'size',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
]

// 行选择配置
const rowSelection: TableRowSelection<any> = {
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: (string | number)[], selectedRows: any[]) => {
    selectedRowKeys.value = keys
  }
}

// 版本列表数据通过API获取，不再使用模拟数据
/*
const mockVersionList: VersionRecord[] = [
  {
    id: 1,
    version: '2.1.0',
    description: '新增AI智能识别功能，优化用户体验',
    status: 'released',
    releaseDate: '2024-06-20T10:00:00Z',
    downloads: 15420,
    isCurrent: true,
    features: ['AI智能识别', '批量处理优化', '界面美化'],
    bugFixes: ['修复内存泄漏问题', '解决崩溃bug'],
    size: '125.6 MB',
    platforms: ['windows', 'mac', 'linux']
  },
  {
    id: 2,
    version: '2.0.5',
    description: '修复关键bug，提升稳定性',
    status: 'released',
    releaseDate: '2024-06-15T14:30:00Z',
    downloads: 28750,
    isCurrent: false,
    features: [],
    bugFixes: ['修复文件解析错误', '优化内存使用'],
    size: '123.2 MB',
    platforms: ['windows', 'mac', 'linux']
  },
  {
    id: 3,
    version: '2.1.0-beta.2',
    description: '测试版本，包含新功能预览',
    status: 'beta',
    releaseDate: '2024-06-10T09:15:00Z',
    downloads: 892,
    isCurrent: false,
    features: ['新功能预览', '性能优化'],
    bugFixes: [],
    size: '124.8 MB',
    platforms: ['windows', 'mac']
  }
]
*/

// 工具方法
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    alpha: 'orange',
    beta: 'blue',
    released: 'green',
    deprecated: 'red'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    alpha: '内测版',
    beta: '公测版',
    released: '正式版',
    deprecated: '已废弃'
  }
  return texts[status] || status
}

const formatDate = (dateStr: string) => {
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm')
}

const getTimeAgo = (dateStr: string) => {
  return dayjs(dateStr).fromNow()
}

// 方法定义
const loadVersionList = () => {
  loading.value = true
  try {
    // TODO: 替换为真实API调用
    // const response = await versionApi.getVersionList(props.productId)
    // versionList.value = response.data.list
    // pagination.total = response.data.total

    // 临时使用空数据，等待API实现
    versionList.value = []
    pagination.total = 0
    console.log('产品版本管理API待实现')
  } catch (error) {
    console.error('加载版本列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleAddVersion = () => {
  currentVersion.value = null
  versionFormVisible.value = true
}

const handleEditVersion = (record: any) => {
  currentVersion.value = record as VersionRecord
  versionFormVisible.value = true
}

const handleViewDetails = (record: any) => {
  currentVersion.value = record
  versionDetailVisible.value = true
}

const handleSetCurrent = (record: any) => {
  const versionRecord = record as VersionRecord
  // TODO: 调用API更新当前版本
  // 更新当前版本
  versionList.value.forEach(v => v.isCurrent = false)
  const index = versionList.value.findIndex(v => v.id === versionRecord.id)
  if (index > -1) {
    versionList.value[index].isCurrent = true
  }
  message.success(`已将 v${versionRecord.version} 设为当前版本`)
  loadVersionList()
}

const handleDownload = (record: any) => {
  const versionRecord = record as VersionRecord
  message.success(`开始下载 v${versionRecord.version}`)
}

const handleDeleteVersion = (record: any) => {
  if (record.isCurrent) {
    message.error('不能删除当前版本')
    return
  }
  message.success(`删除版本 v${record.version} 成功`)
  loadVersionList()
}

const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的版本')
    return
  }
  message.success(`批量删除 ${selectedRowKeys.value.length} 个版本成功`)
  selectedRowKeys.value = []
  loadVersionList()
}

const handleSearch = () => {
  loadVersionList()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadVersionList()
}

const handleVersionSubmit = (versionData: any) => {
  if (versionData.id) {
    // TODO: 调用API更新版本
    const index = versionList.value.findIndex(v => v.id === versionData.id)
    if (index > -1) {
      Object.assign(versionList.value[index], versionData)
    }
    message.success('版本更新成功')
  } else {
    // TODO: 调用API新增版本
    const newVersion = {
      ...versionData,
      id: Date.now(),
      downloads: 0,
      isCurrent: false
    }
    versionList.value.unshift(newVersion)
    message.success('版本创建成功')
  }
  loadVersionList()
}

const handleCancel = () => {
  visible.value = false
}

onMounted(() => {
  if (visible.value) {
    loadVersionList()
  }
})
</script>

<style scoped lang="less">
.version-management {
  .product-info {
    background: #fafafa;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 16px;
  }

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .version-info {
    .ant-badge {
      :deep(.ant-badge-status-text) {
        font-size: 12px;
      }
    }
  }

  .status-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
  }

  .time-info {
    .time-ago {
      font-size: 12px;
      color: #8c8c8c;
      margin-top: 2px;
    }
  }

  :deep(.ant-table) {
    .danger-item {
      color: #ff4d4f !important;

      &:hover {
        background-color: #fff2f0 !important;
      }
    }
  }
}
</style>
