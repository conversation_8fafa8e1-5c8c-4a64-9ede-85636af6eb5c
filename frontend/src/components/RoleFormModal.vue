<template>
  <a-modal
    v-model:open="visible"
    :title="isEdit ? '编辑角色' : '新增角色'"
    width="600px"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      class="role-form"
    >
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="角色名称" name="name">
            <a-input 
              v-model:value="formData.name" 
              placeholder="请输入角色名称"
              :maxlength="50"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="角色编码" name="code">
            <a-input 
              v-model:value="formData.code" 
              placeholder="请输入角色编码"
              :maxlength="50"
              :disabled="isEdit && formData.type === 'system'"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="角色类型" name="type">
        <a-radio-group v-model:value="formData.type" :disabled="isEdit">
          <a-radio value="custom">自定义角色</a-radio>
          <a-radio value="system">系统角色</a-radio>
        </a-radio-group>
        <div class="form-help">
          <small>系统角色由系统预定义，自定义角色可由管理员创建</small>
        </div>
      </a-form-item>

      <a-form-item label="角色描述" name="description">
        <a-textarea 
          v-model:value="formData.description" 
          placeholder="请输入角色描述"
          :rows="3"
          :maxlength="200"
          show-count
        />
      </a-form-item>

      <a-form-item label="角色状态" name="status">
        <a-radio-group v-model:value="formData.status">
          <a-radio value="active">启用</a-radio>
          <a-radio value="inactive">禁用</a-radio>
        </a-radio-group>
        <div class="form-help">
          <small>禁用的角色将无法分配给用户</small>
        </div>
      </a-form-item>

      <a-form-item label="数据权限范围" name="dataScope">
        <a-select 
          v-model:value="formData.dataScope" 
          placeholder="选择数据权限范围"
        >
          <a-select-option value="all">全部数据</a-select-option>
          <a-select-option value="dept">本部门数据</a-select-option>
          <a-select-option value="dept_and_child">本部门及下级部门数据</a-select-option>
          <a-select-option value="self">仅本人数据</a-select-option>
          <a-select-option value="custom">自定义数据权限</a-select-option>
        </a-select>
        <div class="form-help">
          <small>控制该角色可以访问的数据范围</small>
        </div>
      </a-form-item>

      <a-form-item v-if="formData.dataScope === 'custom'" label="自定义数据权限" name="customDataScope">
        <a-select 
          v-model:value="formData.customDataScope" 
          mode="multiple"
          placeholder="选择可访问的产品线"
          :options="productLineOptions"
        />
      </a-form-item>

      <a-form-item label="权限配置" name="permissions">
        <div class="permission-section">
          <div class="permission-header">
            <span>已配置权限：{{ formData.permissions.length }} 项</span>
            <a-button type="link" @click="handleConfigPermissions">
              配置权限
            </a-button>
          </div>
          <div class="permission-preview">
            <a-tag 
              v-for="permission in previewPermissions" 
              :key="permission.key"
              style="margin-bottom: 8px;"
            >
              {{ permission.title }}
            </a-tag>
            <div v-if="formData.permissions.length === 0" class="no-permissions">
              暂未配置权限
            </div>
          </div>
        </div>
      </a-form-item>

      <a-form-item label="备注信息" name="remark">
        <a-textarea 
          v-model:value="formData.remark" 
          placeholder="请输入备注信息（可选）"
          :rows="2"
          :maxlength="500"
          show-count
        />
      </a-form-item>
    </a-form>

    <!-- 权限配置弹窗 -->
    <RolePermissionModal
      v-model="permissionModalVisible"
      :role-info="formData as any"
      @submit="handlePermissionSubmit"
    />
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import RolePermissionModal from './RolePermissionModal.vue'

interface RoleFormData {
  id?: number
  name: string
  code: string
  description: string
  type: 'system' | 'custom'
  status: 'active' | 'inactive'
  dataScope: string
  customDataScope: string[]
  permissions: string[]
  remark: string
}

interface RoleRecord {
  id: number
  name: string
  code: string
  description: string
  type: 'system' | 'custom'
  status: 'active' | 'inactive'
  permissions: string[]
}

const props = defineProps<{
  modelValue: boolean
  roleData?: RoleRecord | null
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'submit': [data: RoleFormData]
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.roleData?.id)
const loading = ref(false)
const formRef = ref()
const permissionModalVisible = ref(false)

// 表单数据
const formData = reactive<RoleFormData>({
  name: '',
  code: '',
  description: '',
  type: 'custom',
  status: 'active',
  dataScope: 'dept',
  customDataScope: [],
  permissions: [],
  remark: ''
})

// 产品线选项
const productLineOptions = [
  { label: 'PDF Reader', value: 'pdf_reader' },
  { label: 'Foxit PDF Editor', value: 'pdf_editor' },
  { label: 'PhantomPDF', value: 'phantom_pdf' },
  { label: 'PDF SDK', value: 'pdf_sdk' },
  { label: 'eSign', value: 'esign' }
]

// 权限预览
const previewPermissions = computed(() => {
  const permissionMap: Record<string, string> = {
    'system:user': '用户管理',
    'system:role': '角色管理',
    'system:permission': '权限管理',
    'system:log': '操作日志',
    'system:config': '系统配置',
    'analytics:dashboard': '数据看板',
    'analytics:user': '用户分析',
    'analytics:behavior': '行为分析',
    'analytics:business': '商业分析',
    'analytics:report': '报表管理',
    'product:config': '产品配置',
    'product:version': '版本管理',
    'product:datasource': '数据源管理',
    'operation:private': '私域运营',
    'operation:activity': '活动运营',
    'operation:content': '内容管理'
  }
  
  return formData.permissions.map(key => ({
    key,
    title: permissionMap[key] || key
  }))
})

// 表单验证规则
const rules: Record<string, Rule[]> = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度为2-50个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入角色编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '角色编码只能包含字母、数字和下划线，且以字母开头', trigger: 'blur' },
    { min: 2, max: 50, message: '角色编码长度为2-50个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入角色描述', trigger: 'blur' },
    { max: 200, message: '角色描述不能超过200个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择角色类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择角色状态', trigger: 'change' }
  ],
  dataScope: [
    { required: true, message: '请选择数据权限范围', trigger: 'change' }
  ]
}

// 监听角色数据变化
watch(() => props.roleData, (newData) => {
  if (newData) {
    Object.assign(formData, {
      id: newData.id,
      name: newData.name,
      code: newData.code,
      description: newData.description,
      type: newData.type,
      status: newData.status,
      dataScope: 'dept',
      customDataScope: [],
      permissions: [...(newData.permissions || [])],
      remark: ''
    })
  } else {
    // 重置表单
    Object.assign(formData, {
      name: '',
      code: '',
      description: '',
      type: 'custom',
      status: 'active',
      dataScope: 'dept',
      customDataScope: [],
      permissions: [],
      remark: ''
    })
  }
}, { immediate: true })

// 方法定义
const handleConfigPermissions = () => {
  permissionModalVisible.value = true
}

const handlePermissionSubmit = (permissions: string[]) => {
  formData.permissions = permissions
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('submit', { ...formData })
    message.success(isEdit.value ? '角色更新成功' : '角色创建成功')
    visible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped lang="less">
.role-form {
  .form-help {
    margin-top: 4px;
    
    small {
      color: #8c8c8c;
      font-size: 12px;
    }
  }

  .permission-section {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 12px;

    .permission-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      font-size: 14px;
      font-weight: 500;
    }

    .permission-preview {
      min-height: 60px;
      
      .no-permissions {
        color: #8c8c8c;
        font-style: italic;
        text-align: center;
        padding: 20px 0;
      }
    }
  }
}
</style>
