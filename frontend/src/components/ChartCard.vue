<template>
  <a-card :title="title" class="chart-card" :class="{ 'chart-card-loading': loading }">
    <template #extra>
      <slot name="extra">
        <a-space>
          <a-tooltip title="刷新数据">
            <a-button type="text" size="small" :icon="h(ReloadOutlined)" @click="handleRefresh" />
          </a-tooltip>
          <a-tooltip title="全屏查看">
            <a-button type="text" size="small" :icon="h(ExpandOutlined)" @click="handleFullscreen" />
          </a-tooltip>
        </a-space>
      </slot>
    </template>
    
    <div class="chart-container" :style="{ height: height + 'px' }">
      <a-spin :spinning="loading" tip="加载中...">
        <div v-if="!loading && !error" class="chart-content">
          <slot />
        </div>
        
        <div v-else-if="error" class="chart-error">
          <a-result
            status="error"
            :title="errorTitle"
            :sub-title="errorMessage"
          >
            <template #extra>
              <a-button type="primary" @click="handleRefresh">
                重新加载
              </a-button>
            </template>
          </a-result>
        </div>
        
        <div v-else-if="!loading && isEmpty" class="chart-empty">
          <a-empty :description="emptyDescription" />
        </div>
      </a-spin>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { h } from 'vue'
import { ReloadOutlined, ExpandOutlined } from '@ant-design/icons-vue'

interface Props {
  title: string
  height?: number
  loading?: boolean
  error?: boolean
  errorTitle?: string
  errorMessage?: string
  isEmpty?: boolean
  emptyDescription?: string
}

interface Emits {
  (e: 'refresh'): void
  (e: 'fullscreen'): void
}

const props = withDefaults(defineProps<Props>(), {
  height: 300,
  loading: false,
  error: false,
  errorTitle: '加载失败',
  errorMessage: '图表数据加载失败，请稍后重试',
  isEmpty: false,
  emptyDescription: '暂无数据'
})

const emit = defineEmits<Emits>()

// 方法
const handleRefresh = () => {
  emit('refresh')
}

const handleFullscreen = () => {
  emit('fullscreen')
}
</script>

<style lang="less" scoped>
.chart-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #1890ff, #52c41a, #faad14);
    transition: left 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
  }

  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    transform: translateY(-4px);

    &::before {
      left: 100%;
    }
  }
  
  &.chart-card-loading {
    .chart-container {
      position: relative;
    }
  }
  
  .chart-container {
    position: relative;
    
    .chart-content {
      width: 100%;
      height: 100%;
    }
    
    .chart-error,
    .chart-empty {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      background: #fafafa;
      border-radius: 8px;
    }
  }
}

:deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;

  .ant-card-head-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

:deep(.ant-card-body) {
  padding: 16px;
}
</style>
