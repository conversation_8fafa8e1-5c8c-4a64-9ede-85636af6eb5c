<template>
  <div class="feature-list">
    <div v-for="(item, index) in items" :key="index" class="feature-item">
      <a-input
        v-model:value="items[index]"
        placeholder="请输入内容"
        @change="handleChange"
      >
        <template #suffix>
          <a-button
            type="text"
            size="small"
            danger
            @click="removeItem(index)"
          >
            <template #icon>
              <DeleteOutlined />
            </template>
          </a-button>
        </template>
      </a-input>
    </div>
    
    <a-button
      type="dashed"
      block
      @click="addItem"
      class="add-button"
    >
      <template #icon>
        <PlusOutlined />
      </template>
      添加项目
    </a-button>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'

const props = defineProps<{
  value?: string[]
}>()

const emit = defineEmits<{
  'update:value': [value: string[]]
}>()

const items = ref<string[]>([])

// 添加项目
const addItem = () => {
  items.value.push('')
  handleChange()
}

// 删除项目
const removeItem = (index: number) => {
  items.value.splice(index, 1)
  handleChange()
}

// 处理变更
const handleChange = () => {
  // 过滤空值
  const filteredItems = items.value.filter(item => item.trim() !== '')
  emit('update:value', filteredItems)
}

// 监听外部值变化
watch(() => props.value, (newVal) => {
  if (newVal) {
    items.value = [...newVal]
    // 如果没有项目，添加一个空项目
    if (items.value.length === 0) {
      items.value.push('')
    }
  } else {
    items.value = ['']
  }
}, { immediate: true })
</script>

<style scoped lang="less">
.feature-list {
  .feature-item {
    margin-bottom: 8px;
  }
  
  .add-button {
    margin-top: 8px;
  }
}
</style>
