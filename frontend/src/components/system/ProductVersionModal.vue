<template>
  <a-modal
    v-model:open="visible"
    :title="`${productInfo?.name} - 版本管理`"
    width="1200px"
    :footer="null"
    :destroy-on-close="true"
    @cancel="handleCancel"
  >
    <div class="version-management">
      <!-- 版本统计卡片 -->
      <div class="version-stats">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="总版本数"
                :value="versionStats.total"
                :value-style="{ color: '#1890ff' }"
              >
                <template #prefix>
                  <TagsOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="已发布"
                :value="versionStats.released"
                :value-style="{ color: '#52c41a' }"
              >
                <template #prefix>
                  <CheckCircleOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="开发中"
                :value="versionStats.development"
                :value-style="{ color: '#faad14' }"
              >
                <template #prefix>
                  <ClockCircleOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="总下载量"
                :value="versionStats.downloads"
                :value-style="{ color: '#722ed1' }"
              >
                <template #prefix>
                  <DownloadOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 操作工具栏 -->
      <div class="version-toolbar">
        <div class="toolbar-left">
          <a-space>
            <a-button type="primary" @click="handleAddVersion">
              <template #icon><PlusOutlined /></template>
              新增版本
            </a-button>
            <a-button @click="handleBatchAction" :disabled="!selectedVersions.length">
              <template #icon><SettingOutlined /></template>
              批量操作 ({{ selectedVersions.length }})
            </a-button>
          </a-space>
        </div>
        <div class="toolbar-right">
          <a-space>
            <a-select
              v-model:value="filterStatus"
              placeholder="版本状态"
              style="width: 120px"
              allow-clear
              @change="loadVersionList"
            >
              <a-select-option value="">全部状态</a-select-option>
              <a-select-option value="1">开发中</a-select-option>
              <a-select-option value="2">测试中</a-select-option>
              <a-select-option value="3">预发布</a-select-option>
              <a-select-option value="4">已发布</a-select-option>
              <a-select-option value="5">已废弃</a-select-option>
            </a-select>
            <a-input-search
              v-model:value="searchKeyword"
              placeholder="搜索版本号或名称"
              style="width: 200px"
              @search="loadVersionList"
            />
            <a-button @click="loadVersionList">
              <template #icon><ReloadOutlined /></template>
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 版本列表表格 -->
      <a-table
        :columns="versionColumns"
        :data-source="versionList"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection as any"
        row-key="id"
        size="small"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'version'">
            <div class="version-info">
              <div class="version-number">{{ record.versionNumber }}</div>
              <div class="version-name">{{ record.versionName }}</div>
            </div>
          </template>
          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.versionType)">
              {{ getTypeText(record.versionType) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'current'">
            <a-tag v-if="record.isCurrent" color="blue">当前版本</a-tag>
          </template>
          <template v-else-if="column.key === 'downloads'">
            <span>{{ formatNumber(record.downloadCount) }}</span>
          </template>
          <template v-else-if="column.key === 'releaseDate'">
            <span v-if="record.releaseDate">{{ formatDate(record.releaseDate) }}</span>
            <span v-else class="text-muted">未发布</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="edit" @click="handleEditVersion(record)">
                    <EditOutlined />
                    编辑版本
                  </a-menu-item>
                  <a-menu-item key="history" @click="handleViewHistory(record)">
                    <HistoryOutlined />
                    发布历史
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item 
                    v-if="record.status === 3" 
                    key="release" 
                    @click="handleReleaseVersion(record)"
                  >
                    <RocketOutlined />
                    发布版本
                  </a-menu-item>
                  <a-menu-item 
                    v-if="record.status === 4 && !record.isCurrent" 
                    key="setCurrent" 
                    @click="handleSetCurrent(record)"
                  >
                    <StarOutlined />
                    设为当前版本
                  </a-menu-item>
                  <a-menu-item 
                    v-if="record.status !== 5 && !record.isCurrent" 
                    key="deprecate" 
                    @click="handleDeprecateVersion(record)"
                  >
                    <StopOutlined />
                    废弃版本
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item 
                    v-if="record.canBeDeleted" 
                    key="delete" 
                    @click="handleDeleteVersion(record)"
                    class="danger-item"
                  >
                    <DeleteOutlined />
                    删除版本
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="text" size="small">
                操作 <DownOutlined />
              </a-button>
            </a-dropdown>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 版本表单模态框 -->
    <VersionFormModal
      v-model:open="versionFormVisible"
      :product-line-id="productInfo?.id"
      :version-data="currentVersion"
      @success="handleVersionFormSuccess"
    />

    <!-- 版本历史模态框 -->
    <VersionHistoryModal
      v-model:open="historyVisible"
      :version-data="currentVersion"
    />
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  TagsOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  DownloadOutlined,
  PlusOutlined,
  SettingOutlined,
  ReloadOutlined,
  EditOutlined,
  HistoryOutlined,
  RocketOutlined,
  StarOutlined,
  StopOutlined,
  DeleteOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType } from 'ant-design-vue'
import dayjs from 'dayjs'
import VersionFormModal from './VersionFormModal.vue'
import VersionHistoryModal from './VersionHistoryModal.vue'
import { productVersionApi } from '@/api/system/productVersion'

interface ProductInfo {
  id: number
  name: string
  code: string
}

interface VersionRecord {
  id: number
  versionNumber: string
  versionName: string
  status: number
  versionType: number
  isCurrent: boolean
  downloadCount: number
  releaseDate?: string
  createTime: string
  canBeDeleted: boolean
}

const props = defineProps<{
  open: boolean
  productInfo?: ProductInfo
}>()

const emit = defineEmits<{
  'update:open': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const loading = ref(false)
const versionList = ref<VersionRecord[]>([])
const selectedVersions = ref<number[]>([])
const filterStatus = ref('')
const searchKeyword = ref('')
const versionFormVisible = ref(false)
const historyVisible = ref(false)
const currentVersion = ref<any>(null)

// 版本统计
const versionStats = reactive({
  total: 0,
  released: 0,
  development: 0,
  downloads: 0
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const versionColumns: TableColumnsType = [
  {
    title: '版本信息',
    key: 'version',
    width: 200
  },
  {
    title: '类型',
    key: 'type',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '当前版本',
    key: 'current',
    width: 100
  },
  {
    title: '下载次数',
    key: 'downloads',
    width: 120
  },
  {
    title: '发布时间',
    key: 'releaseDate',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
]

// 表格行选择配置
const rowSelection = {
  selectedRowKeys: selectedVersions.value,
  onChange: (keys: number[]) => {
    selectedVersions.value = keys
  }
}

// 工具方法
const formatDate = (dateStr: string) => {
  return dayjs(dateStr).format('YYYY-MM-DD')
}

const formatNumber = (num: number) => {
  return num?.toLocaleString() || 0
}

const getStatusColor = (status: number) => {
  const colors: Record<number, string> = {
    1: 'orange',    // 开发中
    2: 'blue',      // 测试中
    3: 'purple',    // 预发布
    4: 'green',     // 已发布
    5: 'red'        // 已废弃
  }
  return colors[status] || 'default'
}

const getStatusText = (status: number) => {
  const texts: Record<number, string> = {
    1: '开发中',
    2: '测试中',
    3: '预发布',
    4: '已发布',
    5: '已废弃'
  }
  return texts[status] || '未知'
}

const getTypeColor = (type: number) => {
  const colors: Record<number, string> = {
    1: 'red',       // 主版本
    2: 'blue',      // 次版本
    3: 'green',     // 修订版
    4: 'orange'     // 预发布版
  }
  return colors[type] || 'default'
}

const getTypeText = (type: number) => {
  const texts: Record<number, string> = {
    1: '主版本',
    2: '次版本',
    3: '修订版',
    4: '预发布版'
  }
  return texts[type] || '未知'
}

// 加载版本列表
const loadVersionList = async () => {
  if (!props.productInfo?.id) return
  
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      size: pagination.pageSize,
      productLineId: props.productInfo.id,
      keyword: searchKeyword.value,
      status: filterStatus.value
    }
    
    const response = await productVersionApi.getVersionList(params)
    versionList.value = response.data.items as any
    pagination.total = response.data.total
    
    // 更新统计数据
    updateStats()
  } catch (error) {
    message.error('加载版本列表失败')
  } finally {
    loading.value = false
  }
}

// 更新统计数据
const updateStats = () => {
  versionStats.total = versionList.value.length
  versionStats.released = versionList.value.filter(v => v.status === 4).length
  versionStats.development = versionList.value.filter(v => v.status === 1).length
  versionStats.downloads = versionList.value.reduce((sum, v) => sum + v.downloadCount, 0)
}

// 事件处理
const handleCancel = () => {
  visible.value = false
}

const handleAddVersion = () => {
  currentVersion.value = null
  versionFormVisible.value = true
}

const handleEditVersion = (record: any) => {
  currentVersion.value = record as VersionRecord
  versionFormVisible.value = true
}

const handleViewHistory = (record: any) => {
  currentVersion.value = record as VersionRecord
  historyVisible.value = true
}

const handleReleaseVersion = async (record: any) => {
  const versionRecord = record as VersionRecord
  try {
    await productVersionApi.releaseVersion(versionRecord.id, '正式发布')
    message.success('版本发布成功')
    loadVersionList()
  } catch (error) {
    message.error('版本发布失败')
  }
}

const handleSetCurrent = async (record: any) => {
  const versionRecord = record as VersionRecord
  try {
    await productVersionApi.setCurrentVersion(versionRecord.id)
    message.success('设置当前版本成功')
    loadVersionList()
  } catch (error) {
    message.error('设置当前版本失败')
  }
}

const handleDeprecateVersion = async (record: any) => {
  const versionRecord = record as VersionRecord
  try {
    await productVersionApi.deprecateVersion(versionRecord.id, '版本废弃')
    message.success('版本废弃成功')
    loadVersionList()
  } catch (error) {
    message.error('版本废弃失败')
  }
}

const handleDeleteVersion = async (record: any) => {
  const versionRecord = record as VersionRecord
  try {
    await productVersionApi.deleteVersion(versionRecord.id)
    message.success('版本删除成功')
    loadVersionList()
  } catch (error) {
    message.error('版本删除失败')
  }
}

const handleBatchAction = () => {
  message.info('批量操作功能开发中...')
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadVersionList()
}

const handleVersionFormSuccess = () => {
  versionFormVisible.value = false
  loadVersionList()
}

// 监听产品信息变化
watch(() => props.productInfo, (newVal) => {
  if (newVal && visible.value) {
    loadVersionList()
  }
}, { immediate: true })

// 监听模态框显示状态
watch(visible, (newVal) => {
  if (newVal && props.productInfo) {
    loadVersionList()
  }
})
</script>

<style scoped lang="less">
.version-management {
  .version-stats {
    margin-bottom: 16px;
  }

  .version-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .version-info {
    .version-number {
      font-weight: 500;
      color: #262626;
    }
    .version-name {
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .text-muted {
    color: #8c8c8c;
  }

  :deep(.danger-item) {
    color: #ff4d4f !important;
    
    &:hover {
      background-color: #fff2f0 !important;
    }
  }
}
</style>
