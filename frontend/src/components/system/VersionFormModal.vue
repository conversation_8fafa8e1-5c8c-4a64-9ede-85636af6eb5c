<template>
  <a-modal
    v-model:open="visible"
    :title="isEdit ? '编辑版本' : '新增版本'"
    width="800px"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
    >
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="版本号" name="versionNumber">
            <a-input
              v-model:value="formData.versionNumber"
              placeholder="例如：1.0.0"
              :disabled="isEdit"
            >
              <template #suffix>
                <a-button 
                  v-if="!isEdit"
                  type="link" 
                  size="small" 
                  @click="handleSuggestVersion"
                >
                  建议
                </a-button>
              </template>
            </a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="版本类型" name="versionType">
            <a-select v-model:value="formData.versionType" placeholder="选择版本类型">
              <a-select-option :value="1">主版本</a-select-option>
              <a-select-option :value="2">次版本</a-select-option>
              <a-select-option :value="3">修订版</a-select-option>
              <a-select-option :value="4">预发布版</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="版本名称" name="versionName">
        <a-input
          v-model:value="formData.versionName"
          placeholder="例如：福昕阅读器GA版 v1.0.0"
        />
      </a-form-item>

      <a-form-item label="版本描述" name="description">
        <a-textarea
          v-model:value="formData.description"
          placeholder="请输入版本描述"
          :rows="3"
        />
      </a-form-item>

      <a-form-item label="发布说明" name="releaseNotes">
        <a-textarea
          v-model:value="formData.releaseNotes"
          placeholder="请输入发布说明"
          :rows="4"
        />
      </a-form-item>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="计划发布时间" name="plannedDate">
            <a-date-picker
              v-model:value="formData.plannedDate"
              style="width: 100%"
              placeholder="选择计划发布时间"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="文件大小(MB)" name="fileSize">
            <a-input-number
              v-model:value="formData.fileSizeMB"
              style="width: 100%"
              placeholder="文件大小"
              :min="0"
              :precision="2"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="下载地址" name="downloadUrl">
        <a-input
          v-model:value="formData.downloadUrl"
          placeholder="请输入下载地址"
        />
      </a-form-item>

      <a-form-item label="支持平台" name="platforms">
        <a-select
          v-model:value="formData.platforms"
          mode="multiple"
          placeholder="选择支持平台"
          style="width: 100%"
        >
          <a-select-option value="Windows">Windows</a-select-option>
          <a-select-option value="macOS">macOS</a-select-option>
          <a-select-option value="Linux">Linux</a-select-option>
          <a-select-option value="Web">Web</a-select-option>
          <a-select-option value="iOS">iOS</a-select-option>
          <a-select-option value="Android">Android</a-select-option>
        </a-select>
      </a-form-item>

      <a-tabs v-model:activeKey="activeTab">
        <a-tab-pane key="features" tab="新功能">
          <a-form-item name="features">
            <FeatureList v-model:value="formData.features" />
          </a-form-item>
        </a-tab-pane>
        <a-tab-pane key="bugFixes" tab="修复问题">
          <a-form-item name="bugFixes">
            <FeatureList v-model:value="formData.bugFixes" />
          </a-form-item>
        </a-tab-pane>
        <a-tab-pane key="breakingChanges" tab="破坏性变更">
          <a-form-item name="breakingChanges">
            <FeatureList v-model:value="formData.breakingChanges" />
          </a-form-item>
        </a-tab-pane>
        <a-tab-pane key="dependencies" tab="依赖信息">
          <a-form-item name="dependencies">
            <FeatureList v-model:value="formData.dependencies" />
          </a-form-item>
        </a-tab-pane>
        <a-tab-pane key="requirements" tab="系统要求">
          <a-form-item name="systemRequirements">
            <FeatureList v-model:value="formData.systemRequirements" />
          </a-form-item>
        </a-tab-pane>
      </a-tabs>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="MD5校验值" name="checksumMd5">
            <a-input
              v-model:value="formData.checksumMd5"
              placeholder="MD5校验值"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="SHA256校验值" name="checksumSha256">
            <a-input
              v-model:value="formData.checksumSha256"
              placeholder="SHA256校验值"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="formData.remark"
          placeholder="请输入备注信息"
          :rows="2"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance, Rule } from 'ant-design-vue/es/form'
import dayjs, { type Dayjs } from 'dayjs'
import FeatureList from './FeatureList.vue'
import { productVersionApi } from '@/api/system/productVersion'

interface VersionData {
  id?: number
  versionNumber: string
  versionName: string
  description: string
  releaseNotes: string
  versionType: number
  plannedDate?: string
  fileSize?: number
  downloadUrl: string
  platforms: string[]
  features: string[]
  bugFixes: string[]
  breakingChanges: string[]
  dependencies: string[]
  systemRequirements: string[]
  checksumMd5: string
  checksumSha256: string
  remark: string
}

const props = defineProps<{
  open: boolean
  productLineId?: number
  versionData?: VersionData | null
}>()

const emit = defineEmits<{
  'update:open': [value: boolean]
  success: []
}>()

// 响应式数据
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const loading = ref(false)
const formRef = ref<FormInstance>()
const activeTab = ref('features')

const isEdit = computed(() => !!props.versionData?.id)

// 表单数据
const formData = reactive({
  versionNumber: '',
  versionName: '',
  description: '',
  releaseNotes: '',
  versionType: 2,
  plannedDate: undefined as Dayjs | undefined,
  fileSizeMB: undefined as number | undefined,
  downloadUrl: '',
  platforms: [] as string[],
  features: [] as string[],
  bugFixes: [] as string[],
  breakingChanges: [] as string[],
  dependencies: [] as string[],
  systemRequirements: [] as string[],
  checksumMd5: '',
  checksumSha256: '',
  remark: ''
})

// 表单验证规则
const formRules: Record<string, Rule[]> = {
  versionNumber: [
    { required: true, message: '请输入版本号' },
    { pattern: /^\d+\.\d+\.\d+(-[a-zA-Z0-9]+(\.\d+)?)?$/, message: '版本号格式不正确，应为 x.y.z 或 x.y.z-alpha.1 格式' }
  ],
  description: [
    { required: true, message: '请输入版本描述' }
  ],
  versionType: [
    { required: true, message: '请选择版本类型' }
  ],
  checksumMd5: [
    { pattern: /^[a-fA-F0-9]{32}$/, message: 'MD5校验值格式不正确' }
  ],
  checksumSha256: [
    { pattern: /^[a-fA-F0-9]{64}$/, message: 'SHA256校验值格式不正确' }
  ]
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    versionNumber: '',
    versionName: '',
    description: '',
    releaseNotes: '',
    versionType: 2,
    plannedDate: null,
    fileSizeMB: null,
    downloadUrl: '',
    platforms: [],
    features: [],
    bugFixes: [],
    breakingChanges: [],
    dependencies: [],
    systemRequirements: [],
    checksumMd5: '',
    checksumSha256: '',
    remark: ''
  })
  formRef.value?.resetFields()
}

// 填充表单数据
const fillFormData = (data: VersionData) => {
  Object.assign(formData, {
    versionNumber: data.versionNumber,
    versionName: data.versionName,
    description: data.description,
    releaseNotes: data.releaseNotes,
    versionType: data.versionType,
    plannedDate: data.plannedDate ? dayjs(data.plannedDate) : null,
    fileSizeMB: data.fileSize ? Math.round(data.fileSize / 1024 / 1024 * 100) / 100 : null,
    downloadUrl: data.downloadUrl,
    platforms: data.platforms || [],
    features: data.features || [],
    bugFixes: data.bugFixes || [],
    breakingChanges: data.breakingChanges || [],
    dependencies: data.dependencies || [],
    systemRequirements: data.systemRequirements || [],
    checksumMd5: data.checksumMd5,
    checksumSha256: data.checksumSha256,
    remark: data.remark
  })
}

// 建议版本号
const handleSuggestVersion = async () => {
  if (!props.productLineId) return
  
  try {
    const response = await productVersionApi.suggestVersionNumber(
      props.productLineId, 
      formData.versionType.toString()
    )
    formData.versionNumber = response.data
    message.success('已生成建议版本号')
  } catch (error) {
    message.error('获取建议版本号失败')
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true

    const submitData = {
      productLineId: props.productLineId!,
      versionNumber: formData.versionNumber,
      versionName: formData.versionName,
      description: formData.description,
      releaseNotes: formData.releaseNotes,
      versionType: formData.versionType,
      plannedDate: formData.plannedDate?.format('YYYY-MM-DD HH:mm:ss'),
      fileSize: formData.fileSizeMB ? Math.round(formData.fileSizeMB * 1024 * 1024) : undefined,
      downloadUrl: formData.downloadUrl,
      platforms: formData.platforms,
      features: formData.features,
      bugFixes: formData.bugFixes,
      breakingChanges: formData.breakingChanges,
      dependencies: formData.dependencies,
      systemRequirements: formData.systemRequirements,
      checksumMd5: formData.checksumMd5,
      checksumSha256: formData.checksumSha256,
      remark: formData.remark
    }

    if (isEdit.value) {
      await productVersionApi.updateVersion(props.versionData!.id!, submitData)
      message.success('版本更新成功')
    } else {
      await productVersionApi.createVersion(submitData)
      message.success('版本创建成功')
    }

    emit('success')
  } catch (error) {
    if (error instanceof Error) {
      message.error(error.message)
    }
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  visible.value = false
}

// 监听版本数据变化
watch(() => props.versionData, (newVal) => {
  if (newVal) {
    fillFormData(newVal)
  } else {
    resetForm()
  }
}, { immediate: true })

// 监听模态框显示状态
watch(visible, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})
</script>

<style scoped lang="less">
// 样式可以根据需要添加
</style>
