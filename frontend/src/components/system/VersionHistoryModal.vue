<template>
  <a-modal
    v-model:open="visible"
    :title="`版本发布历史 - ${versionData?.versionNumber}`"
    width="900px"
    :footer="null"
    :destroy-on-close="true"
  >
    <div class="version-history">
      <a-timeline>
        <a-timeline-item
          v-for="history in historyList"
          :key="history.id"
          :color="getTimelineColor(history.actionType)"
        >
          <template #dot>
            <component :is="getActionIcon(history.actionType)" />
          </template>
          
          <div class="history-item">
            <div class="history-header">
              <div class="history-title">
                <a-tag :color="getActionColor(history.actionType)">
                  {{ history.actionTypeName }}
                </a-tag>
                <span class="history-description">{{ history.actionDescription }}</span>
              </div>
              <div class="history-time">
                {{ formatDateTime(history.actionTime) }}
              </div>
            </div>
            
            <div class="history-content">
              <div class="history-meta">
                <a-space>
                  <span>
                    <UserOutlined />
                    {{ history.actionByName }}
                  </span>
                  <span v-if="history.fromStatus && history.toStatus">
                    <SwapRightOutlined />
                    {{ getStatusName(history.fromStatus) }} → {{ getStatusName(history.toStatus) }}
                  </span>
                  <a-tag 
                    :color="getRiskColor(history.riskLevel)"
                    size="small"
                  >
                    {{ history.riskLevel }}风险
                  </a-tag>
                  <a-tag 
                    v-if="history.isImportantAction"
                    color="orange"
                    size="small"
                  >
                    重要操作
                  </a-tag>
                </a-space>
              </div>
              
              <div v-if="history.actionReason" class="history-reason">
                <strong>操作原因：</strong>{{ history.actionReason }}
              </div>
              
              <div v-if="history.remark" class="history-remark">
                <strong>备注：</strong>{{ history.remark }}
              </div>
              
              <div v-if="history.ipAddress" class="history-ip">
                <EnvironmentOutlined />
                IP: {{ history.ipAddress }}
              </div>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
      
      <div v-if="loading" class="loading-container">
        <a-spin />
      </div>
      
      <a-empty v-if="!loading && historyList.length === 0" description="暂无发布历史" />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  UserOutlined,
  SwapRightOutlined,
  EnvironmentOutlined,
  PlusCircleOutlined,
  EditOutlined,
  RocketOutlined,
  RollbackOutlined,
  StopOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import { productVersionApi } from '@/api/system/productVersion'

interface VersionData {
  id: number
  versionNumber: string
}

interface HistoryRecord {
  id: number
  actionType: number
  actionTypeName: string
  actionDescription: string
  actionReason: string
  actionBy: number
  actionByName: string
  actionTime: string
  fromStatus?: number
  toStatus?: number
  fromStatusName?: string
  toStatusName?: string
  riskLevel: string
  isImportantAction: boolean
  ipAddress: string
  remark?: string
}

const props = defineProps<{
  open: boolean
  versionData?: VersionData | null
}>()

const emit = defineEmits<{
  'update:open': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const loading = ref(false)
const historyList = ref<HistoryRecord[]>([])

// 加载发布历史
const loadHistory = async () => {
  if (!props.versionData?.id) return
  
  loading.value = true
  try {
    const response = await productVersionApi.getVersionHistory(props.versionData.id)
    historyList.value = response.data as any
  } catch (error) {
    message.error('加载发布历史失败')
  } finally {
    loading.value = false
  }
}

// 工具方法
const formatDateTime = (dateStr: string) => {
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss')
}

const getActionIcon = (actionType: number) => {
  const icons: Record<number, any> = {
    1: PlusCircleOutlined,    // 创建
    2: EditOutlined,          // 更新
    3: RocketOutlined,        // 发布
    4: RollbackOutlined,      // 撤回
    5: StopOutlined           // 废弃
  }
  return icons[actionType] || PlusCircleOutlined
}

const getActionColor = (actionType: number) => {
  const colors: Record<number, string> = {
    1: 'blue',      // 创建
    2: 'orange',    // 更新
    3: 'green',     // 发布
    4: 'purple',    // 撤回
    5: 'red'        // 废弃
  }
  return colors[actionType] || 'default'
}

const getTimelineColor = (actionType: number) => {
  const colors: Record<number, string> = {
    1: 'blue',      // 创建
    2: 'orange',    // 更新
    3: 'green',     // 发布
    4: 'purple',    // 撤回
    5: 'red'        // 废弃
  }
  return colors[actionType] || 'gray'
}

const getRiskColor = (riskLevel: string) => {
  const colors: Record<string, string> = {
    '低': 'green',
    '中': 'orange',
    '高': 'red'
  }
  return colors[riskLevel] || 'default'
}

const getStatusName = (status: number) => {
  const names: Record<number, string> = {
    1: '开发中',
    2: '测试中',
    3: '预发布',
    4: '已发布',
    5: '已废弃'
  }
  return names[status] || '未知'
}

// 监听版本数据变化
watch(() => props.versionData, (newVal) => {
  if (newVal && visible.value) {
    loadHistory()
  }
}, { immediate: true })

// 监听模态框显示状态
watch(visible, (newVal) => {
  if (newVal && props.versionData) {
    loadHistory()
  }
})
</script>

<style scoped lang="less">
.version-history {
  max-height: 600px;
  overflow-y: auto;
  
  .history-item {
    .history-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 8px;
      
      .history-title {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .history-description {
          font-weight: 500;
          color: #262626;
        }
      }
      
      .history-time {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
    
    .history-content {
      .history-meta {
        margin-bottom: 8px;
        
        span {
          display: inline-flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #8c8c8c;
        }
      }
      
      .history-reason,
      .history-remark {
        margin-bottom: 4px;
        font-size: 13px;
        color: #595959;
        
        strong {
          color: #262626;
        }
      }
      
      .history-ip {
        font-size: 12px;
        color: #8c8c8c;
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }
  
  .loading-container {
    display: flex;
    justify-content: center;
    padding: 40px;
  }
}
</style>
