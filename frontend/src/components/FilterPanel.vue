<template>
  <a-card class="filter-panel" size="small">
    <a-form layout="inline" :model="filterForm" @finish="handleSubmit">
      <a-form-item label="产品线" name="productLines">
        <a-select
          v-model:value="filterForm.productLines"
          mode="multiple"
          placeholder="选择产品线"
          style="width: 200px"
          :options="productLineOptions"
          allow-clear
        />
      </a-form-item>
      
      <a-form-item label="时间范围" name="timeRange">
        <a-range-picker
          v-model:value="filterForm.timeRange as any"
          :presets="timePresets"
          format="YYYY-MM-DD"
          style="width: 240px"
        />
      </a-form-item>
      
      <a-form-item v-if="showUserType" label="用户类型" name="userType">
        <a-select
          v-model:value="filterForm.userType"
          placeholder="选择用户类型"
          style="width: 120px"
          allow-clear
        >
          <a-select-option value="all">全部</a-select-option>
          <a-select-option value="new">新用户</a-select-option>
          <a-select-option value="old">老用户</a-select-option>
          <a-select-option value="paid">付费用户</a-select-option>
          <a-select-option value="free">免费用户</a-select-option>
        </a-select>
      </a-form-item>
      
      <a-form-item v-if="showChannel" label="渠道" name="channel">
        <a-select
          v-model:value="filterForm.channel"
          placeholder="选择渠道"
          style="width: 120px"
          allow-clear
        >
          <a-select-option value="all">全部</a-select-option>
          <a-select-option value="organic">自然流量</a-select-option>
          <a-select-option value="paid">付费推广</a-select-option>
          <a-select-option value="social">社交媒体</a-select-option>
        </a-select>
      </a-form-item>
      
      <!-- 自定义筛选项 -->
      <slot name="custom-filters" />
      
      <a-form-item>
        <a-space>
          <a-button type="primary" html-type="submit" :loading="loading">
            查询
          </a-button>
          <a-button @click="handleReset">
            重置
          </a-button>
          <a-button v-if="showExport" type="default" :icon="h(DownloadOutlined)" @click="handleExport">
            导出
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </a-card>
</template>

<script setup lang="ts">
import { h } from 'vue'
import { DownloadOutlined } from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'

interface FilterForm {
  productLines: string[]
  timeRange: [Dayjs, Dayjs] | null
  userType?: string
  channel?: string
  [key: string]: any
}

interface Props {
  loading?: boolean
  showUserType?: boolean
  showChannel?: boolean
  showExport?: boolean
  productLineOptions?: Array<{ label: string; value: string }>
}

interface Emits {
  (e: 'submit', filters: FilterForm): void
  (e: 'reset'): void
  (e: 'export'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showUserType: false,
  showChannel: false,
  showExport: false,
  productLineOptions: () => [
    { label: '福昕阅读器GA版', value: 'READER_GA' },
    { label: '福昕阅读器PLUS版', value: 'READER_PLUS' },
    { label: 'PDF编辑器个人版', value: 'EDITOR_PERSONAL' },
    { label: 'PDF编辑器专业版', value: 'EDITOR_PRO' },
    { label: 'PDF365', value: 'PDF365' }
  ]
})

const emit = defineEmits<Emits>()

// 响应式数据
const filterForm = reactive<FilterForm>({
  productLines: [],
  timeRange: [dayjs().subtract(30, 'day'), dayjs()],
  userType: undefined,
  channel: undefined
})

// 时间预设
const timePresets = [
  { label: '最近7天', value: [dayjs().subtract(7, 'day'), dayjs()] },
  { label: '最近30天', value: [dayjs().subtract(30, 'day'), dayjs()] },
  { label: '最近90天', value: [dayjs().subtract(90, 'day'), dayjs()] },
  { label: '本月', value: [dayjs().startOf('month'), dayjs().endOf('month')] },
  { label: '上月', value: [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')] }
]

// 方法
const handleSubmit = () => {
  emit('submit', { ...filterForm })
}

const handleReset = () => {
  filterForm.productLines = []
  filterForm.timeRange = [dayjs().subtract(30, 'day'), dayjs()]
  filterForm.userType = undefined
  filterForm.channel = undefined
  emit('reset')
}

const handleExport = () => {
  emit('export')
}

// 暴露方法给父组件
defineExpose({
  getFilters: () => ({ ...filterForm }),
  setFilters: (filters: Partial<FilterForm>) => {
    Object.assign(filterForm, filters)
  }
})
</script>

<style lang="less" scoped>
.filter-panel {
  margin-bottom: 16px;
  
  :deep(.ant-card-body) {
    padding: 16px;
  }
  
  :deep(.ant-form-item) {
    margin-bottom: 0;
  }
  
  :deep(.ant-form-item-label) {
    font-weight: 500;
  }
}
</style>
