<template>
  <a-card
    class="stat-card"
    :class="[
      { 'stat-card-hover': hoverable },
      { 'stat-card-clickable': clickable },
      `stat-card--${variant}`
    ]"
    @click="handleClick"
  >
    <div class="stat-content">
      <div class="stat-icon" :style="{ backgroundColor: color }">
        <component :is="icon" />
      </div>
      <div class="stat-info">
        <div class="stat-title">{{ title }}</div>
        <div class="stat-value">
          <span v-if="prefix" class="stat-prefix">{{ prefix }}</span>
          {{ formattedValue }}
          <span v-if="unit" class="stat-unit">{{ unit }}</span>
        </div>
        <div v-if="description" class="stat-description">{{ description }}</div>
        <div v-if="trend !== undefined" class="stat-trend" :class="trendType">
          <component :is="trendType === 'up' ? CaretUpOutlined : CaretDownOutlined" />
          {{ Math.abs(Number(trend)) }}%
          <span class="trend-text">{{ trendText }}</span>
        </div>
      </div>
    </div>

    <!-- 额外内容插槽 -->
    <div v-if="$slots.extra" class="stat-extra">
      <slot name="extra"></slot>
    </div>

    <!-- 底部内容插槽 -->
    <div v-if="$slots.footer" class="stat-footer">
      <slot name="footer"></slot>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons-vue'
import { formatNumber } from '@/utils'

interface Props {
  title: string
  value: number | string
  prefix?: string
  unit?: string
  description?: string
  trend?: number | string
  trendText?: string
  icon?: any
  color?: string
  hoverable?: boolean
  clickable?: boolean
  variant?: 'default' | 'compact' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  prefix: '',
  unit: '',
  trendText: '环比',
  color: '#1e88e5',
  hoverable: true,
  clickable: false,
  variant: 'default'
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const handleClick = (event: MouseEvent) => {
  if (props.clickable) {
    emit('click', event)
  }
}

// 计算属性
const formattedValue = computed(() => {
  if (typeof props.value === 'number') {
    return formatNumber(props.value)
  }
  return props.value
})

const trendType = computed(() => {
  if (props.trend === undefined) return ''
  return Number(props.trend) >= 0 ? 'up' : 'down'
})
</script>

<style lang="less" scoped>
.stat-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &.stat-card-hover:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    transform: translateY(-4px);

    &::before {
      left: 100%;
    }
  }

  &.stat-card-clickable {
    cursor: pointer;

    &:hover {
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      transform: translateY(-4px);

      &::before {
        left: 100%;
      }

      .stat-content .stat-icon {
        transform: scale(1.1);
      }

      .stat-content .stat-value {
        color: #1890ff;
      }
    }

    &:active {
      transform: translateY(-2px);
      transition: all 0.1s;
    }
  }

  // 变体样式
  &.stat-card--compact {
    .stat-content {
      .stat-icon {
        width: 40px;
        height: 40px;

        .anticon {
          font-size: 20px;
        }
      }

      .stat-info {
        .stat-value {
          font-size: 20px;
        }
      }
    }
  }

  &.stat-card--large {
    .stat-content {
      .stat-icon {
        width: 56px;
        height: 56px;

        .anticon {
          font-size: 28px;
        }
      }

      .stat-info {
        .stat-value {
          font-size: 28px;
        }
      }
    }
  }
  
  .stat-content {
    display: flex;
    align-items: center;
    
    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      .anticon {
        font-size: 24px;
        color: white;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
    
    .stat-info {
      flex: 1;
      
      .stat-title {
        font-size: 14px;
        color: #666;
        margin-bottom: 4px;
        line-height: 1.4;
      }
      
      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
        line-height: 1.2;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        
        .stat-prefix {
          font-size: 14px;
          font-weight: normal;
          color: #666;
          margin-right: 4px;
        }

        .stat-unit {
          font-size: 14px;
          font-weight: normal;
          color: #666;
          margin-left: 4px;
        }
      }
      
      .stat-description {
        font-size: 12px;
        color: #999;
        margin-bottom: 4px;
        line-height: 1.4;
      }

      .stat-trend {
        font-size: 12px;
        display: flex;
        align-items: center;

        &.up {
          color: #52c41a;
        }

        &.down {
          color: #f5222d;
        }

        .anticon {
          margin-right: 4px;
        }

        .trend-text {
          margin-left: 4px;
          color: #999;
        }
      }
    }
  }

  .stat-extra {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }

  .stat-footer {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
    font-size: 12px;
    color: #666;
  }
}
</style>
