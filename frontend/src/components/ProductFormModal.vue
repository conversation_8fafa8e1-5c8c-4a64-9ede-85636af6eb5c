<template>
  <a-modal
    v-model:open="visible"
    :title="isEdit ? '编辑产品' : '新增产品'"
    width="800px"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      class="product-form"
    >
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="产品名称" name="name">
            <a-input 
              v-model:value="formData.name" 
              placeholder="请输入产品名称"
              :maxlength="100"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="产品编码" name="code">
            <a-input 
              v-model:value="formData.code" 
              placeholder="请输入产品编码"
              :maxlength="50"
              :disabled="isEdit"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="产品描述" name="description">
        <a-textarea 
          v-model:value="formData.description" 
          placeholder="请输入产品描述"
          :rows="3"
          :maxlength="500"
          show-count
        />
      </a-form-item>

      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="产品类别" name="category">
            <a-select 
              v-model:value="formData.category" 
              placeholder="选择产品类别"
            >
              <a-select-option value="pdf">PDF产品</a-select-option>
              <a-select-option value="sdk">SDK产品</a-select-option>
              <a-select-option value="cloud">云服务</a-select-option>
              <a-select-option value="mobile">移动应用</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="产品状态" name="status">
            <a-select 
              v-model:value="formData.status" 
              placeholder="选择产品状态"
            >
              <a-select-option value="active">活跃</a-select-option>
              <a-select-option value="inactive">停用</a-select-option>
              <a-select-option value="deprecated">已废弃</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="当前版本" name="currentVersion">
            <a-input 
              v-model:value="formData.currentVersion" 
              placeholder="如：1.0.0"
              :maxlength="20"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="支持平台" name="platforms">
        <a-checkbox-group v-model:value="formData.platforms">
          <a-checkbox value="windows">Windows</a-checkbox>
          <a-checkbox value="mac">macOS</a-checkbox>
          <a-checkbox value="linux">Linux</a-checkbox>
          <a-checkbox value="web">Web</a-checkbox>
          <a-checkbox value="mobile">Mobile</a-checkbox>
        </a-checkbox-group>
      </a-form-item>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="产品图标" name="icon">
            <div class="icon-upload-section">
              <a-upload
                :show-upload-list="false"
                :before-upload="beforeUpload"
                @change="handleIconChange"
                accept="image/*"
              >
                <div class="icon-upload-area">
                  <img v-if="formData.icon" :src="formData.icon" alt="产品图标" class="uploaded-icon" />
                  <div v-else class="upload-placeholder">
                    <PlusOutlined />
                    <div>上传图标</div>
                  </div>
                </div>
              </a-upload>
              <div class="upload-tips">
                <small>建议尺寸：64x64px，支持 PNG、JPG 格式</small>
              </div>
            </div>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="负责人" name="owner">
            <a-select 
              v-model:value="formData.owner" 
              placeholder="选择产品负责人"
              show-search
              :filter-option="filterOption"
            >
              <a-select-option 
                v-for="user in userOptions" 
                :key="user.value"
                :value="user.value"
              >
                {{ user.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="产品特性" name="features">
        <a-select 
          v-model:value="formData.features" 
          mode="tags"
          placeholder="输入产品特性标签，按回车添加"
          :max-tag-count="10"
        />
      </a-form-item>

      <a-form-item label="技术栈" name="techStack">
        <a-select 
          v-model:value="formData.techStack" 
          mode="multiple"
          placeholder="选择技术栈"
          :max-tag-count="5"
        >
          <a-select-option value="java">Java</a-select-option>
          <a-select-option value="cpp">C++</a-select-option>
          <a-select-option value="javascript">JavaScript</a-select-option>
          <a-select-option value="python">Python</a-select-option>
          <a-select-option value="csharp">C#</a-select-option>
          <a-select-option value="swift">Swift</a-select-option>
          <a-select-option value="kotlin">Kotlin</a-select-option>
          <a-select-option value="react">React</a-select-option>
          <a-select-option value="vue">Vue</a-select-option>
          <a-select-option value="angular">Angular</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="产品链接" name="links">
        <div class="links-section">
          <a-row :gutter="8" style="margin-bottom: 8px;">
            <a-col :span="6">
              <a-input 
                v-model:value="formData.links.homepage" 
                placeholder="官网地址"
                addon-before="官网"
              />
            </a-col>
            <a-col :span="6">
              <a-input 
                v-model:value="formData.links.download" 
                placeholder="下载地址"
                addon-before="下载"
              />
            </a-col>
            <a-col :span="6">
              <a-input 
                v-model:value="formData.links.documentation" 
                placeholder="文档地址"
                addon-before="文档"
              />
            </a-col>
            <a-col :span="6">
              <a-input 
                v-model:value="formData.links.support" 
                placeholder="支持地址"
                addon-before="支持"
              />
            </a-col>
          </a-row>
        </div>
      </a-form-item>

      <a-form-item label="备注信息" name="remark">
        <a-textarea 
          v-model:value="formData.remark" 
          placeholder="请输入备注信息（可选）"
          :rows="3"
          :maxlength="1000"
          show-count
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import type { UploadProps } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'

interface ProductFormData {
  id?: number
  name: string
  code: string
  description: string
  category: string
  status: string
  currentVersion: string
  platforms: string[]
  icon?: string
  owner: string
  features: string[]
  techStack: string[]
  links: {
    homepage: string
    download: string
    documentation: string
    support: string
  }
  remark: string
}

interface ProductRecord {
  id: number
  name: string
  code: string
  description: string
  category: string
  status: string
  currentVersion: string
  platforms: string[]
  icon?: string
}

const props = defineProps<{
  modelValue: boolean
  productData?: ProductRecord | null
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'submit': [data: ProductFormData]
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.productData?.id)
const loading = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive<ProductFormData>({
  name: '',
  code: '',
  description: '',
  category: '',
  status: 'active',
  currentVersion: '1.0.0',
  platforms: [],
  icon: '',
  owner: '',
  features: [],
  techStack: [],
  links: {
    homepage: '',
    download: '',
    documentation: '',
    support: ''
  },
  remark: ''
})

// 用户选项
const userOptions = [
  { label: 'veikin', value: 'veikin' },
  { label: 'admin', value: 'admin' },
  { label: '张三', value: 'zhangsan' },
  { label: '李四', value: 'lisi' },
  { label: '王五', value: 'wangwu' }
]

// 表单验证规则
const rules: Record<string, Rule[]> = {
  name: [
    { required: true, message: '请输入产品名称', trigger: 'blur' },
    { min: 2, max: 100, message: '产品名称长度为2-100个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入产品编码', trigger: 'blur' },
    { pattern: /^[A-Z][A-Z0-9_]*$/, message: '产品编码只能包含大写字母、数字和下划线，且以大写字母开头', trigger: 'blur' },
    { min: 2, max: 50, message: '产品编码长度为2-50个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入产品描述', trigger: 'blur' },
    { max: 500, message: '产品描述不能超过500个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择产品类别', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择产品状态', trigger: 'change' }
  ],
  currentVersion: [
    { required: true, message: '请输入当前版本', trigger: 'blur' },
    { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式应为 x.y.z', trigger: 'blur' }
  ],
  platforms: [
    { required: true, type: 'array', min: 1, message: '请至少选择一个支持平台', trigger: 'change' }
  ]
}

// 监听产品数据变化
watch(() => props.productData, (newData) => {
  if (newData) {
    Object.assign(formData, {
      id: newData.id,
      name: newData.name,
      code: newData.code,
      description: newData.description,
      category: newData.category,
      status: newData.status,
      currentVersion: newData.currentVersion,
      platforms: [...newData.platforms],
      icon: newData.icon || '',
      owner: 'veikin', // 默认值
      features: [],
      techStack: [],
      links: {
        homepage: '',
        download: '',
        documentation: '',
        support: ''
      },
      remark: ''
    })
  } else {
    // 重置表单
    Object.assign(formData, {
      name: '',
      code: '',
      description: '',
      category: '',
      status: 'active',
      currentVersion: '1.0.0',
      platforms: [],
      icon: '',
      owner: '',
      features: [],
      techStack: [],
      links: {
        homepage: '',
        download: '',
        documentation: '',
        support: ''
      },
      remark: ''
    })
  }
}, { immediate: true })

// 方法定义
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase())
}

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('只能上传 JPG/PNG 格式的图片!')
    return false
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过 2MB!')
    return false
  }
  return false // 阻止自动上传
}

const handleIconChange = (info: any) => {
  if (info.file) {
    // 这里可以处理图标上传逻辑
    const reader = new FileReader()
    reader.onload = (e) => {
      formData.icon = e.target?.result as string
    }
    reader.readAsDataURL(info.file)
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('submit', { ...formData })
    message.success(isEdit.value ? '产品更新成功' : '产品创建成功')
    visible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped lang="less">
.product-form {
  .icon-upload-section {
    .icon-upload-area {
      width: 80px;
      height: 80px;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: border-color 0.3s;
      
      &:hover {
        border-color: #1890ff;
      }
      
      .uploaded-icon {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 6px;
      }
      
      .upload-placeholder {
        text-align: center;
        color: #8c8c8c;
        
        .anticon {
          font-size: 24px;
          margin-bottom: 8px;
        }
        
        div {
          font-size: 12px;
        }
      }
    }
    
    .upload-tips {
      margin-top: 8px;
      
      small {
        color: #8c8c8c;
        font-size: 12px;
      }
    }
  }

  .links-section {
    .ant-input-group-addon {
      min-width: 50px;
      font-size: 12px;
    }
  }
}
</style>
