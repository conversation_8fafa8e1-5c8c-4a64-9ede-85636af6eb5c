<template>
  <div class="page-header">
    <div class="page-header-content">
      <div class="page-header-main">
        <div class="page-header-title">
          <h1>{{ title }}</h1>
          <div v-if="description" class="page-header-description">
            {{ description }}
          </div>
        </div>
        
        <div v-if="$slots.extra" class="page-header-extra">
          <slot name="extra" />
        </div>
      </div>
      
      <div v-if="$slots.content" class="page-header-detail">
        <slot name="content" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
  description?: string
}

defineProps<Props>()
</script>

<style lang="less" scoped>
.page-header {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
  
  .page-header-content {
    padding: 24px;
    
    .page-header-main {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      
      .page-header-title {
        flex: 1;
        
        h1 {
          font-size: 24px;
          font-weight: 600;
          color: #333;
          margin: 0 0 8px 0;
          line-height: 1.3;
        }
        
        .page-header-description {
          font-size: 14px;
          color: #666;
          line-height: 1.5;
        }
      }
      
      .page-header-extra {
        margin-left: 24px;
        flex-shrink: 0;
      }
    }
    
    .page-header-detail {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #f0f0f0;
    }
  }
}
</style>
