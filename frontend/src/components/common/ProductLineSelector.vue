<template>
  <a-modal
    v-model:open="modalVisible"
    title="选择产品线"
    :width="600"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="product-line-selector">
      <!-- 搜索框 -->
      <div class="search-section">
        <a-input-search
          v-model:value="searchKeyword"
          placeholder="搜索产品线名称"
          style="margin-bottom: 16px"
          @search="handleSearch"
        />
      </div>

      <!-- 快捷选择 -->
      <div class="quick-select-section">
        <a-space wrap>
          <a-button size="small" @click="selectAll">全选</a-button>
          <a-button size="small" @click="selectNone">清空</a-button>
          <a-button size="small" @click="selectReaders">选择阅读器</a-button>
          <a-button size="small" @click="selectEditors">选择编辑器</a-button>
          <a-button size="small" @click="selectCloudServices">选择云服务</a-button>
        </a-space>
      </div>

      <!-- 产品线列表 -->
      <div class="product-list-section">
        <a-checkbox-group v-model:value="selectedIds" style="width: 100%">
          <a-row :gutter="[16, 16]">
            <a-col 
              :span="12" 
              v-for="product in filteredProducts" 
              :key="product.id"
            >
              <a-checkbox 
                :value="product.id"
                class="product-checkbox"
              >
                <div class="product-item">
                  <div class="product-icon" :style="{ backgroundColor: product.color }">
                    <component :is="getProductIcon(product.type)" />
                  </div>
                  <div class="product-info">
                    <div class="product-name">{{ product.name }}</div>
                    <div class="product-type">{{ getProductTypeName(product.type) }}</div>
                  </div>
                </div>
              </a-checkbox>
            </a-col>
          </a-row>
        </a-checkbox-group>
      </div>

      <!-- 已选择统计 -->
      <div class="selected-summary" v-if="selectedIds.length > 0">
        <a-tag color="blue">
          已选择 {{ selectedIds.length }} 个产品线
        </a-tag>
        <a-space wrap style="margin-top: 8px">
          <a-tag 
            v-for="id in selectedIds" 
            :key="id"
            closable
            @close="removeSelection(id)"
          >
            {{ getProductName(id) }}
          </a-tag>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { 
  FileTextOutlined, 
  EditOutlined, 
  CloudOutlined, 
  ToolOutlined,
  MobileOutlined 
} from '@ant-design/icons-vue'

/**
 * 产品线选择器组件
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */

// Props
interface Props {
  open: boolean
  selected: number[]
}

const props = withDefaults(defineProps<Props>(), {
  open: false,
  selected: () => []
})

// Emits
interface Emits {
  (e: 'update:open', open: boolean): void
  (e: 'update:selected', selected: number[]): void
  (e: 'confirm', selected: number[]): void
}

const emit = defineEmits<Emits>()

// 产品线数据（从API获取）
const productLines = ref<any[]>([])

// 获取产品线数据
const fetchProductLines = async () => {
  try {
    // 使用统一的http工具和公共API路径
    const { http } = await import('@/utils/request')
    const response = await http.get('/system/product-lines/enabled')

    if (response.code === 200 && response.data) {
      // 处理响应数据
      const items = Array.isArray(response.data) ? response.data : []
      productLines.value = items.map((item: any) => ({
        id: item.id,
        name: item.name,
        type: item.type || 1,
        color: item.color || '#1E88E5'
      }))
      return
    }
  } catch (error) {
    console.error('获取产品线数据失败:', error)
    // 如果是权限问题，静默处理，使用默认数据
  }

  // 使用默认数据作为备用
  productLines.value = [
    { id: 1, name: '福昕阅读器GA版', type: 1, color: '#1E88E5' },
    { id: 2, name: '福昕阅读器PLUS版', type: 1, color: '#43A047' },
    { id: 3, name: 'PDF编辑器个人版', type: 2, color: '#FB8C00' },
    { id: 4, name: 'PDF编辑器专业版', type: 2, color: '#E53935' },
    { id: 5, name: 'PDF365在线服务', type: 3, color: '#8E24AA' },
    { id: 6, name: 'PDF转换器', type: 4, color: '#00ACC1' },
    { id: 7, name: '卸载器', type: 4, color: '#5E35B1' },
    { id: 8, name: '福昕移动阅读器', type: 1, color: '#FF5722' },
    { id: 9, name: 'PDF SDK工具包', type: 4, color: '#795548' },
    { id: 10, name: '内容平台', type: 5, color: '#607D8B' }
  ]
}

// 响应式数据
const modalVisible = ref(false)
const selectedIds = ref<number[]>([])
const searchKeyword = ref('')

// 计算属性
const filteredProducts = computed(() => {
  if (!searchKeyword.value) {
    return productLines.value
  }

  return productLines.value.filter((product: any) =>
    product.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 方法
const getProductIcon = (type: number) => {
  const iconMap = {
    1: FileTextOutlined,  // 阅读器
    2: EditOutlined,      // 编辑器
    3: CloudOutlined,     // 云服务
    4: ToolOutlined,      // 工具类
    5: MobileOutlined     // 内容平台
  }
  return iconMap[type as keyof typeof iconMap] || FileTextOutlined
}

const getProductTypeName = (type: number) => {
  const typeMap = {
    1: '阅读器',
    2: '编辑器',
    3: '云服务',
    4: '工具类',
    5: '内容平台'
  }
  return typeMap[type as keyof typeof typeMap] || '未知'
}

const getProductName = (id: number) => {
  const product = productLines.value.find((p: any) => p.id === id)
  return product?.name || ''
}

const handleSearch = (value: string) => {
  searchKeyword.value = value
}

const selectAll = () => {
  selectedIds.value = filteredProducts.value.map((p: any) => p.id)
}

const selectNone = () => {
  selectedIds.value = []
}

const selectReaders = () => {
  const readerIds = productLines.value.filter((p: any) => p.type === 1).map((p: any) => p.id)
  selectedIds.value = [...new Set([...selectedIds.value, ...readerIds])]
}

const selectEditors = () => {
  const editorIds = productLines.value.filter((p: any) => p.type === 2).map((p: any) => p.id)
  selectedIds.value = [...new Set([...selectedIds.value, ...editorIds])]
}

const selectCloudServices = () => {
  const cloudIds = productLines.value.filter((p: any) => p.type === 3).map((p: any) => p.id)
  selectedIds.value = [...new Set([...selectedIds.value, ...cloudIds])]
}

const removeSelection = (id: number) => {
  selectedIds.value = selectedIds.value.filter(selectedId => selectedId !== id)
}

const handleConfirm = () => {
  emit('update:selected', selectedIds.value)
  emit('confirm', selectedIds.value)
  modalVisible.value = false
}

const handleCancel = () => {
  // 恢复到原始选择状态
  selectedIds.value = [...props.selected]
  modalVisible.value = false
}

// 监听props变化
watch(() => props.open, (open) => {
  modalVisible.value = open
  if (open) {
    selectedIds.value = [...props.selected]
    searchKeyword.value = ''
  }
})

watch(() => props.selected, (selected) => {
  selectedIds.value = [...selected]
})

watch(modalVisible, (open) => {
  emit('update:open', open)
})

// 组件挂载时获取产品线数据
onMounted(() => {
  fetchProductLines()
})
</script>

<style scoped lang="less">
.product-line-selector {
  .search-section {
    margin-bottom: 16px;
  }

  .quick-select-section {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .product-list-section {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 16px;

    .product-checkbox {
      width: 100%;
      margin: 0;

      :deep(.ant-checkbox) {
        align-self: flex-start;
        margin-top: 4px;
      }

      .product-item {
        display: flex;
        align-items: center;
        padding: 8px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        transition: all 0.3s ease;
        margin-left: 8px;
        width: calc(100% - 8px);

        &:hover {
          border-color: #1890ff;
          background-color: #f6f8ff;
        }

        .product-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          color: white;

          .anticon {
            font-size: 16px;
          }
        }

        .product-info {
          flex: 1;
          min-width: 0;

          .product-name {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .product-type {
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }
    }

    :deep(.ant-checkbox-group) {
      width: 100%;
    }

    :deep(.ant-checkbox-wrapper) {
      width: 100%;
      margin-right: 0;
    }

    :deep(.ant-checkbox-wrapper:hover .ant-checkbox-inner) {
      border-color: #1890ff;
    }

    :deep(.ant-checkbox-wrapper-checked .product-item) {
      border-color: #1890ff;
      background-color: #f6f8ff;
    }
  }

  .selected-summary {
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}

// 滚动条样式
.product-list-section::-webkit-scrollbar {
  width: 6px;
}

.product-list-section::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.product-list-section::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.product-list-section::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
