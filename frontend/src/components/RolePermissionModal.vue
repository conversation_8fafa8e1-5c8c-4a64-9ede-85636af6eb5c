<template>
  <a-modal
    v-model:open="visible"
    :title="`配置角色权限 - ${roleInfo?.name}`"
    width="800px"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <div class="permission-config">
      <div class="role-info">
        <a-descriptions :column="2" size="small">
          <a-descriptions-item label="角色名称">{{ roleInfo?.name }}</a-descriptions-item>
          <a-descriptions-item label="角色编码">{{ roleInfo?.code }}</a-descriptions-item>
          <a-descriptions-item label="角色类型">
            <a-tag :color="roleInfo?.type === 'system' ? 'blue' : 'green'">
              {{ roleInfo?.type === 'system' ? '系统角色' : '自定义角色' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="当前权限">
            {{ selectedPermissions.length }} 项权限
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <a-divider />

      <div class="permission-tree-section">
        <div class="section-header">
          <h4>权限配置</h4>
          <a-space>
            <a-button size="small" @click="handleExpandAll">
              {{ expandedKeys.length > 0 ? '收起全部' : '展开全部' }}
            </a-button>
            <a-button size="small" @click="handleSelectAll">
              {{ selectedPermissions.length === allPermissionKeys.length ? '取消全选' : '全选' }}
            </a-button>
          </a-space>
        </div>

        <a-tree
          v-model:checkedKeys="selectedPermissions"
          v-model:expandedKeys="expandedKeys"
          :tree-data="permissionTree"
          :field-names="{ children: 'children', title: 'title', key: 'key' }"
          checkable
          :selectable="false"
          class="permission-tree"
        >
          <template #title="{ title, description, key }">
            <div class="tree-node">
              <span class="node-title">{{ title }}</span>
              <span v-if="description" class="node-description">{{ description }}</span>
            </div>
          </template>
        </a-tree>
      </div>

      <div class="selected-permissions">
        <h4>已选择的权限 ({{ selectedPermissions.length }})</h4>
        <div class="permission-tags">
          <a-tag
            v-for="permission in selectedPermissionDetails"
            :key="permission.key"
            closable
            @close="handleRemovePermission(permission.key)"
          >
            {{ permission.title }}
          </a-tag>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'

interface RoleInfo {
  id: number
  name: string
  code: string
  type: 'system' | 'custom'
  permissions: string[]
}

interface PermissionNode {
  key: string
  title: string
  description?: string
  children?: PermissionNode[]
}

const props = defineProps<{
  modelValue: boolean
  roleInfo: RoleInfo | null
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'submit': [permissions: string[]]
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const selectedPermissions = ref<string[]>([])
const expandedKeys = ref<string[]>([])

// 权限树数据
const permissionTree: PermissionNode[] = [
  {
    key: 'system',
    title: '系统管理',
    description: '系统基础管理功能',
    children: [
      {
        key: 'system:user',
        title: '用户管理',
        description: '用户账户的增删改查'
      },
      {
        key: 'system:role',
        title: '角色管理',
        description: '角色的创建和权限配置'
      },
      {
        key: 'system:permission',
        title: '权限管理',
        description: '权限的定义和分配'
      },
      {
        key: 'system:log',
        title: '操作日志',
        description: '系统操作日志查看'
      },
      {
        key: 'system:config',
        title: '系统配置',
        description: '系统参数配置管理'
      }
    ]
  },
  {
    key: 'analytics',
    title: '数据分析',
    description: '数据分析和报表功能',
    children: [
      {
        key: 'analytics:dashboard',
        title: '数据看板',
        description: '总览和产品线仪表盘'
      },
      {
        key: 'analytics:user',
        title: '用户分析',
        description: '用户活跃度和增长分析'
      },
      {
        key: 'analytics:behavior',
        title: '行为分析',
        description: '用户行为轨迹分析'
      },
      {
        key: 'analytics:business',
        title: '商业分析',
        description: '收入和转化分析'
      },
      {
        key: 'analytics:report',
        title: '报表管理',
        description: '自定义报表和导出'
      }
    ]
  },
  {
    key: 'product',
    title: '产品管理',
    description: '产品线和版本管理',
    children: [
      {
        key: 'product:config',
        title: '产品配置',
        description: '产品线基础配置'
      },
      {
        key: 'product:version',
        title: '版本管理',
        description: '产品版本发布管理'
      },
      {
        key: 'product:datasource',
        title: '数据源管理',
        description: '产品数据源配置'
      }
    ]
  },
  {
    key: 'operation',
    title: '运营管理',
    description: '私域运营和活动管理',
    children: [
      {
        key: 'operation:private',
        title: '私域运营',
        description: '私域流量运营分析'
      },
      {
        key: 'operation:activity',
        title: '活动运营',
        description: '活动效果分析'
      },
      {
        key: 'operation:content',
        title: '内容管理',
        description: '运营内容管理'
      }
    ]
  }
]

// 获取所有权限键
const allPermissionKeys = computed(() => {
  const keys: string[] = []
  const traverse = (nodes: PermissionNode[]) => {
    nodes.forEach(node => {
      if (node.children) {
        traverse(node.children)
      } else {
        keys.push(node.key)
      }
    })
  }
  traverse(permissionTree)
  return keys
})

// 获取已选择权限的详细信息
const selectedPermissionDetails = computed(() => {
  const details: { key: string; title: string }[] = []
  const traverse = (nodes: PermissionNode[]) => {
    nodes.forEach(node => {
      if (selectedPermissions.value.includes(node.key)) {
        details.push({ key: node.key, title: node.title })
      }
      if (node.children) {
        traverse(node.children)
      }
    })
  }
  traverse(permissionTree)
  return details
})

// 监听角色信息变化，初始化权限选择
watch(() => props.roleInfo, (newRoleInfo) => {
  if (newRoleInfo) {
    selectedPermissions.value = [...(newRoleInfo.permissions || [])]
  }
}, { immediate: true })

// 方法定义
const handleExpandAll = () => {
  if (expandedKeys.value.length > 0) {
    expandedKeys.value = []
  } else {
    const keys: string[] = []
    const traverse = (nodes: PermissionNode[]) => {
      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          keys.push(node.key)
          traverse(node.children)
        }
      })
    }
    traverse(permissionTree)
    expandedKeys.value = keys
  }
}

const handleSelectAll = () => {
  if (selectedPermissions.value.length === allPermissionKeys.value.length) {
    selectedPermissions.value = []
  } else {
    selectedPermissions.value = [...allPermissionKeys.value]
  }
}

const handleRemovePermission = (permissionKey: string) => {
  const index = selectedPermissions.value.indexOf(permissionKey)
  if (index > -1) {
    selectedPermissions.value.splice(index, 1)
  }
}

const handleSubmit = async () => {
  try {
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('submit', selectedPermissions.value)
    message.success('权限配置保存成功')
    visible.value = false
  } catch (error) {
    message.error('权限配置保存失败')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped lang="less">
.permission-config {
  .role-info {
    background: #fafafa;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 16px;
  }

  .permission-tree-section {
    margin-bottom: 24px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .permission-tree {
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      padding: 12px;
      max-height: 300px;
      overflow-y: auto;

      :deep(.ant-tree-node-content-wrapper) {
        .tree-node {
          display: flex;
          flex-direction: column;
          align-items: flex-start;

          .node-title {
            font-weight: 500;
            color: #262626;
          }

          .node-description {
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 2px;
          }
        }
      }
    }
  }

  .selected-permissions {
    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #262626;
    }

    .permission-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      min-height: 32px;
      padding: 8px;
      background: #fafafa;
      border-radius: 6px;

      .ant-tag {
        margin: 0;
      }
    }
  }
}
</style>
