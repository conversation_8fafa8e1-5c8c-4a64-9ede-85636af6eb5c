<template>
  <div class="line-chart" :style="{ height: height + 'px' }">
    <v-chart
      :option="chartOption"
      :style="{ height: '100%', width: '100%' }"
      autoresize
    />
  </div>
</template>

<script setup lang="ts">
import { computed, watch, ref } from 'vue'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components'

// 注册必要的组件
use([
  CanvasRenderer,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
])

interface SeriesData {
  name: string
  data: number[]
  color?: string
  smooth?: boolean
  area?: boolean
}

interface Props {
  title?: string
  data: {
    categories: string[]
    series: SeriesData[]
  }
  height?: number
  smooth?: boolean
  area?: boolean
  showDataZoom?: boolean
  showLegend?: boolean
  grid?: {
    top?: number | string
    right?: number | string
    bottom?: number | string
    left?: number | string
  }
}

const props = withDefaults(defineProps<Props>(), {
  height: 400,
  smooth: true,
  area: false,
  showDataZoom: false,
  showLegend: true,
  grid: () => ({
    top: 60,
    right: 20,
    bottom: 60,
    left: 60
  })
})

const chartOption = computed(() => {
  const option: any = {
    title: props.title ? {
      text: props.title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
        color: '#1f2937'
      }
    } : undefined,
    
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e7eb',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      },
      formatter: (params: any) => {
        let result = `<div style="margin-bottom: 4px; font-weight: 500;">${params[0].axisValue}</div>`
        params.forEach((param: any) => {
          const marker = `<span style="display:inline-block;margin-right:4px;border-radius:50%;width:8px;height:8px;background-color:${param.color};"></span>`
          result += `<div>${marker}${param.seriesName}: ${param.value.toLocaleString()}</div>`
        })
        return result
      }
    },
    
    legend: props.showLegend ? {
      top: 30,
      type: 'scroll',
      textStyle: {
        color: '#6b7280'
      }
    } : undefined,
    
    grid: props.grid,
    
    xAxis: {
      type: 'category',
      data: props.data.categories,
      axisLine: {
        lineStyle: {
          color: '#e5e7eb'
        }
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 12
      },
      splitLine: {
        show: false
      }
    },
    
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 12,
        formatter: (value: number) => {
          if (value >= 1000000) {
            return (value / 1000000).toFixed(1) + 'M'
          } else if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'K'
          }
          return value.toString()
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed'
        }
      }
    },
    
    series: props.data.series.map((item, index) => ({
      name: item.name,
      type: 'line',
      data: item.data,
      smooth: item.smooth ?? props.smooth,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 2,
        color: item.color || `hsl(${index * 60}, 70%, 50%)`
      },
      itemStyle: {
        color: item.color || `hsl(${index * 60}, 70%, 50%)`
      },
      areaStyle: (item.area ?? props.area) ? {
        opacity: 0.1,
        color: item.color || `hsl(${index * 60}, 70%, 50%)`
      } : undefined,
      emphasis: {
        focus: 'series'
      }
    })),
    
    dataZoom: props.showDataZoom ? [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 100,
        height: 30,
        bottom: 20
      }
    ] : undefined
  }
  
  return option
})
</script>

<style scoped>
.line-chart {
  width: 100%;
}
</style>
