<template>
  <div class="bar-chart" :style="{ height: height + 'px' }">
    <v-chart
      :option="chartOption"
      :style="{ height: '100%', width: '100%' }"
      autoresize
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart as EChartsBarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'

use([
  <PERSON>vas<PERSON>enderer,
  <PERSON>hartsBarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

interface SeriesData {
  name: string
  data: number[]
  color?: string
  stack?: string
}

interface Props {
  title?: string
  data: {
    categories: string[]
    series: SeriesData[]
  }
  height?: number
  horizontal?: boolean
  showLegend?: boolean
  grid?: {
    top?: number | string
    right?: number | string
    bottom?: number | string
    left?: number | string
  }
}

const props = withDefaults(defineProps<Props>(), {
  height: 400,
  horizontal: false,
  showLegend: true,
  grid: () => ({
    top: 60,
    right: 20,
    bottom: 60,
    left: 60
  })
})

const chartOption = computed(() => {
  const option: any = {
    title: props.title ? {
      text: props.title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
        color: '#1f2937'
      }
    } : undefined,
    
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e7eb',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      },
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        let result = `<div style="margin-bottom: 4px; font-weight: 500;">${params[0].axisValue}</div>`
        params.forEach((param: any) => {
          const marker = `<span style="display:inline-block;margin-right:4px;border-radius:2px;width:8px;height:8px;background-color:${param.color};"></span>`
          result += `<div>${marker}${param.seriesName}: ${param.value.toLocaleString()}</div>`
        })
        return result
      }
    },
    
    legend: props.showLegend ? {
      top: 30,
      type: 'scroll',
      textStyle: {
        color: '#6b7280'
      }
    } : undefined,
    
    grid: props.grid,
    
    xAxis: {
      type: props.horizontal ? 'value' : 'category',
      data: props.horizontal ? undefined : props.data.categories,
      axisLine: {
        lineStyle: {
          color: '#e5e7eb'
        }
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 12,
        formatter: props.horizontal ? (value: number) => {
          if (value >= 1000000) {
            return (value / 1000000).toFixed(1) + 'M'
          } else if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'K'
          }
          return value.toString()
        } : undefined
      },
      splitLine: {
        show: props.horizontal,
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed'
        }
      }
    },
    
    yAxis: {
      type: props.horizontal ? 'category' : 'value',
      data: props.horizontal ? props.data.categories : undefined,
      axisLine: {
        show: props.horizontal,
        lineStyle: {
          color: '#e5e7eb'
        }
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 12,
        formatter: !props.horizontal ? (value: number) => {
          if (value >= 1000000) {
            return (value / 1000000).toFixed(1) + 'M'
          } else if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'K'
          }
          return value.toString()
        } : undefined
      },
      splitLine: {
        show: !props.horizontal,
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed'
        }
      }
    },
    
    series: props.data.series.map((item, index) => ({
      name: item.name,
      type: 'bar',
      data: item.data,
      stack: item.stack,
      itemStyle: {
        color: item.color || `hsl(${index * 60}, 70%, 50%)`,
        borderRadius: props.horizontal ? [0, 4, 4, 0] : [4, 4, 0, 0]
      },
      emphasis: {
        focus: 'series',
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.1)'
        }
      },
      barMaxWidth: 40
    }))
  }
  
  return option
})
</script>

<style scoped>
.bar-chart {
  width: 100%;
}
</style>
