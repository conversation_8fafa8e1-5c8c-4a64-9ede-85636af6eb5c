<template>
  <div class="pie-chart" :style="{ height: height + 'px' }">
    <v-chart
      :option="chartOption"
      :style="{ height: '100%', width: '100%' }"
      autoresize
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart as EChartsPieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components'

use([
  CanvasRenderer,
  EChartsPieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent
])

interface DataItem {
  name: string
  value: number
  color?: string
}

interface Props {
  title?: string
  data: DataItem[]
  height?: number
  radius?: [string, string]
  showLegend?: boolean
  legendPosition?: 'top' | 'bottom' | 'left' | 'right'
  donut?: boolean
  showLabels?: boolean
  showPercentage?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  height: 400,
  radius: () => ['0%', '70%'],
  showLegend: true,
  legendPosition: 'right',
  donut: false,
  showLabels: true,
  showPercentage: true
})

const chartOption = computed(() => {
  const legendConfig: any = {
    type: 'scroll',
    textStyle: {
      color: '#6b7280'
    }
  }

  switch (props.legendPosition) {
    case 'top':
      legendConfig.top = 20
      legendConfig.orient = 'horizontal'
      break
    case 'bottom':
      legendConfig.bottom = 20
      legendConfig.orient = 'horizontal'
      break
    case 'left':
      legendConfig.left = 20
      legendConfig.orient = 'vertical'
      break
    case 'right':
      legendConfig.right = 20
      legendConfig.orient = 'vertical'
      break
  }

  const option: any = {
    title: props.title ? {
      text: props.title,
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
        color: '#1f2937'
      }
    } : undefined,
    
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e7eb',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      },
      formatter: (params: any) => {
        const percentage = params.percent.toFixed(1)
        return `<div style="font-weight: 500; margin-bottom: 4px;">${params.name}</div>
                <div>数值: ${params.value.toLocaleString()}</div>
                <div>占比: ${percentage}%</div>`
      }
    },
    
    legend: props.showLegend ? legendConfig : undefined,
    
    series: [
      {
        type: 'pie',
        radius: props.donut ? ['40%', '70%'] : props.radius,
        center: ['50%', '50%'],
        data: props.data.map((item, index) => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.color || `hsl(${index * 45}, 70%, 50%)`
          }
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.2)'
          }
        },
        label: props.showLabels ? {
          show: true,
          formatter: (params: any) => {
            if (props.showPercentage) {
              return `${params.name}\n${params.percent}%`
            }
            return params.name
          },
          fontSize: 12,
          color: '#374151'
        } : {
          show: false
        },
        labelLine: {
          show: props.showLabels
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: (idx: number) => idx * 100
      }
    ]
  }
  
  return option
})
</script>

<style scoped>
.pie-chart {
  width: 100%;
}
</style>
