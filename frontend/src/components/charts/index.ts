export { default as <PERSON><PERSON><PERSON> } from './LineChart.vue'
export { default as <PERSON><PERSON><PERSON> } from './BarChart.vue'
export { default as <PERSON><PERSON><PERSON> } from './PieChart.vue'
export { default as AreaChart } from './AreaChart.vue'

// 图表类型定义
export interface ChartSeriesData {
  name: string
  data: number[]
  color?: string
  smooth?: boolean
  area?: boolean
  stack?: string
}

export interface ChartData {
  categories: string[]
  series: ChartSeriesData[]
}

export interface PieChartDataItem {
  name: string
  value: number
  color?: string
}

export interface ChartProps {
  title?: string
  height?: number
  showLegend?: boolean
  grid?: {
    top?: number | string
    right?: number | string
    bottom?: number | string
    left?: number | string
  }
}
