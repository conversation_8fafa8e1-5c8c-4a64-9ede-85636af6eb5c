<template>
  <div class="metric-card">
    <a-card :bordered="false" :loading="loading" class="card-container">
      <div class="card-content">
        <!-- 图标和标题 -->
        <div class="header">
          <div class="icon-wrapper" :style="{ backgroundColor: iconBgColor }">
            <component :is="icon" :style="{ color: color }" />
          </div>
          <div class="title-section">
            <h4 class="title">{{ title }}</h4>
            <div class="trend-indicator" v-if="growthRate !== undefined">
              <component 
                :is="trendIcon" 
                :class="['trend-icon', trendClass]"
              />
              <span :class="['growth-text', trendClass]">
                {{ formatGrowthRate(growthRate) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 数值显示 -->
        <div class="value-section">
          <div class="main-value">
            <span class="value" :style="{ color }">
              {{ formattedValue }}
            </span>
            <span class="unit" v-if="unit">{{ unit }}</span>
          </div>
          
          <!-- 趋势图（可选） -->
          <div class="trend-chart" v-if="showTrendChart && trendData">
            <div ref="miniChartRef" class="mini-chart"></div>
          </div>
        </div>

        <!-- 额外信息 -->
        <div class="extra-info" v-if="extraInfo">
          <span class="extra-text">{{ extraInfo }}</span>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ArrowUpOutlined, ArrowDownOutlined, MinusOutlined } from '@ant-design/icons-vue'
import * as echarts from 'echarts'

/**
 * 指标卡片组件
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */

// Props
interface Props {
  title: string
  value: number | string
  unit?: string
  growthRate?: number
  trend?: 'up' | 'down' | 'flat'
  icon?: any
  color?: string
  loading?: boolean
  showTrendChart?: boolean
  trendData?: number[]
  extraInfo?: string
}

const props = withDefaults(defineProps<Props>(), {
  unit: '',
  growthRate: undefined,
  trend: 'flat',
  color: '#1890ff',
  loading: false,
  showTrendChart: false,
  trendData: () => [],
  extraInfo: ''
})

// 响应式数据
const miniChartRef = ref<HTMLDivElement>()
let miniChartInstance: echarts.ECharts | null = null

// 计算属性
const formattedValue = computed(() => {
  if (typeof props.value === 'string') {
    return props.value
  }
  
  const value = props.value
  if (value >= 100000000) {
    return (value / 100000000).toFixed(2) + '亿'
  } else if (value >= 10000) {
    return (value / 10000).toFixed(1) + '万'
  } else if (value >= 1000) {
    return value.toLocaleString()
  } else {
    return value.toString()
  }
})

const trendClass = computed(() => {
  if (props.growthRate === undefined) return ''
  
  if (props.growthRate > 0) return 'positive'
  if (props.growthRate < 0) return 'negative'
  return 'neutral'
})

const trendIcon = computed(() => {
  if (props.growthRate === undefined) return MinusOutlined
  
  if (props.growthRate > 0) return ArrowUpOutlined
  if (props.growthRate < 0) return ArrowDownOutlined
  return MinusOutlined
})

const iconBgColor = computed(() => {
  const color = props.color
  // 将颜色转换为浅色背景
  if (color.startsWith('#')) {
    const hex = color.replace('#', '')
    const r = parseInt(hex.substr(0, 2), 16)
    const g = parseInt(hex.substr(2, 2), 16)
    const b = parseInt(hex.substr(4, 2), 16)
    return `rgba(${r}, ${g}, ${b}, 0.1)`
  }
  return `${color}1a` // 添加透明度
})

const miniChartOptions = computed(() => {
  if (!props.trendData || props.trendData.length === 0) {
    return null
  }

  return {
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    },
    xAxis: {
      type: 'category',
      show: false,
      data: props.trendData.map((_, index) => index)
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [
      {
        type: 'line',
        data: props.trendData,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: props.color,
          width: 2
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: props.color + '40'
            },
            {
              offset: 1,
              color: props.color + '10'
            }
          ])
        }
      }
    ],
    animation: false
  }
})

// 方法
const formatGrowthRate = (rate: number) => {
  const sign = rate >= 0 ? '+' : ''
  return `${sign}${rate.toFixed(2)}%`
}

const initMiniChart = () => {
  if (!miniChartRef.value || !props.showTrendChart) return

  miniChartInstance = echarts.init(miniChartRef.value)
  updateMiniChart()
}

const updateMiniChart = () => {
  if (!miniChartInstance || !miniChartOptions.value) return

  miniChartInstance.setOption(miniChartOptions.value, true)
}

const resizeMiniChart = () => {
  if (miniChartInstance) {
    miniChartInstance.resize()
  }
}

const destroyMiniChart = () => {
  if (miniChartInstance) {
    miniChartInstance.dispose()
    miniChartInstance = null
  }
}

// 监听数据变化
watch(() => props.trendData, () => {
  if (props.showTrendChart) {
    nextTick(() => {
      updateMiniChart()
    })
  }
})

// 生命周期
onMounted(() => {
  if (props.showTrendChart) {
    nextTick(() => {
      initMiniChart()
    })
  }

  window.addEventListener('resize', resizeMiniChart)
})

onUnmounted(() => {
  destroyMiniChart()
  window.removeEventListener('resize', resizeMiniChart)
})
</script>

<style scoped lang="less">
.metric-card {
  height: 100%;

  .card-container {
    height: 100%;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }

    :deep(.ant-card-body) {
      padding: 20px;
      height: 100%;
    }
  }

  .card-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .header {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16px;

      .icon-wrapper {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;

        .anticon {
          font-size: 24px;
        }
      }

      .title-section {
        flex: 1;
        min-width: 0;

        .title {
          margin: 0 0 4px 0;
          font-size: 14px;
          font-weight: 500;
          color: #8c8c8c;
          line-height: 1.4;
        }

        .trend-indicator {
          display: flex;
          align-items: center;
          gap: 4px;

          .trend-icon {
            font-size: 12px;

            &.positive {
              color: #52c41a;
            }

            &.negative {
              color: #ff4d4f;
            }

            &.neutral {
              color: #8c8c8c;
            }
          }

          .growth-text {
            font-size: 12px;
            font-weight: 500;

            &.positive {
              color: #52c41a;
            }

            &.negative {
              color: #ff4d4f;
            }

            &.neutral {
              color: #8c8c8c;
            }
          }
        }
      }
    }

    .value-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .main-value {
        display: flex;
        align-items: baseline;
        margin-bottom: 8px;

        .value {
          font-size: 28px;
          font-weight: 600;
          line-height: 1;
          margin-right: 4px;
        }

        .unit {
          font-size: 14px;
          color: #8c8c8c;
          font-weight: normal;
        }
      }

      .trend-chart {
        height: 40px;
        margin-top: 8px;

        .mini-chart {
          width: 100%;
          height: 100%;
        }
      }
    }

    .extra-info {
      margin-top: auto;
      padding-top: 8px;
      border-top: 1px solid #f0f0f0;

      .extra-text {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  }
}

@media (max-width: 768px) {
  .metric-card {
    .card-container {
      :deep(.ant-card-body) {
        padding: 16px;
      }
    }

    .card-content {
      .header {
        .icon-wrapper {
          width: 40px;
          height: 40px;

          .anticon {
            font-size: 20px;
          }
        }
      }

      .value-section {
        .main-value {
          .value {
            font-size: 24px;
          }
        }
      }
    }
  }
}
</style>
