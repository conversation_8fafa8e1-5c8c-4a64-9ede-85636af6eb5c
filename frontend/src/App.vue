<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue'
import { useRoute } from 'vue-router'
import { resetScrollPosition } from '@/utils/scrollUtils'

// 应用根组件
const route = useRoute()

// 监听路由变化，重置滚动位置
watch(
  () => route.path,
  () => {
    // 使用 nextTick 确保 DOM 更新完成后再重置滚动
    setTimeout(() => {
      resetScrollPosition()
    }, 100)
  },
  { immediate: false }
)
</script>

<style lang="less">
#app {
  width: 100%;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
