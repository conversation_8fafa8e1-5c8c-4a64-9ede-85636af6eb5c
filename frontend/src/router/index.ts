import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard/overview'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/layouts/MainLayout.vue'),
    redirect: '/dashboard/overview',
    meta: {
      title: '数据看板',
      requiresAuth: true
    },
    children: [
      {
        path: 'overview',
        name: 'DashboardOverview',
        component: () => import('@/views/dashboard/Overview.vue'),
        meta: {
          title: '总览仪表盘',
          requiresAuth: true
        }
      },
      {
        path: 'product',
        name: 'DashboardProduct',
        component: () => import('@/views/dashboard/ProductNew.vue'),
        meta: {
          title: '产品线仪表盘',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/user',
    name: 'User',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      title: '用户分析',
      requiresAuth: true
    },
    children: [
      {
        path: 'active',
        name: 'UserActive',
        component: () => import('@/views/user/active/index.vue'),
        meta: {
          title: '活跃用户分析',
          requiresAuth: true
        }
      },
      {
        path: 'growth',
        name: 'UserGrowth',
        component: () => import('@/views/user/Growth.vue'),
        meta: {
          title: '用户增长分析',
          requiresAuth: true
        }
      },
      {
        path: 'login',
        name: 'UserLogin',
        component: () => import('@/views/user/Login.vue'),
        meta: {
          title: '登录行为分析',
          requiresAuth: true
        }
      },
      {
        path: 'structure',
        name: 'UserStructure',
        component: () => import('@/views/user/Structure.vue'),
        meta: {
          title: '用户结构分析',
          requiresAuth: true
        }
      },
      {
        path: 'segmentation',
        name: 'UserSegmentation',
        component: () => import('@/views/user/Segmentation.vue'),
        meta: {
          title: '用户分群',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/behavior',
    name: 'Behavior',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      title: '行为分析',
      requiresAuth: true
    },
    children: [
      {
        path: 'event',
        name: 'BehaviorEvent',
        component: () => import('@/views/behavior/EventAnalysis.vue'),
        meta: {
          title: '事件分析',
          requiresAuth: true
        }
      },
      {
        path: 'feature',
        name: 'BehaviorFeature',
        component: () => import('@/views/behavior/Feature.vue'),
        meta: {
          title: '功能使用分析',
          requiresAuth: true
        }
      },
      {
        path: 'search',
        name: 'BehaviorSearch',
        component: () => import('@/views/behavior/Search.vue'),
        meta: {
          title: '搜索行为分析',
          requiresAuth: true
        }
      },
      {
        path: 'path',
        name: 'BehaviorPath',
        component: () => import('@/views/behavior/Path.vue'),
        meta: {
          title: '用户行为路径',
          requiresAuth: true
        }
      },
      {
        path: 'funnel',
        name: 'BehaviorFunnel',
        component: () => import('@/views/behavior/Funnel.vue'),
        meta: {
          title: '漏斗分析',
          requiresAuth: true
        }
      },
      {
        path: 'churn',
        name: 'BehaviorChurn',
        component: () => import('@/views/behavior/Churn.vue'),
        meta: {
          title: '客户端流失分析',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/business',
    name: 'Business',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      title: '商业变现',
      requiresAuth: true
    },
    children: [
      {
        path: 'revenue',
        name: 'BusinessRevenue',
        component: () => import('@/views/business/Revenue.vue'),
        meta: {
          title: '订单与收入分析',
          requiresAuth: true
        }
      },
      {
        path: 'conversion',
        name: 'BusinessConversion',
        component: () => import('@/views/business/Conversion.vue'),
        meta: {
          title: '付费转化分析',
          requiresAuth: true
        }
      },
      {
        path: 'advertising',
        name: 'BusinessAdvertising',
        component: () => import('@/views/business/Advertising.vue'),
        meta: {
          title: '广告效果分析',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/private-domain',
    name: 'PrivateDomain',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      title: '私域运营',
      requiresAuth: true
    },
    children: [
      {
        path: 'fan-analysis',
        name: 'FanAnalysis',
        component: () => import('@/views/private-domain/FanAnalysis.vue'),
        meta: {
          title: '粉丝数据分析',
          requiresAuth: true
        }
      },
      {
        path: 'community-analysis',
        name: 'CommunityAnalysis',
        component: () => import('@/views/private-domain/CommunityAnalysis.vue'),
        meta: {
          title: '社群活跃度分析',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/activity',
    name: 'Activity',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      title: '活动运营',
      requiresAuth: true
    },
    children: [
      {
        path: 'campaign-analysis',
        name: 'CampaignAnalysis',
        component: () => import('@/views/activity/CampaignAnalysis.vue'),
        meta: {
          title: '活动效果分析',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      title: '个人信息',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'ProfileIndex',
        component: () => import('@/views/Profile.vue'),
        meta: {
          title: '个人信息',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      title: '系统设置',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'SettingsIndex',
        component: () => import('@/views/Settings.vue'),
        meta: {
          title: '系统设置',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/system',
    name: 'System',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      title: '系统管理',
      requiresAuth: true
    },
    children: [
      {
        path: 'user',
        name: 'SystemUser',
        component: () => import('@/views/system/User.vue'),
        meta: {
          title: '用户账户与权限管理',
          requiresAuth: true
        }
      },
      {
        path: 'role',
        name: 'SystemRole',
        component: () => import('@/views/system/Role.vue'),
        meta: {
          title: '角色与权限配置',
          requiresAuth: true
        }
      },
      {
        path: 'log',
        name: 'SystemLog',
        component: () => import('@/views/system/Log.vue'),
        meta: {
          title: '系统操作日志审计',
          requiresAuth: true
        }
      },
      {
        path: 'product',
        name: 'SystemProduct',
        component: () => import('@/views/system/Product.vue'),
        meta: {
          title: '产品与版本管理',
          requiresAuth: true
        }
      },
      {
        path: 'config',
        name: 'SystemConfig',
        component: () => import('@/views/system/Config.vue'),
        meta: {
          title: '基础配置管理',
          requiresAuth: true
        }
      },
      {
        path: 'report',
        name: 'SystemReport',
        component: () => import('@/views/system/Report.vue'),
        meta: {
          title: '统一报表与分析配置',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面不存在',
      requiresAuth: false
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的滚动位置（如浏览器前进/后退），恢复到该位置
    if (savedPosition) {
      return savedPosition
    }

    // 对于新的路由导航，滚动到顶部
    // 使用 nextTick 确保 DOM 更新完成后再滚动
    return new Promise((resolve) => {
      setTimeout(() => {
        // 尝试滚动主内容区域
        const contentElement = document.querySelector('.content')
        if (contentElement) {
          contentElement.scrollTop = 0
        }

        // 同时滚动 window（以防万一）
        window.scrollTo(0, 0)

        resolve({ top: 0 })
      }, 100)
    })
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - SCRM-Next`
  }
  
  // 检查是否需要认证
  if (to.meta?.requiresAuth) {
    if (!userStore.token) {
      // 未登录，跳转到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // 如果有token但没有用户信息，尝试获取用户信息
    if (!userStore.userInfo) {
      try {
        await userStore.getCurrentUser()
      } catch (error) {
        // 获取用户信息失败，跳转到登录页
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }
  }
  
  // 如果已登录且访问登录页，重定向到仪表盘
  if (to.path === '/login' && userStore.token) {
    next('/dashboard/overview')
    return
  }
  
  next()
})

export default router
