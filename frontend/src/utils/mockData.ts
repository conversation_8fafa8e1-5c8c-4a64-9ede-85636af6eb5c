import dayjs from 'dayjs'

// 产品线配置
export const PRODUCT_LINES = [
  { id: 'pdf-reader', name: 'PDF Reader', color: '#1E88E5' },
  { id: 'pdf-editor', name: 'PDF Editor', color: '#43A047' },
  { id: 'pdf-creator', name: 'PDF Creator', color: '#FB8C00' },
  { id: 'foxit-cloud', name: 'Foxit Cloud', color: '#8E24AA' },
  { id: 'phantompdf', name: 'PhantomPDF', color: '#E53935' }
]

// 用户类型
export const USER_TYPES = [
  { id: 'free', name: '免费用户', color: '#9E9E9E' },
  { id: 'trial', name: '试用用户', color: '#FF9800' },
  { id: 'paid', name: '付费用户', color: '#4CAF50' },
  { id: 'enterprise', name: '企业用户', color: '#2196F3' }
]

// 地区配置
export const REGIONS = [
  { id: 'north-america', name: '北美', color: '#1E88E5' },
  { id: 'europe', name: '欧洲', color: '#43A047' },
  { id: 'asia-pacific', name: '亚太', color: '#FB8C00' },
  { id: 'china', name: '中国', color: '#8E24AA' },
  { id: 'others', name: '其他', color: '#E53935' }
]

// 工具函数（保留用于开发和测试）
export const random = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

export const randomFloat = (min: number, max: number, decimals = 2): number => {
  return Number((Math.random() * (max - min) + min).toFixed(decimals))
}

// 生成日期范围
export const generateDateRange = (days: number): string[] => {
  const dates: string[] = []
  for (let i = days - 1; i >= 0; i--) {
    dates.push(dayjs().subtract(i, 'day').format('YYYY-MM-DD'))
  }
  return dates
}

// 生成时间范围（小时）
export const generateHourRange = (hours: number): string[] => {
  const times: string[] = []
  for (let i = hours - 1; i >= 0; i--) {
    times.push(dayjs().subtract(i, 'hour').format('HH:mm'))
  }
  return times
}

// 注意：以下生成器函数已弃用并移除，请使用真实API数据
// 如需临时数据，请直接在组件中定义

// 所有弃用的生成器函数已移除
// 请在各个组件中直接调用相应的API接口获取数据
