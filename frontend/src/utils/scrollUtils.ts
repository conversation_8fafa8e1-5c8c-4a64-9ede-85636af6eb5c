/**
 * 滚动工具函数
 * 处理页面滚动相关的功能
 */

/**
 * 重置页面滚动位置到顶部
 * 支持多种滚动容器
 */
export function resetScrollPosition(): void {
  // 重置 window 滚动位置
  window.scrollTo(0, 0)
  
  // 重置 document.body 滚动位置
  if (document.body) {
    document.body.scrollTop = 0
  }
  
  // 重置 document.documentElement 滚动位置
  if (document.documentElement) {
    document.documentElement.scrollTop = 0
  }
  
  // 重置主内容区域滚动位置
  const contentElement = document.querySelector('.content')
  if (contentElement) {
    contentElement.scrollTop = 0
  }
  
  // 重置 Ant Design Layout Content 滚动位置
  const layoutContent = document.querySelector('.ant-layout-content')
  if (layoutContent) {
    layoutContent.scrollTop = 0
  }
  
  // 重置任何可能的滚动容器
  const scrollContainers = document.querySelectorAll('[style*="overflow"], .scrollable, .scroll-container')
  scrollContainers.forEach(container => {
    if (container instanceof HTMLElement) {
      container.scrollTop = 0
    }
  })
}

/**
 * 平滑滚动到顶部
 * @param duration 动画持续时间（毫秒）
 */
export function smoothScrollToTop(duration: number = 300): void {
  const startTime = performance.now()
  const startScrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0
  
  // 同时处理内容区域滚动
  const contentElement = document.querySelector('.content') as HTMLElement
  const contentStartScrollTop = contentElement ? contentElement.scrollTop : 0
  
  function animateScroll(currentTime: number) {
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / duration, 1)
    
    // 使用 easeOutCubic 缓动函数
    const easeOutCubic = (t: number) => 1 - Math.pow(1 - t, 3)
    const easedProgress = easeOutCubic(progress)
    
    // 滚动 window
    const currentScrollTop = startScrollTop * (1 - easedProgress)
    window.scrollTo(0, currentScrollTop)
    
    // 滚动内容区域
    if (contentElement) {
      contentElement.scrollTop = contentStartScrollTop * (1 - easedProgress)
    }
    
    if (progress < 1) {
      requestAnimationFrame(animateScroll)
    }
  }
  
  requestAnimationFrame(animateScroll)
}

/**
 * 监听路由变化并重置滚动位置
 * 在 Vue 组件中使用
 */
export function useScrollReset() {
  const resetScroll = () => {
    // 使用 nextTick 确保 DOM 更新完成
    setTimeout(() => {
      resetScrollPosition()
    }, 50)
  }
  
  return {
    resetScroll
  }
}

/**
 * 检查元素是否在视口中
 * @param element 要检查的元素
 * @param threshold 阈值（0-1）
 */
export function isElementInViewport(element: HTMLElement, threshold: number = 0): boolean {
  const rect = element.getBoundingClientRect()
  const windowHeight = window.innerHeight || document.documentElement.clientHeight
  const windowWidth = window.innerWidth || document.documentElement.clientWidth
  
  const verticalVisible = rect.top <= windowHeight * (1 - threshold) && rect.bottom >= windowHeight * threshold
  const horizontalVisible = rect.left <= windowWidth * (1 - threshold) && rect.right >= windowWidth * threshold
  
  return verticalVisible && horizontalVisible
}

/**
 * 滚动到指定元素
 * @param element 目标元素或选择器
 * @param offset 偏移量（像素）
 * @param behavior 滚动行为
 */
export function scrollToElement(
  element: HTMLElement | string, 
  offset: number = 0, 
  behavior: ScrollBehavior = 'smooth'
): void {
  const targetElement = typeof element === 'string' ? document.querySelector(element) as HTMLElement : element
  
  if (!targetElement) {
    console.warn('滚动目标元素未找到:', element)
    return
  }
  
  const elementTop = targetElement.offsetTop - offset
  
  // 滚动 window
  window.scrollTo({
    top: elementTop,
    behavior
  })
  
  // 同时滚动内容区域（如果存在）
  const contentElement = document.querySelector('.content') as HTMLElement
  if (contentElement) {
    contentElement.scrollTo({
      top: elementTop,
      behavior
    })
  }
}
