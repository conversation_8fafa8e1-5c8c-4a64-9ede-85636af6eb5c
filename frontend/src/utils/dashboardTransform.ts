import type { 
  CoreMetricsResponse, 
  ChartDataResponse, 
  OverviewDashboardResponse,
  RealTimeStatsResponse 
} from '@/api/dashboard'

/**
 * 前端组件需要的核心指标格式
 */
export interface CoreMetricItem {
  key: string
  title: string
  value: number
  unit: string
  change: number
  trendText: string
  icon: any
  color: string
}

/**
 * 前端图表组件需要的数据格式
 */
export interface ChartData {
  categories: string[]
  series: {
    name: string
    data: number[]
    color: string
    type?: string
  }[]
}

/**
 * 转换核心指标数据为前端组件格式
 */
export function transformCoreMetrics(
  data: CoreMetricsResponse,
  icons: Record<string, any>
): CoreMetricItem[] {
  const colorMap = {
    totalUsers: '#1e88e5',
    activeUsers: '#43a047', 
    newUsers: '#fb8c00',
    revenue: '#8e24aa',
    downloads: '#f44336',
    retention: '#00acc1'
  }

  const titleMap = {
    totalUsers: '总用户数',
    activeUsers: '活跃用户',
    newUsers: '新增用户', 
    revenue: '总收入',
    downloads: '下载量',
    retention: '留存率'
  }

  const iconMap = {
    totalUsers: icons.UserOutlined,
    activeUsers: icons.TeamOutlined,
    newUsers: icons.UserAddOutlined,
    revenue: icons.DollarOutlined,
    downloads: icons.DownloadOutlined,
    retention: icons.RiseOutlined
  }

  return Object.entries(data)
    .filter(([key]) => key !== 'updateTime')
    .filter(([key, metric]) => metric !== null && metric !== undefined) // 过滤掉 null 值
    .map(([key, metric]) => ({
      key,
      title: titleMap[key as keyof typeof titleMap] || key,
      value: metric?.value || 0,
      unit: metric?.unit || '',
      change: metric?.changeRate || 0,
      trendText: metric?.comparisonText || '环比上期',
      icon: iconMap[key as keyof typeof iconMap],
      color: colorMap[key as keyof typeof colorMap] || '#1890ff'
    }))
}

/**
 * 转换图表数据为前端组件格式
 */
export function transformChartData(data: ChartDataResponse): ChartData {
  if (!data) {
    return { categories: [], series: [] }
  }

  return {
    categories: data.categories || [],
    series: (data.series || []).map(item => ({
      name: item?.name || '',
      data: item?.data || [],
      color: item?.color || '#1890ff',
      type: item?.type
    }))
  }
}

/**
 * 转换饼图数据格式
 */
export function transformPieChartData(data: ChartDataResponse) {
  if (!data || !data.series || data.series.length === 0) return []

  return (data.categories || []).map((name, index) => ({
    name,
    value: data.series[0]?.data[index] || 0,
    color: data.series[0]?.color || '#1890ff'
  }))
}

/**
 * 转换实时统计数据
 */
export function transformRealTimeStats(data: RealTimeStatsResponse) {
  if (!data) {
    return {
      currentOnline: 0,
      peakOnline: 0,
      totalDownloadsToday: 0,
      systemLoad: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      activeSessions: 0,
      newUsersToday: 0,
      revenueToday: 0,
      updateTime: null
    }
  }

  return {
    currentOnline: data.currentOnline || 0,
    peakOnline: data.peakOnline || 0,
    totalDownloadsToday: data.totalDownloadsToday || 0,
    systemLoad: data.systemLoad || 0,
    memoryUsage: data.memoryUsage || 0,
    cpuUsage: data.cpuUsage || 0,
    activeSessions: data.activeSessions || 0,
    newUsersToday: data.newUsersToday || 0,
    revenueToday: data.revenueToday || 0,
    updateTime: data.updateTime
  }
}

/**
 * 转换总览仪表盘数据
 */
export function transformOverviewDashboard(
  data: OverviewDashboardResponse,
  icons: Record<string, any>
) {
  return {
    coreMetrics: transformCoreMetrics(data.coreMetrics, icons),
    userGrowthData: transformChartData(data.userGrowthChart),
    revenueData: transformChartData(data.revenueChart),
    productLineDistribution: transformPieChartData(data.productLineChart),
    userTypeData: transformChartData(data.userTypeChart),
    realTimeData: transformChartData(data.realTimeChart),
    currentStats: transformRealTimeStats(data.realTimeStats),
    updateTime: data.updateTime,
    dataScope: data.dataScope,
    accessibleProductLines: data.accessibleProductLines
  }
}

/**
 * 格式化数值显示
 */
export function formatValue(value: number, unit: string = ''): string {
  if (value >= 10000) {
    return (value / 10000).toFixed(1) + '万' + unit
  }
  if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K' + unit
  }
  return value.toString() + unit
}

/**
 * 格式化变化率
 */
export function formatChangeRate(rate: number): string {
  const sign = rate >= 0 ? '+' : ''
  return `${sign}${rate.toFixed(1)}%`
}

/**
 * 获取趋势颜色
 */
export function getTrendColor(trend: 'up' | 'down' | 'stable'): string {
  switch (trend) {
    case 'up':
      return '#52c41a'
    case 'down':
      return '#f5222d'
    case 'stable':
    default:
      return '#faad14'
  }
}

/**
 * 获取趋势图标
 */
export function getTrendIcon(trend: 'up' | 'down' | 'stable'): string {
  switch (trend) {
    case 'up':
      return 'arrow-up'
    case 'down':
      return 'arrow-down'
    case 'stable':
    default:
      return 'minus'
  }
}

/**
 * 生成时间范围参数
 */
export function generateTimeRangeParams(
  timeRange: 'today' | 'last7days' | 'last30days' | 'thisMonth' | 'custom',
  customStartDate?: string,
  customEndDate?: string
): { startDate?: string; endDate?: string } {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  
  switch (timeRange) {
    case 'today':
      return {
        startDate: today.toISOString().split('T')[0],
        endDate: today.toISOString().split('T')[0]
      }
    case 'last7days':
      const last7Days = new Date(today.getTime() - 6 * 24 * 60 * 60 * 1000)
      return {
        startDate: last7Days.toISOString().split('T')[0],
        endDate: today.toISOString().split('T')[0]
      }
    case 'last30days':
      const last30Days = new Date(today.getTime() - 29 * 24 * 60 * 60 * 1000)
      return {
        startDate: last30Days.toISOString().split('T')[0],
        endDate: today.toISOString().split('T')[0]
      }
    case 'thisMonth':
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
      return {
        startDate: firstDayOfMonth.toISOString().split('T')[0],
        endDate: today.toISOString().split('T')[0]
      }
    case 'custom':
      return {
        startDate: customStartDate,
        endDate: customEndDate
      }
    default:
      return {}
  }
}

/**
 * 错误处理和默认数据
 */
export function getDefaultCoreMetrics(): CoreMetricItem[] {
  return [
    {
      key: 'totalUsers',
      title: '总用户数',
      value: 0,
      unit: '人',
      change: 0,
      trendText: '暂无数据',
      icon: null,
      color: '#1e88e5'
    },
    {
      key: 'activeUsers', 
      title: '活跃用户',
      value: 0,
      unit: '人',
      change: 0,
      trendText: '暂无数据',
      icon: null,
      color: '#43a047'
    },
    {
      key: 'newUsers',
      title: '新增用户',
      value: 0,
      unit: '人', 
      change: 0,
      trendText: '暂无数据',
      icon: null,
      color: '#fb8c00'
    },
    {
      key: 'revenue',
      title: '总收入',
      value: 0,
      unit: '元',
      change: 0,
      trendText: '暂无数据',
      icon: null,
      color: '#8e24aa'
    }
  ]
}

/**
 * 获取默认图表数据
 */
export function getDefaultChartData(): ChartData {
  return {
    categories: [],
    series: []
  }
}
