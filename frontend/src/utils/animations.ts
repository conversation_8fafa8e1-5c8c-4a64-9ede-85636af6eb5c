// 动画工具函数

// 页面进入动画
export const pageEnterAnimation = {
  name: 'page-enter',
  duration: 0.6,
  delay: 0.1
}

// 卡片进入动画
export const cardEnterAnimation = {
  name: 'card-enter',
  duration: 0.4,
  delay: 0.05
}

// 数字滚动动画
export const animateNumber = (
  element: HTMLElement,
  start: number,
  end: number,
  duration: number = 1000,
  formatter?: (value: number) => string
) => {
  const startTime = performance.now()
  const difference = end - start

  const step = (currentTime: number) => {
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / duration, 1)
    
    // 使用缓动函数
    const easeOutQuart = 1 - Math.pow(1 - progress, 4)
    const current = start + difference * easeOutQuart
    
    element.textContent = formatter ? formatter(current) : Math.round(current).toString()
    
    if (progress < 1) {
      requestAnimationFrame(step)
    }
  }
  
  requestAnimationFrame(step)
}

// 创建交错动画
export const createStaggerAnimation = (
  elements: NodeListOf<Element> | Element[],
  animationClass: string,
  delay: number = 100
) => {
  elements.forEach((element, index) => {
    setTimeout(() => {
      element.classList.add(animationClass)
    }, index * delay)
  })
}

// 滚动触发动画
export const createScrollAnimation = (
  element: Element,
  animationClass: string,
  threshold: number = 0.1
) => {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add(animationClass)
          observer.unobserve(entry.target)
        }
      })
    },
    { threshold }
  )
  
  observer.observe(element)
  return observer
}

// 脉冲动画
export const pulseAnimation = (element: HTMLElement, duration: number = 1000) => {
  element.style.animation = `pulse ${duration}ms ease-in-out`
  
  setTimeout(() => {
    element.style.animation = ''
  }, duration)
}

// 抖动动画
export const shakeAnimation = (element: HTMLElement, duration: number = 500) => {
  element.style.animation = `shake ${duration}ms ease-in-out`
  
  setTimeout(() => {
    element.style.animation = ''
  }, duration)
}

// 弹跳动画
export const bounceAnimation = (element: HTMLElement, duration: number = 600) => {
  element.style.animation = `bounce ${duration}ms ease-in-out`
  
  setTimeout(() => {
    element.style.animation = ''
  }, duration)
}

// 渐入动画
export const fadeInAnimation = (
  element: HTMLElement,
  duration: number = 300,
  delay: number = 0
) => {
  element.style.opacity = '0'
  element.style.transform = 'translateY(20px)'
  element.style.transition = `all ${duration}ms cubic-bezier(0.4, 0, 0.2, 1)`
  
  setTimeout(() => {
    element.style.opacity = '1'
    element.style.transform = 'translateY(0)'
  }, delay)
}

// 缩放进入动画
export const scaleInAnimation = (
  element: HTMLElement,
  duration: number = 300,
  delay: number = 0
) => {
  element.style.opacity = '0'
  element.style.transform = 'scale(0.8)'
  element.style.transition = `all ${duration}ms cubic-bezier(0.4, 0, 0.2, 1)`
  
  setTimeout(() => {
    element.style.opacity = '1'
    element.style.transform = 'scale(1)'
  }, delay)
}

// 从左侧滑入动画
export const slideInLeftAnimation = (
  element: HTMLElement,
  duration: number = 300,
  delay: number = 0
) => {
  element.style.opacity = '0'
  element.style.transform = 'translateX(-30px)'
  element.style.transition = `all ${duration}ms cubic-bezier(0.4, 0, 0.2, 1)`
  
  setTimeout(() => {
    element.style.opacity = '1'
    element.style.transform = 'translateX(0)'
  }, delay)
}

// 从右侧滑入动画
export const slideInRightAnimation = (
  element: HTMLElement,
  duration: number = 300,
  delay: number = 0
) => {
  element.style.opacity = '0'
  element.style.transform = 'translateX(30px)'
  element.style.transition = `all ${duration}ms cubic-bezier(0.4, 0, 0.2, 1)`
  
  setTimeout(() => {
    element.style.opacity = '1'
    element.style.transform = 'translateX(0)'
  }, delay)
}

// 旋转进入动画
export const rotateInAnimation = (
  element: HTMLElement,
  duration: number = 500,
  delay: number = 0
) => {
  element.style.opacity = '0'
  element.style.transform = 'rotate(-180deg) scale(0.8)'
  element.style.transition = `all ${duration}ms cubic-bezier(0.4, 0, 0.2, 1)`
  
  setTimeout(() => {
    element.style.opacity = '1'
    element.style.transform = 'rotate(0deg) scale(1)'
  }, delay)
}

// 组合动画：数字计数 + 脉冲效果
export const animateCounterWithPulse = (
  element: HTMLElement,
  start: number,
  end: number,
  duration: number = 1000,
  formatter?: (value: number) => string
) => {
  // 先执行脉冲动画
  pulseAnimation(element, 300)
  
  // 延迟执行数字动画
  setTimeout(() => {
    animateNumber(element, start, end, duration, formatter)
  }, 150)
}

// 页面加载完成后的整体动画
export const pageLoadAnimation = (container: HTMLElement) => {
  const cards = container.querySelectorAll('.stat-card, .chart-card')
  const headers = container.querySelectorAll('.page-header, .product-selector')
  
  // 头部区域动画
  headers.forEach((header, index) => {
    fadeInAnimation(header as HTMLElement, 400, index * 100)
  })
  
  // 卡片交错动画
  cards.forEach((card, index) => {
    scaleInAnimation(card as HTMLElement, 400, 200 + index * 100)
  })
}

// 导出所有动画函数
export const animations = {
  animateNumber,
  createStaggerAnimation,
  createScrollAnimation,
  pulseAnimation,
  shakeAnimation,
  bounceAnimation,
  fadeInAnimation,
  scaleInAnimation,
  slideInLeftAnimation,
  slideInRightAnimation,
  rotateInAnimation,
  animateCounterWithPulse,
  pageLoadAnimation
}
