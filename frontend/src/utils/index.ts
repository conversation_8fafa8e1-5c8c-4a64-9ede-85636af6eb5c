import dayjs from 'dayjs'

/**
 * 格式化数字，添加千分位分隔符
 */
export function formatNumber(num: number | string): string {
  if (num === null || num === undefined || num === '') return '0'
  return Number(num).toLocaleString()
}

/**
 * 格式化百分比
 */
export function formatPercent(num: number, decimals = 2): string {
  if (num === null || num === undefined) return '0%'
  return `${(num * 100).toFixed(decimals)}%`
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

/**
 * 格式化时间
 */
export function formatTime(time: string | Date, format = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!time) return ''
  return dayjs(time).format(format)
}

/**
 * 获取相对时间
 */
export function getRelativeTime(time: string | Date): string {
  if (!time) return ''
  const now = dayjs()
  const target = dayjs(time)
  const diff = now.diff(target, 'minute')
  
  if (diff < 1) return '刚刚'
  if (diff < 60) return `${diff}分钟前`
  if (diff < 1440) return `${Math.floor(diff / 60)}小时前`
  if (diff < 43200) return `${Math.floor(diff / 1440)}天前`
  
  return target.format('YYYY-MM-DD')
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      func(...args)
    }
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

/**
 * 深拷贝
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  return obj
}

/**
 * 生成随机字符串
 */
export function generateRandomString(length = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 获取URL参数
 */
export function getUrlParams(url?: string): Record<string, string> {
  const urlStr = url || window.location.href
  const params: Record<string, string> = {}
  const urlObj = new URL(urlStr)
  
  urlObj.searchParams.forEach((value, key) => {
    params[key] = value
  })
  
  return params
}

/**
 * 下载文件
 */
export function downloadFile(url: string, filename?: string): void {
  const link = document.createElement('a')
  link.href = url
  if (filename) {
    link.download = filename
  }
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/**
 * 复制文本到剪贴板
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(text)
      return true
    } else {
      // 兼容旧版浏览器
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      return true
    }
  } catch (error) {
    console.error('复制失败:', error)
    return false
  }
}

/**
 * 获取图表颜色
 */
export function getChartColors(): string[] {
  return [
    '#1E88E5', '#43A047', '#FB8C00', '#E53935', '#8E24AA',
    '#00ACC1', '#5E35B1', '#F4511E', '#6D4C41', '#546E7A',
    '#26A69A', '#AB47BC', '#FF7043', '#66BB6A', '#42A5F5'
  ]
}

/**
 * 产品线颜色映射
 */
export function getProductLineColor(code: string): string {
  const colorMap: Record<string, string> = {
    'READER_GA': '#1E88E5',
    'READER_PLUS': '#43A047',
    'EDITOR_PERSONAL': '#FB8C00',
    'EDITOR_PRO': '#E53935',
    'PDF365': '#8E24AA',
    'CONVERTER': '#00ACC1',
    'UNINSTALLER': '#5E35B1'
  }
  return colorMap[code] || '#1E88E5'
}
