import { http } from '@/utils/request'

/**
 * Dashboard查询请求参数
 */
export interface DashboardQueryParams {
  startDate?: string
  endDate?: string
  timeGranularity?: 'hour' | 'day' | 'week' | 'month'
  productLineIds?: number[]
  includeComparison?: boolean
  comparisonType?: 'previous' | 'same_last_year'
  dataScope?: 'all' | 'dept' | 'own'
  metricTypes?: string[]
}

/**
 * 核心指标数据
 */
export interface CoreMetricsResponse {
  totalUsers: MetricData
  activeUsers: MetricData
  newUsers: MetricData
  revenue: MetricData
  downloads: MetricData
  retention: MetricData
  updateTime: string
}

/**
 * 指标数据
 */
export interface MetricData {
  value: number
  unit: string
  changeRate: number
  trend: 'up' | 'down' | 'stable'
  comparisonText: string
  comparisonValue: number
  isAbnormal: boolean
  abnormalReason?: string
}

/**
 * 图表数据响应
 */
export interface ChartDataResponse {
  chartType: string
  title: string
  description?: string
  categories: string[]
  series: SeriesData[]
  options: ChartOptions
}

/**
 * 图表系列数据
 */
export interface SeriesData {
  name: string
  data: number[]
  color: string
  type: string
  visible: boolean
  extra?: Record<string, any>
}

/**
 * 图表配置选项
 */
export interface ChartOptions {
  showLegend: boolean
  legendPosition: 'top' | 'bottom' | 'left' | 'right'
  showDataLabels: boolean
  smooth: boolean
  stacked: boolean
  yAxisUnit: string
  showDataZoom: boolean
  height: number
  extra?: Record<string, any>
}

/**
 * 总览仪表盘响应
 */
export interface OverviewDashboardResponse {
  coreMetrics: CoreMetricsResponse
  userGrowthChart: ChartDataResponse
  revenueChart: ChartDataResponse
  productLineChart: ChartDataResponse
  userTypeChart: ChartDataResponse
  realTimeChart: ChartDataResponse
  realTimeStats: RealTimeStatsResponse
  updateTime: string
  dataScope: string
  accessibleProductLines: ProductLineSummary[]
}

/**
 * 产品线仪表盘响应
 */
export interface ProductLineDashboardResponse {
  productLineInfo: ProductLineInfo
  metrics: ProductLineMetrics
  userGrowthChart: ChartDataResponse
  revenueTrendChart: ChartDataResponse
  featureUsageChart: ChartDataResponse
  userDistributionChart: ChartDataResponse
  comparisons: ProductLineComparison[]
  updateTime: string
}

/**
 * 实时统计响应
 */
export interface RealTimeStatsResponse {
  currentOnline: number
  peakOnline: number
  totalDownloadsToday: number
  systemLoad: number
  memoryUsage: number
  cpuUsage: number
  activeSessions: number
  newUsersToday: number
  revenueToday: number
  recentEvents: RealTimeEvent[]
  hourlyStats: Record<string, HourlyStats>
  updateTime: string
}

/**
 * 实时事件
 */
export interface RealTimeEvent {
  eventId: string
  eventType: string
  description: string
  userId: number
  username: string
  productLine: string
  eventTime: string
  level: 'info' | 'warning' | 'error'
}

/**
 * 小时统计
 */
export interface HourlyStats {
  hour: number
  onlineUsers: number
  downloads: number
  revenue: number
  newUsers: number
}

/**
 * 产品线信息
 */
export interface ProductLineInfo {
  id: number
  name: string
  type: string
  description: string
  owner: string
  status: string
}

/**
 * 产品线指标
 */
export interface ProductLineMetrics {
  totalUsers: number
  activeUsers: number
  newUsers: number
  totalRevenue: number
  downloads: number
  retentionRate: number
  userGrowthRate: number
  revenueGrowthRate: number
  marketShare: number
}

/**
 * 产品线对比
 */
export interface ProductLineComparison {
  productLineId: number
  productLineName: string
  metrics: Record<string, number>
  comparisonResult: 'better' | 'worse' | 'similar'
}

/**
 * 产品线摘要
 */
export interface ProductLineSummary {
  id: number
  name: string
  type: string
  userCount: number
  revenue: number
  growthRate: number
}

/**
 * Dashboard配置响应
 */
export interface DashboardConfigResponse {
  userId: number
  defaultTimeRange: string
  defaultTimeGranularity: string
  accessibleProductLines: ProductLineAccess[]
  defaultMetrics: string[]
  chartConfigs: Record<string, ChartConfig>
  refreshInterval: number
  enableRealTimeUpdate: boolean
  dataScope: string
}

/**
 * 产品线访问权限
 */
export interface ProductLineAccess {
  productLineId: number
  productLineName: string
  accessLevel: 'read' | 'write' | 'admin'
  defaultSelected: boolean
}

/**
 * 图表配置
 */
export interface ChartConfig {
  chartType: string
  visible: boolean
  order: number
  height: number
  customOptions: Record<string, any>
}

/**
 * Dashboard配置请求
 */
export interface DashboardConfigRequest {
  defaultTimeRange?: string
  defaultTimeGranularity?: string
  defaultProductLineIds?: number[]
  defaultMetrics?: string[]
  chartConfigs?: Record<string, ChartConfigUpdate>
  refreshInterval?: number
  enableRealTimeUpdate?: boolean
}

/**
 * 图表配置更新
 */
export interface ChartConfigUpdate {
  visible?: boolean
  order?: number
  height?: number
  customOptions?: Record<string, any>
}

/**
 * Dashboard API 服务
 */
export const dashboardApi = {
  /**
   * 获取总览仪表盘数据
   */
  getOverviewDashboard(params?: DashboardQueryParams) {
    return http.get<OverviewDashboardResponse>('/dashboard/overview', { params })
  },

  /**
   * 获取产品线仪表盘数据
   */
  getProductLineDashboard(params: DashboardQueryParams & { productLineIds: number[] }) {
    return http.get<ProductLineDashboardResponse>('/dashboard/product-line', { params })
  },

  /**
   * 获取实时统计数据
   */
  getRealTimeStats(params?: { dataScope?: string }) {
    return http.get<RealTimeStatsResponse>('/dashboard/realtime', { params })
  },

  /**
   * 获取核心指标数据
   */
  getCoreMetrics(params?: DashboardQueryParams) {
    return http.get<CoreMetricsResponse>('/dashboard/metrics', { params })
  },

  /**
   * 获取图表数据
   */
  getChartData(chartType: string, params?: DashboardQueryParams) {
    return http.get<ChartDataResponse>(`/api/dashboard/charts/${chartType}`, { params })
  },

  /**
   * 刷新Dashboard缓存
   */
  refreshDashboardCache(params?: { dashboardType?: string; dataScope?: string }) {
    return http.post<void>('/dashboard/cache/refresh', null, { params })
  },

  /**
   * 获取Dashboard配置
   */
  getDashboardConfig() {
    return http.get<DashboardConfigResponse>('/dashboard/config')
  },

  /**
   * 更新Dashboard配置
   */
  updateDashboardConfig(data: DashboardConfigRequest) {
    return http.put<void>('/dashboard/config', data)
  },

  /**
   * 健康检查
   */
  healthCheck() {
    return http.get<string>('/dashboard/health')
  }
}

export default dashboardApi
