import { http } from '@/utils/request'
import type { ApiResponse } from '@/types/common'

/**
 * 用户增长分析API接口
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */

/**
 * 时间粒度枚举
 */
export type TimeGranularity = 'DAY' | 'WEEK' | 'MONTH' | 'QUARTER' | 'YEAR'

/**
 * 用户增长分析请求参数
 */
export interface UserGrowthAnalysisRequest {
  /** 开始日期 */
  startDate: string
  /** 结束日期 */
  endDate: string
  /** 时间粒度 */
  granularity?: TimeGranularity
  /** 产品线ID列表 */
  productLineIds?: number[]
  /** 分析类型 */
  analysisType?: string
  /** 是否包含历史对比 */
  includeComparison?: boolean
  /** 是否包含详细数据 */
  includeDetails?: boolean
  /** 留存分析周期 */
  retentionPeriods?: number[]
  /** 用户来源类型过滤 */
  sourceTypes?: string[]
  /** 导出格式 */
  exportFormat?: string
}

/**
 * 指标值
 */
export interface MetricValue {
  name: string
  value: number
  previousValue?: number
  changeRate?: number
  unit: string
  type: 'COUNT' | 'PERCENTAGE' | 'CURRENCY' | 'DURATION'
  trend: 'UP' | 'DOWN' | 'STABLE'
}

/**
 * 趋势数据
 */
export interface TrendData {
  name: string
  categories: string[]
  values: number[]
  unit: string
  chartType: string
}

/**
 * 留存周期数据
 */
export interface RetentionPeriod {
  period: string
  retentionRate: number
  userCount: number
  baseUserCount: number
}

/**
 * 留存数据
 */
export interface RetentionData {
  name: string
  periods: RetentionPeriod[]
  chartType: string
}

/**
 * 用户来源项
 */
export interface UserSourceItem {
  sourceName: string
  userCount: number
  percentage: number
  sourceType: string
}

/**
 * 用户来源数据
 */
export interface UserSourceData {
  name: string
  sources: UserSourceItem[]
  chartType: string
}

/**
 * 时间范围
 */
export interface TimeRange {
  startDate: string
  endDate: string
  granularity: TimeGranularity
}

/**
 * 用户增长分析响应
 */
export interface UserGrowthAnalysisResponse {
  /** 是否成功 */
  success: boolean
  /** 响应消息 */
  message: string
  /** 聚合根ID */
  aggregateId: string
  /** 分析类型 */
  analysisType: string
  /** 时间范围 */
  timeRange: TimeRange
  /** 产品线ID列表 */
  productLineIds?: number[]
  /** 核心指标 */
  coreMetrics?: Record<string, MetricValue>
  /** 趋势数据 */
  trendData?: Record<string, TrendData>
  /** 留存数据 */
  retentionData?: Record<string, RetentionData>
  /** 用户来源数据 */
  userSourceData?: Record<string, UserSourceData>
  /** 数据权限范围 */
  dataScope: string
  /** 最后更新时间 */
  lastUpdateTime: string
  /** 数据摘要 */
  dataSummary?: string
}

/**
 * 用户增长统计摘要
 */
export interface UserGrowthSummary {
  totalNewUsers: number
  totalUsers: number
  growthRate: number
  retention1d: number
  retention7d: number
  retention30d: number
  primarySource: string
  calculatedAt: string
}

/**
 * 用户增长分析API服务
 */
export const userGrowthApi = {
  /**
   * 获取用户增长总览
   */
  getOverview(params: {
    startDate: string
    endDate: string
    granularity?: TimeGranularity
    productLineIds?: number[]
    includeComparison?: boolean
  }) {
    return http.get<ApiResponse<UserGrowthAnalysisResponse>>('/user/growth/public/overview', { params })
  },

  /**
   * 获取新增用户分析
   */
  getNewUserAnalysis(params: {
    startDate: string
    endDate: string
    granularity?: TimeGranularity
    productLineIds?: number[]
  }) {
    return http.get<ApiResponse<UserGrowthAnalysisResponse>>('/user/growth/public/new-users', { params })
  },

  /**
   * 获取用户留存分析
   */
  getRetentionAnalysis(params: {
    startDate: string
    endDate: string
    productLineIds?: number[]
    retentionPeriods?: number[]
  }) {
    return http.get<ApiResponse<UserGrowthAnalysisResponse>>('/user/growth/public/retention', { params })
  },

  /**
   * 获取增长趋势分析
   */
  getGrowthTrends(params: {
    startDate: string
    endDate: string
    granularity?: TimeGranularity
    productLineIds?: number[]
  }) {
    return http.get<ApiResponse<UserGrowthAnalysisResponse>>('/user/growth/public/trends', { params })
  },

  /**
   * 获取用户来源分析
   */
  getUserSourceAnalysis(params: {
    startDate: string
    endDate: string
    productLineIds?: number[]
    sourceTypes?: string[]
  }) {
    return http.get<ApiResponse<UserGrowthAnalysisResponse>>('/user/growth/public/sources', { params })
  },

  /**
   * 获取队列留存分析
   */
  getCohortRetentionAnalysis(params: {
    startDate: string
    endDate: string
    productLineIds?: number[]
  }) {
    return http.get<ApiResponse<UserGrowthAnalysisResponse>>('/user/growth/public/cohort-retention', { params })
  },

  /**
   * 获取产品线对比分析
   */
  getProductLineComparison(params: {
    startDate: string
    endDate: string
    productLineIds: number[]
  }) {
    return http.get<ApiResponse<UserGrowthAnalysisResponse[]>>('/user/growth/comparison', { params })
  },

  /**
   * 获取用户增长统计摘要
   */
  getGrowthSummary(params: {
    startDate: string
    endDate: string
  }) {
    return http.get<ApiResponse<UserGrowthSummary>>('/user/growth/summary', { params })
  },

  /**
   * 导出用户增长数据
   */
  exportData(data: UserGrowthAnalysisRequest) {
    return http.post<Blob>('/user/growth/export', data, {
      responseType: 'blob'
    })
  }
}
