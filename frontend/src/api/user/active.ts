import { http } from '@/utils/request'
import type { ApiResponse } from '@/types/common'

/**
 * 活跃用户分析API接口
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */

/**
 * 时间粒度枚举
 */
export type TimeGranularity = 'DAY' | 'WEEK' | 'MONTH' | 'QUARTER' | 'YEAR'

/**
 * 活跃用户分析请求参数
 */
export interface ActiveUserAnalysisRequest {
  /** 开始日期 */
  startDate: string
  /** 结束日期 */
  endDate: string
  /** 时间粒度 */
  granularity?: TimeGranularity
  /** 产品线ID列表 */
  productLineIds?: number[]
  /** 分析类型 */
  analysisType?: string
  /** 是否包含历史对比 */
  includeComparison?: boolean
  /** 是否包含详细数据 */
  includeDetails?: boolean
  /** 导出格式 */
  exportFormat?: string
}

/**
 * 指标值
 */
export interface MetricValue {
  /** 指标名称 */
  name: string
  /** 指标值 */
  value: number
  /** 上期值 */
  previousValue?: number
  /** 同期值 */
  yearOverYearValue?: number
  /** 单位 */
  unit: string
  /** 指标类型 */
  type: 'COUNT' | 'PERCENTAGE' | 'RATIO' | 'AMOUNT' | 'DURATION' | 'RATE'
  /** 格式化模式 */
  format?: string
  /** 计算时间 */
  calculatedAt: string
  /** 获取环比增长率 */
  getGrowthRate?: () => number
  /** 获取同比增长率 */
  getYearOverYearGrowthRate?: () => number
  /** 获取格式化的值 */
  getFormattedValue?: () => string
  /** 获取格式化的环比增长率 */
  getFormattedGrowthRate?: () => string
}

/**
 * 趋势数据
 */
export interface TrendData {
  /** 名称 */
  name: string
  /** 分类标签 */
  categories: string[]
  /** 数值 */
  values: number[]
  /** 单位 */
  unit: string
  /** 图表类型 */
  chartType: string
}

/**
 * 分布项
 */
export interface DistributionItem {
  /** 名称 */
  name: string
  /** 数值 */
  value: number
  /** 百分比 */
  percentage: number
  /** 颜色 */
  color: string
}

/**
 * 分布数据
 */
export interface DistributionData {
  /** 名称 */
  name: string
  /** 分布项列表 */
  items: DistributionItem[]
  /** 图表类型 */
  chartType: string
}

/**
 * 频次分析数据
 */
export interface FrequencyAnalysisData {
  /** 频次分布 */
  frequencyDistribution: Record<string, number>
  /** 频次留存 */
  retentionByFrequency: Record<string, number>
  /** 平均频次 */
  averageFrequency: number
  /** 总活跃用户数 */
  totalActiveUsers: number
}

/**
 * 活跃用户聚合数据
 */
export interface ActiveUserAggregate {
  /** 聚合根ID */
  id: string
  /** 分析类型 */
  analysisType: 'DAU' | 'WAU' | 'MAU' | 'FREQUENCY' | 'DISTRIBUTION' | 'RETENTION' | 'COHORT'
  /** 时间范围 */
  timeRange: {
    startDate: string
    endDate: string
    granularity: TimeGranularity
  }
  /** 产品线ID列表 */
  productLineIds?: number[]
  /** 核心指标 */
  coreMetrics?: Record<string, MetricValue>
  /** 趋势数据 */
  trendData?: Record<string, TrendData>
  /** 分布数据 */
  distributionData?: Record<string, DistributionData>
  /** 频次分析数据 */
  frequencyData?: FrequencyAnalysisData
  /** 数据权限范围 */
  dataScope: string
  /** 最后更新时间 */
  lastUpdateTime: string
}

/**
 * 活跃用户分析响应
 */
export interface ActiveUserAnalysisResponse {
  /** 是否成功 */
  success: boolean
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data: ActiveUserAggregate | null
  /** 响应时间戳 */
  timestamp: string
  /** 错误代码 */
  errorCode?: string
}

/**
 * 活跃用户分析API服务
 */
export const activeUserApi = {
  /**
   * 获取活跃用户总览
   */
  getOverview(params: {
    startDate: string
    endDate: string
    granularity?: TimeGranularity
    productLineIds?: number[]
    includeComparison?: boolean
  }) {
    return http.get<ApiResponse<ActiveUserAnalysisResponse>>('/user/active/overview', { params })
  },

  /**
   * 获取活跃用户趋势
   */
  getTrends(params: {
    startDate: string
    endDate: string
    granularity?: TimeGranularity
    productLineIds?: number[]
  }) {
    return http.get<ApiResponse<ActiveUserAnalysisResponse>>('/user/active/trends', { params })
  },

  /**
   * 获取活跃用户分布
   */
  getDistribution(params: {
    startDate: string
    endDate: string
    productLineIds?: number[]
  }) {
    return http.get<ApiResponse<ActiveUserAnalysisResponse>>('/user/active/distribution', { params })
  },

  /**
   * 获取活跃频次分析
   */
  getFrequency(params: {
    startDate: string
    endDate: string
    productLineIds?: number[]
  }) {
    return http.get<ApiResponse<ActiveUserAnalysisResponse>>('/user/active/frequency', { params })
  },

  /**
   * 获取产品线对比
   */
  getComparison(params: {
    startDate: string
    endDate: string
    productLineIds: number[]
  }) {
    return http.get<ApiResponse<ActiveUserAnalysisResponse>>('/user/active/comparison', { params })
  },

  /**
   * 获取活跃用户留存
   */
  getRetention(params: {
    startDate: string
    endDate: string
    productLineIds?: number[]
  }) {
    return http.get<ApiResponse<ActiveUserAnalysisResponse>>('/user/active/retention', { params })
  },

  /**
   * 获取队列分析
   */
  getCohort(params: {
    startDate: string
    endDate: string
    productLineIds?: number[]
  }) {
    return http.get<ApiResponse<ActiveUserAnalysisResponse>>('/user/active/cohort', { params })
  },

  /**
   * 导出活跃用户数据
   */
  exportData(data: ActiveUserAnalysisRequest) {
    return http.post<Blob>('/user/active/export', data, {
      responseType: 'blob'
    })
  }
}
