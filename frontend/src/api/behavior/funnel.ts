import { http } from '@/utils/request'
import type { ApiResponse } from '@/types/api'

/**
 * 时间粒度枚举
 */
export type TimeGranularity = 'HOUR' | 'DAY' | 'WEEK' | 'MONTH' | 'YEAR'

/**
 * 漏斗分析请求参数
 */
export interface FunnelAnalysisRequest {
  startDate: string
  endDate: string
  granularity?: TimeGranularity
  funnelSteps: string[]
  productLineIds?: number[]
  cohortPeriod?: string
  userId?: number
  dataScope?: string
  includeComparison?: boolean
  includeDetails?: boolean
  analysisType?: string
  filterCondition?: FunnelFilterCondition
}

/**
 * 漏斗筛选条件
 */
export interface FunnelFilterCondition {
  userSegmentIds?: string[]
  deviceTypes?: string[]
  operatingSystems?: string[]
  appVersions?: string[]
  regions?: string[]
  channels?: string[]
  minConversionRate?: number
  maxConversionRate?: number
  propertyFilters?: PropertyFilter[]
}

/**
 * 属性筛选器
 */
export interface PropertyFilter {
  propertyName: string
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'not_in' | 'contains' | 'not_contains'
  value: any
}

/**
 * 漏斗分析响应数据
 */
export interface FunnelAnalysisResponse {
  conversionData: FunnelConversionData
  dropoutAnalysis: FunnelDropoutAnalysis
  cohortAnalysis: FunnelCohortAnalysis
  optimizationSuggestions: FunnelOptimizationSuggestion[]
  summary: FunnelAnalysisSummary
  trends: FunnelTrendData[]
  segments: FunnelSegmentData[]
  updateTime: string
}

/**
 * 漏斗转化数据
 */
export interface FunnelConversionData {
  steps: FunnelStepData[]
  totalUsers: number
  overallConversionRate: number
  averageTimeToConvert: number
  conversionTrends: ConversionTrendData[]
}

/**
 * 漏斗步骤数据
 */
export interface FunnelStepData {
  stepId: string
  stepName: string
  stepOrder: number
  userCount: number
  conversionRate: number
  dropoutRate: number
  averageTimeFromPrevious: number
  retentionRate: number
}

/**
 * 转化趋势数据
 */
export interface ConversionTrendData {
  date: string
  stepId: string
  userCount: number
  conversionRate: number
}

/**
 * 漏斗流失分析
 */
export interface FunnelDropoutAnalysis {
  criticalDropoutPoints: DropoutPoint[]
  dropoutReasons: DropoutReason[]
  userSegmentDropouts: SegmentDropoutData[]
  improvementOpportunities: ImprovementOpportunity[]
}

/**
 * 流失点数据
 */
export interface DropoutPoint {
  fromStep: string
  toStep: string
  dropoutRate: number
  dropoutCount: number
  severity: 'high' | 'medium' | 'low'
  impact: number
}

/**
 * 流失原因
 */
export interface DropoutReason {
  reason: string
  affectedSteps: string[]
  userCount: number
  percentage: number
  category: string
}

/**
 * 用户分群流失数据
 */
export interface SegmentDropoutData {
  segmentId: string
  segmentName: string
  dropoutRate: number
  criticalSteps: string[]
}

/**
 * 改进机会
 */
export interface ImprovementOpportunity {
  stepId: string
  opportunity: string
  potentialImpact: number
  difficulty: 'easy' | 'medium' | 'hard'
  priority: 'high' | 'medium' | 'low'
}

/**
 * 队列漏斗分析
 */
export interface FunnelCohortAnalysis {
  cohorts: CohortData[]
  retentionMatrix: RetentionMatrixData
  cohortTrends: CohortTrendData[]
  insights: CohortInsight[]
}

/**
 * 队列数据
 */
export interface CohortData {
  cohortId: string
  cohortName: string
  startDate: string
  userCount: number
  conversionRates: number[]
  retentionRates: number[]
}

/**
 * 留存矩阵数据
 */
export interface RetentionMatrixData {
  periods: string[]
  cohorts: string[]
  matrix: number[][]
}

/**
 * 队列趋势数据
 */
export interface CohortTrendData {
  period: string
  cohortId: string
  conversionRate: number
  retentionRate: number
}

/**
 * 队列洞察
 */
export interface CohortInsight {
  type: 'improvement' | 'decline' | 'stable'
  cohortId: string
  description: string
  impact: number
  recommendation: string
}

/**
 * 漏斗优化建议
 */
export interface FunnelOptimizationSuggestion {
  stepId: string
  suggestionType: 'ui_improvement' | 'process_optimization' | 'content_enhancement' | 'technical_fix'
  title: string
  description: string
  expectedImpact: number
  implementationEffort: 'low' | 'medium' | 'high'
  priority: 'high' | 'medium' | 'low'
  estimatedLift: number
}

/**
 * 漏斗分析摘要
 */
export interface FunnelAnalysisSummary {
  totalFunnels: number
  averageConversionRate: number
  bestPerformingStep: string
  worstPerformingStep: string
  totalDropouts: number
  improvementPotential: number
  keyInsights: string[]
}

/**
 * 漏斗趋势数据
 */
export interface FunnelTrendData {
  date: string
  conversionRate: number
  userCount: number
  dropoutRate: number
}

/**
 * 漏斗分群数据
 */
export interface FunnelSegmentData {
  segmentId: string
  segmentName: string
  conversionRate: number
  userCount: number
  performance: 'above_average' | 'average' | 'below_average'
}

/**
 * 漏斗实时统计
 */
export interface FunnelRealTimeStats {
  currentActiveUsers: number
  todayConversions: number
  todayConversionRate: number
  realTimeDropouts: number
  activeSteps: FunnelStepRealTimeData[]
  alerts: FunnelAlert[]
}

/**
 * 漏斗步骤实时数据
 */
export interface FunnelStepRealTimeData {
  stepId: string
  currentUsers: number
  conversionRate: number
  status: 'normal' | 'warning' | 'critical'
}

/**
 * 漏斗告警
 */
export interface FunnelAlert {
  type: 'conversion_drop' | 'unusual_dropout' | 'performance_issue'
  stepId: string
  severity: 'high' | 'medium' | 'low'
  message: string
  timestamp: string
}

/**
 * 漏斗分析 API 服务
 */
export const funnelAnalysisApi = {
  /**
   * 获取漏斗转化分析
   */
  getFunnelConversionAnalysis(params: {
    startDate: string
    endDate: string
    funnelSteps: string[]
    productLineIds?: number[]
  }) {
    return http.get<FunnelAnalysisResponse>('/behavior/funnel/conversion-analysis', { params })
  },

  /**
   * 获取流失点分析
   */
  getDropoutAnalysis(params: {
    startDate: string
    endDate: string
    funnelSteps: string[]
    productLineIds?: number[]
  }) {
    return http.get<FunnelAnalysisResponse>('/behavior/funnel/dropout-analysis', { params })
  },

  /**
   * 获取队列漏斗分析
   */
  getCohortFunnelAnalysis(params: {
    startDate: string
    endDate: string
    funnelSteps: string[]
    cohortPeriod: string
    productLineIds?: number[]
  }) {
    return http.get<FunnelAnalysisResponse>('/behavior/funnel/cohort-analysis', { params })
  },

  /**
   * 获取漏斗优化建议
   */
  getOptimizationSuggestions(params: {
    startDate: string
    endDate: string
    funnelSteps: string[]
    productLineIds?: number[]
  }) {
    return http.get<FunnelOptimizationSuggestion[]>('/behavior/funnel/optimization-suggestions', { params })
  },

  /**
   * 获取漏斗步骤列表
   */
  getFunnelStepList(params?: {
    productLineIds?: number[]
  }) {
    return http.get<any[]>('/behavior/funnel/steps', { params })
  },

  /**
   * 获取实时漏斗统计
   */
  getRealTimeStats(params?: {
    funnelSteps?: string[]
    productLineIds?: number[]
  }) {
    return http.get<FunnelRealTimeStats>('/behavior/funnel/realtime-stats', { params })
  },

  /**
   * 获取漏斗分析统计摘要
   */
  getSummary(params: {
    startDate: string
    endDate: string
  }) {
    return http.get<FunnelAnalysisSummary>('/behavior/funnel/summary', { params })
  },

  /**
   * 导出漏斗分析数据
   */
  exportData(data: FunnelAnalysisRequest) {
    return http.post<Blob>('/behavior/funnel/export', data, {
      responseType: 'blob'
    })
  }
}
