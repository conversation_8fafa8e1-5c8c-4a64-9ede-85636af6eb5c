import { http } from '@/utils/request'
import type { ApiResponse } from '@/types/api'

/**
 * 时间粒度枚举
 */
export type TimeGranularity = 'HOUR' | 'DAY' | 'WEEK' | 'MONTH' | 'YEAR'

/**
 * 用户路径分析请求参数
 */
export interface UserPathAnalysisRequest {
  startDate: string
  endDate: string
  granularity?: TimeGranularity
  startNodes: string[]
  endNodes?: string[]
  productLineIds?: number[]
  userId?: number
  dataScope?: string
  includeComparison?: boolean
  includeDetails?: boolean
  analysisType?: string
  filterCondition?: PathFilterCondition
}

/**
 * 路径筛选条件
 */
export interface PathFilterCondition {
  userSegmentIds?: string[]
  deviceTypes?: string[]
  operatingSystems?: string[]
  appVersions?: string[]
  regions?: string[]
  channels?: string[]
  minPathLength?: number
  maxPathLength?: number
  minDuration?: number
  maxDuration?: number
  propertyFilters?: PropertyFilter[]
}

/**
 * 属性筛选器
 */
export interface PropertyFilter {
  propertyName: string
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'not_in' | 'contains' | 'not_contains'
  value: any
}

/**
 * 用户路径分析响应数据
 */
export interface UserPathAnalysisResponse {
  flowAnalysis: PathFlowAnalysis
  statisticsAnalysis: PathStatisticsAnalysis
  anomalyDetection: PathAnomalyDetection
  efficiencyAnalysis: PathEfficiencyAnalysis
  comparisonAnalysis: PathComparisonAnalysis
  optimizationSuggestions: PathOptimizationSuggestion[]
  summary: PathAnalysisSummary
  trends: PathTrendData[]
  updateTime: string
}

/**
 * 路径流向分析
 */
export interface PathFlowAnalysis {
  nodes: PathNode[]
  edges: PathEdge[]
  flowMetrics: FlowMetrics
  sankeyData: SankeyData
  heatmapData: HeatmapData
}

/**
 * 路径节点
 */
export interface PathNode {
  nodeId: string
  nodeName: string
  nodeType: string
  userCount: number
  visitCount: number
  averageDuration: number
  bounceRate: number
  conversionRate: number
  position: { x: number; y: number }
}

/**
 * 路径边
 */
export interface PathEdge {
  fromNode: string
  toNode: string
  userCount: number
  transitionRate: number
  averageTime: number
  strength: number
}

/**
 * 流向指标
 */
export interface FlowMetrics {
  totalPaths: number
  uniqueUsers: number
  averagePathLength: number
  averageSessionDuration: number
  mostCommonPath: string[]
  conversionPaths: string[]
}

/**
 * 桑基图数据
 */
export interface SankeyData {
  nodes: { name: string; value: number }[]
  links: { source: string; target: string; value: number }[]
}

/**
 * 热力图数据
 */
export interface HeatmapData {
  matrix: number[][]
  labels: string[]
  maxValue: number
  minValue: number
}

/**
 * 路径统计分析
 */
export interface PathStatisticsAnalysis {
  pathLengthDistribution: PathLengthData[]
  completionRates: CompletionRateData[]
  durationAnalysis: DurationAnalysisData
  dropoffPoints: DropoffPointData[]
  popularPaths: PopularPathData[]
}

/**
 * 路径长度数据
 */
export interface PathLengthData {
  pathLength: number
  userCount: number
  percentage: number
  averageDuration: number
}

/**
 * 完成率数据
 */
export interface CompletionRateData {
  pathType: string
  completionRate: number
  userCount: number
  averageTime: number
}

/**
 * 持续时间分析数据
 */
export interface DurationAnalysisData {
  averageDuration: number
  medianDuration: number
  durationDistribution: { range: string; count: number }[]
  durationByPath: { path: string; duration: number }[]
}

/**
 * 流失点数据
 */
export interface DropoffPointData {
  nodeId: string
  nodeName: string
  dropoffRate: number
  dropoffCount: number
  severity: 'high' | 'medium' | 'low'
}

/**
 * 热门路径数据
 */
export interface PopularPathData {
  path: string[]
  userCount: number
  percentage: number
  averageDuration: number
  conversionRate: number
}

/**
 * 路径异常检测
 */
export interface PathAnomalyDetection {
  anomalies: PathAnomaly[]
  unusualPatterns: UnusualPattern[]
  alerts: PathAlert[]
  recommendations: AnomalyRecommendation[]
}

/**
 * 路径异常
 */
export interface PathAnomaly {
  type: 'unusual_flow' | 'high_dropout' | 'long_duration' | 'loop_behavior'
  nodeId: string
  description: string
  severity: 'high' | 'medium' | 'low'
  affectedUsers: number
  detectedAt: string
}

/**
 * 异常模式
 */
export interface UnusualPattern {
  pattern: string
  frequency: number
  deviation: number
  impact: 'positive' | 'negative' | 'neutral'
}

/**
 * 路径告警
 */
export interface PathAlert {
  alertType: 'performance' | 'behavior' | 'conversion'
  message: string
  severity: 'high' | 'medium' | 'low'
  timestamp: string
  affectedPaths: string[]
}

/**
 * 异常建议
 */
export interface AnomalyRecommendation {
  anomalyType: string
  recommendation: string
  priority: 'high' | 'medium' | 'low'
  expectedImpact: number
}

/**
 * 路径效率分析
 */
export interface PathEfficiencyAnalysis {
  efficiencyMetrics: EfficiencyMetrics
  bottlenecks: Bottleneck[]
  optimizationOpportunities: OptimizationOpportunity[]
  benchmarks: EfficiencyBenchmark[]
}

/**
 * 效率指标
 */
export interface EfficiencyMetrics {
  averagePathEfficiency: number
  timeToConversion: number
  stepEfficiency: { stepId: string; efficiency: number }[]
  userExperienceScore: number
}

/**
 * 瓶颈
 */
export interface Bottleneck {
  nodeId: string
  nodeName: string
  bottleneckType: 'time' | 'conversion' | 'usability'
  impact: number
  severity: 'high' | 'medium' | 'low'
}

/**
 * 优化机会
 */
export interface OptimizationOpportunity {
  nodeId: string
  opportunityType: string
  description: string
  potentialImprovement: number
  implementationEffort: 'low' | 'medium' | 'high'
}

/**
 * 效率基准
 */
export interface EfficiencyBenchmark {
  metric: string
  currentValue: number
  benchmarkValue: number
  performance: 'above' | 'at' | 'below'
}

/**
 * 路径对比分析
 */
export interface PathComparisonAnalysis {
  comparisonMetrics: ComparisonMetrics
  pathPerformance: PathPerformanceComparison[]
  insights: ComparisonInsight[]
}

/**
 * 对比指标
 */
export interface ComparisonMetrics {
  totalPaths: number
  bestPerformingPath: string
  worstPerformingPath: string
  averageImprovement: number
}

/**
 * 路径性能对比
 */
export interface PathPerformanceComparison {
  pathId: string
  pathName: string
  conversionRate: number
  averageDuration: number
  userSatisfaction: number
  efficiency: number
  rank: number
}

/**
 * 对比洞察
 */
export interface ComparisonInsight {
  type: 'performance' | 'behavior' | 'conversion'
  insight: string
  impact: number
  recommendation: string
}

/**
 * 路径优化建议
 */
export interface PathOptimizationSuggestion {
  nodeId: string
  suggestionType: 'ui_improvement' | 'process_optimization' | 'content_enhancement' | 'navigation_fix'
  title: string
  description: string
  expectedImpact: number
  implementationEffort: 'low' | 'medium' | 'high'
  priority: 'high' | 'medium' | 'low'
  estimatedLift: number
}

/**
 * 路径分析摘要
 */
export interface PathAnalysisSummary {
  totalPaths: number
  uniqueUsers: number
  averagePathLength: number
  overallConversionRate: number
  criticalIssues: number
  improvementPotential: number
  keyInsights: string[]
}

/**
 * 路径趋势数据
 */
export interface PathTrendData {
  date: string
  pathCount: number
  userCount: number
  conversionRate: number
  averageDuration: number
}

/**
 * 路径节点信息
 */
export interface PathNodeInfo {
  nodeId: string
  nodeName: string
  nodeType: string
  category: string
  description?: string
}

/**
 * 路径实时统计
 */
export interface PathRealTimeStats {
  currentActiveUsers: number
  todayPaths: number
  realTimeConversions: number
  activeNodes: PathNodeRealTimeData[]
  alerts: PathAlert[]
}

/**
 * 路径节点实时数据
 */
export interface PathNodeRealTimeData {
  nodeId: string
  currentUsers: number
  conversionRate: number
  status: 'normal' | 'warning' | 'critical'
}

/**
 * 用户路径分析 API 服务
 */
export const userPathAnalysisApi = {
  /**
   * 获取路径流向分析
   */
  getPathFlowAnalysis(params: {
    startDate: string
    endDate: string
    startNodes: string[]
    endNodes?: string[]
    productLineIds?: number[]
  }) {
    return http.get<UserPathAnalysisResponse>('/behavior/path/flow-analysis', { params })
  },

  /**
   * 获取路径统计分析
   */
  getPathStatisticsAnalysis(params: {
    startDate: string
    endDate: string
    startNodes: string[]
    endNodes?: string[]
    productLineIds?: number[]
  }) {
    return http.get<UserPathAnalysisResponse>('/behavior/path/statistics-analysis', { params })
  },

  /**
   * 获取异常路径检测
   */
  getAnomalyPathDetection(params: {
    startDate: string
    endDate: string
    startNodes: string[]
    endNodes?: string[]
    productLineIds?: number[]
  }) {
    return http.get<UserPathAnalysisResponse>('/behavior/path/anomaly-detection', { params })
  },

  /**
   * 获取路径效率分析
   */
  getPathEfficiencyAnalysis(params: {
    startDate: string
    endDate: string
    startNodes: string[]
    endNodes?: string[]
    productLineIds?: number[]
  }) {
    return http.get<UserPathAnalysisResponse>('/behavior/path/efficiency-analysis', { params })
  },

  /**
   * 获取路径对比分析
   */
  getPathComparisonAnalysis(data: UserPathAnalysisRequest) {
    return http.post<UserPathAnalysisResponse>('/behavior/path/comparison-analysis', data)
  },

  /**
   * 获取路径节点列表
   */
  getPathNodeList(params?: {
    productLineIds?: number[]
  }) {
    return http.get<PathNodeInfo[]>('/behavior/path/nodes', { params })
  },

  /**
   * 获取实时路径统计
   */
  getRealTimeStats(params?: {
    startNodes?: string[]
    endNodes?: string[]
    productLineIds?: number[]
  }) {
    return http.get<PathRealTimeStats>('/behavior/path/realtime-stats', { params })
  },

  /**
   * 获取路径优化建议
   */
  getOptimizationSuggestions(params: {
    startDate: string
    endDate: string
    startNodes: string[]
    endNodes?: string[]
    productLineIds?: number[]
  }) {
    return http.get<PathOptimizationSuggestion[]>('/behavior/path/optimization-suggestions', { params })
  },

  /**
   * 导出路径数据
   */
  exportData(data: UserPathAnalysisRequest) {
    return http.post<Blob>('/behavior-analysis/user-paths/export', data, {
      responseType: 'blob'
    })
  }
}
