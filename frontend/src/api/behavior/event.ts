import { http } from '@/utils/request'
import type { ApiResponse } from '@/types/api'

/**
 * 时间粒度枚举
 */
export type TimeGranularity = 'HOUR' | 'DAY' | 'WEEK' | 'MONTH' | 'YEAR'

/**
 * 事件分析请求参数
 */
export interface EventAnalysisRequest {
  startDate: string
  endDate: string
  granularity?: TimeGranularity
  productLineIds?: number[]
  eventIds?: string[]
  startEventIds?: string[]
  endEventIds?: string[]
  eventId?: string
  propertyNames?: string[]
  userId?: number
  dataScope?: string
  includeComparison?: boolean
  includeDetails?: boolean
  analysisType?: string
  filterCondition?: EventFilterCondition
}

/**
 * 事件筛选条件
 */
export interface EventFilterCondition {
  eventCategories?: string[]
  userSegmentIds?: string[]
  deviceTypes?: string[]
  operatingSystems?: string[]
  appVersions?: string[]
  regions?: string[]
  channels?: string[]
  minEventCount?: number
  maxEventCount?: number
  minUserCount?: number
  maxUserCount?: number
  propertyFilters?: PropertyFilter[]
}

/**
 * 属性筛选条件
 */
export interface PropertyFilter {
  propertyName: string
  operator: string
  propertyValue?: string
  propertyValues?: string[]
}

/**
 * 事件分析响应数据
 */
export interface EventAnalysisResponse {
  analysisId: string
  analysisType: string
  timeRange: {
    startDate: string
    endDate: string
    granularity: TimeGranularity
  }
  coreMetrics: Record<string, MetricValue>
  trendData?: Record<string, EventTrendData>
  funnelData?: Record<string, EventFunnelData>
  pathData?: Record<string, EventPathData>
  eventPropertyData?: Record<string, EventPropertyData>
  lastUpdateTime: string
  dataStatus: string
  errorMessage?: string
}

/**
 * 指标值
 */
export interface MetricValue {
  name: string
  value: number
  previousValue?: number
  changeRate?: number
  unit?: string
  format?: string
}

/**
 * 事件趋势数据
 */
export interface EventTrendData {
  eventId: string
  eventName: string
  categories: string[]
  triggerCounts: number[]
  userCounts: number[]
  unit: string
  chartType: string
}

/**
 * 事件漏斗数据
 */
export interface EventFunnelData {
  funnelName: string
  steps: FunnelStepData[]
  totalConversionRate: number
  totalUsers: number
  timeRange: string
}

/**
 * 漏斗步骤数据
 */
export interface FunnelStepData {
  eventId: string
  eventName: string
  userCount: number
  conversionRate: number
  dropOffRate: number
  stepOrder: number
}

/**
 * 事件路径数据
 */
export interface EventPathData {
  pathName: string
  nodes: PathNodeData[]
  links: PathLinkData[]
  totalUsers: number
  pathType: string
}

/**
 * 路径节点数据
 */
export interface PathNodeData {
  eventId: string
  eventName: string
  userCount: number
  percentage: number
  level: number
}

/**
 * 路径链接数据
 */
export interface PathLinkData {
  sourceEventId: string
  targetEventId: string
  userCount: number
  percentage: number
  avgTimeSpent: number
}

/**
 * 事件属性数据
 */
export interface EventPropertyData {
  eventId: string
  propertyName: string
  propertyValues: Record<string, number>
  dataType: string
  totalCount: number
}

/**
 * 事件信息
 */
export interface EventInfo {
  eventId: string
  eventName: string
  eventCategory: string
  description: string
  properties: string[]
  productLineId: number
  productLineName: string
  isActive: boolean
}

/**
 * 实时事件统计
 */
export interface EventRealTimeStats {
  totalEvents: number
  uniqueUsers: number
  avgEventsPerUser: number
  topEvents: EventStats[]
  lastUpdateTime: string
}

/**
 * 事件统计数据
 */
export interface EventStats {
  eventId: string
  eventName: string
  eventCount: number
  userCount: number
  percentage: number
}

/**
 * 事件分析统计摘要
 */
export interface EventAnalysisSummary {
  totalEvents: number
  uniqueUsers: number
  activeEvents: number
  avgEventsPerUser: number
  topEventCategories: string[]
  summaryTime: string
}

/**
 * 事件分析API服务
 */
export const eventAnalysisApi = {
  /**
   * 获取事件趋势分析
   */
  getEventTrends(params: {
    startDate: string
    endDate: string
    granularity?: TimeGranularity
    eventIds: string[]
    productLineIds?: number[]
    includeComparison?: boolean
  }) {
    return http.get<EventAnalysisResponse>('/behavior/event/public/trends', { params })
  },

  /**
   * 获取事件漏斗分析
   */
  getEventFunnel(params: {
    startDate: string
    endDate: string
    eventIds: string[]
    productLineIds?: number[]
  }) {
    return http.get<EventAnalysisResponse>('/behavior/event/public/funnel', { params })
  },

  /**
   * 获取事件路径分析
   */
  getEventPath(params: {
    startDate: string
    endDate: string
    startEventIds: string[]
    endEventIds?: string[]
    productLineIds?: number[]
  }) {
    return http.get<EventAnalysisResponse>('/behavior/event/public/path', { params })
  },

  /**
   * 获取事件对比分析
   */
  getEventComparison(params: {
    startDate: string
    endDate: string
    granularity?: TimeGranularity
    eventIds: string[]
    productLineIds?: number[]
  }) {
    return http.get<EventAnalysisResponse>('/behavior/event/public/comparison', { params })
  },

  /**
   * 获取事件属性分析
   */
  getEventProperty(params: {
    startDate: string
    endDate: string
    eventId: string
    propertyNames: string[]
    productLineIds?: number[]
  }) {
    return http.get<EventAnalysisResponse>('/behavior/event/public/property', { params })
  },

  /**
   * 获取事件列表
   */
  getEventList(params?: {
    productLineIds?: number[]
  }) {
    return http.get<EventInfo[]>('/behavior/event/public/list', { params })
  },

  /**
   * 获取实时事件统计
   */
  getRealTimeStats(params?: {
    eventIds?: string[]
    productLineIds?: number[]
  }) {
    return http.get<EventRealTimeStats>('/behavior/event/public/realtime-stats', { params })
  },

  /**
   * 获取事件分析统计摘要
   */
  getSummary(params: {
    startDate: string
    endDate: string
  }) {
    return http.get<EventAnalysisSummary>('/behavior/event/public/summary', { params })
  },

  /**
   * 导出事件分析数据
   */
  exportData(data: EventAnalysisRequest) {
    return http.post<Blob>('/behavior/event/export', data, {
      responseType: 'blob'
    })
  }
}
