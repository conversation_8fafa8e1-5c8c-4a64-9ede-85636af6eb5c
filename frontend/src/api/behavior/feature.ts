import { http } from '@/utils/request'
import type { ApiResponse } from '@/types/api'

/**
 * 时间粒度枚举
 */
export type TimeGranularity = 'HOUR' | 'DAY' | 'WEEK' | 'MONTH' | 'YEAR'

/**
 * 功能使用分析请求参数
 */
export interface FeatureUsageRequest {
  startDate: string
  endDate: string
  granularity?: TimeGranularity
  productLineIds?: number[]
  featureIds?: string[]
  startFeatureIds?: string[]
  endFeatureIds?: string[]
  featureId?: string
  featureCategories?: string[]
  userId?: number
  dataScope?: string
  includeComparison?: boolean
  includeDetails?: boolean
  analysisType?: string
  filterCondition?: FeatureFilterCondition
}

/**
 * 功能筛选条件
 */
export interface FeatureFilterCondition {
  featureTypes?: string[]
  priorities?: number[]
  versions?: string[]
  userSegmentIds?: string[]
  deviceTypes?: string[]
  operatingSystems?: string[]
  appVersions?: string[]
  regions?: string[]
  channels?: string[]
  minUsageCount?: number
  maxUsageCount?: number
  minUserCount?: number
  maxUserCount?: number
  minUsageRate?: number
  maxUsageRate?: number
  propertyFilters?: PropertyFilter[]
}

/**
 * 属性筛选条件
 */
export interface PropertyFilter {
  propertyName: string
  operator: string
  propertyValue?: string
  propertyValues?: string[]
}

/**
 * 功能使用分析响应数据
 */
export interface FeatureUsageResponse {
  analysisId: string
  analysisType: string
  timeRange: {
    startDate: string
    endDate: string
    granularity: TimeGranularity
  }
  coreMetrics: Record<string, MetricValue>
  usageStats?: Record<string, FeatureUsageStats>
  heatData?: Record<string, FeatureHeatData>
  pathData?: Record<string, FeaturePathData>
  valueData?: Record<string, FeatureValueData>
  satisfactionData?: Record<string, FeatureSatisfactionData>
  lastUpdateTime: string
  dataStatus: string
  errorMessage?: string
}

/**
 * 指标值
 */
export interface MetricValue {
  name: string
  value: number
  previousValue?: number
  changeRate?: number
  unit?: string
  format?: string
}

/**
 * 功能使用统计数据
 */
export interface FeatureUsageStats {
  featureId: string
  featureName: string
  featureCategory: string
  totalUsageCount: number
  uniqueUsers: number
  avgUsagePerUser: number
  usageRate: number
  categories: string[]
  usageCounts: number[]
  userCounts: number[]
  trendDirection: string
  growthRate: number
}

/**
 * 功能热度数据
 */
export interface FeatureHeatData {
  featureId: string
  featureName: string
  heatScore: number
  heatRank: number
  popularityIndex: number
  engagementScore: number
  retentionRate: number
  heatMapPoints: HeatMapPoint[]
  heatLevel: string
  changeRate: number
}

/**
 * 热力图点数据
 */
export interface HeatMapPoint {
  dimension: string
  category: string
  value: number
  color: string
  intensity: number
}

/**
 * 功能路径数据
 */
export interface FeaturePathData {
  pathName: string
  nodes: FeaturePathNode[]
  links: FeaturePathLink[]
  totalUsers: number
  avgPathLength: number
  mostCommonPath: string
  topPaths: string[]
}

/**
 * 功能路径节点数据
 */
export interface FeaturePathNode {
  featureId: string
  featureName: string
  userCount: number
  percentage: number
  level: number
  avgTimeSpent: number
  nodeType: string
}

/**
 * 功能路径链接数据
 */
export interface FeaturePathLink {
  sourceFeatureId: string
  targetFeatureId: string
  userCount: number
  percentage: number
  avgTransitionTime: number
  conversionRate: number
  linkStrength: string
}

/**
 * 功能价值数据
 */
export interface FeatureValueData {
  featureId: string
  featureName: string
  valueScore: number
  valueRank: number
  businessImpact: number
  userSatisfactionImpact: number
  retentionImpact: number
  revenueContribution: number
  valueMetrics: ValueMetric[]
  valueLevel: string
}

/**
 * 价值指标数据
 */
export interface ValueMetric {
  metricName: string
  metricValue: number
  metricUnit: string
  weight: number
  description: string
}

/**
 * 功能满意度数据
 */
export interface FeatureSatisfactionData {
  featureId: string
  featureName: string
  satisfactionScore: number
  satisfactionRank: number
  usabilityScore: number
  utilityScore: number
  reliabilityScore: number
  factors: SatisfactionFactor[]
  positiveComments: string[]
  negativeComments: string[]
  satisfactionLevel: string
}

/**
 * 满意度因子数据
 */
export interface SatisfactionFactor {
  factorName: string
  factorScore: number
  weight: number
  impact: string
  description: string
}

/**
 * 功能信息
 */
export interface FeatureInfo {
  featureId: string
  featureName: string
  featureCategory: string
  description: string
  featureType: string
  productLineId: number
  productLineName: string
  isActive: boolean
  priority: number
  version: string
}

/**
 * 实时功能使用统计
 */
export interface FeatureRealTimeStats {
  totalUsage: number
  uniqueUsers: number
  avgUsagePerUser: number
  topFeatures: FeatureStats[]
  lastUpdateTime: string
}

/**
 * 功能统计数据
 */
export interface FeatureStats {
  featureId: string
  featureName: string
  usageCount: number
  userCount: number
  usageRate: number
  trendDirection: string
}

/**
 * 功能使用分析统计摘要
 */
export interface FeatureUsageAnalysisSummary {
  totalFeatures: number
  activeFeatures: number
  totalUsage: number
  uniqueUsers: number
  avgUsagePerFeature: number
  topFeatureCategories: string[]
  summaryTime: string
}

/**
 * 功能使用趋势对比
 */
export interface FeatureUsageTrendComparison {
  currentPeriodData: FeatureTrendData[]
  previousPeriodData: FeatureTrendData[]
  trendChanges: FeatureTrendChange[]
  overallGrowthRate: number
  comparisonTime: string
}

/**
 * 功能趋势数据
 */
export interface FeatureTrendData {
  featureId: string
  featureName: string
  usageCount: number
  userCount: number
  usageRate: number
  timeLabel: string
}

/**
 * 功能趋势变化
 */
export interface FeatureTrendChange {
  featureId: string
  featureName: string
  usageGrowthRate: number
  userGrowthRate: number
  changeDirection: string
  changeLevel: string
}

/**
 * 功能使用分析API服务
 */
export const featureUsageApi = {
  /**
   * 获取功能使用统计
   */
  getFeatureUsageStatistics(params: {
    startDate: string
    endDate: string
    granularity?: TimeGranularity
    featureIds: string[]
    productLineIds?: number[]
    includeComparison?: boolean
  }) {
    return http.get<FeatureUsageResponse>('/behavior/feature/usage-statistics', { params })
  },

  /**
   * 获取功能热度分析
   */
  getFeatureHeatAnalysis(params: {
    startDate: string
    endDate: string
    featureIds?: string[]
    productLineIds?: number[]
  }) {
    return http.get<FeatureUsageResponse>('/behavior/feature/heat-analysis', { params })
  },

  /**
   * 获取功能路径分析
   */
  getFeaturePathAnalysis(params: {
    startDate: string
    endDate: string
    startFeatureIds: string[]
    endFeatureIds?: string[]
    productLineIds?: number[]
  }) {
    return http.get<FeatureUsageResponse>('/behavior/feature/path-analysis', { params })
  },

  /**
   * 获取功能价值贡献分析
   */
  getFeatureValueContribution(params: {
    startDate: string
    endDate: string
    featureIds: string[]
    productLineIds?: number[]
  }) {
    return http.get<FeatureUsageResponse>('/behavior/feature/value-contribution', { params })
  },

  /**
   * 获取功能满意度评估
   */
  getFeatureSatisfactionEvaluation(params: {
    startDate: string
    endDate: string
    featureIds: string[]
    productLineIds?: number[]
  }) {
    return http.get<FeatureUsageResponse>('/behavior/feature/satisfaction-evaluation', { params })
  },

  /**
   * 获取功能列表
   */
  getFeatureList(params?: {
    productLineIds?: number[]
  }) {
    return http.get<FeatureInfo[]>('/behavior/feature/list', { params })
  },

  /**
   * 获取实时功能使用统计
   */
  getRealTimeStats(params?: {
    featureIds?: string[]
    productLineIds?: number[]
  }) {
    return http.get<FeatureRealTimeStats>('/behavior/feature/realtime-stats', { params })
  },

  /**
   * 获取功能使用分析统计摘要
   */
  getSummary(params: {
    startDate: string
    endDate: string
  }) {
    return http.get<FeatureUsageAnalysisSummary>('/behavior/feature/summary', { params })
  },

  /**
   * 获取功能使用趋势对比
   */
  getTrendComparison(params: {
    startDate: string
    endDate: string
    featureIds?: string[]
    productLineIds?: number[]
  }) {
    return http.get<FeatureUsageTrendComparison>('/behavior/feature/trend-comparison', { params })
  },

  /**
   * 导出功能使用分析数据
   */
  exportData(data: FeatureUsageRequest) {
    return http.post<Blob>('/behavior/feature/export', data, {
      responseType: 'blob'
    })
  }
}
