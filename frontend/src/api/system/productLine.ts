import { http } from '@/utils/request'

export interface ProductLineCreateRequest {
  name: string
  code: string
  type: number
  description?: string
  dataSourceConfig?: any
  status?: string
  remark?: string
}

export interface ProductLineUpdateRequest {
  name?: string
  description?: string
  dataSourceConfig?: any
  status?: string
  remark?: string
}

export interface ProductLineListParams {
  page: number
  size: number
  keyword?: string
  type?: number
  status?: string
}

export interface ProductLineDetailResponse {
  id: number
  name: string
  code: string
  type: number
  typeName: string
  description?: string
  dataSourceConfig?: any
  status: string
  userCount: number
  dataCount: number
  lastSyncTime?: string
  createdAt: string
  updatedAt: string
  remark?: string
}

export interface ProductLineSimpleResponse {
  id: number
  name: string
  code: string
  type: number
  typeName: string
  status: string
}

export interface ProductLineListResponse {
  items: ProductLineDetailResponse[]
  total: number
  page: number
  size: number
  totalPages: number
}

export interface ProductLineStatsResponse {
  totalCount: number
  activeCount: number
  inactiveCount: number
  typeDistribution: Array<{
    type: number
    typeName: string
    count: number
  }>
  recentActivity: Array<{
    productLineId: number
    productLineName: string
    action: string
    timestamp: string
  }>
}

export const productLineApi = {
  /**
   * 创建产品线
   */
  createProductLine(data: ProductLineCreateRequest) {
    return http.post<number>('/admin/product-lines', data)
  },

  /**
   * 更新产品线
   */
  updateProductLine(id: number, data: ProductLineUpdateRequest) {
    return http.put<void>(`/admin/product-lines/${id}`, data)
  },

  /**
   * 删除产品线
   */
  deleteProductLine(id: number) {
    return http.delete<void>(`/admin/product-lines/${id}`)
  },

  /**
   * 获取产品线详情
   */
  getProductLineDetail(id: number) {
    return http.get<ProductLineDetailResponse>(`/admin/product-lines/${id}`)
  },

  /**
   * 分页查询产品线列表
   */
  getProductLineList(params: ProductLineListParams) {
    return http.get<ProductLineListResponse>('/admin/product-lines', { params })
  },

  /**
   * 获取所有启用的产品线
   */
  getAllEnabledProductLines() {
    return http.get<ProductLineSimpleResponse[]>('/admin/product-lines/enabled')
  },

  /**
   * 启用产品线
   */
  enableProductLine(id: number) {
    return http.put<void>(`/admin/product-lines/${id}/enable`)
  },

  /**
   * 禁用产品线
   */
  disableProductLine(id: number) {
    return http.put<void>(`/admin/product-lines/${id}/disable`)
  },

  /**
   * 测试产品线数据源连接
   */
  testDataSourceConnection(id: number) {
    return http.post<boolean>(`/admin/product-lines/${id}/test-connection`)
  },

  /**
   * 同步产品线数据
   */
  syncProductLineData(id: number) {
    return http.post<void>(`/admin/product-lines/${id}/sync`)
  },

  /**
   * 获取产品线统计信息
   */
  getProductLineStats() {
    return http.get<ProductLineStatsResponse>('/admin/product-lines/stats')
  },

  /**
   * 获取产品线类型列表
   */
  getProductLineTypes() {
    return http.get<Array<{ value: number; label: string }>>('/admin/product-lines/types')
  },

  /**
   * 检查产品线代码是否可用
   */
  checkProductLineCode(code: string, excludeId?: number) {
    return http.get<boolean>('/admin/product-lines/check-code', {
      params: { code, excludeId }
    })
  },

  /**
   * 获取产品线用户列表
   */
  getProductLineUsers(id: number) {
    return http.get<any[]>(`/admin/product-lines/${id}/users`)
  },

  /**
   * 为产品线分配用户
   */
  assignUsersToProductLine(id: number, userIds: number[]) {
    return http.post<void>(`/admin/product-lines/${id}/users`, { userIds })
  },

  /**
   * 从产品线移除用户
   */
  removeUsersFromProductLine(id: number, userIds: number[]) {
    return http.delete<void>(`/admin/product-lines/${id}/users`, { data: { userIds } })
  },

  /**
   * 批量启用产品线
   */
  enableProductLinesBatch(ids: number[]) {
    return http.put<void>('/admin/product-lines/batch/enable', { data: ids })
  },

  /**
   * 批量禁用产品线
   */
  disableProductLinesBatch(ids: number[]) {
    return http.put<void>('/admin/product-lines/batch/disable', { data: ids })
  },

  /**
   * 导出产品线配置
   */
  exportProductLineConfig(id: number) {
    return http.get<Blob>(`/admin/product-lines/${id}/export`, { responseType: 'blob' })
  },

  /**
   * 导入产品线配置
   */
  importProductLineConfig(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return http.post<void>('/admin/product-lines/import', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  }
}
