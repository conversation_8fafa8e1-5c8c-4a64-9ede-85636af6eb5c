import { http } from '@/utils/request'

export interface ProductVersionCreateRequest {
  productLineId: number
  versionNumber: string
  versionName?: string
  description: string
  releaseNotes?: string
  versionType: number
  plannedDate?: string
  fileSize?: number
  downloadUrl?: string
  platforms?: string[]
  features?: string[]
  bugFixes?: string[]
  breakingChanges?: string[]
  dependencies?: string[]
  systemRequirements?: string[]
  checksumMd5?: string
  checksumSha256?: string
  remark?: string
}

export interface ProductVersionUpdateRequest {
  versionName?: string
  description?: string
  releaseNotes?: string
  versionType?: number
  plannedDate?: string
  fileSize?: number
  downloadUrl?: string
  platforms?: string[]
  features?: string[]
  bugFixes?: string[]
  breakingChanges?: string[]
  dependencies?: string[]
  systemRequirements?: string[]
  checksumMd5?: string
  checksumSha256?: string
  remark?: string
}

export interface ProductVersionListParams {
  page: number
  size: number
  productLineId?: number
  keyword?: string
  status?: string
  versionType?: string
}

export interface ProductVersionDetailResponse {
  id: number
  productLineId: number
  productLineName?: string
  versionNumber: string
  versionName?: string
  description: string
  releaseNotes?: string
  versionType: number
  versionTypeName: string
  status: number
  statusName: string
  isCurrent: boolean
  releaseDate?: string
  plannedDate?: string
  fileSize?: number
  formattedFileSize?: string
  downloadUrl?: string
  downloadCount: number
  platforms?: string[]
  features?: string[]
  bugFixes?: string[]
  breakingChanges?: string[]
  dependencies?: string[]
  systemRequirements?: string[]
  checksumMd5?: string
  checksumSha256?: string
  createdBy: number
  createdByName: string
  approvedBy?: number
  approvedByName?: string
  approvedAt?: string
  remark?: string
  createTime: string
  updateTime: string
  version: number
  canBeDeleted: boolean
  canBeReleased: boolean
  canBeSetAsCurrent: boolean
  canBeDeprecated: boolean
}

export interface ProductVersionListResponse {
  items: ProductVersionDetailResponse[]
  total: number
  page: number
  size: number
  totalPages: number
}

export interface ProductVersionSimpleResponse {
  id: number
  versionNumber: string
  versionName?: string
  status: number
  statusName: string
  isCurrent: boolean
  releaseDate?: string
  downloadCount: number
  createTime: string
}

export interface VersionReleaseHistoryResponse {
  id: number
  versionId: number
  actionType: number
  actionTypeName: string
  fromStatus?: number
  fromStatusName?: string
  toStatus?: number
  toStatusName?: string
  actionReason?: string
  actionBy: number
  actionByName: string
  actionTime: string
  ipAddress?: string
  actionDescription: string
  riskLevel: string
  isImportantAction: boolean
  remark?: string
}

export const productVersionApi = {
  /**
   * 创建产品版本
   */
  createVersion(data: ProductVersionCreateRequest) {
    return http.post<number>('/api/product-versions', data)
  },

  /**
   * 更新产品版本
   */
  updateVersion(id: number, data: ProductVersionUpdateRequest) {
    return http.put<void>(`/api/product-versions/${id}`, data)
  },

  /**
   * 删除产品版本
   */
  deleteVersion(id: number) {
    return http.delete<void>(`/api/product-versions/${id}`)
  },

  /**
   * 批量删除产品版本
   */
  deleteVersionsBatch(ids: number[]) {
    return http.delete<void>('/api/product-versions/batch', { data: ids })
  },

  /**
   * 获取产品版本详情
   */
  getVersionDetail(id: number) {
    return http.get<ProductVersionDetailResponse>(`/api/product-versions/${id}`)
  },

  /**
   * 分页查询产品版本列表
   */
  getVersionList(params: ProductVersionListParams) {
    return http.get<ProductVersionListResponse>('/api/product-versions', { params })
  },

  /**
   * 获取产品线版本列表
   */
  getVersionsByProductLine(productLineId: number) {
    return http.get<ProductVersionSimpleResponse[]>(`/api/product-versions/by-product-line/${productLineId}`)
  },

  /**
   * 获取产品线当前版本
   */
  getCurrentVersion(productLineId: number) {
    return http.get<ProductVersionDetailResponse>(`/api/product-versions/current/${productLineId}`)
  },

  /**
   * 发布版本
   */
  releaseVersion(id: number, reason?: string) {
    return http.post<void>(`/api/product-versions/${id}/release`, null, {
      params: { reason }
    })
  },

  /**
   * 设置当前版本
   */
  setCurrentVersion(id: number) {
    return http.post<void>(`/api/product-versions/${id}/set-current`)
  },

  /**
   * 废弃版本
   */
  deprecateVersion(id: number, reason?: string) {
    return http.post<void>(`/api/product-versions/${id}/deprecate`, null, {
      params: { reason }
    })
  },

  /**
   * 更新版本状态
   */
  updateVersionStatus(id: number, status: string, reason?: string) {
    return http.post<void>(`/api/product-versions/${id}/status`, null, {
      params: { status, reason }
    })
  },

  /**
   * 记录版本下载
   */
  recordDownload(id: number, platform?: string, region?: string) {
    return http.post<void>(`/api/product-versions/${id}/download`, null, {
      params: { platform, region }
    })
  },

  /**
   * 获取版本发布历史
   */
  getVersionHistory(id: number) {
    return http.get<VersionReleaseHistoryResponse[]>(`/api/product-versions/${id}/history`)
  },

  /**
   * 获取版本统计信息
   */
  getVersionStats(productLineId: number) {
    return http.get<any>(`/api/product-versions/stats/${productLineId}`)
  },

  /**
   * 获取热门版本排行
   */
  getTopDownloadVersions(days: number = 30, limit: number = 10) {
    return http.get<any[]>('/api/product-versions/top-downloads', {
      params: { days, limit }
    })
  },

  /**
   * 检查版本号可用性
   */
  checkVersionNumber(productLineId: number, versionNumber: string, excludeId?: number) {
    return http.get<boolean>('/api/product-versions/check-version-number', {
      params: { productLineId, versionNumber, excludeId }
    })
  },

  /**
   * 建议下一个版本号
   */
  suggestVersionNumber(productLineId: number, versionType: string) {
    return http.get<string>('/api/product-versions/suggest-version-number', {
      params: { productLineId, versionType }
    })
  },

  /**
   * 批量更新版本状态
   */
  updateVersionStatusBatch(ids: number[], status: string, reason?: string) {
    return http.post<void>('/api/product-versions/batch/status', null, {
      params: { ids: ids.join(','), status, reason }
    })
  },

  /**
   * 获取版本下载统计
   */
  getVersionDownloadStats(id: number, days: number = 30) {
    return http.get<any>(`/api/product-versions/${id}/download-stats`, {
      params: { days }
    })
  },

  /**
   * 同步版本信息
   */
  syncVersionInfo(productLineId: number) {
    return http.post<void>(`/api/product-versions/sync/${productLineId}`)
  }
}
