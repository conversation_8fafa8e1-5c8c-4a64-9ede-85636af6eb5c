import { http } from '@/utils/request'

export interface RoleCreateRequest {
  name: string
  code: string
  description?: string
  permissions?: number[]
  status?: string
  remark?: string
}

export interface RoleUpdateRequest {
  name?: string
  description?: string
  permissions?: number[]
  status?: string
  remark?: string
}

export interface RoleListParams {
  page: number
  size: number
  keyword?: string
  status?: string
}

export interface RoleDetailResponse {
  id: number
  name: string
  code: string
  description?: string
  permissions: number[]
  permissionNames: string[]
  status: string
  userCount: number
  createdAt: string
  updatedAt: string
  remark?: string
}

export interface RoleSimpleResponse {
  id: number
  name: string
  code: string
  description?: string
}

export interface RoleListResponse {
  items: RoleDetailResponse[]
  total: number
  page: number
  size: number
  totalPages: number
}

export const roleApi = {
  /**
   * 创建角色
   */
  createRole(data: RoleCreateRequest) {
    return http.post<number>('/admin/roles', data)
  },

  /**
   * 更新角色
   */
  updateRole(id: number, data: RoleUpdateRequest) {
    return http.put<void>(`/admin/roles/${id}`, data)
  },

  /**
   * 删除角色
   */
  deleteRole(id: number) {
    return http.delete<void>(`/admin/roles/${id}`)
  },

  /**
   * 获取角色详情
   */
  getRoleDetail(id: number) {
    return http.get<RoleDetailResponse>(`/admin/roles/${id}`)
  },

  /**
   * 分页查询角色列表
   */
  getRoleList(params: RoleListParams) {
    return http.get<RoleListResponse>('/admin/roles', { params })
  },

  /**
   * 获取所有启用的角色
   */
  getAllEnabledRoles() {
    return http.get<RoleSimpleResponse[]>('/admin/roles/enabled')
  },

  /**
   * 为角色分配权限
   */
  assignPermissions(id: number, permissionIds: number[]) {
    return http.post<void>(`/admin/roles/${id}/permissions`, { permissionIds })
  },

  /**
   * 移除角色权限
   */
  removePermissions(id: number, permissionIds: number[]) {
    return http.delete<void>(`/admin/roles/${id}/permissions`, { data: { permissionIds } })
  },

  /**
   * 获取角色权限列表
   */
  getRolePermissions(id: number) {
    return http.get<number[]>(`/admin/roles/${id}/permissions`)
  },

  /**
   * 启用角色
   */
  enableRole(id: number) {
    return http.put<void>(`/admin/roles/${id}/enable`)
  },

  /**
   * 禁用角色
   */
  disableRole(id: number) {
    return http.put<void>(`/admin/roles/${id}/disable`)
  },

  /**
   * 复制角色
   */
  copyRole(id: number, newName: string) {
    return http.post<number>(`/admin/roles/${id}/copy`, { name: newName })
  },

  /**
   * 获取角色用户列表
   */
  getRoleUsers(id: number) {
    return http.get<any[]>(`/admin/roles/${id}/users`)
  },

  /**
   * 为角色添加用户
   */
  addUsersToRole(id: number, userIds: number[]) {
    return http.post<void>(`/admin/roles/${id}/users`, { userIds })
  },

  /**
   * 从角色移除用户
   */
  removeUsersFromRole(id: number, userIds: number[]) {
    return http.delete<void>(`/admin/roles/${id}/users`, { data: { userIds } })
  }
}
