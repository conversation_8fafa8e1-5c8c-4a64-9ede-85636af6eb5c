import { http } from '@/utils/request'

export interface PermissionCreateRequest {
  name: string
  code: string
  type: number
  parentId?: number
  path?: string
  component?: string
  icon?: string
  sort?: number
  description?: string
  status?: string
  remark?: string
}

export interface PermissionUpdateRequest {
  name?: string
  type?: number
  parentId?: number
  path?: string
  component?: string
  icon?: string
  sort?: number
  description?: string
  status?: string
  remark?: string
}

export interface PermissionListParams {
  page: number
  size: number
  keyword?: string
  permissionType?: number
  status?: string
}

export interface PermissionDetailResponse {
  id: number
  name: string
  code: string
  type: number
  typeName: string
  parentId?: number
  parentName?: string
  path?: string
  component?: string
  icon?: string
  sort: number
  description?: string
  status: string
  level: number
  hasChildren: boolean
  createdAt: string
  updatedAt: string
  remark?: string
}

export interface PermissionTreeResponse {
  id: number
  name: string
  code: string
  type: number
  typeName: string
  parentId?: number
  path?: string
  component?: string
  icon?: string
  sort: number
  description?: string
  status: string
  level: number
  children?: PermissionTreeResponse[]
}

/**
 * @deprecated 使用 PageResponse<PermissionDetailResponse> 替代
 */
export interface PermissionListResponse {
  items: PermissionDetailResponse[]
  total: number
  page: number
  size: number
  totalPages: number
}

export const permissionApi = {
  /**
   * 创建权限
   */
  createPermission(data: PermissionCreateRequest) {
    return http.post<number>('/admin/permissions', data)
  },

  /**
   * 更新权限
   */
  updatePermission(id: number, data: PermissionUpdateRequest) {
    return http.put<void>(`/admin/permissions/${id}`, data)
  },

  /**
   * 删除权限
   */
  deletePermission(id: number) {
    return http.delete<void>(`/admin/permissions/${id}`)
  },

  /**
   * 获取权限详情
   */
  getPermissionDetail(id: number) {
    return http.get<PermissionDetailResponse>(`/admin/permissions/${id}`)
  },

  /**
   * 分页查询权限列表
   */
  getPermissionList(params: PermissionListParams) {
    return http.get<PermissionListResponse>('/admin/permissions', { params })
  },

  /**
   * 获取权限树结构
   */
  getPermissionTree() {
    return http.get<PermissionTreeResponse[]>('/admin/permissions/tree')
  },

  /**
   * 根据父权限ID获取子权限列表
   */
  getPermissionsByParentId(parentId: number) {
    return http.get<PermissionDetailResponse[]>(`/admin/permissions/parent/${parentId}`)
  },

  /**
   * 根据权限类型获取权限列表
   */
  getPermissionsByType(permissionType: number) {
    return http.get<PermissionDetailResponse[]>(`/admin/permissions/type/${permissionType}`)
  },

  /**
   * 启用权限
   */
  enablePermission(id: number) {
    return http.put<void>(`/admin/permissions/${id}/enable`)
  },

  /**
   * 禁用权限
   */
  disablePermission(id: number) {
    return http.put<void>(`/admin/permissions/${id}/disable`)
  },

  /**
   * 获取所有启用的菜单权限
   */
  getEnabledMenuPermissions() {
    return http.get<PermissionTreeResponse[]>('/admin/permissions/menu/enabled')
  },

  /**
   * 检查权限代码是否可用
   */
  checkPermissionCode(code: string, excludeId?: number) {
    return http.get<boolean>('/admin/permissions/check-code', {
      params: { code, excludeId }
    })
  },

  /**
   * 移动权限到新的父级
   */
  movePermission(id: number, newParentId?: number) {
    return http.put<void>(`/admin/permissions/${id}/move`, { newParentId })
  },

  /**
   * 复制权限
   */
  copyPermission(id: number, newName: string, newCode: string) {
    return http.post<number>(`/admin/permissions/${id}/copy`, { name: newName, code: newCode })
  },

  /**
   * 批量更新权限排序
   */
  updatePermissionSort(sortData: Array<{ id: number; sort: number }>) {
    return http.put<void>('/admin/permissions/batch/sort', { sortData })
  }
}
