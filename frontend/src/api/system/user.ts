import { http } from '@/utils/request'

export interface UserCreateRequest {
  username: string
  email: string
  realName?: string
  phone?: string
  company?: string
  department?: string
  roles?: string[]
  permissions?: string[]
  status?: string
  remark?: string
}

export interface UserUpdateRequest {
  email?: string
  realName?: string
  phone?: string
  company?: string
  department?: string
  roles?: string[]
  permissions?: string[]
  status?: string
  remark?: string
}

export interface UserListParams {
  page: number
  size: number
  keyword?: string
  role?: string
  status?: string
  dateRange?: string[]
}

export interface UserDetailResponse {
  id: number
  username: string
  email: string
  realName?: string
  phone?: string
  company?: string
  department?: string
  roles: string[]
  permissions: string[]
  status: string
  online: boolean
  avatar?: string
  createdAt: string
  lastLogin?: string
  remark?: string
}

export interface UserListResponse {
  items: UserDetailResponse[]
  total: number
  page: number
  size: number
  totalPages: number
}

export const userApi = {
  /**
   * 创建用户
   */
  createUser(data: UserCreateRequest) {
    return http.post<string>('/admin/users/create', data)
  },

  /**
   * 更新用户
   */
  updateUser(id: number, data: UserUpdateRequest) {
    return http.put<void>(`/admin/users/${id}`, data)
  },

  /**
   * 删除用户
   */
  deleteUser(id: number) {
    return http.delete<void>(`/admin/users/${id}`)
  },

  /**
   * 批量删除用户
   */
  deleteUsersBatch(ids: number[]) {
    return http.delete<void>('/admin/users/batch', { data: ids })
  },

  /**
   * 获取用户详情
   */
  getUserDetail(id: number) {
    return http.get<UserDetailResponse>(`/admin/users/${id}`)
  },

  /**
   * 分页查询用户列表
   */
  getUserList(params: UserListParams) {
    return http.get<UserListResponse>('/admin/users', { params })
  },

  /**
   * 启用用户
   */
  enableUser(id: number) {
    return http.put<void>(`/admin/users/${id}/enable`)
  },

  /**
   * 禁用用户
   */
  disableUser(id: number) {
    return http.put<void>(`/admin/users/${id}/disable`)
  },

  /**
   * 重置用户密码
   */
  resetPassword(id: number) {
    return http.post<string>(`/admin/users/${id}/reset-password`)
  },

  /**
   * 批量启用用户
   */
  enableUsersBatch(ids: number[]) {
    return http.put<void>('/admin/users/batch/enable', { data: ids })
  },

  /**
   * 批量禁用用户
   */
  disableUsersBatch(ids: number[]) {
    return http.put<void>('/admin/users/batch/disable', { data: ids })
  }
}
