import { http } from '@/utils/request'

export interface SystemConfigCreateRequest {
  configKey: string
  configValue: string
  configGroup: string
  configType: number
  description?: string
  isSystem?: boolean
  isEncrypted?: boolean
  validationRule?: string
  defaultValue?: string
  remark?: string
}

export interface SystemConfigUpdateRequest {
  configValue?: string
  configGroup?: string
  configType?: number
  description?: string
  isSystem?: boolean
  isEncrypted?: boolean
  validationRule?: string
  defaultValue?: string
  remark?: string
}

export interface SystemConfigListParams {
  page: number
  size: number
  keyword?: string
  configGroup?: string
  configType?: number
  isSystem?: boolean
}

export interface SystemConfigDetailResponse {
  id: number
  configKey: string
  configValue: string
  configGroup: string
  configType: number
  configTypeName: string
  description?: string
  isSystem: boolean
  isEncrypted: boolean
  validationRule?: string
  defaultValue?: string
  lastModifiedBy?: string
  lastModifiedTime?: string
  createdAt: string
  updatedAt: string
  remark?: string
}

/**
 * @deprecated 使用 PageResponse<SystemConfigDetailResponse> 替代
 */
export interface SystemConfigListResponse {
  items: SystemConfigDetailResponse[]
  total: number
  page: number
  size: number
  totalPages: number
}

export interface SystemConfigGroupResponse {
  groupName: string
  groupLabel: string
  configCount: number
  configs: SystemConfigDetailResponse[]
}

export interface SystemConfigStatsResponse {
  totalCount: number
  systemConfigCount: number
  userConfigCount: number
  encryptedConfigCount: number
  groupDistribution: Array<{
    groupName: string
    groupLabel: string
    count: number
  }>
  typeDistribution: Array<{
    type: number
    typeName: string
    count: number
  }>
}

export const systemConfigApi = {
  /**
   * 创建系统配置
   */
  createSystemConfig(data: SystemConfigCreateRequest) {
    return http.post<number>('/admin/system-configs', data)
  },

  /**
   * 更新系统配置
   */
  updateSystemConfig(id: number, data: SystemConfigUpdateRequest) {
    return http.put<void>(`/admin/system-configs/${id}`, data)
  },

  /**
   * 删除系统配置
   */
  deleteSystemConfig(id: number) {
    return http.delete<void>(`/admin/system-configs/${id}`)
  },

  /**
   * 批量删除系统配置
   */
  deleteSystemConfigsBatch(ids: number[]) {
    return http.delete<void>('/admin/system-configs/batch', { data: ids })
  },

  /**
   * 获取系统配置详情
   */
  getSystemConfigDetail(id: number) {
    return http.get<SystemConfigDetailResponse>(`/admin/system-configs/${id}`)
  },

  /**
   * 分页查询系统配置列表
   */
  getSystemConfigList(params: SystemConfigListParams) {
    return http.get<SystemConfigListResponse>('/admin/system-configs', { params })
  },

  /**
   * 根据配置键获取配置值
   */
  getConfigValue(configKey: string) {
    return http.get<string>(`/admin/system-configs/value/${configKey}`)
  },

  /**
   * 批量获取配置值
   */
  getConfigValuesBatch(configKeys: string[]) {
    return http.post<Record<string, string>>('/admin/system-configs/values/batch', { configKeys })
  },

  /**
   * 根据分组获取配置列表
   */
  getConfigsByGroup(groupName: string) {
    return http.get<SystemConfigDetailResponse[]>(`/admin/system-configs/group/${groupName}`)
  },

  /**
   * 获取所有配置分组
   */
  getAllConfigGroups() {
    return http.get<SystemConfigGroupResponse[]>('/admin/system-configs/groups')
  },

  /**
   * 检查配置键是否可用
   */
  checkConfigKey(configKey: string, excludeId?: number) {
    return http.get<boolean>('/admin/system-configs/check-key', {
      params: { configKey, excludeId }
    })
  },

  /**
   * 获取配置类型列表
   */
  getConfigTypes() {
    return http.get<Array<{ value: number; label: string }>>('/admin/system-configs/types')
  },

  /**
   * 批量更新配置
   */
  updateConfigsBatch(configs: Array<{ id: number; configValue: string }>) {
    return http.put<void>('/admin/system-configs/batch', { configs })
  },

  /**
   * 重置配置为默认值
   */
  resetConfigToDefault(id: number) {
    return http.put<void>(`/admin/system-configs/${id}/reset`)
  },

  /**
   * 批量重置配置为默认值
   */
  resetConfigsToDefaultBatch(ids: number[]) {
    return http.put<void>('/admin/system-configs/batch/reset', { data: ids })
  },

  /**
   * 刷新配置缓存
   */
  refreshConfigCache() {
    return http.post<void>('/admin/system-configs/refresh-cache')
  },

  /**
   * 导出配置
   */
  exportConfigs(groupName?: string) {
    return http.get<Blob>('/admin/system-configs/export', {
      params: { groupName },
      responseType: 'blob'
    })
  },

  /**
   * 导入配置
   */
  importConfigs(file: File, overwrite: boolean = false) {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('overwrite', overwrite.toString())
    return http.post<void>('/admin/system-configs/import', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  /**
   * 获取配置统计信息
   */
  getConfigStats() {
    return http.get<SystemConfigStatsResponse>('/admin/system-configs/stats')
  },

  /**
   * 验证配置值
   */
  validateConfigValue(configKey: string, configValue: string) {
    return http.post<boolean>('/admin/system-configs/validate', { configKey, configValue })
  },

  /**
   * 获取配置变更历史
   */
  getConfigHistory(configKey: string, days: number = 30) {
    return http.get<any[]>(`/admin/system-configs/history/${configKey}`, {
      params: { days }
    })
  }
}
