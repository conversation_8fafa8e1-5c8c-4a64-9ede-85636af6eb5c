import { http } from '@/utils/request'

export interface OperationLogListParams {
  page: number
  size: number
  keyword?: string
  userId?: number
  operation?: string
  module?: string
  status?: string
  startTime?: string
  endTime?: string
  ipAddress?: string
}

export interface OperationLogDetailResponse {
  id: number
  userId: number
  username: string
  realName?: string
  operation: string
  operationName: string
  module: string
  moduleName: string
  method: string
  requestUrl: string
  requestMethod: string
  requestParams?: string
  responseData?: string
  ipAddress: string
  userAgent?: string
  location?: string
  status: string
  errorMessage?: string
  executionTime: number
  operationTime: string
  remark?: string
}

export interface OperationLogListResponse {
  items: OperationLogDetailResponse[]
  total: number
  page: number
  size: number
  totalPages: number
}

export interface OperationLogStatsResponse {
  totalCount: number
  todayCount: number
  successCount: number
  failureCount: number
  avgExecutionTime: number
  topOperations: Array<{
    operation: string
    operationName: string
    count: number
  }>
  topUsers: Array<{
    userId: number
    username: string
    realName?: string
    count: number
  }>
  hourlyDistribution: Array<{
    hour: number
    count: number
  }>
  dailyTrend: Array<{
    date: string
    count: number
  }>
}

export interface OperationLogExportParams {
  startTime?: string
  endTime?: string
  userId?: number
  operation?: string
  module?: string
  status?: string
  format?: 'excel' | 'csv'
}

export const operationLogApi = {
  /**
   * 分页查询操作日志列表
   */
  getOperationLogList(params: OperationLogListParams) {
    return http.get<OperationLogListResponse>('/admin/operation-logs/public', { params })
  },

  /**
   * 获取操作日志详情
   */
  getOperationLogDetail(id: number) {
    return http.get<OperationLogDetailResponse>(`/admin/operation-logs/${id}`)
  },

  /**
   * 删除操作日志
   */
  deleteOperationLog(id: number) {
    return http.delete<void>(`/admin/operation-logs/${id}`)
  },

  /**
   * 批量删除操作日志
   */
  deleteOperationLogsBatch(ids: number[]) {
    return http.delete<void>('/admin/operation-logs/batch', { data: ids })
  },

  /**
   * 清理过期日志
   */
  cleanExpiredLogs(days: number) {
    return http.delete<void>('/admin/operation-logs/clean', {
      params: { days }
    })
  },

  /**
   * 获取操作日志统计信息
   */
  getOperationLogStats(days: number = 30) {
    return http.get<OperationLogStatsResponse>('/admin/operation-logs/stats', {
      params: { days }
    })
  },

  /**
   * 获取操作类型列表
   */
  getOperationTypes() {
    return http.get<Array<{ value: string; label: string }>>('/admin/operation-logs/operation-types')
  },

  /**
   * 获取模块列表
   */
  getModuleTypes() {
    return http.get<Array<{ value: string; label: string }>>('/admin/operation-logs/module-types')
  },

  /**
   * 导出操作日志
   */
  exportOperationLogs(params: OperationLogExportParams) {
    return http.get<Blob>('/admin/operation-logs/export', {
      params,
      responseType: 'blob'
    })
  },

  /**
   * 获取用户操作历史
   */
  getUserOperationHistory(userId: number, days: number = 30) {
    return http.get<OperationLogDetailResponse[]>(`/admin/operation-logs/user/${userId}`, {
      params: { days }
    })
  },

  /**
   * 获取IP地址操作历史
   */
  getIpOperationHistory(ipAddress: string, days: number = 30) {
    return http.get<OperationLogDetailResponse[]>('/admin/operation-logs/ip', {
      params: { ipAddress, days }
    })
  },

  /**
   * 获取异常操作日志
   */
  getAbnormalOperations(days: number = 7) {
    return http.get<OperationLogDetailResponse[]>('/admin/operation-logs/abnormal', {
      params: { days }
    })
  },

  /**
   * 获取高频操作用户
   */
  getHighFrequencyUsers(days: number = 7, limit: number = 10) {
    return http.get<any[]>('/admin/operation-logs/high-frequency-users', {
      params: { days, limit }
    })
  },

  /**
   * 获取操作趋势分析
   */
  getOperationTrend(days: number = 30, groupBy: 'hour' | 'day' = 'day') {
    return http.get<any[]>('/admin/operation-logs/trend', {
      params: { days, groupBy }
    })
  },

  /**
   * 生成操作报告
   */
  generateOperationReport(startTime: string, endTime: string, reportType: 'summary' | 'detail' = 'summary') {
    return http.post<any>('/admin/operation-logs/report', null, {
      params: { startTime, endTime, reportType }
    })
  },

  /**
   * 获取实时操作监控
   */
  getRealTimeOperations(limit: number = 50) {
    return http.get<OperationLogDetailResponse[]>('/admin/operation-logs/realtime', {
      params: { limit }
    })
  }
}
