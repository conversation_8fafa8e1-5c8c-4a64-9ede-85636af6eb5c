// 通用类型定义

// API响应结果类型 - 与后端Result格式保持一致
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

// 分页参数类型
export interface PageParams {
  current: number
  pageSize: number
  total?: number
}

// 分页响应类型
export interface PageResult<T = any> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// 用户信息类型
export interface UserInfo {
  id: number
  username: string
  realName?: string
  email?: string
  avatar?: string
  userType: number
  phone?: string
  company?: string
  department?: string
  bio?: string
  roles?: string[]
  lastLoginTime?: string
  lastLoginIp?: string
  createdAt?: string
}

// 登录请求类型
export interface LoginRequest {
  username: string
  password: string
  rememberMe?: boolean
}

// 登录响应类型
export interface LoginResponse {
  accessToken: string
  tokenType: string
  expiresIn: number
  userInfo: UserInfo
}

// 产品线类型
export interface ProductLine {
  id: number
  code: string
  name: string
  description?: string
  type: number
  status: number
  sortOrder: number
  icon?: string
  color?: string
  ownerName?: string
}

// 菜单项类型
export interface MenuItem {
  key: string
  label: string
  icon?: string
  path?: string
  children?: MenuItem[]
}

// 图表数据类型
export interface ChartData {
  name: string
  value: number
  [key: string]: any
}

// 时间范围类型
export interface TimeRange {
  startTime: string
  endTime: string
}

// 筛选条件类型
export interface FilterParams {
  productLines?: string[]
  timeRange?: TimeRange
  [key: string]: any
}

// 表格列类型
export interface TableColumn {
  title: string
  dataIndex: string
  key?: string
  width?: number
  align?: 'left' | 'center' | 'right'
  sorter?: boolean
  fixed?: 'left' | 'right'
  ellipsis?: boolean
}

// 统计卡片数据类型
export interface StatCard {
  title: string
  value: number | string
  unit?: string
  trend?: number
  trendType?: 'up' | 'down'
  icon?: string
  color?: string
}
