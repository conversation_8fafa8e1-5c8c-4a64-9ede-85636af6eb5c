/**
 * 通用类型定义
 * 
 * <AUTHOR>
 * @since 2025-06-23
 */

/**
 * 通用分页响应接口
 * 
 * 用于统一所有分页查询的响应格式，替代各个模块中重复定义的ListResponse接口
 * 
 * @template T 数据项类型
 */
export interface PageResponse<T> {
  /**
   * 数据列表
   */
  items: T[]
  
  /**
   * 总记录数
   */
  total: number
  
  /**
   * 当前页码
   */
  page: number
  
  /**
   * 每页大小
   */
  size: number
  
  /**
   * 总页数
   */
  totalPages: number
}

/**
 * 通用API响应接口 - 与后端Result格式保持一致
 *
 * @template T 响应数据类型
 */
export interface ApiResponse<T = any> {
  /**
   * 响应码
   */
  code: number

  /**
   * 响应消息
   */
  message: string

  /**
   * 响应数据
   */
  data: T

  /**
   * 时间戳
   */
  timestamp: number
}

/**
 * 通用查询参数接口
 */
export interface BaseQueryParams {
  /**
   * 页码
   */
  page?: number
  
  /**
   * 每页大小
   */
  size?: number
  
  /**
   * 关键词搜索
   */
  keyword?: string
  
  /**
   * 排序字段
   */
  sortBy?: string
  
  /**
   * 排序方向
   */
  sortOrder?: 'asc' | 'desc'
}

/**
 * 选项接口
 */
export interface Option<T = any> {
  /**
   * 显示标签
   */
  label: string
  
  /**
   * 选项值
   */
  value: T
  
  /**
   * 是否禁用
   */
  disabled?: boolean
  
  /**
   * 额外数据
   */
  extra?: any
}

/**
 * 树形节点接口
 */
export interface TreeNode<T = any> {
  /**
   * 节点ID
   */
  id: string | number
  
  /**
   * 节点标题
   */
  title: string
  
  /**
   * 节点值
   */
  value?: T
  
  /**
   * 父节点ID
   */
  parentId?: string | number
  
  /**
   * 子节点
   */
  children?: TreeNode<T>[]
  
  /**
   * 是否禁用
   */
  disabled?: boolean
  
  /**
   * 是否可选择
   */
  selectable?: boolean
  
  /**
   * 额外数据
   */
  extra?: any
}
