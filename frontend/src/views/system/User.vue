<template>
  <div class="system-user-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">用户账户与权限管理</h1>
          <p class="page-description">管理系统用户账户信息，配置用户访问权限和角色分配</p>
        </div>
        <div class="header-right">
          <a-space>
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出数据
            </a-button>
            <a-button type="primary" @click="handleAdd">
              <template #icon><PlusOutlined /></template>
              新增用户
            </a-button>
          </a-space>
        </div>
      </div>
    </div>

    <!-- 统计卡片区 -->
    <div class="stats-cards">
      <a-row :gutter="24">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="总用户数"
              :value="userStats.total"
              :value-style="{ color: '#1e88e5' }"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="活跃用户"
              :value="userStats.active"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="禁用用户"
              :value="userStats.inactive"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <StopOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="在线用户"
              :value="userStats.online"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <GlobalOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选和操作区 -->
    <div class="content-wrapper">
      <a-card>
        <div class="filter-section">
          <a-row :gutter="16" align="middle">
            <a-col :span="6">
              <a-select
                v-model:value="filterForm.role"
                placeholder="选择角色"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部角色</a-select-option>
                <a-select-option value="admin">管理员</a-select-option>
                <a-select-option value="user">普通用户</a-select-option>
                <a-select-option value="viewer">只读用户</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="6">
              <a-select
                v-model:value="filterForm.status"
                placeholder="选择状态"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部状态</a-select-option>
                <a-select-option value="active">正常</a-select-option>
                <a-select-option value="inactive">禁用</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="6">
              <a-range-picker
                v-model:value="filterForm.dateRange"
                :placeholder="['创建开始时间', '创建结束时间']"
                style="width: 100%"
                @change="handleFilter"
              />
            </a-col>
            <a-col :span="6">
              <a-input-search
                v-model:value="searchText"
                placeholder="搜索用户名、邮箱或公司"
                style="width: 100%"
                @search="handleSearch"
              />
            </a-col>
          </a-row>
        </div>

        <div class="table-toolbar">
          <div class="toolbar-left">
            <a-space>
              <a-button
                @click="handleBatchDelete"
                :disabled="!selectedRowKeys.length"
                danger
              >
                <template #icon><DeleteOutlined /></template>
                批量删除 ({{ selectedRowKeys.length }})
              </a-button>
              <a-button @click="handleBatchEnable" :disabled="!selectedRowKeys.length">
                <template #icon><CheckOutlined /></template>
                批量启用
              </a-button>
              <a-button @click="handleBatchDisable" :disabled="!selectedRowKeys.length">
                <template #icon><StopOutlined /></template>
                批量禁用
              </a-button>
            </a-space>
          </div>
          <div class="toolbar-right">
            <a-space>
              <a-tooltip title="刷新数据">
                <a-button @click="loadUserList">
                  <template #icon><ReloadOutlined /></template>
                </a-button>
              </a-tooltip>
              <a-tooltip title="列设置">
                <a-button @click="showColumnSetting = true">
                  <template #icon><SettingOutlined /></template>
                </a-button>
              </a-tooltip>
            </a-space>
          </div>
        </div>
        
        <a-table
          :columns="visibleColumns"
          :data-source="userList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          :scroll="{ x: 1500 }"
          row-key="id"
          size="middle"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'avatar'">
              <div class="user-info">
                <a-avatar :src="record.avatar" :alt="record.username" size="large">
                  {{ record.username?.charAt(0)?.toUpperCase() }}
                </a-avatar>
                <div class="user-details">
                  <div class="username">{{ record.username }}</div>
                  <div class="email">{{ record.email }}</div>
                </div>
              </div>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag
                :color="getStatusColor(record.status)"
                class="status-tag"
              >
                <template #icon>
                  <CheckCircleOutlined v-if="record.status === 'active'" />
                  <StopOutlined v-else />
                </template>
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'roles'">
              <div class="roles-container">
                <a-tag
                  v-for="role in record.roles"
                  :key="role"
                  :color="getRoleColor(role)"
                  class="role-tag"
                >
                  {{ getRoleText(role) }}
                </a-tag>
              </div>
            </template>
            <template v-else-if="column.key === 'permissions'">
              <a-tooltip>
                <template #title>
                  <div>
                    <div v-for="permission in record.permissions" :key="permission">
                      {{ getPermissionText(permission) }}
                    </div>
                  </div>
                </template>
                <a-tag color="purple">
                  {{ record.permissions?.length || 0 }} 项权限
                </a-tag>
              </a-tooltip>
            </template>
            <template v-else-if="column.key === 'lastLogin'">
              <div class="time-info">
                <div>{{ formatDate(record.lastLogin) }}</div>
                <div class="time-ago">{{ getTimeAgo(record.lastLogin) }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'createdAt'">
              <div class="time-info">
                <div>{{ formatDate(record.createdAt) }}</div>
                <div class="time-ago">{{ getTimeAgo(record.createdAt) }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'online'">
              <a-badge
                :status="record.online ? 'processing' : 'default'"
                :text="record.online ? '在线' : '离线'"
              />
            </template>
            <template v-else-if="column.key === 'action'">
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="edit" @click="handleEdit(record)">
                      <EditOutlined />
                      编辑用户
                    </a-menu-item>
                    <a-menu-item key="permissions" @click="handlePermissions(record)">
                      <SafetyOutlined />
                      权限配置
                    </a-menu-item>
                    <a-menu-item key="reset-password" @click="handleResetPassword(record)">
                      <KeyOutlined />
                      重置密码
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item
                      key="toggle-status"
                      @click="handleToggleStatus(record)"
                    >
                      <template v-if="record.status === 'active'">
                        <StopOutlined />
                        禁用用户
                      </template>
                      <template v-else>
                        <CheckOutlined />
                        启用用户
                      </template>
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" @click="handleDelete(record)" class="danger-item">
                      <DeleteOutlined />
                      删除用户
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="text" size="small">
                  操作 <DownOutlined />
                </a-button>
              </a-dropdown>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
    
    <!-- 用户编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="800px"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="用户名" name="username">
              <a-input
                v-model:value="formData.username"
                placeholder="请输入用户名"
                :disabled="!!formData.id"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="邮箱" name="email">
              <a-input v-model:value="formData.email" placeholder="请输入邮箱" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="真实姓名" name="realName">
              <a-input v-model:value="formData.realName" placeholder="请输入真实姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号" name="phone">
              <a-input v-model:value="formData.phone" placeholder="请输入手机号" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="所属公司" name="company">
              <a-input v-model:value="formData.company" placeholder="请输入所属公司" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="部门" name="department">
              <a-input v-model:value="formData.department" placeholder="请输入部门" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="角色" name="roles">
              <a-select
                v-model:value="formData.roles"
                mode="multiple"
                placeholder="请选择角色"
                :options="roleOptions"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-select v-model:value="formData.status" placeholder="请选择状态">
                <a-select-option value="active">正常</a-select-option>
                <a-select-option value="inactive">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="权限配置" name="permissions">
          <a-tree
            v-model:checkedKeys="formData.permissions"
            :tree-data="permissionTree"
            checkable
            :default-expand-all="true"
            :height="200"
          />
        </a-form-item>

        <a-form-item label="备注" name="remark">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入备注信息"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 权限配置弹窗 -->
    <a-modal
      v-model:open="permissionModalVisible"
      title="权限配置"
      width="600px"
      @ok="handlePermissionModalOk"
      @cancel="permissionModalVisible = false"
    >
      <div class="permission-config">
        <div class="user-info-card">
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="用户名">{{ currentUser?.username }}</a-descriptions-item>
            <a-descriptions-item label="邮箱">{{ currentUser?.email }}</a-descriptions-item>
            <a-descriptions-item label="角色">
              <a-tag v-for="role in currentUser?.roles" :key="role" :color="getRoleColor(role)">
                {{ getRoleText(role) }}
              </a-tag>
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <a-divider>权限配置</a-divider>

        <a-tree
          v-model:checkedKeys="userPermissions"
          :tree-data="permissionTree"
          checkable
          :default-expand-all="true"
          :height="300"
        />
      </div>
    </a-modal>

    <!-- 列设置弹窗 -->
    <a-modal
      v-model:open="showColumnSetting"
      title="列设置"
      width="400px"
      @ok="showColumnSetting = false"
      @cancel="showColumnSetting = false"
    >
      <a-checkbox-group v-model:value="visibleColumnKeys" class="column-setting">
        <div v-for="column in allColumns" :key="column.key" class="column-item">
          <a-checkbox :value="column.key">{{ column.title }}</a-checkbox>
        </div>
      </a-checkbox-group>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  DeleteOutlined,
  ExportOutlined,
  UserOutlined,
  CheckCircleOutlined,
  StopOutlined,
  GlobalOutlined,
  CheckOutlined,
  ReloadOutlined,
  SettingOutlined,
  EditOutlined,
  SafetyOutlined,
  KeyOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType } from 'ant-design-vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import { userApi, type UserListParams, type UserDetailResponse } from '@/api/system/user'
import {
  STATUS_MAP,
  USER_TYPE_MAP,
  getEnumText,
  getStatusColor as getEnumStatusColor,
  getUserTypeColor
} from '@/constants/enums'

dayjs.extend(relativeTime)

interface UserRecord {
  id: number
  username: string
  email: string
  realName?: string
  phone?: string
  company?: string
  department?: string
  roles: string[]
  permissions: string[]
  status: string
  avatar?: string
  online: boolean
  createdAt: string
  lastLogin: string
  remark?: string
}

// 统计数据
const userStats = reactive({
  total: 0,
  active: 0,
  inactive: 0,
  online: 0
})

// 筛选表单
const filterForm = reactive({
  role: '',
  status: '',
  dateRange: null as any
})

// 表格列配置
const allColumns: TableColumnsType = [
  {
    title: '用户信息',
    key: 'avatar',
    width: 200,
    fixed: 'left'
  },
  {
    title: '角色',
    key: 'roles',
    width: 150
  },
  {
    title: '权限',
    key: 'permissions',
    width: 120
  },
  {
    title: '公司',
    dataIndex: 'company',
    key: 'company',
    width: 150
  },
  {
    title: '部门',
    dataIndex: 'department',
    key: 'department',
    width: 120
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    key: 'phone',
    width: 130
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '在线状态',
    key: 'online',
    width: 100
  },
  {
    title: '最后登录',
    key: 'lastLogin',
    width: 160
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
]

// 可见列配置
const visibleColumnKeys = ref(['avatar', 'roles', 'permissions', 'status', 'online', 'lastLogin', 'action'])
const visibleColumns = computed(() =>
  allColumns.filter(col => visibleColumnKeys.value.includes(col.key as string))
)

// 响应式数据
const loading = ref(false)
const searchText = ref('')
const selectedRowKeys = ref<(string | number)[]>([])
const userList = ref<UserRecord[]>([])
const modalVisible = ref(false)
const modalTitle = ref('')
const formRef = ref()
const permissionModalVisible = ref(false)
const showColumnSetting = ref(false)
const currentUser = ref<UserRecord | null>(null)
const userPermissions = ref<string[]>([])

// 角色选项
const roleOptions = [
  { label: '超级管理员', value: 'super_admin' },
  { label: '管理员', value: 'admin' },
  { label: '普通用户', value: 'user' },
  { label: '只读用户', value: 'viewer' }
]

// 权限树数据
const permissionTree = [
  {
    title: '系统管理',
    key: 'system',
    children: [
      { title: '用户管理', key: 'system:user' },
      { title: '角色管理', key: 'system:role' },
      { title: '权限管理', key: 'system:permission' },
      { title: '操作日志', key: 'system:log' }
    ]
  },
  {
    title: '数据分析',
    key: 'analytics',
    children: [
      { title: '用户分析', key: 'analytics:user' },
      { title: '行为分析', key: 'analytics:behavior' },
      { title: '商业分析', key: 'analytics:business' }
    ]
  },
  {
    title: '产品管理',
    key: 'product',
    children: [
      { title: '产品配置', key: 'product:config' },
      { title: '版本管理', key: 'product:version' }
    ]
  }
]

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格行选择配置
const rowSelection: any = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: (string | number)[]) => {
    selectedRowKeys.value = keys
  }
}

// 表单数据
const formData = reactive({
  id: null as number | null,
  username: '',
  email: '',
  realName: '',
  phone: '',
  company: '',
  department: '',
  roles: [] as string[],
  permissions: [] as string[],
  status: 'active',
  remark: ''
})

// 表单验证规则
const formRules: any = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  roles: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 用户列表数据通过API获取，不再使用模拟数据
/*
const mockUserList: UserRecord[] = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    realName: '系统管理员',
    phone: '***********',
    company: 'Foxit Software',
    department: 'IT部门',
    roles: ['super_admin'],
    permissions: ['system:user', 'system:role', 'system:permission', 'system:log', 'analytics:user', 'analytics:behavior', 'analytics:business', 'product:config', 'product:version'],
    status: 'active',
    online: true,
    createdAt: '2024-01-01T10:00:00Z',
    lastLogin: '2024-06-22T09:30:00Z',
    remark: '系统超级管理员账户'
  },
  {
    id: 2,
    username: 'john_doe',
    email: '<EMAIL>',
    realName: '约翰·多伊',
    phone: '***********',
    company: 'Foxit Software',
    department: '产品部',
    roles: ['admin'],
    permissions: ['analytics:user', 'analytics:behavior', 'product:config'],
    status: 'active',
    online: false,
    createdAt: '2024-02-15T14:20:00Z',
    lastLogin: '2024-06-21T16:45:00Z',
    remark: '产品部门管理员'
  },
  {
    id: 3,
    username: 'jane_smith',
    email: '<EMAIL>',
    realName: '简·史密斯',
    phone: '***********',
    company: 'Foxit Software',
    department: '市场部',
    roles: ['user'],
    permissions: ['analytics:user', 'analytics:business'],
    status: 'active',
    online: true,
    createdAt: '2024-03-10T11:15:00Z',
    lastLogin: '2024-06-22T08:20:00Z',
    remark: '市场部数据分析师'
  },
  {
    id: 4,
    username: 'viewer_test',
    email: '<EMAIL>',
    realName: '测试查看员',
    phone: '***********',
    company: 'Foxit Software',
    department: '测试部',
    roles: ['viewer'],
    permissions: ['analytics:user'],
    status: 'inactive',
    online: false,
    createdAt: '2024-04-05T09:30:00Z',
    lastLogin: '2024-06-18T15:10:00Z',
    remark: '测试用只读账户'
  },
  {
    id: 5,
    username: 'sales_manager',
    email: '<EMAIL>',
    realName: '销售经理',
    phone: '***********',
    company: 'Foxit Software',
    department: '销售部',
    roles: ['user', 'admin'],
    permissions: ['analytics:business', 'analytics:user'],
    status: 'active',
    online: true,
    createdAt: '2024-05-20T13:45:00Z',
    lastLogin: '2024-06-22T10:15:00Z',
    remark: '销售部门经理'
  }
]
*/

// 工具方法
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss')
}

const getTimeAgo = (dateStr: string) => {
  if (!dateStr) return ''
  return dayjs(dateStr).fromNow()
}

// 使用枚举映射替代硬编码的状态处理
const getStatusColor = (status: string | number) => {
  // 兼容字符串和数字状态值
  const statusNum = typeof status === 'string' ?
    (status === 'active' ? 1 : 0) : status
  return getEnumStatusColor(statusNum)
}

const getStatusText = (status: string | number) => {
  // 兼容字符串和数字状态值
  const statusNum = typeof status === 'string' ?
    (status === 'active' ? 1 : 0) : status
  return getEnumText(STATUS_MAP, statusNum, '未知')
}

const getRoleColor = (role: string) => {
  const colors: Record<string, string> = {
    super_admin: 'red',
    admin: 'blue',
    user: 'green',
    viewer: 'orange'
  }
  return colors[role] || 'default'
}

const getRoleText = (role: string) => {
  const texts: Record<string, string> = {
    super_admin: '超级管理员',
    admin: '管理员',
    user: '普通用户',
    viewer: '只读用户'
  }
  return texts[role] || role
}

const getPermissionText = (permission: string) => {
  const texts: Record<string, string> = {
    'system:user': '用户管理',
    'system:role': '角色管理',
    'system:permission': '权限管理',
    'system:log': '操作日志',
    'analytics:user': '用户分析',
    'analytics:behavior': '行为分析',
    'analytics:business': '商业分析',
    'product:config': '产品配置',
    'product:version': '版本管理'
  }
  return texts[permission] || permission
}

// 统计数据更新
const updateStats = () => {
  userStats.total = userList.value.length
  userStats.active = userList.value.filter(u => u.status === 'active').length
  userStats.inactive = userList.value.filter(u => u.status === 'inactive').length
  userStats.online = userList.value.filter(u => u.online).length
}

// 方法定义
const loadUserList = async () => {
  loading.value = true
  try {
    const params: UserListParams = {
      page: pagination.current,
      size: pagination.pageSize,
      keyword: searchText.value || undefined,
      role: filterForm.role || undefined,
      status: filterForm.status || undefined,
      dateRange: filterForm.dateRange ? [
        dayjs(filterForm.dateRange[0]).format('YYYY-MM-DD'),
        dayjs(filterForm.dateRange[1]).format('YYYY-MM-DD')
      ] : undefined
    }

    const response = await userApi.getUserList(params)
    console.log('User API response:', response)

    // 兼容不同的响应格式
    const data = response.data || response
    const items = (data as any).items || (data as any).records || []
    const total = (data as any).total || (data as any).totalCount || 0

    userList.value = items as any
    pagination.total = total
    updateStats()
  } catch (error) {
    message.error('加载用户列表失败')
    console.error('Load user list error:', error)
    console.error('Error details:', {
      message: (error as any).message,
      response: (error as any).response,
      status: (error as any).response?.status,
      data: (error as any).response?.data
    })
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  modalTitle.value = '新增用户'
  Object.assign(formData, {
    id: null,
    username: '',
    email: '',
    realName: '',
    phone: '',
    company: '',
    department: '',
    roles: [],
    permissions: [],
    status: 'active',
    remark: ''
  })
  modalVisible.value = true
}

const handleEdit = (record: any) => {
  modalTitle.value = '编辑用户'
  Object.assign(formData, record as UserRecord)
  modalVisible.value = true
}

const handleDelete = async (record: any) => {
  const userRecord = record as UserRecord
  try {
    await userApi.deleteUser(userRecord.id)
    message.success(`删除用户 ${userRecord.username} 成功`)
    loadUserList()
  } catch (error) {
    message.error('删除用户失败')
  }
}

const handleBatchDelete = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的用户')
    return
  }
  try {
    await userApi.deleteUsersBatch(selectedRowKeys.value as number[])
    message.success(`批量删除 ${selectedRowKeys.value.length} 个用户成功`)
    selectedRowKeys.value = []
    loadUserList()
  } catch (error) {
    message.error('批量删除用户失败')
  }
}

const handleBatchEnable = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要启用的用户')
    return
  }
  try {
    await userApi.enableUsersBatch(selectedRowKeys.value as number[])
    message.success(`批量启用 ${selectedRowKeys.value.length} 个用户成功`)
    selectedRowKeys.value = []
    loadUserList()
  } catch (error) {
    message.error('批量启用用户失败')
  }
}

const handleBatchDisable = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要禁用的用户')
    return
  }
  try {
    await userApi.disableUsersBatch(selectedRowKeys.value as number[])
    message.success(`批量禁用 ${selectedRowKeys.value.length} 个用户成功`)
    selectedRowKeys.value = []
    loadUserList()
  } catch (error) {
    message.error('批量禁用用户失败')
  }
}

const handleToggleStatus = async (record: any) => {
  const userRecord = record as UserRecord
  const newStatus = userRecord.status === 'active' ? 'inactive' : 'active'
  const action = newStatus === 'active' ? '启用' : '禁用'
  try {
    if (newStatus === 'active') {
      await userApi.enableUser(userRecord.id)
    } else {
      await userApi.disableUser(userRecord.id)
    }
    message.success(`${action}用户 ${userRecord.username} 成功`)
    loadUserList()
  } catch (error) {
    message.error(`${action}用户失败`)
  }
}

const handlePermissions = (record: any) => {
  const userRecord = record as UserRecord
  currentUser.value = userRecord
  userPermissions.value = [...userRecord.permissions]
  permissionModalVisible.value = true
}

const handlePermissionModalOk = () => {
  if (currentUser.value) {
    message.success(`更新用户 ${currentUser.value.username} 权限成功`)
    permissionModalVisible.value = false
    loadUserList()
  }
}

const handleResetPassword = (record: any) => {
  const userRecord = record as UserRecord
  message.success(`重置用户 ${userRecord.username} 密码成功，新密码已发送至邮箱`)
}

const handleExport = () => {
  message.success('导出用户数据成功')
}

const handleFilter = () => {
  loadUserList()
}

const handleSearch = () => {
  loadUserList()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadUserList()
}

const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    const action = formData.id ? '更新' : '创建'
    message.success(`${action}用户成功`)
    modalVisible.value = false
    loadUserList()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
}

onMounted(() => {
  loadUserList()
})
</script>

<style scoped lang="less">
.system-user-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;

  .page-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .header-left {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 28px;
          font-weight: 600;
          color: #262626;
        }

        .page-description {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  .stats-cards {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;

      :deep(.ant-statistic-title) {
        font-size: 14px;
        color: #8c8c8c;
      }

      :deep(.ant-statistic-content) {
        font-size: 24px;
        font-weight: 600;
      }
    }
  }

  .content-wrapper {
    .filter-section {
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;
      margin-bottom: 16px;
    }

    .table-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .toolbar-left {
        .ant-btn {
          margin-right: 8px;
        }
      }
    }

    // 表格样式
    :deep(.ant-table) {
      .user-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .user-details {
          .username {
            font-weight: 500;
            color: #262626;
            margin-bottom: 4px;
          }

          .email {
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }

      .status-tag {
        display: inline-flex;
        align-items: center;
        gap: 4px;
      }

      .roles-container {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .role-tag {
          margin: 0;
        }
      }

      .time-info {
        .time-ago {
          font-size: 12px;
          color: #8c8c8c;
          margin-top: 2px;
        }
      }

      .danger-item {
        color: #ff4d4f !important;

        &:hover {
          background-color: #fff2f0 !important;
        }
      }
    }
  }

  // 弹窗样式
  .permission-config {
    .user-info-card {
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;
      margin-bottom: 16px;
    }
  }

  .column-setting {
    .column-item {
      display: block;
      margin-bottom: 8px;
      padding: 8px;
      border-radius: 4px;

      &:hover {
        background: #f5f5f5;
      }
    }
  }
}
</style>
