<template>
  <div class="system-permission-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">权限管理</h1>
          <p class="page-description">管理系统权限结构，包括菜单权限、功能权限和数据权限</p>
        </div>
        <div class="header-right">
          <a-space>
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出权限
            </a-button>
            <a-button type="primary" @click="handleAdd">
              <template #icon><PlusOutlined /></template>
              新增权限
            </a-button>
          </a-space>
        </div>
      </div>
    </div>

    <!-- 统计卡片区 -->
    <div class="stats-cards">
      <a-row :gutter="24">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="总权限数"
              :value="permissionStats.total"
              :value-style="{ color: '#1e88e5' }"
            >
              <template #prefix>
                <SafetyOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="菜单权限"
              :value="permissionStats.menu"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <MenuOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="功能权限"
              :value="permissionStats.function"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <FunctionOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="数据权限"
              :value="permissionStats.data"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <DatabaseOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>
    
    <!-- 内容区 -->
    <div class="content-wrapper">
      <a-card>
        <div class="filter-section">
          <a-row :gutter="16" align="middle">
            <a-col :span="6">
              <a-select
                v-model:value="filterForm.type"
                placeholder="权限类型"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部类型</a-select-option>
                <a-select-option :value="1">菜单权限</a-select-option>
                <a-select-option :value="2">功能权限</a-select-option>
                <a-select-option :value="3">数据权限</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="6">
              <a-select
                v-model:value="filterForm.status"
                placeholder="权限状态"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部状态</a-select-option>
                <a-select-option value="enabled">启用</a-select-option>
                <a-select-option value="disabled">禁用</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="12">
              <a-input-search
                v-model:value="searchText"
                placeholder="搜索权限名称或代码"
                style="width: 100%"
                @search="handleSearch"
              />
            </a-col>
          </a-row>
        </div>

        <div class="table-toolbar">
          <div class="toolbar-left">
            <a-space>
              <a-button 
                @click="handleBatchDelete" 
                :disabled="!selectedRowKeys.length"
                danger
              >
                <template #icon><DeleteOutlined /></template>
                批量删除 ({{ selectedRowKeys.length }})
              </a-button>
              <a-button @click="handleBatchEnable" :disabled="!selectedRowKeys.length">
                <template #icon><CheckOutlined /></template>
                批量启用
              </a-button>
              <a-button @click="handleBatchDisable" :disabled="!selectedRowKeys.length">
                <template #icon><StopOutlined /></template>
                批量禁用
              </a-button>
            </a-space>
          </div>
          <div class="toolbar-right">
            <a-space>
              <a-tooltip title="展开全部">
                <a-button @click="expandAll">
                  <template #icon><ExpandOutlined /></template>
                </a-button>
              </a-tooltip>
              <a-tooltip title="收起全部">
                <a-button @click="collapseAll">
                  <template #icon><CompressOutlined /></template>
                </a-button>
              </a-tooltip>
              <a-tooltip title="刷新数据">
                <a-button @click="loadPermissionTree">
                  <template #icon><ReloadOutlined /></template>
                </a-button>
              </a-tooltip>
            </a-space>
          </div>
        </div>
        
        <a-table
          :columns="columns"
          :data-source="permissionTree"
          :loading="loading"
          :pagination="false"
          :row-selection="rowSelection"
          :scroll="{ x: 1200 }"
          row-key="id"
          size="middle"
          :default-expand-all-rows="false"
          :expanded-row-keys="expandedKeys"
          @expand="handleExpand"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="permission-info">
                <div class="permission-name">
                  <component :is="getPermissionIcon(record.type)" style="margin-right: 8px;" />
                  {{ record.name }}
                </div>
                <div class="permission-code">{{ record.code }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag 
                :color="record.status === 'enabled' ? 'success' : 'error'"
                class="status-tag"
              >
                <template #icon>
                  <CheckCircleOutlined v-if="record.status === 'enabled'" />
                  <StopOutlined v-else />
                </template>
                {{ record.status === 'enabled' ? '启用' : '禁用' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="edit" @click="handleEdit(record)">
                      <EditOutlined />
                      编辑权限
                    </a-menu-item>
                    <a-menu-item key="add-child" @click="handleAddChild(record)">
                      <PlusOutlined />
                      添加子权限
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item 
                      key="toggle-status" 
                      @click="handleToggleStatus(record)"
                    >
                      <template v-if="record.status === 'enabled'">
                        <StopOutlined />
                        禁用权限
                      </template>
                      <template v-else>
                        <CheckOutlined />
                        启用权限
                      </template>
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" @click="handleDelete(record)" class="danger-item">
                      <DeleteOutlined />
                      删除权限
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="text" size="small">
                  操作 <DownOutlined />
                </a-button>
              </a-dropdown>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 权限编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="600px"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="权限名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入权限名称" />
        </a-form-item>

        <a-form-item label="权限代码" name="code">
          <a-input v-model:value="formData.code" placeholder="请输入权限代码" />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="权限类型" name="type">
              <a-select v-model:value="formData.type" placeholder="请选择权限类型">
                <a-select-option :value="1">菜单权限</a-select-option>
                <a-select-option :value="2">功能权限</a-select-option>
                <a-select-option :value="3">数据权限</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="父级权限" name="parentId">
              <a-tree-select
                v-model:value="formData.parentId"
                :tree-data="parentOptions"
                placeholder="请选择父级权限"
                allow-clear
                tree-default-expand-all
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16" v-if="formData.type === 1">
          <a-col :span="12">
            <a-form-item label="路由路径" name="path">
              <a-input v-model:value="formData.path" placeholder="请输入路由路径" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="组件路径" name="component">
              <a-input v-model:value="formData.component" placeholder="请输入组件路径" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="图标" name="icon">
              <a-input v-model:value="formData.icon" placeholder="请输入图标名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="排序" name="sort">
              <a-input-number 
                v-model:value="formData.sort" 
                style="width: 100%"
                placeholder="排序值"
                :min="0"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="权限描述" name="description">
          <a-textarea 
            v-model:value="formData.description" 
            placeholder="请输入权限描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  DeleteOutlined,
  ExportOutlined,
  SafetyOutlined,
  MenuOutlined,
  FunctionOutlined,
  DatabaseOutlined,
  CheckOutlined,
  StopOutlined,
  ExpandOutlined,
  CompressOutlined,
  ReloadOutlined,
  EditOutlined,
  DownOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType } from 'ant-design-vue'
import { permissionApi, type PermissionListParams, type PermissionTreeResponse } from '@/api/system/permission'

interface PermissionRecord extends PermissionTreeResponse {
  status: string
}

// 统计数据
const permissionStats = reactive({
  total: 0,
  menu: 0,
  function: 0,
  data: 0
})

// 筛选表单
const filterForm = reactive({
  type: undefined as number | undefined,
  status: ''
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '权限信息',
    key: 'name',
    width: 300
  },
  {
    title: '类型',
    key: 'type',
    width: 120
  },
  {
    title: '路径',
    dataIndex: 'path',
    key: 'path',
    width: 200,
    ellipsis: true
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
    width: 80
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
]

// 响应式数据
const loading = ref(false)
const searchText = ref('')
const selectedRowKeys = ref<(string | number)[]>([])
const permissionTree = ref<PermissionRecord[]>([])
const expandedKeys = ref<(string | number)[]>([])
const modalVisible = ref(false)
const modalTitle = ref('')
const formRef = ref()

// 表格行选择配置
const rowSelection: any = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: (string | number)[]) => {
    selectedRowKeys.value = keys
  }
}

// 表单数据
const formData = reactive({
  id: null as number | null,
  name: '',
  code: '',
  type: 1,
  parentId: null as number | null,
  path: '',
  component: '',
  icon: '',
  sort: 0,
  description: ''
})

// 表单验证规则
const formRules: any = {
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入权限代码', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择权限类型', trigger: 'change' }
  ]
}

// 父级权限选项
const parentOptions = computed(() => {
  const buildOptions = (items: PermissionRecord[]): any[] => {
    return items.map(item => ({
      title: item.name,
      value: item.id,
      key: item.id,
      children: item.children ? buildOptions(item.children) : undefined
    }))
  }
  return buildOptions(permissionTree.value)
})

// 工具方法
const getPermissionIcon = (type: number) => {
  const icons: Record<number, any> = {
    1: MenuOutlined,
    2: FunctionOutlined,
    3: DatabaseOutlined
  }
  return icons[type] || MenuOutlined
}

const getTypeColor = (type: number) => {
  const colors: Record<number, string> = {
    1: 'blue',
    2: 'green',
    3: 'orange'
  }
  return colors[type] || 'default'
}

const getTypeText = (type: number) => {
  const texts: Record<number, string> = {
    1: '菜单权限',
    2: '功能权限',
    3: '数据权限'
  }
  return texts[type] || '未知'
}

// 统计数据更新
const updateStats = () => {
  const flattenPermissions = (items: PermissionRecord[]): PermissionRecord[] => {
    let result: PermissionRecord[] = []
    items.forEach(item => {
      result.push(item)
      if (item.children) {
        result = result.concat(flattenPermissions(item.children))
      }
    })
    return result
  }

  const allPermissions = flattenPermissions(permissionTree.value)
  permissionStats.total = allPermissions.length
  permissionStats.menu = allPermissions.filter(p => p.type === 1).length
  permissionStats.function = allPermissions.filter(p => p.type === 2).length
  permissionStats.data = allPermissions.filter(p => p.type === 3).length
}

// 方法定义
const loadPermissionTree = async () => {
  loading.value = true
  try {
    const response = await permissionApi.getPermissionTree()
    permissionTree.value = response.data.map(item => ({
      ...item,
      status: item.status === 'ENABLED' ? 'enabled' : 'disabled'
    }))
    updateStats()
  } catch (error) {
    message.error('加载权限树失败')
    console.error('Load permission tree error:', error)
  } finally {
    loading.value = false
  }
}

const expandAll = () => {
  const getAllKeys = (items: PermissionRecord[]): (string | number)[] => {
    let keys: (string | number)[] = []
    items.forEach(item => {
      keys.push(item.id)
      if (item.children) {
        keys = keys.concat(getAllKeys(item.children))
      }
    })
    return keys
  }
  expandedKeys.value = getAllKeys(permissionTree.value)
}

const collapseAll = () => {
  expandedKeys.value = []
}

const handleExpand = (expanded: boolean, record: any) => {
  const permissionRecord = record as PermissionRecord
  if (expanded) {
    expandedKeys.value.push(permissionRecord.id)
  } else {
    expandedKeys.value = expandedKeys.value.filter(key => key !== permissionRecord.id)
  }
}

const handleAdd = () => {
  modalTitle.value = '新增权限'
  Object.assign(formData, {
    id: null,
    name: '',
    code: '',
    type: 1,
    parentId: null,
    path: '',
    component: '',
    icon: '',
    sort: 0,
    description: ''
  })
  modalVisible.value = true
}

const handleAddChild = (record: any) => {
  const permissionRecord = record as PermissionRecord
  modalTitle.value = '新增子权限'
  Object.assign(formData, {
    id: null,
    name: '',
    code: '',
    type: permissionRecord.type,
    parentId: permissionRecord.id,
    path: '',
    component: '',
    icon: '',
    sort: 0,
    description: ''
  })
  modalVisible.value = true
}

const handleEdit = (record: any) => {
  const permissionRecord = record as PermissionRecord
  modalTitle.value = '编辑权限'
  Object.assign(formData, {
    id: permissionRecord.id,
    name: permissionRecord.name,
    code: permissionRecord.code,
    type: permissionRecord.type,
    parentId: permissionRecord.parentId,
    path: permissionRecord.path,
    component: permissionRecord.component,
    icon: permissionRecord.icon,
    sort: permissionRecord.sort,
    description: permissionRecord.description
  })
  modalVisible.value = true
}

const handleDelete = async (record: any) => {
  const permissionRecord = record as PermissionRecord
  try {
    await permissionApi.deletePermission(permissionRecord.id)
    message.success(`删除权限 ${permissionRecord.name} 成功`)
    loadPermissionTree()
  } catch (error) {
    message.error('删除权限失败')
  }
}

const handleBatchDelete = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的权限')
    return
  }
  try {
    for (const id of selectedRowKeys.value) {
      await permissionApi.deletePermission(id as number)
    }
    message.success(`批量删除 ${selectedRowKeys.value.length} 个权限成功`)
    selectedRowKeys.value = []
    loadPermissionTree()
  } catch (error) {
    message.error('批量删除权限失败')
  }
}

const handleBatchEnable = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要启用的权限')
    return
  }
  try {
    for (const id of selectedRowKeys.value) {
      await permissionApi.enablePermission(id as number)
    }
    message.success(`批量启用 ${selectedRowKeys.value.length} 个权限成功`)
    selectedRowKeys.value = []
    loadPermissionTree()
  } catch (error) {
    message.error('批量启用权限失败')
  }
}

const handleBatchDisable = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要禁用的权限')
    return
  }
  try {
    for (const id of selectedRowKeys.value) {
      await permissionApi.disablePermission(id as number)
    }
    message.success(`批量禁用 ${selectedRowKeys.value.length} 个权限成功`)
    selectedRowKeys.value = []
    loadPermissionTree()
  } catch (error) {
    message.error('批量禁用权限失败')
  }
}

const handleToggleStatus = async (record: any) => {
  const permissionRecord = record as PermissionRecord
  const newStatus = permissionRecord.status === 'enabled' ? 'disabled' : 'enabled'
  const action = newStatus === 'enabled' ? '启用' : '禁用'
  try {
    if (newStatus === 'enabled') {
      await permissionApi.enablePermission(permissionRecord.id)
    } else {
      await permissionApi.disablePermission(permissionRecord.id)
    }
    message.success(`${action}权限 ${permissionRecord.name} 成功`)
    loadPermissionTree()
  } catch (error) {
    message.error(`${action}权限失败`)
  }
}

const handleExport = () => {
  message.success('导出权限配置成功')
}

const handleFilter = () => {
  loadPermissionTree()
}

const handleSearch = () => {
  loadPermissionTree()
}

const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    
    const submitData = {
      name: formData.name,
      code: formData.code,
      type: formData.type,
      parentId: formData.parentId || undefined,
      path: formData.path,
      component: formData.component,
      icon: formData.icon,
      sort: formData.sort,
      description: formData.description
    }

    if (formData.id) {
      await permissionApi.updatePermission(formData.id, submitData)
      message.success('权限更新成功')
    } else {
      await permissionApi.createPermission(submitData)
      message.success('权限创建成功')
    }

    modalVisible.value = false
    loadPermissionTree()
  } catch (error) {
    if (error instanceof Error) {
      message.error(error.message)
    }
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
}

onMounted(() => {
  loadPermissionTree()
})
</script>

<style scoped lang="less">
.system-permission-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;

  .page-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .header-left {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 28px;
          font-weight: 600;
          color: #262626;
        }

        .page-description {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  .stats-cards {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;

      :deep(.ant-statistic-title) {
        font-size: 14px;
        color: #8c8c8c;
      }

      :deep(.ant-statistic-content) {
        font-size: 24px;
        font-weight: 600;
      }
    }
  }

  .content-wrapper {
    .filter-section {
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;
      margin-bottom: 16px;
    }

    .table-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .toolbar-left {
        .ant-btn {
          margin-right: 8px;
        }
      }
    }

    :deep(.ant-table) {
      .permission-info {
        .permission-name {
          font-weight: 500;
          color: #262626;
          margin-bottom: 4px;
          display: flex;
          align-items: center;
        }

        .permission-code {
          font-size: 12px;
          color: #8c8c8c;
          font-family: 'Monaco', 'Menlo', monospace;
        }
      }

      .status-tag {
        display: inline-flex;
        align-items: center;
        gap: 4px;
      }

      .danger-item {
        color: #ff4d4f !important;

        &:hover {
          background-color: #fff2f0 !important;
        }
      }
    }
  }
}
</style>
