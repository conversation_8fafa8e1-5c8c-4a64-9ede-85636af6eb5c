<template>
  <div class="system-report-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">统一报表与分析配置</h1>
          <p class="page-description">配置和管理系统报表模板、数据源连接和分析规则</p>
        </div>
        <div class="header-right">
          <a-space>
            <a-button @click="handlePreview">
              <template #icon><EyeOutlined /></template>
              预览报表
            </a-button>
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出配置
            </a-button>
            <a-button type="primary" @click="handleAdd">
              <template #icon><PlusOutlined /></template>
              新增报表
            </a-button>
          </a-space>
        </div>
      </div>
    </div>

    <!-- 统计卡片区 -->
    <div class="stats-cards">
      <a-row :gutter="24">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="报表总数"
              :value="reportStats.total"
              :value-style="{ color: '#1e88e5' }"
            >
              <template #prefix>
                <FileTextOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="活跃报表"
              :value="reportStats.active"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="数据源"
              :value="reportStats.dataSources"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <DatabaseOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="今日生成"
              :value="reportStats.todayGenerated"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <CalendarOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>
    
    <!-- 报表分类标签 -->
    <div class="report-tabs">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <a-tab-pane key="templates" tab="报表模板">
          <template #tab>
            <FileTextOutlined />
            报表模板
          </template>
        </a-tab-pane>
        <a-tab-pane key="datasources" tab="数据源">
          <template #tab>
            <DatabaseOutlined />
            数据源
          </template>
        </a-tab-pane>
        <a-tab-pane key="schedules" tab="定时任务">
          <template #tab>
            <ClockCircleOutlined />
            定时任务
          </template>
        </a-tab-pane>
        <a-tab-pane key="analytics" tab="分析规则">
          <template #tab>
            <FunctionOutlined />
            分析规则
          </template>
        </a-tab-pane>
      </a-tabs>
    </div>
    
    <!-- 内容区 -->
    <div class="content-wrapper">
      <a-card>
        <div class="filter-section">
          <a-row :gutter="16" align="middle">
            <a-col :span="4">
              <a-select
                v-model:value="filterForm.status"
                placeholder="状态"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部状态</a-select-option>
                <a-select-option value="active">活跃</a-select-option>
                <a-select-option value="inactive">停用</a-select-option>
                <a-select-option value="draft">草稿</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-select
                v-model:value="filterForm.type"
                placeholder="类型"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部类型</a-select-option>
                <a-select-option value="dashboard">仪表盘</a-select-option>
                <a-select-option value="chart">图表</a-select-option>
                <a-select-option value="table">表格</a-select-option>
                <a-select-option value="export">导出</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-select
                v-model:value="filterForm.category"
                placeholder="分类"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部分类</a-select-option>
                <a-select-option value="user">用户分析</a-select-option>
                <a-select-option value="behavior">行为分析</a-select-option>
                <a-select-option value="business">商业分析</a-select-option>
                <a-select-option value="system">系统报表</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="6">
              <a-range-picker
                v-model:value="filterForm.dateRange"
                :placeholder="['创建开始时间', '创建结束时间']"
                style="width: 100%"
                @change="handleFilter"
              />
            </a-col>
            <a-col :span="6">
              <a-input-search
                v-model:value="searchText"
                placeholder="搜索报表名称或描述"
                style="width: 100%"
                @search="handleSearch"
              />
            </a-col>
          </a-row>
        </div>

        <div class="table-toolbar">
          <div class="toolbar-left">
            <a-space>
              <a-button 
                @click="handleBatchDelete" 
                :disabled="!selectedRowKeys.length"
                danger
              >
                <template #icon><DeleteOutlined /></template>
                批量删除 ({{ selectedRowKeys.length }})
              </a-button>
              <a-button @click="handleBatchEnable" :disabled="!selectedRowKeys.length">
                <template #icon><CheckOutlined /></template>
                批量启用
              </a-button>
              <a-button @click="handleBatchGenerate" :disabled="!selectedRowKeys.length">
                <template #icon><PlayCircleOutlined /></template>
                批量生成
              </a-button>
            </a-space>
          </div>
          <div class="toolbar-right">
            <a-space>
              <a-tooltip title="刷新数据">
                <a-button @click="loadReportList">
                  <template #icon><ReloadOutlined /></template>
                </a-button>
              </a-tooltip>
              <a-tooltip title="视图切换">
                <a-button-group>
                  <a-button 
                    :type="viewMode === 'table' ? 'primary' : 'default'"
                    @click="viewMode = 'table'"
                  >
                    <template #icon><TableOutlined /></template>
                  </a-button>
                  <a-button 
                    :type="viewMode === 'grid' ? 'primary' : 'default'"
                    @click="viewMode = 'grid'"
                  >
                    <template #icon><AppstoreOutlined /></template>
                  </a-button>
                </a-button-group>
              </a-tooltip>
            </a-space>
          </div>
        </div>
        
        <!-- 表格视图 -->
        <a-table
          v-if="viewMode === 'table'"
          :columns="columns"
          :data-source="reportList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          :scroll="{ x: 1500 }"
          row-key="id"
          size="middle"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="report-info">
                <div class="report-name">{{ record.name }}</div>
                <div class="report-description">{{ record.description }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'category'">
              <a-tag :color="getCategoryColor(record.category)">
                {{ getCategoryText(record.category) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag 
                :color="getStatusColor(record.status)"
                class="status-tag"
              >
                <template #icon>
                  <CheckCircleOutlined v-if="record.status === 'active'" />
                  <StopOutlined v-else-if="record.status === 'inactive'" />
                  <EditOutlined v-else />
                </template>
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'lastGenerated'">
              <div class="time-info">
                <div>{{ formatDate(record.lastGenerated) }}</div>
                <div class="time-ago">{{ getTimeAgo(record.lastGenerated) }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'createdAt'">
              <div class="time-info">
                <div>{{ formatDate(record.createdAt) }}</div>
                <div class="time-ago">{{ getTimeAgo(record.createdAt) }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="preview" @click="handlePreviewReport(record)">
                      <EyeOutlined />
                      预览报表
                    </a-menu-item>
                    <a-menu-item key="edit" @click="handleEdit(record)">
                      <EditOutlined />
                      编辑配置
                    </a-menu-item>
                    <a-menu-item key="generate" @click="handleGenerate(record)">
                      <PlayCircleOutlined />
                      立即生成
                    </a-menu-item>
                    <a-menu-item key="schedule" @click="handleSchedule(record)">
                      <ClockCircleOutlined />
                      定时设置
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item 
                      key="toggle-status" 
                      @click="handleToggleStatus(record)"
                    >
                      <template v-if="record.status === 'active'">
                        <StopOutlined />
                        停用报表
                      </template>
                      <template v-else>
                        <CheckOutlined />
                        启用报表
                      </template>
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" @click="handleDelete(record)" class="danger-item">
                      <DeleteOutlined />
                      删除报表
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="text" size="small">
                  操作 <DownOutlined />
                </a-button>
              </a-dropdown>
            </template>
          </template>
        </a-table>

        <!-- 网格视图 -->
        <div v-else class="grid-view">
          <a-row :gutter="[24, 24]">
            <a-col 
              v-for="report in reportList" 
              :key="report.id"
              :xs="24" :sm="12" :md="8" :lg="6"
            >
              <a-card 
                class="report-card"
                :class="{ selected: selectedRowKeys.includes(report.id) }"
                @click="toggleSelection(report.id)"
              >
                <template #cover>
                  <div class="card-cover">
                    <div class="report-icon">
                      <FileTextOutlined v-if="report.type === 'dashboard'" />
                      <BarChartOutlined v-else-if="report.type === 'chart'" />
                      <TableOutlined v-else-if="report.type === 'table'" />
                      <ExportOutlined v-else />
                    </div>
                  </div>
                </template>
                <template #actions>
                  <EyeOutlined @click.stop="handlePreviewReport(report)" />
                  <EditOutlined @click.stop="handleEdit(report)" />
                  <PlayCircleOutlined @click.stop="handleGenerate(report)" />
                  <DeleteOutlined @click.stop="handleDelete(report)" />
                </template>
                <a-card-meta 
                  :title="report.name"
                  :description="report.description"
                />
                <div class="card-content">
                  <div class="card-item">
                    <span class="label">类型:</span>
                    <a-tag :color="getTypeColor(report.type)" size="small">
                      {{ getTypeText(report.type) }}
                    </a-tag>
                  </div>
                  <div class="card-item">
                    <span class="label">分类:</span>
                    <a-tag :color="getCategoryColor(report.category)" size="small">
                      {{ getCategoryText(report.category) }}
                    </a-tag>
                  </div>
                  <div class="card-item">
                    <span class="label">状态:</span>
                    <a-tag :color="getStatusColor(report.status)" size="small">
                      {{ getStatusText(report.status) }}
                    </a-tag>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  PlusOutlined, 
  DeleteOutlined, 
  ExportOutlined,
  EyeOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  DatabaseOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  FunctionOutlined,
  CheckOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
  TableOutlined,
  AppstoreOutlined,
  EditOutlined,
  StopOutlined,
  DownOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType } from 'ant-design-vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

dayjs.extend(relativeTime)

interface ReportRecord {
  id: number
  name: string
  description: string
  type: 'dashboard' | 'chart' | 'table' | 'export'
  category: 'user' | 'behavior' | 'business' | 'system'
  status: 'active' | 'inactive' | 'draft'
  dataSource: string
  schedule?: string
  lastGenerated?: string
  createdAt: string
  updatedAt: string
  createdBy: string
}

// 统计数据
const reportStats = reactive({
  total: 0,
  active: 0,
  dataSources: 0,
  todayGenerated: 0
})

// 当前活跃标签
const activeTab = ref('templates')

// 视图模式
const viewMode = ref<'table' | 'grid'>('table')

// 筛选表单
const filterForm = reactive({
  status: '',
  type: '',
  category: '',
  dateRange: null as any
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '报表信息',
    key: 'name',
    width: 250,
    fixed: 'left'
  },
  {
    title: '类型',
    key: 'type',
    width: 100
  },
  {
    title: '分类',
    key: 'category',
    width: 120
  },
  {
    title: '数据源',
    dataIndex: 'dataSource',
    key: 'dataSource',
    width: 150
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '最后生成',
    key: 'lastGenerated',
    width: 160
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
]

// 响应式数据
const loading = ref(false)
const searchText = ref('')
const selectedRowKeys = ref<(string | number)[]>([])
const reportList = ref<ReportRecord[]>([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 12,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格行选择配置
const rowSelection: any = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: (string | number)[]) => {
    selectedRowKeys.value = keys
  }
}

// TODO: 替换为真实API数据
// 临时空数据，等待API实现
const mockReportList: ReportRecord[] = [
  // 模拟数据已移除，等待后端API实现
]

// 工具方法
const formatDate = (dateStr?: string) => {
  if (!dateStr) return '-'
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss')
}

const getTimeAgo = (dateStr?: string) => {
  if (!dateStr) return ''
  return dayjs(dateStr).fromNow()
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    dashboard: 'blue',
    chart: 'green',
    table: 'orange',
    export: 'purple'
  }
  return colors[type] || 'default'
}

const getTypeText = (type: string) => {
  const texts: Record<string, string> = {
    dashboard: '仪表盘',
    chart: '图表',
    table: '表格',
    export: '导出'
  }
  return texts[type] || type
}

const getCategoryColor = (category: string) => {
  const colors: Record<string, string> = {
    user: 'cyan',
    behavior: 'geekblue',
    business: 'gold',
    system: 'red'
  }
  return colors[category] || 'default'
}

const getCategoryText = (category: string) => {
  const texts: Record<string, string> = {
    user: '用户分析',
    behavior: '行为分析',
    business: '商业分析',
    system: '系统报表'
  }
  return texts[category] || category
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    active: 'success',
    inactive: 'warning',
    draft: 'default'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    active: '活跃',
    inactive: '停用',
    draft: '草稿'
  }
  return texts[status] || status
}

// 统计数据更新
const updateStats = () => {
  reportStats.total = reportList.value.length
  reportStats.active = reportList.value.filter(r => r.status === 'active').length
  reportStats.dataSources = new Set(reportList.value.map(r => r.dataSource)).size

  const today = dayjs().startOf('day')
  reportStats.todayGenerated = reportList.value.filter(r =>
    r.lastGenerated && dayjs(r.lastGenerated).isAfter(today)
  ).length
}

// 网格选择切换
const toggleSelection = (id: number) => {
  const index = selectedRowKeys.value.indexOf(id)
  if (index > -1) {
    selectedRowKeys.value.splice(index, 1)
  } else {
    selectedRowKeys.value.push(id)
  }
}

// 方法定义
const loadReportList = () => {
  loading.value = true

  try {
    // TODO: 替换为真实API调用
    // const response = await reportApi.getReportList(filterForm)
    // reportList.value = response.data.list
    // pagination.total = response.data.total

    // 临时使用空数据，等待API实现
    reportList.value = []
    pagination.total = 0
    updateStats()
    console.log('报表管理API待实现')
  } catch (error) {
    console.error('加载报表列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleTabChange = (key: any) => {
  const tabKey = String(key)
  activeTab.value = tabKey
  // 根据不同标签页加载不同数据
  loadReportList()
}

const handleAdd = () => {
  message.info('新增报表功能开发中...')
}

const handleEdit = (record: any) => {
  const reportRecord = record as ReportRecord
  message.info(`编辑报表 ${reportRecord.name} 功能开发中...`)
}

const handleDelete = (record: any) => {
  const reportRecord = record as ReportRecord
  message.success(`删除报表 ${reportRecord.name} 成功`)
  loadReportList()
}

const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的报表')
    return
  }
  message.success(`批量删除 ${selectedRowKeys.value.length} 个报表成功`)
  selectedRowKeys.value = []
  loadReportList()
}

const handleBatchEnable = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要启用的报表')
    return
  }
  message.success(`批量启用 ${selectedRowKeys.value.length} 个报表成功`)
  selectedRowKeys.value = []
  loadReportList()
}

const handleBatchGenerate = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要生成的报表')
    return
  }
  message.success(`批量生成 ${selectedRowKeys.value.length} 个报表成功`)
  selectedRowKeys.value = []
  loadReportList()
}

const handleToggleStatus = (record: any) => {
  const reportRecord = record as ReportRecord
  const newStatus = reportRecord.status === 'active' ? 'inactive' : 'active'
  const action = newStatus === 'active' ? '启用' : '停用'
  message.success(`${action}报表 ${reportRecord.name} 成功`)
  loadReportList()
}

const handlePreviewReport = (record: any) => {
  const reportRecord = record as ReportRecord
  message.info(`预览报表 ${reportRecord.name} 功能开发中...`)
}

const handleGenerate = (record: any) => {
  const reportRecord = record as ReportRecord
  message.success(`生成报表 ${reportRecord.name} 成功`)
  loadReportList()
}

const handleSchedule = (record: any) => {
  const reportRecord = record as ReportRecord
  message.info(`设置报表 ${reportRecord.name} 定时任务功能开发中...`)
}

const handlePreview = () => {
  message.info('预览报表功能开发中...')
}

const handleExport = () => {
  message.success('导出报表配置成功')
}

const handleFilter = () => {
  loadReportList()
}

const handleSearch = () => {
  loadReportList()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadReportList()
}

onMounted(() => {
  loadReportList()
})
</script>

<style scoped lang="less">
.system-report-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;

  .page-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .header-left {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 28px;
          font-weight: 600;
          color: #262626;
        }

        .page-description {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  .stats-cards {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;

      :deep(.ant-statistic-title) {
        font-size: 14px;
        color: #8c8c8c;
      }

      :deep(.ant-statistic-content) {
        font-size: 24px;
        font-weight: 600;
      }
    }
  }

  .report-tabs {
    margin-bottom: 24px;

    :deep(.ant-tabs-nav) {
      background: white;
      border-radius: 6px;
      padding: 0 16px;
      margin-bottom: 0;
    }
  }

  .content-wrapper {
    .filter-section {
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;
      margin-bottom: 16px;
    }

    .table-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .toolbar-left {
        .ant-btn {
          margin-right: 8px;
        }
      }
    }

    // 表格样式
    :deep(.ant-table) {
      .report-info {
        .report-name {
          font-weight: 500;
          color: #262626;
          margin-bottom: 4px;
        }

        .report-description {
          font-size: 12px;
          color: #8c8c8c;
          line-height: 1.4;
        }
      }

      .status-tag {
        display: inline-flex;
        align-items: center;
        gap: 4px;
      }

      .time-info {
        .time-ago {
          font-size: 12px;
          color: #8c8c8c;
          margin-top: 2px;
        }
      }

      .danger-item {
        color: #ff4d4f !important;

        &:hover {
          background-color: #fff2f0 !important;
        }
      }
    }

    // 网格视图样式
    .grid-view {
      .report-card {
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          transform: translateY(-2px);
        }

        &.selected {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .card-cover {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 120px;
          background: #fafafa;

          .report-icon {
            font-size: 48px;
            color: #1890ff;
          }
        }

        .card-content {
          margin-top: 16px;

          .card-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .label {
              font-size: 12px;
              color: #8c8c8c;
            }
          }
        }
      }
    }
  }
}
</style>
