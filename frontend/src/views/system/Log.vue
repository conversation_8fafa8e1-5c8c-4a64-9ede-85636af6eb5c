<template>
  <div class="system-log-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">系统操作日志审计</h1>
          <p class="page-description">记录和审计系统中所有用户操作行为，确保系统安全和合规性</p>
        </div>
        <div class="header-right">
          <a-space>
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出日志
            </a-button>
            <a-button @click="handleCleanup">
              <template #icon><DeleteOutlined /></template>
              清理日志
            </a-button>
          </a-space>
        </div>
      </div>
    </div>

    <!-- 统计卡片区 -->
    <div class="stats-cards">
      <a-row :gutter="24">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="今日操作"
              :value="logStats.today"
              :value-style="{ color: '#1e88e5' }"
            >
              <template #prefix>
                <CalendarOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="本周操作"
              :value="logStats.week"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <BarChartOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="异常操作"
              :value="logStats.error"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <ExclamationCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="活跃用户"
              :value="logStats.activeUsers"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>
    
    <!-- 筛选和操作区 -->
    <div class="content-wrapper">
      <a-card>
        <div class="filter-section">
          <a-row :gutter="16" align="middle">
            <a-col :span="4">
              <a-select
                v-model:value="filterForm.level"
                placeholder="日志级别"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部级别</a-select-option>
                <a-select-option value="info">信息</a-select-option>
                <a-select-option value="warn">警告</a-select-option>
                <a-select-option value="error">错误</a-select-option>
                <a-select-option value="debug">调试</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-select
                v-model:value="filterForm.module"
                placeholder="操作模块"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部模块</a-select-option>
                <a-select-option value="user">用户管理</a-select-option>
                <a-select-option value="role">角色管理</a-select-option>
                <a-select-option value="system">系统设置</a-select-option>
                <a-select-option value="analytics">数据分析</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-select
                v-model:value="filterForm.action"
                placeholder="操作类型"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部操作</a-select-option>
                <a-select-option value="create">创建</a-select-option>
                <a-select-option value="update">更新</a-select-option>
                <a-select-option value="delete">删除</a-select-option>
                <a-select-option value="login">登录</a-select-option>
                <a-select-option value="logout">登出</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="6">
              <a-range-picker
                v-model:value="filterForm.dateRange"
                show-time
                :placeholder="['开始时间', '结束时间']"
                style="width: 100%"
                @change="handleFilter"
              />
            </a-col>
            <a-col :span="6">
              <a-input-search
                v-model:value="searchText"
                placeholder="搜索用户、IP或操作内容"
                style="width: 100%"
                @search="handleSearch"
              />
            </a-col>
          </a-row>
        </div>

        <div class="table-toolbar">
          <div class="toolbar-left">
            <a-space>
              <a-button 
                @click="handleBatchDelete" 
                :disabled="!selectedRowKeys.length"
                danger
              >
                <template #icon><DeleteOutlined /></template>
                批量删除 ({{ selectedRowKeys.length }})
              </a-button>
              <a-button @click="handleBatchExport" :disabled="!selectedRowKeys.length">
                <template #icon><ExportOutlined /></template>
                批量导出
              </a-button>
            </a-space>
          </div>
          <div class="toolbar-right">
            <a-space>
              <a-tooltip title="自动刷新">
                <a-switch 
                  v-model:checked="autoRefresh" 
                  @change="handleAutoRefreshChange"
                  size="small"
                />
              </a-tooltip>
              <a-tooltip title="刷新数据">
                <a-button @click="loadLogList">
                  <template #icon><ReloadOutlined /></template>
                </a-button>
              </a-tooltip>
            </a-space>
          </div>
        </div>
        
        <a-table
          :columns="columns"
          :data-source="logList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          :scroll="{ x: 1400 }"
          row-key="id"
          size="small"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'level'">
              <a-tag :color="getLevelColor(record.level)" class="level-tag">
                <template #icon>
                  <InfoCircleOutlined v-if="record.level === 'info'" />
                  <WarningOutlined v-else-if="record.level === 'warn'" />
                  <ExclamationCircleOutlined v-else-if="record.level === 'error'" />
                  <BugOutlined v-else />
                </template>
                {{ getLevelText(record.level) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'user'">
              <div class="user-info">
                <div class="username">{{ record.username }}</div>
                <div class="user-ip">{{ record.ip }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'module'">
              <a-tag :color="getModuleColor(record.module)">
                {{ getModuleText(record.module) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-tag :color="getActionColor(record.action)">
                {{ getActionText(record.action) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'content'">
              <a-tooltip>
                <template #title>{{ record.content }}</template>
                <div class="content-text">{{ record.content }}</div>
              </a-tooltip>
            </template>
            <template v-else-if="column.key === 'createdAt'">
              <div class="time-info">
                <div>{{ formatDate(record.createdAt) }}</div>
                <div class="time-ago">{{ getTimeAgo(record.createdAt) }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'action_btn'">
              <a-space>
                <a-button type="link" size="small" @click="handleViewDetail(record)">
                  详情
                </a-button>
                <a-popconfirm
                  title="确定要删除这条日志吗？"
                  @confirm="handleDelete(record)"
                >
                  <a-button type="link" size="small" danger>
                    删除
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 日志详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="日志详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentLog" class="log-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="日志ID">{{ currentLog.id }}</a-descriptions-item>
          <a-descriptions-item label="日志级别">
            <a-tag :color="getLevelColor(currentLog.level)">
              {{ getLevelText(currentLog.level) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="操作用户">{{ currentLog.username }}</a-descriptions-item>
          <a-descriptions-item label="用户IP">{{ currentLog.ip }}</a-descriptions-item>
          <a-descriptions-item label="操作模块">
            <a-tag :color="getModuleColor(currentLog.module)">
              {{ getModuleText(currentLog.module) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="操作类型">
            <a-tag :color="getActionColor(currentLog.action)">
              {{ getActionText(currentLog.action) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="操作时间" :span="2">
            {{ formatDate(currentLog.createdAt) }}
          </a-descriptions-item>
          <a-descriptions-item label="操作内容" :span="2">
            <div class="log-content">{{ currentLog.content }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="请求参数" :span="2" v-if="currentLog.params">
            <pre class="log-params">{{ JSON.stringify(currentLog.params, null, 2) }}</pre>
          </a-descriptions-item>
          <a-descriptions-item label="响应结果" :span="2" v-if="currentLog.result">
            <pre class="log-result">{{ JSON.stringify(currentLog.result, null, 2) }}</pre>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  ExportOutlined,
  DeleteOutlined,
  CalendarOutlined,
  BarChartOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  BugOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType } from 'ant-design-vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import { operationLogApi, type OperationLogListParams, type OperationLogDetailResponse } from '@/api/system/operationLog'

dayjs.extend(relativeTime)

interface LogRecord {
  id: number
  level: 'info' | 'warn' | 'error' | 'debug'
  username: string
  ip: string
  module: string
  action: string
  content: string
  params?: any
  result?: any
  createdAt: string
}

// 统计数据
const logStats = reactive({
  today: 0,
  week: 0,
  error: 0,
  activeUsers: 0
})

// 筛选表单
const filterForm = reactive({
  level: '',
  module: '',
  action: '',
  dateRange: null as any
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '级别',
    key: 'level',
    width: 80,
    fixed: 'left'
  },
  {
    title: '用户信息',
    key: 'user',
    width: 150
  },
  {
    title: '模块',
    key: 'module',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 100
  },
  {
    title: '操作内容',
    key: 'content',
    width: 300,
    ellipsis: true
  },
  {
    title: '操作时间',
    key: 'createdAt',
    width: 160
  },
  {
    title: '操作',
    key: 'action_btn',
    width: 120,
    fixed: 'right'
  }
]

// 响应式数据
const loading = ref(false)
const searchText = ref('')
const selectedRowKeys = ref<(string | number)[]>([])
const logList = ref<LogRecord[]>([])
const detailModalVisible = ref(false)
const currentLog = ref<LogRecord | null>(null)
const autoRefresh = ref(false)
let refreshTimer: NodeJS.Timeout | null = null

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格行选择配置
const rowSelection: any = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: (string | number)[]) => {
    selectedRowKeys.value = keys
  }
}

// TODO: 替换为真实API数据
// 临时空数据，等待API实现
const mockLogList: LogRecord[] = [
  // 模拟数据已移除，等待后端API实现
]

// 工具方法
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss')
}

const getTimeAgo = (dateStr: string) => {
  if (!dateStr) return ''
  return dayjs(dateStr).fromNow()
}

const getLevelColor = (level: string) => {
  const colors: Record<string, string> = {
    info: 'blue',
    warn: 'orange',
    error: 'red',
    debug: 'purple'
  }
  return colors[level] || 'default'
}

const getLevelText = (level: string) => {
  const texts: Record<string, string> = {
    info: '信息',
    warn: '警告',
    error: '错误',
    debug: '调试'
  }
  return texts[level] || level
}

const getModuleColor = (module: string) => {
  const colors: Record<string, string> = {
    user: 'green',
    role: 'blue',
    system: 'orange',
    analytics: 'purple'
  }
  return colors[module] || 'default'
}

const getModuleText = (module: string) => {
  const texts: Record<string, string> = {
    user: '用户管理',
    role: '角色管理',
    system: '系统设置',
    analytics: '数据分析'
  }
  return texts[module] || module
}

const getActionColor = (action: string) => {
  const colors: Record<string, string> = {
    create: 'green',
    update: 'blue',
    delete: 'red',
    login: 'cyan',
    logout: 'gray',
    view: 'purple'
  }
  return colors[action] || 'default'
}

const getActionText = (action: string) => {
  const texts: Record<string, string> = {
    create: '创建',
    update: '更新',
    delete: '删除',
    login: '登录',
    logout: '登出',
    view: '查看'
  }
  return texts[action] || action
}

// 统计数据更新
const updateStats = () => {
  const today = dayjs().startOf('day')
  const weekStart = dayjs().startOf('week')

  logStats.today = logList.value.filter(log =>
    dayjs(log.createdAt).isAfter(today)
  ).length

  logStats.week = logList.value.filter(log =>
    dayjs(log.createdAt).isAfter(weekStart)
  ).length

  logStats.error = logList.value.filter(log =>
    log.level === 'error'
  ).length

  logStats.activeUsers = new Set(logList.value.map(log => log.username)).size
}

// 方法定义
const loadLogList = async () => {
  loading.value = true
  try {
    const params: OperationLogListParams = {
      page: pagination.current,
      size: pagination.pageSize,
      keyword: searchText.value || undefined,
      operation: filterForm.action || undefined,
      module: filterForm.module || undefined,
      status: filterForm.level === 'error' ? 'FAILURE' : undefined,
      startTime: filterForm.dateRange?.[0] ? dayjs(filterForm.dateRange[0]).format('YYYY-MM-DD HH:mm:ss') : undefined,
      endTime: filterForm.dateRange?.[1] ? dayjs(filterForm.dateRange[1]).format('YYYY-MM-DD HH:mm:ss') : undefined
    }

    const response = await operationLogApi.getOperationLogList(params)
    console.log('Log API response:', response)

    // 兼容不同的响应格式
    const data = response.data || response
    const items = (data as any).items || (data as any).records || []
    const total = (data as any).total || (data as any).totalCount || 0

    logList.value = items.map((item: any) => ({
      id: item.id,
      level: item.status === 'SUCCESS' ? 'info' : 'error',
      username: item.username,
      ip: item.ipAddress,
      module: item.module,
      action: item.operation,
      content: item.operationName,
      params: item.requestParams ? JSON.parse(item.requestParams) : undefined,
      result: item.responseData ? JSON.parse(item.responseData) : undefined,
      createdAt: item.operationTime
    }))
    pagination.total = total
    updateStats()
  } catch (error) {
    message.error('加载操作日志失败')
    console.error('Load log list error:', error)
    console.error('Error details:', {
      message: (error as any).message,
      response: (error as any).response,
      status: (error as any).response?.status,
      data: (error as any).response?.data
    })
  } finally {
    loading.value = false
  }
}

const handleViewDetail = (record: any) => {
  currentLog.value = record as LogRecord
  detailModalVisible.value = true
}

const handleDelete = (record: any) => {
  message.success(`删除日志 ${record.id} 成功`)
  loadLogList()
}

const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的日志')
    return
  }
  message.success(`批量删除 ${selectedRowKeys.value.length} 条日志成功`)
  selectedRowKeys.value = []
  loadLogList()
}

const handleBatchExport = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要导出的日志')
    return
  }
  message.success(`批量导出 ${selectedRowKeys.value.length} 条日志成功`)
}

const handleExport = () => {
  message.success('导出日志成功')
}

const handleCleanup = () => {
  message.success('清理历史日志成功')
  loadLogList()
}

const handleAutoRefreshChange = (checked: any) => {
  const isChecked = Boolean(checked)
  if (isChecked) {
    refreshTimer = setInterval(() => {
      loadLogList()
    }, 30000) // 30秒刷新一次
    message.info('已开启自动刷新（30秒间隔）')
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
    message.info('已关闭自动刷新')
  }
}

const handleFilter = () => {
  loadLogList()
}

const handleSearch = () => {
  loadLogList()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadLogList()
}

onMounted(() => {
  loadLogList()
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped lang="less">
.system-log-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;

  .page-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .header-left {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 28px;
          font-weight: 600;
          color: #262626;
        }

        .page-description {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  .stats-cards {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;

      :deep(.ant-statistic-title) {
        font-size: 14px;
        color: #8c8c8c;
      }

      :deep(.ant-statistic-content) {
        font-size: 24px;
        font-weight: 600;
      }
    }
  }

  .content-wrapper {
    .filter-section {
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;
      margin-bottom: 16px;
    }

    .table-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .toolbar-left {
        .ant-btn {
          margin-right: 8px;
        }
      }
    }

    // 表格样式
    :deep(.ant-table) {
      .level-tag {
        display: inline-flex;
        align-items: center;
        gap: 4px;
      }

      .user-info {
        .username {
          font-weight: 500;
          color: #262626;
          margin-bottom: 4px;
        }

        .user-ip {
          font-size: 12px;
          color: #8c8c8c;
          font-family: 'Monaco', 'Menlo', monospace;
        }
      }

      .content-text {
        max-width: 280px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .time-info {
        .time-ago {
          font-size: 12px;
          color: #8c8c8c;
          margin-top: 2px;
        }
      }
    }
  }

  // 弹窗样式
  .log-detail {
    .log-content {
      padding: 8px;
      background: #f5f5f5;
      border-radius: 4px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
    }

    .log-params,
    .log-result {
      padding: 8px;
      background: #f5f5f5;
      border-radius: 4px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
      max-height: 200px;
      overflow-y: auto;
    }
  }
}
</style>
