<template>
  <div class="system-product-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">产品与版本管理</h1>
          <p class="page-description">管理系统支持的产品线配置，版本发布和生命周期管理</p>
        </div>
        <div class="header-right">
          <a-space>
            <a-button @click="handleSync">
              <template #icon><SyncOutlined /></template>
              同步产品
            </a-button>
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出配置
            </a-button>
            <a-button type="primary" @click="handleAdd">
              <template #icon><PlusOutlined /></template>
              新增产品
            </a-button>
          </a-space>
        </div>
      </div>
    </div>

    <!-- 统计卡片区 -->
    <div class="stats-cards">
      <a-row :gutter="24">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="产品总数"
              :value="productStats.total"
              :value-style="{ color: '#1e88e5' }"
            >
              <template #prefix>
                <AppstoreOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="活跃产品"
              :value="productStats.active"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="版本总数"
              :value="productStats.versions"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <TagsOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="待发布"
              :value="productStats.pending"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <ClockCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>
    
    <!-- 筛选和操作区 -->
    <div class="content-wrapper">
      <a-card>
        <div class="filter-section">
          <a-row :gutter="16" align="middle">
            <a-col :span="4">
              <a-select
                v-model:value="filterForm.status"
                placeholder="产品状态"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部状态</a-select-option>
                <a-select-option value="active">活跃</a-select-option>
                <a-select-option value="inactive">停用</a-select-option>
                <a-select-option value="deprecated">已废弃</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-select
                v-model:value="filterForm.category"
                placeholder="产品类别"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部类别</a-select-option>
                <a-select-option value="pdf">PDF产品</a-select-option>
                <a-select-option value="sdk">SDK产品</a-select-option>
                <a-select-option value="cloud">云服务</a-select-option>
                <a-select-option value="mobile">移动应用</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-select
                v-model:value="filterForm.platform"
                placeholder="支持平台"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部平台</a-select-option>
                <a-select-option value="windows">Windows</a-select-option>
                <a-select-option value="mac">macOS</a-select-option>
                <a-select-option value="linux">Linux</a-select-option>
                <a-select-option value="web">Web</a-select-option>
                <a-select-option value="mobile">Mobile</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="6">
              <a-range-picker
                v-model:value="filterForm.dateRange"
                :placeholder="['创建开始时间', '创建结束时间']"
                style="width: 100%"
                @change="handleFilter"
              />
            </a-col>
            <a-col :span="6">
              <a-input-search
                v-model:value="searchText"
                placeholder="搜索产品名称或代码"
                style="width: 100%"
                @search="handleSearch"
              />
            </a-col>
          </a-row>
        </div>

        <div class="table-toolbar">
          <div class="toolbar-left">
            <a-space>
              <a-button 
                @click="handleBatchDelete" 
                :disabled="!selectedRowKeys.length"
                danger
              >
                <template #icon><DeleteOutlined /></template>
                批量删除 ({{ selectedRowKeys.length }})
              </a-button>
              <a-button @click="handleBatchEnable" :disabled="!selectedRowKeys.length">
                <template #icon><CheckOutlined /></template>
                批量启用
              </a-button>
              <a-button @click="handleBatchDisable" :disabled="!selectedRowKeys.length">
                <template #icon><StopOutlined /></template>
                批量停用
              </a-button>
            </a-space>
          </div>
          <div class="toolbar-right">
            <a-space>
              <a-tooltip title="刷新数据">
                <a-button @click="loadProductList">
                  <template #icon><ReloadOutlined /></template>
                </a-button>
              </a-tooltip>
              <a-tooltip title="视图切换">
                <a-button-group>
                  <a-button 
                    :type="viewMode === 'table' ? 'primary' : 'default'"
                    @click="viewMode = 'table'"
                  >
                    <template #icon><TableOutlined /></template>
                  </a-button>
                  <a-button 
                    :type="viewMode === 'card' ? 'primary' : 'default'"
                    @click="viewMode = 'card'"
                  >
                    <template #icon><AppstoreOutlined /></template>
                  </a-button>
                </a-button-group>
              </a-tooltip>
            </a-space>
          </div>
        </div>
        
        <!-- 表格视图 -->
        <a-table
          v-if="viewMode === 'table'"
          :columns="columns"
          :data-source="productList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          :scroll="{ x: 1600 }"
          row-key="id"
          size="middle"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'product'">
              <div class="product-info">
                <a-avatar 
                  :src="record.icon" 
                  :alt="record.name"
                  shape="square"
                  size="large"
                >
                  {{ record.name.charAt(0) }}
                </a-avatar>
                <div class="product-details">
                  <div class="product-name">{{ record.name }}</div>
                  <div class="product-code">{{ record.code }}</div>
                </div>
              </div>
            </template>
            <template v-else-if="column.key === 'category'">
              <a-tag :color="getCategoryColor(record.category)">
                {{ getCategoryText(record.category) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'platforms'">
              <div class="platforms-container">
                <a-tag 
                  v-for="platform in record.platforms" 
                  :key="platform"
                  :color="getPlatformColor(platform)"
                  size="small"
                >
                  {{ getPlatformText(platform) }}
                </a-tag>
              </div>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag 
                :color="getStatusColor(record.status)"
                class="status-tag"
              >
                <template #icon>
                  <CheckCircleOutlined v-if="record.status === 'active'" />
                  <StopOutlined v-else-if="record.status === 'inactive'" />
                  <ExclamationCircleOutlined v-else />
                </template>
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'versions'">
              <a-button type="link" @click="handleViewVersions(record)">
                {{ record.versionCount }} 个版本
              </a-button>
            </template>
            <template v-else-if="column.key === 'currentVersion'">
              <a-tag color="blue">v{{ record.currentVersion }}</a-tag>
            </template>
            <template v-else-if="column.key === 'createdAt'">
              <div class="time-info">
                <div>{{ formatDate(record.createdAt) }}</div>
                <div class="time-ago">{{ getTimeAgo(record.createdAt) }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="edit" @click="handleEdit(record)">
                      <EditOutlined />
                      编辑产品
                    </a-menu-item>
                    <a-menu-item key="versions" @click="handleViewVersions(record)">
                      <TagsOutlined />
                      版本管理
                    </a-menu-item>
                    <a-menu-item key="config" @click="handleConfig(record)">
                      <SettingOutlined />
                      产品配置
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item 
                      key="toggle-status" 
                      @click="handleToggleStatus(record)"
                    >
                      <template v-if="record.status === 'active'">
                        <StopOutlined />
                        停用产品
                      </template>
                      <template v-else>
                        <CheckOutlined />
                        启用产品
                      </template>
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" @click="handleDelete(record)" class="danger-item">
                      <DeleteOutlined />
                      删除产品
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="text" size="small">
                  操作 <DownOutlined />
                </a-button>
              </a-dropdown>
            </template>
          </template>
        </a-table>

        <!-- 卡片视图 -->
        <div v-else class="card-view">
          <a-row :gutter="[24, 24]">
            <a-col 
              v-for="product in productList" 
              :key="product.id"
              :xs="24" :sm="12" :md="8" :lg="6"
            >
              <a-card 
                class="product-card"
                :class="{ selected: selectedRowKeys.includes(product.id) }"
                @click="toggleSelection(product.id)"
              >
                <template #cover>
                  <div class="card-cover">
                    <a-avatar 
                      :src="product.icon" 
                      :alt="product.name"
                      shape="square"
                      :size="64"
                    >
                      {{ product.name.charAt(0) }}
                    </a-avatar>
                  </div>
                </template>
                <template #actions>
                  <EditOutlined @click.stop="handleEdit(product)" />
                  <TagsOutlined @click.stop="handleViewVersions(product)" />
                  <SettingOutlined @click.stop="handleConfig(product)" />
                  <DeleteOutlined @click.stop="handleDelete(product)" />
                </template>
                <a-card-meta 
                  :title="product.name"
                  :description="product.description"
                />
                <div class="card-content">
                  <div class="card-item">
                    <span class="label">代码:</span>
                    <span class="value">{{ product.code }}</span>
                  </div>
                  <div class="card-item">
                    <span class="label">类别:</span>
                    <a-tag :color="getCategoryColor(product.category)" size="small">
                      {{ getCategoryText(product.category) }}
                    </a-tag>
                  </div>
                  <div class="card-item">
                    <span class="label">状态:</span>
                    <a-tag :color="getStatusColor(product.status)" size="small">
                      {{ getStatusText(product.status) }}
                    </a-tag>
                  </div>
                  <div class="card-item">
                    <span class="label">版本:</span>
                    <a-tag color="blue" size="small">v{{ product.currentVersion }}</a-tag>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </div>

    <!-- 版本管理模态框 -->
    <ProductVersionModal
      v-model:open="versionModalVisible"
      :product-info="selectedProduct"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { 
  PlusOutlined, 
  DeleteOutlined, 
  ExportOutlined,
  SyncOutlined,
  AppstoreOutlined,
  CheckCircleOutlined,
  TagsOutlined,
  ClockCircleOutlined,
  CheckOutlined,
  StopOutlined,
  ReloadOutlined,
  TableOutlined,
  EditOutlined,
  SettingOutlined,
  DownOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType } from 'ant-design-vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import ProductVersionModal from '@/components/system/ProductVersionModal.vue'
import { productLineApi, type ProductLineListParams, type ProductLineDetailResponse } from '@/api/system/productLine'

dayjs.extend(relativeTime)

interface ProductRecord {
  id: number
  name: string
  code: string
  description: string
  category: string
  platforms: string[]
  status: 'active' | 'inactive' | 'deprecated'
  currentVersion: string
  versionCount: number
  icon?: string
  createdAt: string
  updatedAt: string
  createdBy: string
}

// 统计数据
const productStats = reactive({
  total: 0,
  active: 0,
  versions: 0,
  pending: 0
})

// 筛选表单
const filterForm = reactive({
  status: '',
  category: '',
  platform: '',
  dateRange: null as any
})

// 视图模式
const viewMode = ref<'table' | 'card'>('table')

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '产品信息',
    key: 'product',
    width: 250,
    fixed: 'left'
  },
  {
    title: '类别',
    key: 'category',
    width: 120
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    width: 200,
    ellipsis: true
  },
  {
    title: '支持平台',
    key: 'platforms',
    width: 200
  },
  {
    title: '当前版本',
    key: 'currentVersion',
    width: 120
  },
  {
    title: '版本管理',
    key: 'versions',
    width: 120
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
]

// 响应式数据
const loading = ref(false)
const searchText = ref('')
const selectedRowKeys = ref<(string | number)[]>([])
const productList = ref<ProductRecord[]>([])
const versionModalVisible = ref(false)
const selectedProduct = ref<any>(null)

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格行选择配置
const rowSelection: any = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: (string | number)[]) => {
    selectedRowKeys.value = keys
  }
}

// 产品列表数据通过API获取，不再使用模拟数据

// 工具方法
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss')
}

const getTimeAgo = (dateStr: string) => {
  if (!dateStr) return ''
  return dayjs(dateStr).fromNow()
}

const getCategoryColor = (category: string) => {
  const colors: Record<string, string> = {
    pdf: 'red',
    sdk: 'blue',
    cloud: 'cyan',
    mobile: 'green'
  }
  return colors[category] || 'default'
}

const getCategoryText = (category: string) => {
  const texts: Record<string, string> = {
    pdf: 'PDF产品',
    sdk: 'SDK产品',
    cloud: '云服务',
    mobile: '移动应用'
  }
  return texts[category] || category
}

const getPlatformColor = (platform: string) => {
  const colors: Record<string, string> = {
    windows: 'blue',
    mac: 'gray',
    linux: 'orange',
    web: 'green',
    mobile: 'purple'
  }
  return colors[platform] || 'default'
}

const getPlatformText = (platform: string) => {
  const texts: Record<string, string> = {
    windows: 'Windows',
    mac: 'macOS',
    linux: 'Linux',
    web: 'Web',
    mobile: 'Mobile'
  }
  return texts[platform] || platform
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    active: 'success',
    inactive: 'warning',
    deprecated: 'error'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    active: '活跃',
    inactive: '停用',
    deprecated: '已废弃'
  }
  return texts[status] || status
}

// 统计数据更新
const updateStats = () => {
  productStats.total = productList.value.length
  productStats.active = productList.value.filter(p => p.status === 'active').length
  productStats.versions = productList.value.reduce((sum, p) => sum + p.versionCount, 0)
  productStats.pending = productList.value.filter(p => p.status === 'inactive').length
}

// 卡片选择切换
const toggleSelection = (id: number) => {
  const index = selectedRowKeys.value.indexOf(id)
  if (index > -1) {
    selectedRowKeys.value.splice(index, 1)
  } else {
    selectedRowKeys.value.push(id)
  }
}

// 方法定义
const loadProductList = async () => {
  loading.value = true
  try {
    const params: ProductLineListParams = {
      page: pagination.current,
      size: pagination.pageSize,
      keyword: searchText.value || undefined,
      status: filterForm.status || undefined
    }

    const response = await productLineApi.getProductLineList(params)
    console.log('Product API response:', response)

    // 兼容不同的响应格式
    const data = response.data || response
    const items = (data as any).items || (data as any).records || []
    const total = (data as any).total || (data as any).totalCount || 0

    productList.value = items.map((item: any) => ({
      id: item.id,
      name: item.name,
      code: item.code,
      category: item.typeName || '未分类',
      description: item.description || '',
      status: item.status,
      platforms: ['Web', 'Desktop'], // 默认平台，实际应从API获取
      userCount: item.userCount || 0,
      dataCount: item.dataCount || 0,
      lastSync: item.lastSyncTime,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      remark: item.remark
    }))
    pagination.total = total
    updateStats()
  } catch (error) {
    message.error('加载产品线列表失败')
    console.error('Load product list error:', error)
    console.error('Error details:', {
      message: (error as any).message,
      response: (error as any).response,
      status: (error as any).response?.status,
      data: (error as any).response?.data
    })
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  message.info('新增产品功能开发中...')
}

const handleEdit = (record: any) => {
  const productRecord = record as ProductRecord
  message.info(`编辑产品 ${productRecord.name} 功能开发中...`)
}

const handleDelete = (record: any) => {
  const productRecord = record as ProductRecord
  message.success(`删除产品 ${productRecord.name} 成功`)
  loadProductList()
}

const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的产品')
    return
  }
  message.success(`批量删除 ${selectedRowKeys.value.length} 个产品成功`)
  selectedRowKeys.value = []
  loadProductList()
}

const handleBatchEnable = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要启用的产品')
    return
  }
  message.success(`批量启用 ${selectedRowKeys.value.length} 个产品成功`)
  selectedRowKeys.value = []
  loadProductList()
}

const handleBatchDisable = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要停用的产品')
    return
  }
  message.success(`批量停用 ${selectedRowKeys.value.length} 个产品成功`)
  selectedRowKeys.value = []
  loadProductList()
}

const handleToggleStatus = (record: any) => {
  const productRecord = record as ProductRecord
  const newStatus = productRecord.status === 'active' ? 'inactive' : 'active'
  const action = newStatus === 'active' ? '启用' : '停用'
  message.success(`${action}产品 ${productRecord.name} 成功`)
  loadProductList()
}

const handleViewVersions = (record: any) => {
  selectedProduct.value = record as ProductRecord
  versionModalVisible.value = true
}

const handleConfig = (record: any) => {
  const productRecord = record as ProductRecord
  message.info(`配置产品 ${productRecord.name} 功能开发中...`)
}

const handleSync = () => {
  message.success('同步产品数据成功')
  loadProductList()
}

const handleExport = () => {
  message.success('导出产品配置成功')
}

const handleFilter = () => {
  loadProductList()
}

const handleSearch = () => {
  loadProductList()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadProductList()
}

onMounted(() => {
  loadProductList()
})
</script>

<style scoped lang="less">
.system-product-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;

  .page-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .header-left {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 28px;
          font-weight: 600;
          color: #262626;
        }

        .page-description {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  .stats-cards {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;

      :deep(.ant-statistic-title) {
        font-size: 14px;
        color: #8c8c8c;
      }

      :deep(.ant-statistic-content) {
        font-size: 24px;
        font-weight: 600;
      }
    }
  }

  .content-wrapper {
    .filter-section {
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;
      margin-bottom: 16px;
    }

    .table-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .toolbar-left {
        .ant-btn {
          margin-right: 8px;
        }
      }
    }

    // 表格样式
    :deep(.ant-table) {
      .product-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .product-details {
          .product-name {
            font-weight: 500;
            color: #262626;
            margin-bottom: 4px;
          }

          .product-code {
            font-size: 12px;
            color: #8c8c8c;
            font-family: 'Monaco', 'Menlo', monospace;
          }
        }
      }

      .platforms-container {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
      }

      .status-tag {
        display: inline-flex;
        align-items: center;
        gap: 4px;
      }

      .time-info {
        .time-ago {
          font-size: 12px;
          color: #8c8c8c;
          margin-top: 2px;
        }
      }

      .danger-item {
        color: #ff4d4f !important;

        &:hover {
          background-color: #fff2f0 !important;
        }
      }
    }

    // 卡片视图样式
    .card-view {
      .product-card {
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          transform: translateY(-2px);
        }

        &.selected {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .card-cover {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 120px;
          background: #fafafa;
        }

        .card-content {
          margin-top: 16px;

          .card-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .label {
              font-size: 12px;
              color: #8c8c8c;
            }

            .value {
              font-size: 12px;
              color: #262626;
              font-family: 'Monaco', 'Menlo', monospace;
            }
          }
        }
      }
    }
  }
}
</style>
