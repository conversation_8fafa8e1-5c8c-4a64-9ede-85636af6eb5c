<template>
  <div class="system-config-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">基础配置管理</h1>
          <p class="page-description">管理系统基础配置参数，包括系统设置、业务参数和集成配置</p>
        </div>
        <div class="header-right">
          <a-space>
            <a-button @click="handleImport">
              <template #icon><ImportOutlined /></template>
              导入配置
            </a-button>
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出配置
            </a-button>
            <a-button type="primary" @click="handleAdd">
              <template #icon><PlusOutlined /></template>
              新增配置
            </a-button>
          </a-space>
        </div>
      </div>
    </div>

    <!-- 配置分类标签 -->
    <div class="config-tabs">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <a-tab-pane key="system" tab="系统配置">
          <template #tab>
            <SettingOutlined />
            系统配置
          </template>
        </a-tab-pane>
        <a-tab-pane key="business" tab="业务配置">
          <template #tab>
            <AppstoreOutlined />
            业务配置
          </template>
        </a-tab-pane>
        <a-tab-pane key="integration" tab="集成配置">
          <template #tab>
            <ApiOutlined />
            集成配置
          </template>
        </a-tab-pane>
        <a-tab-pane key="security" tab="安全配置">
          <template #tab>
            <SafetyOutlined />
            安全配置
          </template>
        </a-tab-pane>
      </a-tabs>
    </div>
    
    <!-- 配置内容区 -->
    <div class="content-wrapper">
      <a-card>
        <div class="filter-section">
          <a-row :gutter="16" align="middle">
            <a-col :span="6">
              <a-select
                v-model:value="filterForm.status"
                placeholder="配置状态"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部状态</a-select-option>
                <a-select-option value="enabled">启用</a-select-option>
                <a-select-option value="disabled">禁用</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="6">
              <a-select
                v-model:value="filterForm.type"
                placeholder="配置类型"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部类型</a-select-option>
                <a-select-option value="string">字符串</a-select-option>
                <a-select-option value="number">数字</a-select-option>
                <a-select-option value="boolean">布尔值</a-select-option>
                <a-select-option value="json">JSON</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="6">
              <a-select
                v-model:value="filterForm.environment"
                placeholder="环境"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部环境</a-select-option>
                <a-select-option value="development">开发环境</a-select-option>
                <a-select-option value="testing">测试环境</a-select-option>
                <a-select-option value="production">生产环境</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="6">
              <a-input-search
                v-model:value="searchText"
                placeholder="搜索配置键或描述"
                style="width: 100%"
                @search="handleSearch"
              />
            </a-col>
          </a-row>
        </div>

        <div class="table-toolbar">
          <div class="toolbar-left">
            <a-space>
              <a-button 
                @click="handleBatchDelete" 
                :disabled="!selectedRowKeys.length"
                danger
              >
                <template #icon><DeleteOutlined /></template>
                批量删除 ({{ selectedRowKeys.length }})
              </a-button>
              <a-button @click="handleBatchEnable" :disabled="!selectedRowKeys.length">
                <template #icon><CheckOutlined /></template>
                批量启用
              </a-button>
              <a-button @click="handleBatchDisable" :disabled="!selectedRowKeys.length">
                <template #icon><StopOutlined /></template>
                批量禁用
              </a-button>
              <a-button @click="handleBatchSync" :disabled="!selectedRowKeys.length">
                <template #icon><SyncOutlined /></template>
                批量同步
              </a-button>
            </a-space>
          </div>
          <div class="toolbar-right">
            <a-space>
              <a-tooltip title="刷新数据">
                <a-button @click="loadConfigList">
                  <template #icon><ReloadOutlined /></template>
                </a-button>
              </a-tooltip>
            </a-space>
          </div>
        </div>
        
        <a-table
          :columns="columns"
          :data-source="configList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          :scroll="{ x: 1400 }"
          row-key="id"
          size="middle"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'key'">
              <div class="config-key">
                <div class="key-name">{{ record.key }}</div>
                <div class="key-category">{{ record.category }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'value'">
              <div class="config-value">
                <template v-if="record.type === 'boolean'">
                  <a-switch 
                    :checked="record.value === 'true'" 
                    size="small"
                    @change="(checked) => handleQuickUpdate(record, checked.toString())"
                  />
                </template>
                <template v-else-if="record.type === 'json'">
                  <a-tooltip>
                    <template #title>
                      <pre>{{ JSON.stringify(JSON.parse(record.value), null, 2) }}</pre>
                    </template>
                    <a-tag color="purple">JSON对象</a-tag>
                  </a-tooltip>
                </template>
                <template v-else>
                  <a-tooltip>
                    <template #title>{{ record.value }}</template>
                    <div class="value-text">{{ record.value }}</div>
                  </a-tooltip>
                </template>
              </div>
            </template>
            <template v-else-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag 
                :color="record.status === 'enabled' ? 'success' : 'error'"
                class="status-tag"
              >
                <template #icon>
                  <CheckCircleOutlined v-if="record.status === 'enabled'" />
                  <StopOutlined v-else />
                </template>
                {{ record.status === 'enabled' ? '启用' : '禁用' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'environment'">
              <a-tag :color="getEnvironmentColor(record.environment)">
                {{ getEnvironmentText(record.environment) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'updatedAt'">
              <div class="time-info">
                <div>{{ formatDate(record.updatedAt) }}</div>
                <div class="time-ago">{{ getTimeAgo(record.updatedAt) }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="edit" @click="handleEdit(record)">
                      <EditOutlined />
                      编辑配置
                    </a-menu-item>
                    <a-menu-item key="history" @click="handleViewHistory(record)">
                      <HistoryOutlined />
                      变更历史
                    </a-menu-item>
                    <a-menu-item key="sync" @click="handleSync(record)">
                      <SyncOutlined />
                      同步配置
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item 
                      key="toggle-status" 
                      @click="handleToggleStatus(record)"
                    >
                      <template v-if="record.status === 'enabled'">
                        <StopOutlined />
                        禁用配置
                      </template>
                      <template v-else>
                        <CheckOutlined />
                        启用配置
                      </template>
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" @click="handleDelete(record)" class="danger-item">
                      <DeleteOutlined />
                      删除配置
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="text" size="small">
                  操作 <DownOutlined />
                </a-button>
              </a-dropdown>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 配置编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="800px"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="配置键" name="key">
              <a-input 
                v-model:value="formData.key" 
                placeholder="请输入配置键"
                :disabled="!!formData.id"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="配置分类" name="category">
              <a-select v-model:value="formData.category" placeholder="请选择分类">
                <a-select-option value="system">系统配置</a-select-option>
                <a-select-option value="business">业务配置</a-select-option>
                <a-select-option value="integration">集成配置</a-select-option>
                <a-select-option value="security">安全配置</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="数据类型" name="type">
              <a-select v-model:value="formData.type" placeholder="请选择类型">
                <a-select-option value="string">字符串</a-select-option>
                <a-select-option value="number">数字</a-select-option>
                <a-select-option value="boolean">布尔值</a-select-option>
                <a-select-option value="json">JSON</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="环境" name="environment">
              <a-select v-model:value="formData.environment" placeholder="请选择环境">
                <a-select-option value="development">开发环境</a-select-option>
                <a-select-option value="testing">测试环境</a-select-option>
                <a-select-option value="production">生产环境</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="配置值" name="value">
          <template v-if="formData.type === 'boolean'">
            <a-switch v-model:checked="booleanValue" />
          </template>
          <template v-else-if="formData.type === 'number'">
            <a-input-number 
              v-model:value="formData.value" 
              style="width: 100%"
              placeholder="请输入数字"
            />
          </template>
          <template v-else-if="formData.type === 'json'">
            <a-textarea 
              v-model:value="formData.value" 
              placeholder="请输入JSON格式数据"
              :rows="6"
            />
          </template>
          <template v-else>
            <a-input 
              v-model:value="formData.value" 
              placeholder="请输入配置值"
            />
          </template>
        </a-form-item>

        <a-form-item label="配置描述" name="description">
          <a-textarea 
            v-model:value="formData.description" 
            placeholder="请输入配置描述"
            :rows="3"
          />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-select v-model:value="formData.status" placeholder="请选择状态">
                <a-select-option value="enabled">启用</a-select-option>
                <a-select-option value="disabled">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="是否敏感" name="sensitive">
              <a-switch v-model:checked="formData.sensitive" />
              <span style="margin-left: 8px; color: #8c8c8c;">敏感配置将加密存储</span>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { 
  PlusOutlined, 
  DeleteOutlined, 
  ExportOutlined,
  ImportOutlined,
  SettingOutlined,
  AppstoreOutlined,
  ApiOutlined,
  SafetyOutlined,
  CheckOutlined,
  StopOutlined,
  SyncOutlined,
  ReloadOutlined,
  EditOutlined,
  HistoryOutlined,
  DownOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType } from 'ant-design-vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import { systemConfigApi, type SystemConfigListParams, type SystemConfigDetailResponse } from '@/api/system/systemConfig'

dayjs.extend(relativeTime)

interface ConfigRecord {
  id: number
  key: string
  value: string
  type: 'string' | 'number' | 'boolean' | 'json'
  category: 'system' | 'business' | 'integration' | 'security'
  description: string
  status: 'enabled' | 'disabled'
  environment: 'development' | 'testing' | 'production'
  sensitive: boolean
  createdAt: string
  updatedAt: string
  updatedBy: string
}

// 当前活跃标签
const activeTab = ref('system')

// 筛选表单
const filterForm = reactive({
  status: '',
  type: '',
  environment: ''
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '配置键',
    key: 'key',
    width: 250,
    fixed: 'left'
  },
  {
    title: '配置值',
    key: 'value',
    width: 200,
    ellipsis: true
  },
  {
    title: '类型',
    key: 'type',
    width: 100
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    width: 200,
    ellipsis: true
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '环境',
    key: 'environment',
    width: 120
  },
  {
    title: '更新时间',
    key: 'updatedAt',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
]

// 响应式数据
const loading = ref(false)
const searchText = ref('')
const selectedRowKeys = ref<(string | number)[]>([])
const configList = ref<ConfigRecord[]>([])
const modalVisible = ref(false)
const modalTitle = ref('')
const formRef = ref()

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 15,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格行选择配置
const rowSelection: any = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: (string | number)[]) => {
    selectedRowKeys.value = keys
  }
}

// 表单数据
const formData = reactive({
  id: null as number | null,
  key: '',
  value: '',
  type: 'string' as ConfigRecord['type'],
  category: 'system' as ConfigRecord['category'],
  description: '',
  status: 'enabled' as ConfigRecord['status'],
  environment: 'development' as ConfigRecord['environment'],
  sensitive: false
})

// 布尔值处理
const booleanValue = computed({
  get: () => formData.value === 'true',
  set: (val: boolean) => {
    formData.value = val.toString()
  }
})

// 表单验证规则
const formRules: any = {
  key: [
    { required: true, message: '请输入配置键', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9._-]*$/, message: '配置键格式不正确', trigger: 'blur' }
  ],
  value: [
    { required: true, message: '请输入配置值', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择配置分类', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ],
  environment: [
    { required: true, message: '请选择环境', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入配置描述', trigger: 'blur' }
  ]
}

// TODO: 替换为真实API数据
// 临时空数据，等待API实现
const mockConfigList: ConfigRecord[] = [
  // 模拟数据已移除，等待后端API实现
]

// 工具方法
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss')
}

const getTimeAgo = (dateStr: string) => {
  if (!dateStr) return ''
  return dayjs(dateStr).fromNow()
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    string: 'blue',
    number: 'green',
    boolean: 'orange',
    json: 'purple'
  }
  return colors[type] || 'default'
}

const getTypeText = (type: string) => {
  const texts: Record<string, string> = {
    string: '字符串',
    number: '数字',
    boolean: '布尔值',
    json: 'JSON'
  }
  return texts[type] || type
}

const getEnvironmentColor = (environment: string) => {
  const colors: Record<string, string> = {
    development: 'cyan',
    testing: 'orange',
    production: 'red'
  }
  return colors[environment] || 'default'
}

const getEnvironmentText = (environment: string) => {
  const texts: Record<string, string> = {
    development: '开发环境',
    testing: '测试环境',
    production: '生产环境'
  }
  return texts[environment] || environment
}

// 方法定义
const loadConfigList = async () => {
  loading.value = true
  try {
    const params: SystemConfigListParams = {
      page: pagination.current,
      size: pagination.pageSize,
      keyword: searchText.value || undefined,
      configGroup: activeTab.value || undefined,
      configType: filterForm.type ? parseInt(filterForm.type) : undefined
    }

    const response = await systemConfigApi.getSystemConfigList(params)
    configList.value = response.data.items.map(item => ({
      id: item.id,
      key: item.configKey,
      value: item.configValue,
      type: getTypeFromNumber(item.configType),
      category: item.configGroup as any,
      description: item.description || '',
      status: item.isSystem ? 'enabled' : 'disabled', // 简化状态映射
      environment: 'production', // 默认环境，实际应从API获取
      sensitive: item.isEncrypted,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      updatedBy: item.lastModifiedBy || 'system'
    }))
    pagination.total = response.data.total
  } catch (error) {
    message.error('加载配置列表失败')
    console.error('Load config list error:', error)
  } finally {
    loading.value = false
  }
}

// 辅助方法：将数字类型转换为字符串类型
const getTypeFromNumber = (typeNumber: number): 'string' | 'number' | 'boolean' | 'json' => {
  const typeMap: Record<number, 'string' | 'number' | 'boolean' | 'json'> = {
    1: 'string',
    2: 'number',
    3: 'boolean',
    4: 'json'
  }
  return typeMap[typeNumber] || 'string'
}

const handleTabChange = (key: string | number) => {
  activeTab.value = String(key)
  loadConfigList()
}

const handleQuickUpdate = (record: any, value: string) => {
  const configRecord = record as ConfigRecord
  message.success(`快速更新配置 ${configRecord.key} 成功`)
  loadConfigList()
}

const handleAdd = () => {
  modalTitle.value = '新增配置'
  Object.assign(formData, {
    id: null,
    key: '',
    value: '',
    type: 'string',
    category: activeTab.value,
    description: '',
    status: 'enabled',
    environment: 'development',
    sensitive: false
  })
  modalVisible.value = true
}

const handleEdit = (record: any) => {
  modalTitle.value = '编辑配置'
  Object.assign(formData, record as ConfigRecord)
  modalVisible.value = true
}

const handleDelete = (record: any) => {
  const configRecord = record as ConfigRecord
  message.success(`删除配置 ${configRecord.key} 成功`)
  loadConfigList()
}

const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的配置')
    return
  }
  message.success(`批量删除 ${selectedRowKeys.value.length} 个配置成功`)
  selectedRowKeys.value = []
  loadConfigList()
}

const handleBatchEnable = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要启用的配置')
    return
  }
  message.success(`批量启用 ${selectedRowKeys.value.length} 个配置成功`)
  selectedRowKeys.value = []
  loadConfigList()
}

const handleBatchDisable = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要禁用的配置')
    return
  }
  message.success(`批量禁用 ${selectedRowKeys.value.length} 个配置成功`)
  selectedRowKeys.value = []
  loadConfigList()
}

const handleBatchSync = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要同步的配置')
    return
  }
  message.success(`批量同步 ${selectedRowKeys.value.length} 个配置成功`)
  selectedRowKeys.value = []
  loadConfigList()
}

const handleToggleStatus = (record: any) => {
  const configRecord = record as ConfigRecord
  const newStatus = configRecord.status === 'enabled' ? 'disabled' : 'enabled'
  const action = newStatus === 'enabled' ? '启用' : '禁用'
  message.success(`${action}配置 ${configRecord.key} 成功`)
  loadConfigList()
}

const handleViewHistory = (record: any) => {
  const configRecord = record as ConfigRecord
  message.info(`查看配置 ${configRecord.key} 变更历史功能开发中...`)
}

const handleSync = (record: any) => {
  const configRecord = record as ConfigRecord
  message.success(`同步配置 ${configRecord.key} 成功`)
  loadConfigList()
}

const handleImport = () => {
  message.info('导入配置功能开发中...')
}

const handleExport = () => {
  message.success('导出配置成功')
}

const handleFilter = () => {
  loadConfigList()
}

const handleSearch = () => {
  loadConfigList()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadConfigList()
}

const handleModalOk = async () => {
  try {
    await formRef.value.validate()

    // JSON类型验证
    if (formData.type === 'json') {
      try {
        JSON.parse(formData.value)
      } catch (error) {
        message.error('JSON格式不正确')
        return
      }
    }

    const action = formData.id ? '更新' : '创建'
    message.success(`${action}配置成功`)
    modalVisible.value = false
    loadConfigList()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
}

// 监听表单类型变化
watch(() => formData.type, (newType) => {
  if (newType === 'boolean' && !['true', 'false'].includes(formData.value)) {
    formData.value = 'false'
  }
})

onMounted(() => {
  loadConfigList()
})
</script>

<style scoped lang="less">
.system-config-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;

  .page-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .header-left {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 28px;
          font-weight: 600;
          color: #262626;
        }

        .page-description {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  .config-tabs {
    margin-bottom: 24px;

    :deep(.ant-tabs-nav) {
      background: white;
      border-radius: 6px;
      padding: 0 16px;
      margin-bottom: 0;
    }
  }

  .content-wrapper {
    .filter-section {
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;
      margin-bottom: 16px;
    }

    .table-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .toolbar-left {
        .ant-btn {
          margin-right: 8px;
        }
      }
    }

    // 表格样式
    :deep(.ant-table) {
      .config-key {
        .key-name {
          font-weight: 500;
          color: #262626;
          margin-bottom: 4px;
          font-family: 'Monaco', 'Menlo', monospace;
        }

        .key-category {
          font-size: 12px;
          color: #8c8c8c;
        }
      }

      .config-value {
        .value-text {
          max-width: 180px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-family: 'Monaco', 'Menlo', monospace;
          font-size: 12px;
        }
      }

      .status-tag {
        display: inline-flex;
        align-items: center;
        gap: 4px;
      }

      .time-info {
        .time-ago {
          font-size: 12px;
          color: #8c8c8c;
          margin-top: 2px;
        }
      }

      .danger-item {
        color: #ff4d4f !important;

        &:hover {
          background-color: #fff2f0 !important;
        }
      }
    }
  }
}
</style>
