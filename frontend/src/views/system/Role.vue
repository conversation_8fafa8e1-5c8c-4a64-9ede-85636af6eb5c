<template>
  <div class="system-role-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">角色与权限配置</h1>
          <p class="page-description">管理系统角色定义，配置角色权限和访问控制策略</p>
        </div>
        <div class="header-right">
          <a-space>
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出配置
            </a-button>
            <a-button type="primary" @click="handleAdd">
              <template #icon><PlusOutlined /></template>
              新增角色
            </a-button>
          </a-space>
        </div>
      </div>
    </div>

    <!-- 统计卡片区 -->
    <div class="stats-cards">
      <a-row :gutter="24">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="总角色数"
              :value="roleStats.total"
              :value-style="{ color: '#1e88e5' }"
            >
              <template #prefix>
                <SafetyOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="启用角色"
              :value="roleStats.active"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="禁用角色"
              :value="roleStats.inactive"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <StopOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="关联用户"
              :value="roleStats.userCount"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>
    
    <!-- 筛选和操作区 -->
    <div class="content-wrapper">
      <a-card>
        <div class="filter-section">
          <a-row :gutter="16" align="middle">
            <a-col :span="6">
              <a-select
                v-model:value="filterForm.status"
                placeholder="选择状态"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部状态</a-select-option>
                <a-select-option value="active">启用</a-select-option>
                <a-select-option value="inactive">禁用</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="6">
              <a-select
                v-model:value="filterForm.type"
                placeholder="选择类型"
                allow-clear
                style="width: 100%"
                @change="handleFilter"
              >
                <a-select-option value="">全部类型</a-select-option>
                <a-select-option value="system">系统角色</a-select-option>
                <a-select-option value="custom">自定义角色</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="6">
              <a-range-picker
                v-model:value="filterForm.dateRange"
                :placeholder="['创建开始时间', '创建结束时间']"
                style="width: 100%"
                @change="handleFilter"
              />
            </a-col>
            <a-col :span="6">
              <a-input-search
                v-model:value="searchText"
                placeholder="搜索角色名称或描述"
                style="width: 100%"
                @search="handleSearch"
              />
            </a-col>
          </a-row>
        </div>

        <div class="table-toolbar">
          <div class="toolbar-left">
            <a-space>
              <a-button 
                @click="handleBatchDelete" 
                :disabled="!selectedRowKeys.length"
                danger
              >
                <template #icon><DeleteOutlined /></template>
                批量删除 ({{ selectedRowKeys.length }})
              </a-button>
              <a-button @click="handleBatchEnable" :disabled="!selectedRowKeys.length">
                <template #icon><CheckOutlined /></template>
                批量启用
              </a-button>
              <a-button @click="handleBatchDisable" :disabled="!selectedRowKeys.length">
                <template #icon><StopOutlined /></template>
                批量禁用
              </a-button>
            </a-space>
          </div>
          <div class="toolbar-right">
            <a-space>
              <a-tooltip title="刷新数据">
                <a-button @click="loadRoleList">
                  <template #icon><ReloadOutlined /></template>
                </a-button>
              </a-tooltip>
            </a-space>
          </div>
        </div>
        
        <a-table
          :columns="columns"
          :data-source="roleList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          :scroll="{ x: 1200 }"
          row-key="id"
          size="middle"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="role-info">
                <div class="role-name">{{ record.name }}</div>
                <div class="role-code">{{ record.code }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'type'">
              <a-tag :color="record.type === 'system' ? 'blue' : 'green'">
                {{ record.type === 'system' ? '系统角色' : '自定义角色' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag 
                :color="record.status === 'active' ? 'success' : 'error'"
                class="status-tag"
              >
                <template #icon>
                  <CheckCircleOutlined v-if="record.status === 'active'" />
                  <StopOutlined v-else />
                </template>
                {{ record.status === 'active' ? '启用' : '禁用' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'permissions'">
              <a-tooltip>
                <template #title>
                  <div>
                    <div v-for="permission in record.permissions" :key="permission">
                      {{ getPermissionText(permission) }}
                    </div>
                  </div>
                </template>
                <a-tag color="purple">
                  {{ record.permissions?.length || 0 }} 项权限
                </a-tag>
              </a-tooltip>
            </template>
            <template v-else-if="column.key === 'userCount'">
              <a-button type="link" @click="handleViewUsers(record)">
                {{ record.userCount }} 个用户
              </a-button>
            </template>
            <template v-else-if="column.key === 'createdAt'">
              <div class="time-info">
                <div>{{ formatDate(record.createdAt) }}</div>
                <div class="time-ago">{{ getTimeAgo(record.createdAt) }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="edit" @click="handleEdit(record)">
                      <EditOutlined />
                      编辑角色
                    </a-menu-item>
                    <a-menu-item key="permissions" @click="handlePermissions(record)">
                      <SafetyOutlined />
                      权限配置
                    </a-menu-item>
                    <a-menu-item key="copy" @click="handleCopy(record)">
                      <CopyOutlined />
                      复制角色
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item 
                      key="toggle-status" 
                      @click="handleToggleStatus(record)"
                    >
                      <template v-if="record.status === 'active'">
                        <StopOutlined />
                        禁用角色
                      </template>
                      <template v-else>
                        <CheckOutlined />
                        启用角色
                      </template>
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item 
                      key="delete" 
                      @click="handleDelete(record)" 
                      class="danger-item"
                      :disabled="record.type === 'system'"
                    >
                      <DeleteOutlined />
                      删除角色
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="text" size="small">
                  操作 <DownOutlined />
                </a-button>
              </a-dropdown>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 角色表单弹窗 -->
    <RoleFormModal
      v-model="roleFormVisible"
      :role-data="currentRole"
      @submit="handleRoleSubmit"
    />

    <!-- 权限配置弹窗 -->
    <RolePermissionModal
      v-model="permissionModalVisible"
      :role-info="currentRole"
      @submit="handlePermissionSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { 
  PlusOutlined, 
  DeleteOutlined, 
  ExportOutlined,
  SafetyOutlined,
  CheckCircleOutlined,
  StopOutlined,
  UserOutlined,
  CheckOutlined,
  ReloadOutlined,
  EditOutlined,
  CopyOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType } from 'ant-design-vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import RoleFormModal from '@/components/RoleFormModal.vue'
import RolePermissionModal from '@/components/RolePermissionModal.vue'
import { roleApi, type RoleListParams, type RoleDetailResponse } from '@/api/system/role'

dayjs.extend(relativeTime)

interface RoleRecord {
  id: number
  name: string
  code: string
  description: string
  type: 'system' | 'custom'
  status: 'active' | 'inactive'
  permissions: string[]
  userCount: number
  createdAt: string
  updatedAt: string
  createdBy: string
}

// 统计数据
const roleStats = reactive({
  total: 0,
  active: 0,
  inactive: 0,
  userCount: 0
})

// 筛选表单
const filterForm = reactive({
  status: '',
  type: '',
  dateRange: null as any
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '角色信息',
    key: 'name',
    width: 200,
    fixed: 'left'
  },
  {
    title: '类型',
    key: 'type',
    width: 100
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    width: 200,
    ellipsis: true
  },
  {
    title: '权限',
    key: 'permissions',
    width: 120
  },
  {
    title: '关联用户',
    key: 'userCount',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
]

// 响应式数据
const loading = ref(false)
const searchText = ref('')
const selectedRowKeys = ref<(string | number)[]>([])
const roleList = ref<RoleRecord[]>([])

// 弹窗状态
const roleFormVisible = ref(false)
const permissionModalVisible = ref(false)
const currentRole = ref<RoleRecord | null>(null)

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格行选择配置
const rowSelection: any = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: (string | number)[]) => {
    selectedRowKeys.value = keys
  }
}

// TODO: 替换为真实API数据
// 临时空数据，等待API实现
const mockRoleList: RoleRecord[] = [
  // 模拟数据已移除，等待后端API实现
]

// 工具方法
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss')
}

const getTimeAgo = (dateStr: string) => {
  if (!dateStr) return ''
  return dayjs(dateStr).fromNow()
}

const getPermissionText = (permission: string) => {
  const texts: Record<string, string> = {
    'system:user': '用户管理',
    'system:role': '角色管理',
    'system:permission': '权限管理',
    'system:log': '操作日志',
    'analytics:user': '用户分析',
    'analytics:behavior': '行为分析',
    'analytics:business': '商业分析',
    'product:config': '产品配置',
    'product:version': '版本管理'
  }
  return texts[permission] || permission
}

// 统计数据更新
const updateStats = () => {
  roleStats.total = roleList.value.length
  roleStats.active = roleList.value.filter(r => r.status === 'active').length
  roleStats.inactive = roleList.value.filter(r => r.status === 'inactive').length
  roleStats.userCount = roleList.value.reduce((sum, r) => sum + r.userCount, 0)
}

// 方法定义
const loadRoleList = async () => {
  loading.value = true
  try {
    const params: RoleListParams = {
      page: pagination.current,
      size: pagination.pageSize,
      keyword: searchText.value || undefined,
      status: filterForm.status || undefined
    }

    const response = await roleApi.getRoleList(params)
    console.log('Role API response:', response)

    // 兼容不同的响应格式
    const data = response.data || response
    const items = (data as any).items || (data as any).records || []
    const total = (data as any).total || (data as any).totalCount || 0

    roleList.value = items.map((item: any) => ({
      id: item.id,
      name: item.name,
      code: item.code,
      description: item.description || '',
      permissions: item.permissionNames || [],
      status: item.status,
      userCount: item.userCount || 0,
      type: 'custom', // 默认为自定义角色
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      createdBy: 'admin', // 实际应从API获取
      remark: item.remark
    }))
    pagination.total = total
    updateStats()
  } catch (error) {
    message.error('加载角色列表失败')
    console.error('Load role list error:', error)
    console.error('Error details:', {
      message: (error as any).message,
      response: (error as any).response,
      status: (error as any).response?.status,
      data: (error as any).response?.data
    })
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  currentRole.value = null
  roleFormVisible.value = true
}

const handleEdit = (record: any) => {
  currentRole.value = record as RoleRecord
  roleFormVisible.value = true
}

const handleDelete = async (record: any) => {
  const roleRecord = record as RoleRecord
  if (roleRecord.type === 'system') {
    message.warning('系统角色不能删除')
    return
  }
  try {
    await roleApi.deleteRole(roleRecord.id)
    message.success(`删除角色 ${roleRecord.name} 成功`)
    loadRoleList()
  } catch (error) {
    message.error('删除角色失败')
  }
}

const handleBatchDelete = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的角色')
    return
  }
  try {
    // 注意：这里需要逐个删除，因为后端可能没有批量删除接口
    for (const id of selectedRowKeys.value) {
      await roleApi.deleteRole(id as number)
    }
    message.success(`批量删除 ${selectedRowKeys.value.length} 个角色成功`)
    selectedRowKeys.value = []
    loadRoleList()
  } catch (error) {
    message.error('批量删除角色失败')
  }
}

const handleBatchEnable = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要启用的角色')
    return
  }
  try {
    for (const id of selectedRowKeys.value) {
      await roleApi.enableRole(id as number)
    }
    message.success(`批量启用 ${selectedRowKeys.value.length} 个角色成功`)
    selectedRowKeys.value = []
    loadRoleList()
  } catch (error) {
    message.error('批量启用角色失败')
  }
}

const handleBatchDisable = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要禁用的角色')
    return
  }
  try {
    for (const id of selectedRowKeys.value) {
      await roleApi.disableRole(id as number)
    }
    message.success(`批量禁用 ${selectedRowKeys.value.length} 个角色成功`)
    selectedRowKeys.value = []
    loadRoleList()
  } catch (error) {
    message.error('批量禁用角色失败')
  }
}

const handleToggleStatus = async (record: any) => {
  const roleRecord = record as RoleRecord
  const newStatus = roleRecord.status === 'active' ? 'inactive' : 'active'
  const action = newStatus === 'active' ? '启用' : '禁用'
  try {
    if (newStatus === 'active') {
      await roleApi.enableRole(roleRecord.id)
    } else {
      await roleApi.disableRole(roleRecord.id)
    }
    message.success(`${action}角色 ${roleRecord.name} 成功`)
    loadRoleList()
  } catch (error) {
    message.error(`${action}角色失败`)
  }
}

const handlePermissions = (record: any) => {
  currentRole.value = record as RoleRecord
  permissionModalVisible.value = true
}

const handleCopy = (record: any) => {
  const roleRecord = record as RoleRecord
  message.success(`复制角色 ${roleRecord.name} 成功`)
  loadRoleList()
}

const handleViewUsers = (record: any) => {
  const roleRecord = record as RoleRecord
  message.info(`查看角色 ${roleRecord.name} 关联用户功能开发中...`)
}

const handleExport = () => {
  message.success('导出角色配置成功')
}

const handleFilter = () => {
  loadRoleList()
}

const handleSearch = () => {
  loadRoleList()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadRoleList()
}

// 新增处理方法
const handleRoleSubmit = (roleData: any) => {
  // TODO: 替换为真实API调用
  // if (roleData.id) {
  //   await roleApi.updateRole(roleData.id, roleData)
  //   message.success('角色更新成功')
  // } else {
  //   await roleApi.createRole(roleData)
  //   message.success('角色创建成功')
  // }

  // 临时提示，等待API实现
  const action = roleData.id ? '更新' : '创建'
  message.success(`${action}角色成功（模拟）`)
  console.log('角色管理API待实现')
  loadRoleList()
}

const handlePermissionSubmit = (permissions: string[]) => {
  if (currentRole.value) {
    // TODO: 替换为真实API调用
    // await roleApi.updateRolePermissions(currentRole.value.id, permissions)

    // 临时提示，等待API实现
    message.success(`角色 ${currentRole.value.name} 权限配置成功（模拟）`)
    console.log('角色权限管理API待实现')
    loadRoleList()
  }
}

onMounted(() => {
  loadRoleList()
})
</script>

<style scoped lang="less">
.system-role-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;

  .page-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .header-left {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 28px;
          font-weight: 600;
          color: #262626;
        }

        .page-description {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  .stats-cards {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;

      :deep(.ant-statistic-title) {
        font-size: 14px;
        color: #8c8c8c;
      }

      :deep(.ant-statistic-content) {
        font-size: 24px;
        font-weight: 600;
      }
    }
  }

  .content-wrapper {
    .filter-section {
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;
      margin-bottom: 16px;
    }

    .table-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .toolbar-left {
        .ant-btn {
          margin-right: 8px;
        }
      }
    }

    // 表格样式
    :deep(.ant-table) {
      .role-info {
        .role-name {
          font-weight: 500;
          color: #262626;
          margin-bottom: 4px;
        }

        .role-code {
          font-size: 12px;
          color: #8c8c8c;
          font-family: 'Monaco', 'Menlo', monospace;
        }
      }

      .status-tag {
        display: inline-flex;
        align-items: center;
        gap: 4px;
      }

      .time-info {
        .time-ago {
          font-size: 12px;
          color: #8c8c8c;
          margin-top: 2px;
        }
      }

      .danger-item {
        color: #ff4d4f !important;

        &:hover {
          background-color: #fff2f0 !important;
        }
      }
    }
  }
}
</style>
