<template>
  <div class="user-growth-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <RiseOutlined />
          用户增长分析
        </h1>
        <p class="page-description">
          分析用户增长趋势、新增用户、留存情况等关键指标，洞察用户增长模式
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="header-actions">
        <a-space>
          <a-button
            type="primary"
            :icon="h(ReloadOutlined)"
            :loading="loading"
            @click="handleRefresh"
          >
            刷新数据
          </a-button>
          <a-button
            :icon="h(DownloadOutlined)"
            :loading="loading"
            @click="handleExport"
          >
            导出数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <a-card :bordered="false" size="small">
        <UserGrowthFilter
          v-model:params="filterParams"
          @change="handleFilterChange"
        />
      </a-card>
    </div>

    <div class="content-wrapper">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-card title="增长概览" class="overview-card" :loading="loading">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic
                  title="新增用户"
                  :value="coreMetricsList[0]?.value || 0"
                  suffix="人"
                  :value-style="{ color: '#3f8600' }"
                  :precision="0"
                />
                <div class="metric-trend" v-if="coreMetricsList[0]?.changeRate">
                  <span :class="['trend-text', coreMetricsList[0].changeRate > 0 ? 'trend-up' : 'trend-down']">
                    {{ coreMetricsList[0].changeRate > 0 ? '↗' : '↘' }} {{ Math.abs(coreMetricsList[0].changeRate).toFixed(1) }}%
                  </span>
                </div>
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="总用户数"
                  :value="coreMetricsList[1]?.value || 0"
                  suffix="人"
                  :value-style="{ color: '#1890ff' }"
                  :precision="0"
                />
                <div class="metric-trend" v-if="coreMetricsList[1]?.changeRate">
                  <span :class="['trend-text', coreMetricsList[1].changeRate > 0 ? 'trend-up' : 'trend-down']">
                    {{ coreMetricsList[1].changeRate > 0 ? '↗' : '↘' }} {{ Math.abs(coreMetricsList[1].changeRate).toFixed(1) }}%
                  </span>
                </div>
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="增长率"
                  :value="coreMetricsList[2]?.value || 0"
                  suffix="%"
                  :precision="2"
                  :value-style="{ color: '#722ed1' }"
                />
                <div class="metric-trend" v-if="coreMetricsList[2]?.changeRate">
                  <span :class="['trend-text', coreMetricsList[2].changeRate > 0 ? 'trend-up' : 'trend-down']">
                    {{ coreMetricsList[2].changeRate > 0 ? '↗' : '↘' }} {{ Math.abs(coreMetricsList[2].changeRate).toFixed(1) }}%
                  </span>
                </div>
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="7日留存率"
                  :value="coreMetricsList[3]?.value || 0"
                  suffix="%"
                  :precision="2"
                  :value-style="{ color: '#cf1322' }"
                />
                <div class="metric-trend" v-if="coreMetricsList[3]?.changeRate">
                  <span :class="['trend-text', coreMetricsList[3].changeRate > 0 ? 'trend-up' : 'trend-down']">
                    {{ coreMetricsList[3].changeRate > 0 ? '↗' : '↘' }} {{ Math.abs(coreMetricsList[3].changeRate).toFixed(1) }}%
                  </span>
                </div>
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-top: 16px;">
        <a-col :span="24">
          <a-card title="用户增长趋势" class="chart-card" :loading="loading">
            <template #extra>
              <a-radio-group v-model:value="trendChartType" size="small">
                <a-radio-button value="newUsers">新增用户</a-radio-button>
                <a-radio-button value="totalUsers">累计用户</a-radio-button>
                <a-radio-button value="both">对比显示</a-radio-button>
              </a-radio-group>
            </template>
            <div class="chart-container">
              <GrowthTrendChart
                :data="trendData"
                :chart-type="trendChartType"
                :loading="loading"
                style="height: 400px;"
              />
            </div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-top: 16px;">
        <a-col :span="12">
          <a-card title="用户留存分析" class="chart-card" :loading="loading">
            <template #extra>
              <a-select v-model:value="retentionType" size="small" style="width: 120px">
                <a-select-option value="retention">基础留存</a-select-option>
                <a-select-option value="cohort">队列留存</a-select-option>
              </a-select>
            </template>
            <div class="chart-container">
              <RetentionChart
                :data="retentionData"
                :chart-type="retentionType"
                :loading="loading"
                style="height: 300px;"
              />
            </div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="用户来源分析" class="chart-card" :loading="loading">
            <template #extra>
              <a-select v-model:value="sourceType" size="small" style="width: 120px">
                <a-select-option value="overview">总览</a-select-option>
                <a-select-option value="detailed">详细</a-select-option>
              </a-select>
            </template>
            <div class="chart-container">
              <UserSourceChart
                :data="sourceData"
                :chart-type="sourceType"
                :loading="loading"
                style="height: 300px;"
              />
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { message } from 'ant-design-vue'
import {
  RiseOutlined,
  ReloadOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import { useUserGrowthStore } from '@/stores/user/growth'
import type { UserGrowthAnalysisRequest } from '@/api/user/growth'

// 组件导入
import UserGrowthFilter from './components/UserGrowthFilter.vue'
import GrowthTrendChart from './components/GrowthTrendChart.vue'
import RetentionChart from './components/RetentionChart.vue'
import UserSourceChart from './components/UserSourceChart.vue'

/**
 * 用户增长分析页面
 *
 * <AUTHOR>
 * @since 2025-07-01
 */

// Store
const userGrowthStore = useUserGrowthStore()

// 响应式数据
const loading = computed(() => userGrowthStore.loading)
const filterParams = ref<UserGrowthAnalysisRequest>({
  startDate: '',
  endDate: '',
  granularity: 'DAY',
  productLineIds: [],
  includeComparison: true,
  includeDetails: false
})

const trendChartType = ref<'newUsers' | 'totalUsers' | 'both'>('both')
const retentionType = ref<'retention' | 'cohort'>('retention')
const sourceType = ref<'overview' | 'detailed'>('overview')

// 计算属性
const coreMetricsList = computed(() => {
  const metrics = userGrowthStore.coreMetrics
  if (!metrics) return []

  return [
    {
      name: '新增用户',
      value: metrics.newUsers?.value || 0,
      changeRate: metrics.newUsers?.changeRate || 0,
      unit: '人'
    },
    {
      name: '总用户数',
      value: metrics.totalUsers?.value || 0,
      changeRate: metrics.totalUsers?.changeRate || 0,
      unit: '人'
    },
    {
      name: '增长率',
      value: metrics.growthRate?.value || 0,
      changeRate: metrics.growthRate?.changeRate || 0,
      unit: '%'
    },
    {
      name: '7日留存率',
      value: metrics.retention7d?.value || 0,
      changeRate: metrics.retention7d?.changeRate || 0,
      unit: '%'
    }
  ]
})

const trendData = computed(() => {
  return userGrowthStore.trendData?.trendData || {}
})

const retentionData = computed(() => {
  if (retentionType.value === 'cohort') {
    return userGrowthStore.cohortData?.retentionData || {}
  }
  return userGrowthStore.retentionData?.retentionData || {}
})

const sourceData = computed(() => {
  return userGrowthStore.sourceData?.userSourceData || {}
})

// 方法
const handleFilterChange = async () => {
  try {
    userGrowthStore.setQueryParams(filterParams.value)
    await refreshData()
  } catch (error) {
    console.error('筛选器变更失败:', error)
  }
}

const handleRefresh = async () => {
  try {
    await refreshData()
    message.success('数据刷新成功')
  } catch (error) {
    console.error('刷新数据失败:', error)
  }
}

const handleExport = async () => {
  try {
    // TODO: 实现数据导出功能
    message.info('导出功能开发中...')
  } catch (error) {
    console.error('导出数据失败:', error)
  }
}

const refreshData = async () => {
  await Promise.all([
    userGrowthStore.fetchOverview(),
    userGrowthStore.fetchGrowthTrends(),
    userGrowthStore.fetchRetentionAnalysis(),
    userGrowthStore.fetchUserSourceAnalysis(),
    userGrowthStore.fetchCohortRetentionAnalysis()
  ])
}

// 初始化数据
const initializeData = () => {
  const now = new Date()
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

  filterParams.value = {
    startDate: thirtyDaysAgo.toISOString().split('T')[0],
    endDate: now.toISOString().split('T')[0],
    granularity: 'DAY',
    productLineIds: [],
    includeComparison: true,
    includeDetails: false
  }

  userGrowthStore.setQueryParams(filterParams.value)
}

onMounted(async () => {
  initializeData()
  await refreshData()
})


</script>

<style scoped lang="less">
.user-growth-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .header-content {
    flex: 1;

    .page-title {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #262626;
      display: flex;
      align-items: center;
      gap: 8px;

      .anticon {
        color: #1890ff;
      }
    }

    .page-description {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .header-actions {
    flex-shrink: 0;
  }
}

.filter-section {
  margin-bottom: 16px;
}

.content-wrapper {
  .overview-card {
    .ant-statistic {
      text-align: center;

      .ant-statistic-content {
        font-size: 24px;
        font-weight: 600;
      }
    }

    .metric-trend {
      margin-top: 8px;
      text-align: center;

      .trend-text {
        font-size: 12px;
        font-weight: 500;

        &.trend-up {
          color: #52c41a;
        }

        &.trend-down {
          color: #ff4d4f;
        }
      }
    }
  }

  .chart-card {
    .chart-container {
      padding: 16px 0;
      min-height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .ant-card-extra {
      .ant-radio-group,
      .ant-select {
        border-radius: 4px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .user-growth-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;

    .header-actions {
      width: 100%;

      .ant-space {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }

  .content-wrapper {
    .ant-col {
      margin-bottom: 16px;
    }
  }
}
</style>
