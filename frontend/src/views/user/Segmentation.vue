<template>
  <div class="user-segmentation">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <GroupOutlined />
          用户分群
        </h1>
        <p class="page-description">
          基于用户行为、属性等多维度特征进行智能分群，实现精准营销和个性化运营
        </p>
      </div>
      
      <!-- 操作按钮 -->
      <div class="header-actions">
        <a-space>
          <a-button 
            type="primary" 
            :icon="h(PlusOutlined)"
            @click="handleCreateSegment"
          >
            创建分群
          </a-button>
          <a-button 
            :icon="h(ReloadOutlined)" 
            :loading="loading"
            @click="handleRefresh"
          >
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <a-card :bordered="false" size="small">
        <div class="filter-content">
          <a-row :gutter="16" align="middle">
            <a-col :span="6">
              <div class="filter-item">
                <label>分群状态：</label>
                <a-select
                  v-model:value="statusFilter"
                  placeholder="选择状态"
                  style="width: 100%"
                  @change="handleStatusChange"
                >
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="active">活跃</a-select-option>
                  <a-select-option value="inactive">非活跃</a-select-option>
                  <a-select-option value="computing">计算中</a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="filter-item">
                <label>分群类型：</label>
                <a-select
                  v-model:value="typeFilter"
                  placeholder="选择类型"
                  style="width: 100%"
                  @change="handleTypeChange"
                >
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="behavior">行为分群</a-select-option>
                  <a-select-option value="attribute">属性分群</a-select-option>
                  <a-select-option value="rfm">RFM分群</a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="filter-item">
                <label>创建时间：</label>
                <a-range-picker
                  v-model:value="dateRange"
                  @change="handleDateChange"
                  style="width: 100%"
                />
              </div>
            </a-col>
            <a-col :span="6">
              <div class="filter-item">
                <a-input-search
                  v-model:value="searchKeyword"
                  placeholder="搜索分群名称"
                  @search="handleSearch"
                  style="width: 100%"
                />
              </div>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </div>

    <!-- 分群概览统计 -->
    <div class="overview-section">
      <a-row :gutter="16">
        <a-col :xs="24" :sm="6">
          <a-card :bordered="false" class="overview-card">
            <a-statistic
              title="总分群数"
              :value="overview.totalSegments"
              :loading="loading"
              suffix="个"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <GroupOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="6">
          <a-card :bordered="false" class="overview-card">
            <a-statistic
              title="活跃分群"
              :value="overview.activeSegments"
              :loading="loading"
              suffix="个"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <FireOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="6">
          <a-card :bordered="false" class="overview-card">
            <a-statistic
              title="覆盖用户"
              :value="overview.totalUsers"
              :loading="loading"
              suffix="人"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="6">
          <a-card :bordered="false" class="overview-card">
            <a-statistic
              title="今日更新"
              :value="overview.todayUpdated"
              :loading="loading"
              suffix="个"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <SyncOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 分群列表 -->
    <div class="segments-section">
      <a-card title="分群列表" :bordered="false">
        <template #extra>
          <a-space>
            <a-button size="small" @click="handleBatchOperation">
              <SettingOutlined />
              批量操作
            </a-button>
            <a-button size="small" @click="handleExport">
              <DownloadOutlined />
              导出
            </a-button>
          </a-space>
        </template>
        
        <a-table
          :columns="tableColumns"
          :data-source="segmentsList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="segment-name">
                <a @click="handleViewSegment(record)">{{ record.name }}</a>
                <div class="segment-description">{{ record.description }}</div>
              </div>
            </template>
            
            <template v-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeName(record.type) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'status'">
              <a-badge 
                :status="getStatusBadge(record.status)" 
                :text="getStatusName(record.status)" 
              />
            </template>
            
            <template v-if="column.key === 'userCount'">
              <span class="user-count">{{ record.userCount.toLocaleString() }}</span>
            </template>
            
            <template v-if="column.key === 'updateTime'">
              <span>{{ record.updateTime }}</span>
            </template>
            
            <template v-if="column.key === 'actions'">
              <a-space>
                <a-button type="link" size="small" @click="handleViewSegment(record)">
                  查看
                </a-button>
                <a-button type="link" size="small" @click="handleEditSegment(record)">
                  编辑
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleMenuAction(String(key), record)">
                      <a-menu-item key="refresh">刷新数据</a-menu-item>
                      <a-menu-item key="export">导出用户</a-menu-item>
                      <a-menu-item key="duplicate">复制分群</a-menu-item>
                      <a-menu-divider />
                      <a-menu-item key="delete" class="danger-item">删除</a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多 <DownOutlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 创建分群弹窗 -->
    <a-modal
      v-model:open="createModalVisible"
      title="创建用户分群"
      :width="800"
      @ok="handleCreateConfirm"
      @cancel="handleCreateCancel"
    >
      <div class="create-segment-form">
        <a-form :model="createForm" layout="vertical">
          <a-form-item label="分群名称" required>
            <a-input v-model:value="createForm.name" placeholder="请输入分群名称" />
          </a-form-item>
          
          <a-form-item label="分群描述">
            <a-textarea 
              v-model:value="createForm.description" 
              placeholder="请输入分群描述"
              :rows="3"
            />
          </a-form-item>
          
          <a-form-item label="分群类型" required>
            <a-radio-group v-model:value="createForm.type">
              <a-radio value="behavior">行为分群</a-radio>
              <a-radio value="attribute">属性分群</a-radio>
              <a-radio value="rfm">RFM分群</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="筛选条件">
            <div class="conditions-builder">
              <a-button type="dashed" @click="handleAddCondition">
                <PlusOutlined />
                添加条件
              </a-button>
            </div>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue'
import { message } from 'ant-design-vue'
import type { TableRowSelection } from 'ant-design-vue/es/table/interface'
import { 
  GroupOutlined,
  PlusOutlined,
  ReloadOutlined,
  DownloadOutlined,
  SettingOutlined,
  FireOutlined,
  UserOutlined,
  SyncOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import { http } from '@/utils/request'

/**
 * 用户分群页面
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */

// 响应式数据
const loading = ref(false)
const statusFilter = ref('')
const typeFilter = ref('')
const dateRange = ref<[Dayjs, Dayjs]>([dayjs().subtract(30, 'day'), dayjs()])
const searchKeyword = ref('')
const createModalVisible = ref(false)

// 概览数据
const overview = reactive({
  totalSegments: 24,
  activeSegments: 18,
  totalUsers: 850000,
  todayUpdated: 6
})

// 创建表单
const createForm = reactive({
  name: '',
  description: '',
  type: 'behavior'
})

// 表格配置
const tableColumns = [
  { title: '分群名称', dataIndex: 'name', key: 'name', width: 200 },
  { title: '类型', dataIndex: 'type', key: 'type', width: 100 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
  { title: '用户数量', dataIndex: 'userCount', key: 'userCount', width: 120 },
  { title: '更新时间', dataIndex: 'updateTime', key: 'updateTime', width: 150 },
  { title: '操作', key: 'actions', width: 200 }
]

// TODO: 替换为真实API数据
const segmentsList = ref([
  // 模拟数据已移除，等待后端API实现
])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 24,
  showSizeChanger: true,
  showQuickJumper: true
})

const selectedRowKeys = ref<(string | number)[]>([])

const rowSelection: TableRowSelection<any> = {
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: (string | number)[], _selectedRows: any[]) => {
    selectedRowKeys.value = keys
  }
}

// 工具方法
const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    behavior: 'blue',
    attribute: 'green',
    rfm: 'purple'
  }
  return colors[type] || 'default'
}

const getTypeName = (type: string) => {
  const names: Record<string, string> = {
    behavior: '行为分群',
    attribute: '属性分群',
    rfm: 'RFM分群'
  }
  return names[type] || type
}

const getStatusBadge = (status: string): "default" | "error" | "success" | "warning" | "processing" => {
  const badges: Record<string, "default" | "error" | "success" | "warning" | "processing"> = {
    active: 'success',
    inactive: 'default',
    computing: 'processing'
  }
  return badges[status] || 'default'
}

const getStatusName = (status: string) => {
  const names: Record<string, string> = {
    active: '活跃',
    inactive: '非活跃',
    computing: '计算中'
  }
  return names[status] || status
}

// 方法
const handleCreateSegment = () => {
  createModalVisible.value = true
}

const handleRefresh = async () => {
  loading.value = true
  try {
    // 调用粉丝分析API获取分群相关数据（基于粉丝数据模拟分群）
    const response = await http.get('/private-domain/fan/public/realtime-stats', {
      params: {
        dataScope: 'PUBLIC'
      }
    })

    if (response.data.success && response.data.data) {
      const data = response.data.data

      // 更新概览数据（基于粉丝数据模拟分群数据）
      overview.totalSegments = 24
      overview.activeSegments = 18
      overview.totalUsers = data.totalFans * 85 || 850000 // 假设分群覆盖粉丝的85倍用户
      overview.todayUpdated = 6

      // 模拟分群列表数据
      segmentsList.value = [
        {
          id: 1,
          name: '高价值用户群',
          type: 'value',
          status: 'active',
          userCount: data.highValueFans || 120,
          updateTime: '2024-01-15 14:30:25'
        },
        {
          id: 2,
          name: '活跃用户群',
          type: 'behavior',
          status: 'active',
          userCount: data.activeFans || 200,
          updateTime: '2024-01-15 13:45:12'
        },
        {
          id: 3,
          name: '新用户群',
          type: 'lifecycle',
          status: 'active',
          userCount: data.todayNewFans || 50,
          updateTime: '2024-01-15 12:20:08'
        }
      ]
      pagination.total = segmentsList.value.length

      message.success('数据刷新成功')
    } else {
      message.warning('暂无分群数据')
    }
  } catch (error) {
    console.error('刷新数据失败:', error)
    message.error('刷新数据失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

const handleStatusChange = () => {
  // 处理状态筛选变化
}

const handleTypeChange = () => {
  // 处理类型筛选变化
}

const handleDateChange = () => {
  // 处理日期范围变化
}

const handleSearch = () => {
  // 处理搜索
}

const handleBatchOperation = () => {
  message.info('批量操作功能开发中...')
}

const handleExport = () => {
  message.info('导出功能开发中...')
}

const handleViewSegment = (record: any) => {
  message.info(`查看分群: ${record.name}`)
}

const handleEditSegment = (record: any) => {
  message.info(`编辑分群: ${record.name}`)
}

const handleMenuAction = (key: string, record: any) => {
  switch (key) {
    case 'refresh':
      message.info(`刷新分群: ${record.name}`)
      break
    case 'export':
      message.info(`导出用户: ${record.name}`)
      break
    case 'duplicate':
      message.info(`复制分群: ${record.name}`)
      break
    case 'delete':
      message.warning(`删除分群: ${record.name}`)
      break
  }
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const handleCreateConfirm = () => {
  if (!createForm.name) {
    message.error('请输入分群名称')
    return
  }
  
  message.success('分群创建成功')
  createModalVisible.value = false
  
  // 重置表单
  Object.assign(createForm, {
    name: '',
    description: '',
    type: 'behavior'
  })
}

const handleCreateCancel = () => {
  createModalVisible.value = false
}

const handleAddCondition = () => {
  message.info('添加筛选条件功能开发中...')
}

// 初始化
onMounted(() => {
  // 加载初始数据
})
</script>

<style scoped lang="less">
.user-segmentation {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

    .header-content {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #262626;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .page-description {
        font-size: 14px;
        color: #8c8c8c;
        margin: 0;
        line-height: 1.5;
      }
    }
  }

  .filter-section {
    margin-bottom: 24px;

    .filter-content {
      .filter-item {
        label {
          display: block;
          margin-bottom: 4px;
          font-weight: 500;
          color: #262626;
        }
      }
    }
  }

  .overview-section {
    margin-bottom: 24px;

    .overview-card {
      text-align: center;
    }
  }

  .segments-section {
    .segment-name {
      a {
        font-weight: 500;
        color: #1890ff;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      .segment-description {
        font-size: 12px;
        color: #8c8c8c;
        margin-top: 4px;
      }
    }

    .user-count {
      font-weight: 500;
      color: #1890ff;
    }

    .danger-item {
      color: #ff4d4f !important;
    }
  }

  .create-segment-form {
    .conditions-builder {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      padding: 20px;
      text-align: center;
      background: #fafafa;
    }
  }
}
</style>
