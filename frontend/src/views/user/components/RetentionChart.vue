<template>
  <div class="retention-chart">
    <div v-if="loading" class="chart-loading">
      <a-spin size="large" />
      <p>数据加载中...</p>
    </div>
    <div v-else-if="!hasData" class="chart-empty">
      <a-empty description="暂无数据" />
    </div>
    <v-chart v-else :option="chartOption" :style="{ height: '100%', width: '100%' }" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import VChart from 'vue-echarts'
import type { EChartsOption } from 'echarts'
import type { RetentionData } from '@/api/user/growth'

/**
 * 用户留存分析图表组件
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */

interface Props {
  data: Record<string, RetentionData>
  chartType: 'retention' | 'cohort'
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 计算属性
const hasData = computed(() => {
  return Object.keys(props.data).length > 0
})

const retentionData = computed(() => {
  if (props.chartType === 'cohort') {
    return props.data.cohortRetention || props.data.detailedRetention
  }
  return props.data.retention || props.data.detailedRetention
})

const chartOption = computed<EChartsOption>(() => {
  if (!hasData.value || !retentionData.value) {
    return {}
  }

  const periods = retentionData.value.periods || []
  const categories = periods.map(p => p.period)
  const retentionRates = periods.map(p => p.retentionRate)
  const userCounts = periods.map(p => p.userCount)

  const baseOption: EChartsOption = {
    title: {
      text: getChartTitle(),
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        if (Array.isArray(params) && params.length > 0) {
          const param = params[0]
          const index = param.dataIndex
          const period = periods[index]
          
          return `
            <div style="font-weight: bold; margin-bottom: 8px;">${param.axisValue}留存</div>
            <div style="margin-bottom: 4px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              留存率: <strong>${period.retentionRate.toFixed(2)}%</strong>
            </div>
            <div style="margin-bottom: 4px;">
              留存用户: <strong>${period.userCount.toLocaleString()}人</strong>
            </div>
            <div>
              基础用户: <strong>${period.baseUserCount.toLocaleString()}人</strong>
            </div>
          `
        }
        return ''
      }
    },
    grid: {
      top: 60,
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLabel: {
        fontSize: 12
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '留存率 (%)',
        position: 'left',
        axisLabel: {
          formatter: '{value}%'
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#e8e8e8'
          }
        }
      },
      {
        type: 'value',
        name: '用户数',
        position: 'right',
        axisLabel: {
          formatter: (value: number) => {
            if (value >= 10000) {
              return (value / 10000).toFixed(1) + 'w'
            }
            return value.toString()
          }
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '留存率',
        type: 'line',
        yAxisIndex: 0,
        data: retentionRates,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 4,
          color: '#1890ff'
        },
        itemStyle: {
          color: '#1890ff',
          borderWidth: 2,
          borderColor: '#fff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
              { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
            ]
          }
        },
        markLine: {
          silent: true,
          lineStyle: {
            color: '#ff4d4f',
            type: 'dashed'
          },
          data: [
            {
              yAxis: getAverageRetention(),
              label: {
                formatter: '平均留存率: {c}%',
                position: 'insideEndTop'
              }
            }
          ]
        },
        animationDelay: (idx: number) => idx * 100
      },
      {
        name: '留存用户数',
        type: 'bar',
        yAxisIndex: 1,
        data: userCounts,
        itemStyle: {
          color: 'rgba(82, 196, 26, 0.6)',
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: 'rgba(82, 196, 26, 0.8)'
          }
        },
        animationDelay: (idx: number) => idx * 80
      }
    ],
    legend: {
      data: ['留存率', '留存用户数'],
      top: 30,
      itemGap: 20
    },
    color: ['#1890ff', '#52c41a']
  }

  return baseOption
})

// 获取图表标题
const getChartTitle = () => {
  switch (props.chartType) {
    case 'cohort':
      return '队列留存分析'
    case 'retention':
      return '用户留存分析'
    default:
      return '用户留存分析'
  }
}

// 计算平均留存率
const getAverageRetention = () => {
  if (!retentionData.value?.periods) {
    return 0
  }
  
  const rates = retentionData.value.periods.map(p => p.retentionRate)
  const average = rates.reduce((sum, rate) => sum + rate, 0) / rates.length
  return Math.round(average * 100) / 100
}
</script>

<style scoped lang="less">
.retention-chart {
  width: 100%;
  height: 100%;
  position: relative;
  
  .chart-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #8c8c8c;
    
    p {
      margin-top: 16px;
      font-size: 14px;
    }
  }
  
  .chart-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
}
</style>
