<template>
  <div class="user-growth-filter">
    <a-form layout="inline" :model="formData" @finish="handleSubmit">
      <a-form-item label="时间范围" name="dateRange">
        <a-range-picker
          v-model:value="formData.dateRange"
          :placeholder="['开始日期', '结束日期']"
          format="YYYY-MM-DD"
          :disabled-date="disabledDate"
          @change="handleDateRangeChange"
        />
      </a-form-item>

      <a-form-item label="时间粒度" name="granularity">
        <a-select
          v-model:value="formData.granularity"
          style="width: 120px"
          @change="handleGranularityChange"
        >
          <a-select-option value="DAY">按天</a-select-option>
          <a-select-option value="WEEK">按周</a-select-option>
          <a-select-option value="MONTH">按月</a-select-option>
          <a-select-option value="QUARTER">按季度</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="产品线" name="productLineIds">
        <a-select
          v-model:value="formData.productLineIds"
          mode="multiple"
          style="min-width: 200px"
          placeholder="选择产品线"
          :options="productLineOptions"
          @change="handleProductLineChange"
        />
      </a-form-item>

      <a-form-item>
        <a-space>
          <a-button type="primary" html-type="submit" :loading="loading">
            查询
          </a-button>
          <a-button @click="handleReset">
            重置
          </a-button>
          <a-dropdown>
            <template #overlay>
              <a-menu @click="handleQuickSelect">
                <a-menu-item key="last7days">最近7天</a-menu-item>
                <a-menu-item key="last30days">最近30天</a-menu-item>
                <a-menu-item key="currentMonth">本月</a-menu-item>
                <a-menu-item key="lastMonth">上月</a-menu-item>
                <a-menu-item key="currentQuarter">本季度</a-menu-item>
              </a-menu>
            </template>
            <a-button>
              快速选择
              <DownOutlined />
            </a-button>
          </a-dropdown>
        </a-space>
      </a-form-item>

      <a-form-item>
        <a-space>
          <a-checkbox v-model:checked="formData.includeComparison">
            包含对比
          </a-checkbox>
          <a-checkbox v-model:checked="formData.includeDetails">
            详细数据
          </a-checkbox>
        </a-space>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { DownOutlined } from '@ant-design/icons-vue'
import type { MenuInfo } from 'ant-design-vue/es/menu/src/interface'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import type { UserGrowthAnalysisRequest, TimeGranularity } from '@/api/user/growth'

/**
 * 用户增长分析筛选器组件
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */

interface Props {
  params: UserGrowthAnalysisRequest
  loading?: boolean
}

interface Emits {
  (e: 'update:params', params: UserGrowthAnalysisRequest): void
  (e: 'change', params: UserGrowthAnalysisRequest): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 表单数据
const formData = reactive({
  dateRange: [dayjs().subtract(29, 'day'), dayjs()] as [Dayjs, Dayjs],
  granularity: 'DAY' as TimeGranularity,
  productLineIds: [] as number[],
  includeComparison: true,
  includeDetails: false
})

// 产品线选项
const productLineOptions = ref([
  { label: '福昕阅读器GA版', value: 1 },
  { label: '福昕阅读器PLUS版', value: 2 },
  { label: 'PDF编辑器个人版', value: 3 },
  { label: 'PDF编辑器专业版', value: 4 },
  { label: 'PDF365在线服务', value: 5 }
])

// 禁用日期
const disabledDate = (current: Dayjs) => {
  // 不能选择未来日期
  if (current && current > dayjs().endOf('day')) {
    return true
  }
  // 不能选择1年前的日期
  if (current && current < dayjs().subtract(1, 'year')) {
    return true
  }
  return false
}

// 处理日期范围变化
const handleDateRangeChange = (value: [string, string] | [Dayjs, Dayjs], dateString: [string, string]) => {
  if (value && value.length === 2) {
    // 如果是字符串数组，转换为Dayjs对象
    if (typeof value[0] === 'string') {
      formData.dateRange = [dayjs(value[0]), dayjs(value[1])]
    } else {
      formData.dateRange = value as [Dayjs, Dayjs]
    }
    emitChange()
  }
}

// 处理时间粒度变化
const handleGranularityChange = () => {
  emitChange()
}

// 处理产品线变化
const handleProductLineChange = () => {
  emitChange()
}

// 处理表单提交
const handleSubmit = () => {
  emitChange()
}

// 处理重置
const handleReset = () => {
  const now = dayjs()
  const thirtyDaysAgo = now.subtract(30, 'day')
  
  formData.dateRange = [thirtyDaysAgo, now]
  formData.granularity = 'DAY'
  formData.productLineIds = []
  formData.includeComparison = true
  formData.includeDetails = false
  
  emitChange()
}

// 处理快速选择
const handleQuickSelect = (info: MenuInfo) => {
  const key = String(info.key)
  const now = dayjs()
  
  switch (key) {
    case 'last7days':
      formData.dateRange = [now.subtract(6, 'day'), now]
      formData.granularity = 'DAY'
      break
    case 'last30days':
      formData.dateRange = [now.subtract(29, 'day'), now]
      formData.granularity = 'DAY'
      break
    case 'currentMonth':
      formData.dateRange = [now.startOf('month'), now]
      formData.granularity = 'DAY'
      break
    case 'lastMonth':
      const lastMonth = now.subtract(1, 'month')
      formData.dateRange = [lastMonth.startOf('month'), lastMonth.endOf('month')]
      formData.granularity = 'DAY'
      break
    case 'currentQuarter':
      // 计算当前季度的开始日期
      const currentQuarter = Math.floor((now.month()) / 3)
      const quarterStart = now.month(currentQuarter * 3).startOf('month')
      formData.dateRange = [quarterStart, now]
      formData.granularity = 'WEEK'
      break
  }
  
  emitChange()
}

// 发出变化事件
const emitChange = () => {
  if (formData.dateRange.length !== 2) {
    return
  }
  
  const params: UserGrowthAnalysisRequest = {
    startDate: formData.dateRange[0].format('YYYY-MM-DD'),
    endDate: formData.dateRange[1].format('YYYY-MM-DD'),
    granularity: formData.granularity,
    productLineIds: formData.productLineIds,
    includeComparison: formData.includeComparison,
    includeDetails: formData.includeDetails
  }
  
  emit('update:params', params)
  emit('change', params)
}

// 初始化表单数据
const initFormData = () => {
  if (props.params.startDate && props.params.endDate) {
    formData.dateRange = [
      dayjs(props.params.startDate),
      dayjs(props.params.endDate)
    ]
  } else {
    const now = dayjs()
    formData.dateRange = [now.subtract(29, 'day'), now]
  }
  
  formData.granularity = props.params.granularity || 'DAY'
  formData.productLineIds = props.params.productLineIds || []
  formData.includeComparison = props.params.includeComparison ?? true
  formData.includeDetails = props.params.includeDetails ?? false
}

// 监听props变化
watch(() => props.params, () => {
  initFormData()
}, { deep: true })

onMounted(() => {
  initFormData()
})
</script>

<style scoped lang="less">
.user-growth-filter {
  .ant-form {
    .ant-form-item {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .ant-form-item-label {
      font-weight: 500;
    }
  }
  
  .ant-range-picker {
    width: 240px;
  }
  
  .ant-select {
    min-width: 100px;
  }
}

@media (max-width: 768px) {
  .user-growth-filter {
    .ant-form {
      .ant-form-item {
        width: 100%;
        margin-bottom: 12px;
      }
      
      .ant-range-picker {
        width: 100%;
      }
      
      .ant-select {
        width: 100%;
      }
    }
  }
}
</style>
