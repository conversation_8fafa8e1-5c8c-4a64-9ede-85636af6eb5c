<template>
  <div class="user-source-chart">
    <div v-if="loading" class="chart-loading">
      <a-spin size="large" />
      <p>数据加载中...</p>
    </div>
    <div v-else-if="!hasData" class="chart-empty">
      <a-empty description="暂无数据" />
    </div>
    <v-chart v-else :option="chartOption" :style="{ height: '100%', width: '100%' }" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import VChart from 'vue-echarts'
import type { EChartsOption } from 'echarts'
import type { UserSourceData } from '@/api/user/growth'

/**
 * 用户来源分析图表组件
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */

interface Props {
  data: Record<string, UserSourceData>
  chartType: 'overview' | 'detailed'
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 计算属性
const hasData = computed(() => {
  return Object.keys(props.data).length > 0
})

const sourceData = computed(() => {
  if (props.chartType === 'detailed') {
    return props.data.detailedUserSource || props.data.userSource
  }
  return props.data.userSource || props.data.detailedUserSource
})

const chartOption = computed<EChartsOption>(() => {
  if (!hasData.value || !sourceData.value) {
    return {}
  }

  const sources = sourceData.value.sources || []
  const pieData = sources.map(source => ({
    name: source.sourceName,
    value: source.userCount,
    percentage: source.percentage,
    sourceType: source.sourceType
  }))

  // 颜色配置
  const colors = [
    '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
    '#fa8c16', '#13c2c2', '#eb2f96', '#a0d911', '#2f54eb'
  ]

  const baseOption: EChartsOption = {
    title: {
      text: getChartTitle(),
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const data = params.data
        return `
          <div style="font-weight: bold; margin-bottom: 8px;">${data.name}</div>
          <div style="margin-bottom: 4px;">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${params.color}; border-radius: 50%; margin-right: 8px;"></span>
            用户数: <strong>${data.value.toLocaleString()}人</strong>
          </div>
          <div style="margin-bottom: 4px;">
            占比: <strong>${data.percentage.toFixed(1)}%</strong>
          </div>
          <div>
            类型: <strong>${getSourceTypeLabel(data.sourceType)}</strong>
          </div>
        `
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 10,
      top: 60,
      bottom: 20,
      itemWidth: 14,
      itemHeight: 14,
      textStyle: {
        fontSize: 12
      },
      formatter: (name: string) => {
        const item = pieData.find(d => d.name === name)
        if (item) {
          return `${name} (${item.percentage.toFixed(1)}%)`
        }
        return name
      }
    },
    series: [
      {
        name: '用户来源',
        type: 'pie',
        radius: ['35%', '65%'],
        center: ['40%', '50%'],
        data: pieData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true,
          position: 'outside',
          formatter: (params: any) => {
            return `${params.name}\n${params.percent}%`
          },
          fontSize: 11,
          lineHeight: 14
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10
        },
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 2
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: (idx: number) => idx * 100
      }
    ],
    color: colors
  }

  return baseOption
})

// 获取图表标题
const getChartTitle = () => {
  switch (props.chartType) {
    case 'detailed':
      return '详细用户来源分布'
    case 'overview':
      return '用户来源分布'
    default:
      return '用户来源分布'
  }
}

// 获取来源类型标签
const getSourceTypeLabel = (sourceType: string) => {
  const typeMap: Record<string, string> = {
    'organic': '自然流量',
    'direct': '直接访问',
    'social': '社交媒体',
    'paid': '付费推广',
    'search': '搜索引擎',
    'other': '其他'
  }
  
  return typeMap[sourceType] || sourceType
}
</script>

<style scoped lang="less">
.user-source-chart {
  width: 100%;
  height: 100%;
  position: relative;
  
  .chart-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #8c8c8c;
    
    p {
      margin-top: 16px;
      font-size: 14px;
    }
  }
  
  .chart-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
}
</style>
