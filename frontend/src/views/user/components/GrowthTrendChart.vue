<template>
  <div class="growth-trend-chart">
    <div v-if="loading" class="chart-loading">
      <a-spin size="large" />
      <p>数据加载中...</p>
    </div>
    <div v-else-if="!hasData" class="chart-empty">
      <a-empty description="暂无数据" />
    </div>
    <v-chart v-else :option="chartOption" :style="{ height: '100%', width: '100%' }" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import VChart from 'vue-echarts'
import type { EChartsOption } from 'echarts'
import type { TrendData } from '@/api/user/growth'

/**
 * 用户增长趋势图表组件
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */

interface Props {
  data: Record<string, TrendData>
  chartType: 'newUsers' | 'totalUsers' | 'both'
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 计算属性
const hasData = computed(() => {
  return Object.keys(props.data).length > 0
})

const chartOption = computed<EChartsOption>(() => {
  if (!hasData.value) {
    return {}
  }

  const newUsersData = props.data.newUsers || props.data.monthlyNewUsers
  const totalUsersData = props.data.totalUsers || props.data.monthlyTotalUsers

  // 基础配置
  const baseOption: EChartsOption = {
    title: {
      text: getChartTitle(),
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: (params: any) => {
        if (Array.isArray(params)) {
          let result = `<div style="font-weight: bold; margin-bottom: 8px;">${params[0].axisValue}</div>`
          params.forEach((param: any) => {
            const value = typeof param.value === 'number' 
              ? param.value.toLocaleString() 
              : param.value
            result += `
              <div style="display: flex; align-items: center; margin-bottom: 4px;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
                <span style="flex: 1;">${param.seriesName}:</span>
                <span style="font-weight: bold; margin-left: 8px;">${value}${param.seriesName.includes('用户') ? '人' : ''}</span>
              </div>
            `
          })
          return result
        }
        return ''
      }
    },
    legend: {
      data: getLegendData(),
      top: 35,
      itemGap: 20
    },
    grid: {
      top: 80,
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: props.chartType === 'newUsers' || props.chartType === 'both',
      data: getCategories(),
      axisLabel: {
        rotate: getCategories().length > 12 ? 45 : 0
      }
    },
    yAxis: getYAxisConfig(),
    series: getSeriesConfig(),
    color: ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1']
  }

  return baseOption
})

// 获取图表标题
const getChartTitle = () => {
  switch (props.chartType) {
    case 'newUsers':
      return '新增用户趋势'
    case 'totalUsers':
      return '累计用户趋势'
    case 'both':
      return '用户增长趋势对比'
    default:
      return '用户增长趋势'
  }
}

// 获取图例数据
const getLegendData = () => {
  const legend: string[] = []
  
  if (props.chartType === 'newUsers' || props.chartType === 'both') {
    legend.push('新增用户')
  }
  
  if (props.chartType === 'totalUsers' || props.chartType === 'both') {
    legend.push('累计用户')
  }
  
  return legend
}

// 获取分类数据
const getCategories = () => {
  const newUsersData = props.data.newUsers || props.data.monthlyNewUsers
  const totalUsersData = props.data.totalUsers || props.data.monthlyTotalUsers
  
  if (newUsersData?.categories) {
    return newUsersData.categories
  }
  
  if (totalUsersData?.categories) {
    return totalUsersData.categories
  }
  
  return []
}

// 获取Y轴配置
const getYAxisConfig = () => {
  if (props.chartType === 'both') {
    return [
      {
        type: 'value' as const,
        name: '新增用户',
        position: 'left' as const,
        axisLabel: {
          formatter: '{value}'
        },
        splitLine: {
          show: false
        }
      },
      {
        type: 'value' as const,
        name: '累计用户',
        position: 'right' as const,
        axisLabel: {
          formatter: (value: number) => {
            if (value >= 10000) {
              return (value / 10000).toFixed(1) + 'w'
            }
            return value.toString()
          }
        }
      }
    ]
  }
  
  return {
    type: 'value' as const,
    axisLabel: {
      formatter: (value: number) => {
        if (props.chartType === 'totalUsers' && value >= 10000) {
          return (value / 10000).toFixed(1) + 'w'
        }
        return value.toString()
      }
    }
  }
}

// 获取系列配置
const getSeriesConfig = () => {
  const series: any[] = []
  
  const newUsersData = props.data.newUsers || props.data.monthlyNewUsers
  const totalUsersData = props.data.totalUsers || props.data.monthlyTotalUsers
  
  if ((props.chartType === 'newUsers' || props.chartType === 'both') && newUsersData) {
    series.push({
      name: '新增用户',
      type: 'bar',
      yAxisIndex: props.chartType === 'both' ? 0 : undefined,
      data: newUsersData.values,
      itemStyle: {
        color: '#1890ff',
        borderRadius: [4, 4, 0, 0]
      },
      emphasis: {
        itemStyle: {
          color: '#40a9ff'
        }
      },
      animationDelay: (idx: number) => idx * 50
    })
  }
  
  if ((props.chartType === 'totalUsers' || props.chartType === 'both') && totalUsersData) {
    series.push({
      name: '累计用户',
      type: 'line',
      yAxisIndex: props.chartType === 'both' ? 1 : undefined,
      data: totalUsersData.values,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3,
        color: '#52c41a'
      },
      itemStyle: {
        color: '#52c41a'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(82, 196, 26, 0.3)' },
            { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }
          ]
        }
      },
      animationDelay: (idx: number) => idx * 30
    })
  }
  
  return series
}
</script>

<style scoped lang="less">
.growth-trend-chart {
  width: 100%;
  height: 100%;
  position: relative;
  
  .chart-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #8c8c8c;
    
    p {
      margin-top: 16px;
      font-size: 14px;
    }
  }
  
  .chart-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
}
</style>
