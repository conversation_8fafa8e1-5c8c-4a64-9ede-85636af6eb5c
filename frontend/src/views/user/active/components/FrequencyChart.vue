<template>
  <div class="frequency-chart">
    <div v-if="loading" class="chart-loading">
      <a-spin size="large" />
    </div>
    <div v-else-if="!hasData" class="chart-empty">
      <a-empty description="暂无数据" />
    </div>
    <div v-else ref="chartRef" :style="{ height, width: '100%' }"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { FrequencyAnalysisData } from '@/api/user/active'

/**
 * 活跃频次分析图表组件
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */

// Props
interface Props {
  data: FrequencyAnalysisData | null
  loading?: boolean
  height?: string
  width?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  height: '400px',
  width: '100%'
})

// 响应式数据
const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

// 计算属性
const hasData = computed(() => {
  return props.data && Object.keys(props.data.frequencyDistribution).length > 0
})

const chartData = computed(() => {
  if (!props.data) {
    return null
  }

  const { frequencyDistribution, retentionByFrequency } = props.data
  
  // 转换数据格式
  const categories = Object.keys(frequencyDistribution)
  const userCounts = Object.values(frequencyDistribution)
  const retentionRates = categories.map(category => retentionByFrequency[category] || 0)

  return {
    categories,
    userCounts,
    retentionRates
  }
})

const chartOptions = computed(() => {
  if (!chartData.value) {
    return null
  }

  const { categories, userCounts, retentionRates } = chartData.value

  return {
    title: {
      text: '活跃频次分析',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
        color: '#262626'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e8e8e8',
      borderWidth: 1,
      textStyle: {
        color: '#262626'
      },
      formatter: (params: any) => {
        const [barParam, lineParam] = params
        return `
          <div style="padding: 8px;">
            <div style="margin-bottom: 4px; font-weight: 500;">${barParam.axisValue}</div>
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
              <span style="display: inline-block; width: 10px; height: 10px; background: ${barParam.color}; margin-right: 8px;"></span>
              <span>用户数量: ${barParam.value.toLocaleString()}</span>
            </div>
            <div style="display: flex; align-items: center;">
              <span style="display: inline-block; width: 10px; height: 10px; background: ${lineParam.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span>留存率: ${lineParam.value}%</span>
            </div>
          </div>
        `
      }
    },
    legend: {
      data: ['用户数量', '留存率'],
      top: '8%',
      textStyle: {
        color: '#262626'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '20%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: categories,
        axisPointer: {
          type: 'shadow'
        },
        axisLine: {
          lineStyle: {
            color: '#e8e8e8'
          }
        },
        axisLabel: {
          color: '#8c8c8c',
          fontSize: 12
        },
        axisTick: {
          show: false
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '用户数量',
        position: 'left',
        axisLine: {
          show: false
        },
        axisLabel: {
          color: '#8c8c8c',
          fontSize: 12,
          formatter: (value: number) => {
            if (value >= 10000) {
              return (value / 10000).toFixed(1) + 'w'
            }
            return value.toLocaleString()
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
            type: 'dashed'
          }
        }
      },
      {
        type: 'value',
        name: '留存率(%)',
        position: 'right',
        axisLine: {
          show: false
        },
        axisLabel: {
          color: '#8c8c8c',
          fontSize: 12,
          formatter: '{value}%'
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '用户数量',
        type: 'bar',
        yAxisIndex: 0,
        data: userCounts,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ]),
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2378f7' },
              { offset: 0.7, color: '#2378f7' },
              { offset: 1, color: '#83bff6' }
            ])
          }
        },
        barWidth: '60%'
      },
      {
        name: '留存率',
        type: 'line',
        yAxisIndex: 1,
        data: retentionRates,
        lineStyle: {
          color: '#52c41a',
          width: 3
        },
        itemStyle: {
          color: '#52c41a',
          borderColor: '#fff',
          borderWidth: 2
        },
        symbol: 'circle',
        symbolSize: 8,
        smooth: true,
        emphasis: {
          focus: 'series',
          itemStyle: {
            shadowBlur: 10,
            shadowColor: '#52c41a'
          }
        }
      }
    ],
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut' as any
  }
})

// 方法
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || !chartOptions.value) return

  chartInstance.setOption(chartOptions.value, true)
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

const destroyChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
}

// 监听数据变化
watch(() => props.data, () => {
  nextTick(() => {
    updateChart()
  })
})

watch(() => props.loading, (loading) => {
  if (!loading && hasData.value) {
    nextTick(() => {
      if (!chartInstance) {
        initChart()
      } else {
        updateChart()
      }
    })
  }
})

// 生命周期
onMounted(() => {
  if (!props.loading && hasData.value) {
    nextTick(() => {
      initChart()
    })
  }

  // 监听窗口大小变化
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  destroyChart()
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped lang="less">
.frequency-chart {
  position: relative;
  width: 100%;

  .chart-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }

  .chart-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }
}
</style>
