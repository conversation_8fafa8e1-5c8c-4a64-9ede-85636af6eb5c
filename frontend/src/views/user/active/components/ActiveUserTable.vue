<template>
  <div class="active-user-table">
    <a-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="paginationConfig"
      :scroll="{ x: 1200 }"
      row-key="id"
      size="middle"
    >
      <!-- 日期列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'date'">
          <span class="date-cell">{{ formatDate(record.date) }}</span>
        </template>
        
        <!-- 活跃用户数列 -->
        <template v-else-if="column.key === 'activeUsers'">
          <div class="metric-cell">
            <span class="value">{{ formatNumber(record.activeUsers) }}</span>
            <span v-if="record.activeUsersGrowth !== undefined" 
                  :class="['growth', record.activeUsersGrowth >= 0 ? 'positive' : 'negative']">
              {{ formatGrowth(record.activeUsersGrowth) }}
            </span>
          </div>
        </template>
        
        <!-- DAU列 -->
        <template v-else-if="column.key === 'dau'">
          <div class="metric-cell">
            <span class="value">{{ formatNumber(record.dau) }}</span>
            <span v-if="record.dauGrowth !== undefined" 
                  :class="['growth', record.dauGrowth >= 0 ? 'positive' : 'negative']">
              {{ formatGrowth(record.dauGrowth) }}
            </span>
          </div>
        </template>
        
        <!-- WAU列 -->
        <template v-else-if="column.key === 'wau'">
          <div class="metric-cell">
            <span class="value">{{ formatNumber(record.wau) }}</span>
            <span v-if="record.wauGrowth !== undefined" 
                  :class="['growth', record.wauGrowth >= 0 ? 'positive' : 'negative']">
              {{ formatGrowth(record.wauGrowth) }}
            </span>
          </div>
        </template>
        
        <!-- MAU列 -->
        <template v-else-if="column.key === 'mau'">
          <div class="metric-cell">
            <span class="value">{{ formatNumber(record.mau) }}</span>
            <span v-if="record.mauGrowth !== undefined" 
                  :class="['growth', record.mauGrowth >= 0 ? 'positive' : 'negative']">
              {{ formatGrowth(record.mauGrowth) }}
            </span>
          </div>
        </template>
        
        <!-- 黏性系数列 -->
        <template v-else-if="column.key === 'stickinessRatio'">
          <div class="metric-cell">
            <span class="value">{{ formatPercentage(record.stickinessRatio) }}</span>
            <span v-if="record.stickinessRatioGrowth !== undefined" 
                  :class="['growth', record.stickinessRatioGrowth >= 0 ? 'positive' : 'negative']">
              {{ formatGrowth(record.stickinessRatioGrowth) }}
            </span>
          </div>
        </template>
        
        <!-- 活跃率列 -->
        <template v-else-if="column.key === 'activeRate'">
          <div class="metric-cell">
            <span class="value">{{ formatPercentage(record.activeRate) }}</span>
            <span v-if="record.activeRateGrowth !== undefined" 
                  :class="['growth', record.activeRateGrowth >= 0 ? 'positive' : 'negative']">
              {{ formatGrowth(record.activeRateGrowth) }}
            </span>
          </div>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import dayjs from 'dayjs'

/**
 * 活跃用户数据表格组件
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */

// Props
interface Props {
  data: any[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  loading: false
})

// Emits
interface Emits {
  (e: 'refresh'): void
}

const emit = defineEmits<Emits>()

// 表格列配置
const columns: any[] = [
  {
    title: '日期',
    dataIndex: 'date',
    key: 'date',
    width: 120,
    fixed: 'left',
    sorter: (a: any, b: any) => dayjs(a.date).unix() - dayjs(b.date).unix()
  },
  {
    title: '活跃用户',
    dataIndex: 'activeUsers',
    key: 'activeUsers',
    width: 140,
    sorter: (a: any, b: any) => a.activeUsers - b.activeUsers
  },
  {
    title: 'DAU',
    dataIndex: 'dau',
    key: 'dau',
    width: 120,
    sorter: (a: any, b: any) => a.dau - b.dau
  },
  {
    title: 'WAU',
    dataIndex: 'wau',
    key: 'wau',
    width: 120,
    sorter: (a: any, b: any) => a.wau - b.wau
  },
  {
    title: 'MAU',
    dataIndex: 'mau',
    key: 'mau',
    width: 120,
    sorter: (a: any, b: any) => a.mau - b.mau
  },
  {
    title: '黏性系数',
    dataIndex: 'stickinessRatio',
    key: 'stickinessRatio',
    width: 120,
    sorter: (a: any, b: any) => a.stickinessRatio - b.stickinessRatio
  },
  {
    title: '活跃率',
    dataIndex: 'activeRate',
    key: 'activeRate',
    width: 120,
    sorter: (a: any, b: any) => a.activeRate - b.activeRate
  }
]

// 分页配置
const paginationConfig = {
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => 
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  pageSizeOptions: ['10', '20', '50', '100']
}

// 响应式数据
const tableDataList = ref<any[]>([])
const loading = ref(false)

// 计算属性
const tableData = computed(() => {
  // 优先使用传入的数据
  if (props.data && props.data.length > 0) {
    return props.data.map((item, index) => ({
      id: index + 1,
      ...item
    }))
  }

  // 否则使用从API获取的数据
  return tableDataList.value
})

// 初始化数据
const initData = async () => {
  if (!props.data || props.data.length === 0) {
    tableDataList.value = await fetchActiveUserData()
  }
}

// 组件挂载时获取数据
onMounted(() => {
  initData()
})

// 方法
const formatDate = (date: string) => {
  return dayjs(date).format('MM-DD')
}

const formatNumber = (value: number) => {
  if (value >= 10000) {
    return (value / 10000).toFixed(1) + 'w'
  }
  return value.toLocaleString()
}

const formatPercentage = (value: number) => {
  return value.toFixed(2) + '%'
}

const formatGrowth = (value: number) => {
  const sign = value >= 0 ? '+' : ''
  return `${sign}${value.toFixed(2)}%`
}

// 从API获取真实数据
const fetchActiveUserData = async () => {
  try {
    loading.value = true

    const today = dayjs()
    const thirtyDaysAgo = today.subtract(29, 'day')

    // 调用活跃用户API
    const response = await fetch('/user/active/public/daily', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        startDate: thirtyDaysAgo.format('YYYY-MM-DD'),
        endDate: today.format('YYYY-MM-DD'),
        granularity: 'DAY'
      })
    })

    if (response.ok) {
      const result = await response.json()
      if (result.code === 200 && result.data) {
        // 转换API数据格式
        return result.data.map((item: any, index: number) => ({
          id: index + 1,
          date: item.date,
          activeUsers: item.activeUsers || 0,
          activeUsersGrowth: item.activeUsersGrowth || 0,
          dau: item.dau || 0,
          dauGrowth: item.dauGrowth || 0,
          wau: item.wau || 0,
          wauGrowth: item.wauGrowth || 0,
          mau: item.mau || 0,
          mauGrowth: item.mauGrowth || 0,
          stickinessRatio: item.stickinessRatio || 0,
          stickinessRatioGrowth: item.stickinessRatioGrowth || 0,
          activeRate: item.activeRate || 0,
          activeRateGrowth: item.activeRateGrowth || 0
        }))
      }
    }

    // API调用失败时返回空数组
    return []
  } catch (error) {
    console.error('获取活跃用户数据失败:', error)
    return []
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="less">
.active-user-table {
  .date-cell {
    font-weight: 500;
    color: #262626;
  }

  .metric-cell {
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .value {
      font-weight: 500;
      color: #262626;
      margin-bottom: 2px;
    }

    .growth {
      font-size: 12px;
      padding: 1px 4px;
      border-radius: 2px;
      
      &.positive {
        color: #52c41a;
        background-color: #f6ffed;
      }
      
      &.negative {
        color: #ff4d4f;
        background-color: #fff2f0;
      }
    }
  }

  :deep(.ant-table) {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
      color: #262626;
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f5f5f5;
    }
  }
}

@media (max-width: 768px) {
  .active-user-table {
    :deep(.ant-table) {
      .ant-table-tbody > tr > td {
        padding: 8px 12px;
      }
    }
  }
}
</style>
