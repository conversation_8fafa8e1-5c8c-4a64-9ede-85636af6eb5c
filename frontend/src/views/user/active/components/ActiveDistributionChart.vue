<template>
  <div class="active-distribution-chart">
    <div v-if="loading" class="chart-loading">
      <a-spin size="large" />
    </div>
    <div v-else-if="!hasData" class="chart-empty">
      <a-empty description="暂无数据" />
    </div>
    <div v-else ref="chartRef" :style="{ height, width: '100%' }"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { DistributionData } from '@/api/user/active'

/**
 * 活跃用户分布图表组件
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */

// Props
interface Props {
  data: Record<string, DistributionData> | null
  type: 'region' | 'device'
  loading?: boolean
  height?: string
  width?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  height: '400px',
  width: '100%'
})

// 响应式数据
const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

// 计算属性
const hasData = computed(() => {
  return props.data && Object.keys(props.data).length > 0
})

const chartData = computed(() => {
  if (!props.data || !props.data[props.type]) {
    return null
  }
  
  const distributionData = props.data[props.type]
  return {
    items: distributionData.items,
    name: distributionData.name,
    chartType: distributionData.chartType
  }
})

const chartOptions = computed(() => {
  if (!chartData.value) {
    return null
  }

  const { items, name } = chartData.value

  // 转换数据格式
  const pieData = items.map(item => ({
    name: item.name,
    value: item.value,
    itemStyle: {
      color: item.color
    }
  }))

  return {
    title: {
      text: name,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
        color: '#262626'
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e8e8e8',
      borderWidth: 1,
      textStyle: {
        color: '#262626'
      },
      formatter: (params: any) => {
        return `
          <div style="padding: 8px;">
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
              <span style="display: inline-block; width: 10px; height: 10px; background: ${params.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="font-weight: 500;">${params.name}</span>
            </div>
            <div style="margin-left: 18px;">
              <div>数量: ${params.value.toLocaleString()}</div>
              <div>占比: ${params.percent}%</div>
            </div>
          </div>
        `
      }
    },
    legend: {
      type: 'scroll',
      orient: 'horizontal',
      bottom: '5%',
      left: 'center',
      textStyle: {
        color: '#262626',
        fontSize: 12
      },
      pageTextStyle: {
        color: '#8c8c8c'
      }
    },
    series: [
      {
        name: name,
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold',
            formatter: (params: any) => {
              return `${params.name}\n${params.percent}%`
            }
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: {
          show: false
        },
        data: pieData,
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: (idx: number) => Math.random() * 200
      }
    ],
    animation: true,
    animationDuration: 1000
  }
})

// 方法
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || !chartOptions.value) return

  chartInstance.setOption(chartOptions.value, true)
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

const destroyChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
}

// 监听数据变化
watch([() => props.data, () => props.type], () => {
  nextTick(() => {
    updateChart()
  })
})

watch(() => props.loading, (loading) => {
  if (!loading && hasData.value) {
    nextTick(() => {
      if (!chartInstance) {
        initChart()
      } else {
        updateChart()
      }
    })
  }
})

// 生命周期
onMounted(() => {
  if (!props.loading && hasData.value) {
    nextTick(() => {
      initChart()
    })
  }

  // 监听窗口大小变化
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  destroyChart()
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped lang="less">
.active-distribution-chart {
  position: relative;
  width: 100%;

  .chart-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }

  .chart-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }
}
</style>
