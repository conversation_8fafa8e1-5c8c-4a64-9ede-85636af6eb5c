<template>
  <div class="active-trend-chart">
    <div v-if="loading" class="chart-loading">
      <a-spin size="large" />
    </div>
    <div v-else-if="!hasData" class="chart-empty">
      <a-empty description="暂无数据" />
    </div>
    <div v-else ref="chartRef" :style="{ height, width: '100%' }"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { TrendData } from '@/api/user/active'

/**
 * 活跃用户趋势图表组件
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */

// Props
interface Props {
  data: Record<string, TrendData> | null
  type: 'dau' | 'wau' | 'mau'
  loading?: boolean
  height?: string
  width?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  height: '400px',
  width: '100%'
})

// 响应式数据
const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

// 计算属性
const hasData = computed(() => {
  return props.data && Object.keys(props.data).length > 0
})

const chartData = computed(() => {
  if (!props.data || !props.data[props.type]) {
    return null
  }
  
  const trendData = props.data[props.type]
  return {
    categories: trendData.categories,
    values: trendData.values,
    name: trendData.name,
    unit: trendData.unit
  }
})

const chartOptions = computed(() => {
  if (!chartData.value) {
    return null
  }

  const { categories, values, name, unit } = chartData.value

  return {
    title: {
      text: name,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
        color: '#262626'
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e8e8e8',
      borderWidth: 1,
      textStyle: {
        color: '#262626'
      },
      formatter: (params: any) => {
        const param = params[0]
        return `
          <div style="padding: 8px;">
            <div style="margin-bottom: 4px; font-weight: 500;">${param.axisValue}</div>
            <div style="display: flex; align-items: center;">
              <span style="display: inline-block; width: 10px; height: 10px; background: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span>${param.seriesName}: ${param.value.toLocaleString()} ${unit}</span>
            </div>
          </div>
        `
      }
    },
    legend: {
      show: false
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: categories,
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      axisLabel: {
        color: '#8c8c8c',
        fontSize: 12
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisLabel: {
        color: '#8c8c8c',
        fontSize: 12,
        formatter: (value: number) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + 'w'
          }
          return value.toLocaleString()
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: name,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 3,
          color: getLineColor(props.type)
        },
        itemStyle: {
          color: getLineColor(props.type),
          borderColor: '#fff',
          borderWidth: 2
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: getAreaColor(props.type, 0.3)
            },
            {
              offset: 1,
              color: getAreaColor(props.type, 0.05)
            }
          ])
        },
        data: values,
        emphasis: {
          focus: 'series',
          itemStyle: {
            shadowBlur: 10,
            shadowColor: getLineColor(props.type)
          }
        }
      }
    ],
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut' as any
  }
})

// 方法
const getLineColor = (type: string) => {
  const colors = {
    dau: '#1890ff',
    wau: '#52c41a',
    mau: '#fa8c16'
  }
  return colors[type as keyof typeof colors] || '#1890ff'
}

const getAreaColor = (type: string, opacity: number) => {
  const baseColor = getLineColor(type)
  // 将hex颜色转换为rgba
  const hex = baseColor.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  return `rgba(${r}, ${g}, ${b}, ${opacity})`
}

const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || !chartOptions.value) return

  chartInstance.setOption(chartOptions.value, true)
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

const destroyChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
}

// 监听数据变化
watch([() => props.data, () => props.type], () => {
  nextTick(() => {
    updateChart()
  })
})

watch(() => props.loading, (loading) => {
  if (!loading && hasData.value) {
    nextTick(() => {
      if (!chartInstance) {
        initChart()
      } else {
        updateChart()
      }
    })
  }
})

// 生命周期
onMounted(() => {
  if (!props.loading && hasData.value) {
    nextTick(() => {
      initChart()
    })
  }

  // 监听窗口大小变化
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  destroyChart()
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped lang="less">
.active-trend-chart {
  position: relative;
  width: 100%;

  .chart-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }

  .chart-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }
}
</style>
