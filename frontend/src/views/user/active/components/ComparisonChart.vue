<template>
  <div class="comparison-chart">
    <div v-if="loading" class="chart-loading">
      <a-spin size="large" />
    </div>
    <div v-else-if="!hasData" class="chart-empty">
      <a-empty description="请选择产品线进行对比分析" />
    </div>
    <div v-else ref="chartRef" :style="{ height, width: '100%' }"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { ActiveUserAggregate } from '@/api/user/active'

/**
 * 产品线对比图表组件
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */

// Props
interface Props {
  data: ActiveUserAggregate | null
  loading?: boolean
  height?: string
  width?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  height: '400px',
  width: '100%'
})

// 响应式数据
const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

// 计算属性
const hasData = computed(() => {
  return props.data && props.data.trendData && Object.keys(props.data.trendData).length > 0
})

const chartData = computed(() => {
  if (!props.data?.trendData) {
    return null
  }

  const trendData = props.data.trendData
  const series: any[] = []
  let categories: string[] = []

  // 预定义颜色
  const colors = [
    '#1890ff', '#52c41a', '#fa8c16', '#eb2f96', 
    '#722ed1', '#13c2c2', '#faad14', '#f5222d'
  ]

  Object.entries(trendData).forEach(([key, trend], index) => {
    if (trend.categories && trend.categories.length > 0) {
      categories = trend.categories
    }

    series.push({
      name: trend.name,
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3,
        color: colors[index % colors.length]
      },
      itemStyle: {
        color: colors[index % colors.length],
        borderColor: '#fff',
        borderWidth: 2
      },
      data: trend.values,
      emphasis: {
        focus: 'series',
        itemStyle: {
          shadowBlur: 10,
          shadowColor: colors[index % colors.length]
        }
      }
    })
  })

  return {
    categories,
    series
  }
})

const chartOptions = computed(() => {
  if (!chartData.value) {
    return null
  }

  const { categories, series } = chartData.value

  return {
    title: {
      text: '产品线活跃用户对比',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
        color: '#262626'
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e8e8e8',
      borderWidth: 1,
      textStyle: {
        color: '#262626'
      },
      formatter: (params: any) => {
        let content = `<div style="padding: 8px;">
          <div style="margin-bottom: 8px; font-weight: 500;">${params[0].axisValue}</div>`
        
        params.forEach((param: any) => {
          content += `
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
              <span style="display: inline-block; width: 10px; height: 10px; background: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span>${param.seriesName}: ${param.value.toLocaleString()}</span>
            </div>`
        })
        
        content += '</div>'
        return content
      }
    },
    legend: {
      type: 'scroll',
      top: '8%',
      textStyle: {
        color: '#262626',
        fontSize: 12
      },
      pageTextStyle: {
        color: '#8c8c8c'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: categories,
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      axisLabel: {
        color: '#8c8c8c',
        fontSize: 12
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisLabel: {
        color: '#8c8c8c',
        fontSize: 12,
        formatter: (value: number) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + 'w'
          }
          return value.toLocaleString()
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      }
    },
    series,
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut' as any
  }
})

// 方法
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || !chartOptions.value) return

  chartInstance.setOption(chartOptions.value, true)
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

const destroyChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
}

// 监听数据变化
watch(() => props.data, () => {
  nextTick(() => {
    updateChart()
  })
})

watch(() => props.loading, (loading) => {
  if (!loading && hasData.value) {
    nextTick(() => {
      if (!chartInstance) {
        initChart()
      } else {
        updateChart()
      }
    })
  }
})

// 生命周期
onMounted(() => {
  if (!props.loading && hasData.value) {
    nextTick(() => {
      initChart()
    })
  }

  // 监听窗口大小变化
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  destroyChart()
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped lang="less">
.comparison-chart {
  position: relative;
  width: 100%;

  .chart-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }

  .chart-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }
}
</style>
