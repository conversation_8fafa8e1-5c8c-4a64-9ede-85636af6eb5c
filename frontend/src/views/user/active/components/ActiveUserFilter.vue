<template>
  <div class="active-user-filter">
    <a-form layout="inline" :model="formData" @finish="handleSubmit">
      <a-row :gutter="[16, 16]" style="width: 100%">
        <!-- 时间范围选择 -->
        <a-col :xs="24" :sm="12" :md="8" :lg="6">
          <a-form-item label="时间范围" name="dateRange">
            <a-range-picker
              v-model:value="formData.dateRange"
              :placeholder="['开始日期', '结束日期']"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              @change="handleDateRangeChange"
            />
          </a-form-item>
        </a-col>

        <!-- 时间粒度选择 -->
        <a-col :xs="24" :sm="12" :md="8" :lg="6">
          <a-form-item label="时间粒度" name="granularity">
            <a-select
              v-model:value="formData.granularity"
              style="width: 100%"
              @change="handleGranularityChange"
            >
              <a-select-option value="DAY">按天</a-select-option>
              <a-select-option value="WEEK">按周</a-select-option>
              <a-select-option value="MONTH">按月</a-select-option>
              <a-select-option value="QUARTER">按季度</a-select-option>
              <a-select-option value="YEAR">按年</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 产品线选择 -->
        <a-col :xs="24" :sm="12" :md="8" :lg="6">
          <a-form-item label="产品线" name="productLineIds">
            <a-select
              v-model:value="formData.productLineIds"
              mode="multiple"
              style="width: 100%"
              placeholder="选择产品线"
              :options="productLineOptions"
              :loading="productLineLoading"
              @change="handleProductLineChange"
            />
          </a-form-item>
        </a-col>

        <!-- 快捷时间选择 -->
        <a-col :xs="24" :sm="12" :md="8" :lg="6">
          <a-form-item label="快捷选择">
            <a-select
              v-model:value="quickTimeRange"
              style="width: 100%"
              placeholder="选择时间范围"
              @change="handleQuickTimeChange"
            >
              <a-select-option value="today">今天</a-select-option>
              <a-select-option value="yesterday">昨天</a-select-option>
              <a-select-option value="last7days">最近7天</a-select-option>
              <a-select-option value="last30days">最近30天</a-select-option>
              <a-select-option value="thisWeek">本周</a-select-option>
              <a-select-option value="lastWeek">上周</a-select-option>
              <a-select-option value="thisMonth">本月</a-select-option>
              <a-select-option value="lastMonth">上月</a-select-option>
              <a-select-option value="thisQuarter">本季度</a-select-option>
              <a-select-option value="lastQuarter">上季度</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 高级选项 -->
        <a-col :xs="24" :sm="12" :md="8" :lg="6">
          <a-form-item>
            <a-space>
              <a-checkbox 
                v-model:checked="formData.includeComparison"
                @change="handleComparisonChange"
              >
                包含对比
              </a-checkbox>
              <a-checkbox 
                v-model:checked="formData.includeDetails"
                @change="handleDetailsChange"
              >
                详细数据
              </a-checkbox>
            </a-space>
          </a-form-item>
        </a-col>

        <!-- 操作按钮 -->
        <a-col :xs="24" :sm="12" :md="8" :lg="6">
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit" :loading="loading">
                <SearchOutlined />
                查询
              </a-button>
              <a-button @click="handleReset">
                <ReloadOutlined />
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import type { ActiveUserAnalysisRequest, TimeGranularity } from '@/api/user/active'

/**
 * 活跃用户筛选器组件
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */

// Props
interface Props {
  params: ActiveUserAnalysisRequest
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// Emits
interface Emits {
  (e: 'update:params', params: ActiveUserAnalysisRequest): void
  (e: 'change', params: ActiveUserAnalysisRequest): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const formData = reactive<{
  dateRange: [Dayjs, Dayjs]
  granularity: TimeGranularity
  productLineIds: number[]
  includeComparison: boolean
  includeDetails: boolean
}>({
  dateRange: [dayjs().subtract(29, 'day'), dayjs()],
  granularity: 'DAY',
  productLineIds: [],
  includeComparison: true,
  includeDetails: false
})

const quickTimeRange = ref<string>('')
const productLineLoading = ref(false)

// 产品线选项（模拟数据）
const productLineOptions = ref([
  { label: '福昕阅读器GA版', value: 1 },
  { label: '福昕阅读器PLUS版', value: 2 },
  { label: 'PDF编辑器个人版', value: 3 },
  { label: 'PDF编辑器专业版', value: 4 },
  { label: 'PDF365在线服务', value: 5 },
  { label: 'PDF转换器', value: 6 },
  { label: '卸载器', value: 7 },
  { label: '福昕移动阅读器', value: 8 },
  { label: 'PDF SDK工具包', value: 9 },
  { label: '内容平台', value: 10 }
])

// 计算属性
const currentParams = computed<ActiveUserAnalysisRequest>(() => {
  const [startDate, endDate] = formData.dateRange
  
  return {
    startDate: startDate ? startDate.format('YYYY-MM-DD') : '',
    endDate: endDate ? endDate.format('YYYY-MM-DD') : '',
    granularity: formData.granularity,
    productLineIds: formData.productLineIds,
    includeComparison: formData.includeComparison,
    includeDetails: formData.includeDetails
  }
})

// 方法
const handleSubmit = () => {
  const params = currentParams.value
  if (!params.startDate || !params.endDate) {
    return
  }
  
  emit('update:params', params)
  emit('change', params)
}

const handleDateRangeChange = (dates: [string, string] | [Dayjs, Dayjs] | null) => {
  if (dates && Array.isArray(dates)) {
    // 如果是字符串数组，转换为Dayjs对象
    if (typeof dates[0] === 'string') {
      formData.dateRange = [dayjs(dates[0]), dayjs(dates[1])]
    } else {
      formData.dateRange = dates as [Dayjs, Dayjs]
    }
    quickTimeRange.value = '' // 清空快捷选择
    handleSubmit()
  }
}

const handleGranularityChange = (value: any) => {
  formData.granularity = value as TimeGranularity
  handleSubmit()
}

const handleProductLineChange = (value: any) => {
  formData.productLineIds = value as number[]
  handleSubmit()
}

const handleComparisonChange = (e: any) => {
  formData.includeComparison = e.target ? e.target.checked : e
  handleSubmit()
}

const handleDetailsChange = (e: any) => {
  formData.includeDetails = e.target ? e.target.checked : e
  handleSubmit()
}

const handleQuickTimeChange = (value: any) => {
  const stringValue = value as string
  const today = dayjs()
  let startDate: Dayjs
  let endDate: Dayjs

  switch (stringValue) {
    case 'today':
      startDate = today
      endDate = today
      break
    case 'yesterday':
      startDate = today.subtract(1, 'day')
      endDate = today.subtract(1, 'day')
      break
    case 'last7days':
      startDate = today.subtract(6, 'day')
      endDate = today
      break
    case 'last30days':
      startDate = today.subtract(29, 'day')
      endDate = today
      break
    case 'thisWeek':
      startDate = today.startOf('week')
      endDate = today.endOf('week')
      break
    case 'lastWeek':
      startDate = today.subtract(1, 'week').startOf('week')
      endDate = today.subtract(1, 'week').endOf('week')
      break
    case 'thisMonth':
      startDate = today.startOf('month')
      endDate = today.endOf('month')
      break
    case 'lastMonth':
      startDate = today.subtract(1, 'month').startOf('month')
      endDate = today.subtract(1, 'month').endOf('month')
      break
    case 'thisQuarter':
      startDate = today.startOf('quarter' as any)
      endDate = today.endOf('quarter' as any)
      break
    case 'lastQuarter':
      startDate = today.subtract(1, 'quarter' as any).startOf('quarter' as any)
      endDate = today.subtract(1, 'quarter' as any).endOf('quarter' as any)
      break
    default:
      return
  }

  formData.dateRange = [startDate, endDate]
  handleSubmit()
}

const handleReset = () => {
  // 重置为默认值
  const today = dayjs()
  formData.dateRange = [today.subtract(29, 'day'), today]
  formData.granularity = 'DAY'
  formData.productLineIds = []
  formData.includeComparison = true
  formData.includeDetails = false
  quickTimeRange.value = 'last30days'
  
  handleSubmit()
}

// 初始化表单数据
const initFormData = () => {
  if (props.params.startDate && props.params.endDate) {
    formData.dateRange = [
      dayjs(props.params.startDate),
      dayjs(props.params.endDate)
    ]
  } else {
    // 默认最近30天
    const today = dayjs()
    formData.dateRange = [today.subtract(29, 'day'), today]
    quickTimeRange.value = 'last30days'
  }
  
  formData.granularity = props.params.granularity || 'DAY'
  formData.productLineIds = props.params.productLineIds || []
  formData.includeComparison = props.params.includeComparison ?? true
  formData.includeDetails = props.params.includeDetails ?? false
}

// 监听props变化
watch(() => props.params, () => {
  initFormData()
}, { immediate: true })

// 生命周期
onMounted(() => {
  initFormData()
})
</script>

<style scoped lang="less">
.active-user-filter {
  .ant-form {
    width: 100%;
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-form-item-label {
    font-weight: 500;
  }
}

@media (max-width: 768px) {
  .active-user-filter {
    .ant-form-item {
      margin-bottom: 12px;
    }
  }
}
</style>
