<template>
  <div class="active-user-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <UserOutlined />
          活跃用户分析
        </h1>
        <p class="page-description">
          分析用户活跃度、使用频次、地域分布等关键指标，洞察用户行为模式
        </p>
      </div>
      
      <!-- 操作按钮 -->
      <div class="header-actions">
        <a-space>
          <a-button 
            type="primary" 
            :icon="h(ReloadOutlined)" 
            :loading="loading"
            @click="handleRefresh"
          >
            刷新数据
          </a-button>
          <a-button 
            :icon="h(DownloadOutlined)"
            :loading="loading"
            @click="handleExport"
          >
            导出数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <a-card :bordered="false" size="small">
        <ActiveUserFilter 
          v-model:params="filterParams"
          @change="handleFilterChange"
        />
      </a-card>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6" v-for="metric in coreMetricsList" :key="metric.key">
          <MetricCard 
            :title="metric.title"
            :value="metric.value"
            :unit="metric.unit"
            :growth-rate="metric.growthRate"
            :trend="metric.trend"
            :icon="metric.icon"
            :color="metric.color"
            :loading="loading"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <a-row :gutter="[16, 16]">
        <!-- 活跃用户趋势图 -->
        <a-col :xs="24" :lg="12">
          <a-card title="活跃用户趋势" :bordered="false">
            <template #extra>
              <a-radio-group 
                v-model:value="trendChartType" 
                size="small"
                @change="handleTrendTypeChange"
              >
                <a-radio-button value="dau">DAU</a-radio-button>
                <a-radio-button value="wau">WAU</a-radio-button>
                <a-radio-button value="mau">MAU</a-radio-button>
              </a-radio-group>
            </template>
            <ActiveTrendChart 
              :data="trendChartData"
              :type="trendChartType"
              :loading="loading"
              height="300px"
            />
          </a-card>
        </a-col>

        <!-- 活跃用户分布图 -->
        <a-col :xs="24" :lg="12">
          <a-card title="活跃用户分布" :bordered="false">
            <template #extra>
              <a-radio-group 
                v-model:value="distributionType" 
                size="small"
                @change="handleDistributionTypeChange"
              >
                <a-radio-button value="region">地域</a-radio-button>
                <a-radio-button value="device">设备</a-radio-button>
              </a-radio-group>
            </template>
            <ActiveDistributionChart 
              :data="distributionChartData"
              :type="distributionType"
              :loading="loading"
              height="300px"
            />
          </a-card>
        </a-col>

        <!-- 活跃频次分析 -->
        <a-col :xs="24" :lg="12">
          <a-card title="活跃频次分析" :bordered="false">
            <FrequencyChart 
              :data="frequencyChartData"
              :loading="loading"
              height="300px"
            />
          </a-card>
        </a-col>

        <!-- 产品线对比 -->
        <a-col :xs="24" :lg="12">
          <a-card title="产品线对比" :bordered="false">
            <template #extra>
              <a-button 
                size="small" 
                type="link"
                @click="showProductLineSelector = true"
              >
                选择产品线
              </a-button>
            </template>
            <ComparisonChart 
              :data="comparisonChartData"
              :loading="loading"
              height="300px"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 详细数据表格 -->
    <div class="table-section">
      <a-card title="详细数据" :bordered="false">
        <template #extra>
          <a-space>
            <a-button size="small" @click="handleTableRefresh">
              <ReloadOutlined />
            </a-button>
            <a-button size="small" @click="handleTableExport">
              <DownloadOutlined />
            </a-button>
          </a-space>
        </template>
        <ActiveUserTable 
          :data="tableData"
          :loading="loading"
          @refresh="handleTableRefresh"
        />
      </a-card>
    </div>

    <!-- 产品线选择器弹窗 -->
    <ProductLineSelector
      v-model:open="showProductLineSelector"
      v-model:selected="selectedProductLines"
      @confirm="handleProductLineConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { message } from 'ant-design-vue'
import { 
  UserOutlined, 
  ReloadOutlined, 
  DownloadOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons-vue'
import { useActiveUserStore } from '@/stores/user/active'
import type { ActiveUserAnalysisRequest } from '@/api/user/active'

// 组件导入
import ActiveUserFilter from './components/ActiveUserFilter.vue'
import MetricCard from '@/components/charts/MetricCard.vue'
import ActiveTrendChart from './components/ActiveTrendChart.vue'
import ActiveDistributionChart from './components/ActiveDistributionChart.vue'
import FrequencyChart from './components/FrequencyChart.vue'
import ComparisonChart from './components/ComparisonChart.vue'
import ActiveUserTable from './components/ActiveUserTable.vue'
import ProductLineSelector from '@/components/common/ProductLineSelector.vue'

/**
 * 活跃用户分析页面
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */

// Store
const activeUserStore = useActiveUserStore()

// 响应式数据
const loading = computed(() => activeUserStore.loading)
const filterParams = ref<ActiveUserAnalysisRequest>({
  startDate: '',
  endDate: '',
  granularity: 'DAY',
  productLineIds: [],
  includeComparison: true,
  includeDetails: false
})

const trendChartType = ref<'dau' | 'wau' | 'mau'>('dau')
const distributionType = ref<'region' | 'device'>('region')
const showProductLineSelector = ref(false)
const selectedProductLines = ref<number[]>([])

// 计算属性
const coreMetricsList = computed(() => {
  const metrics = activeUserStore.coreMetrics
  if (!metrics) return []

  const getDauGrowthRate = () => {
    return metrics.dau && typeof metrics.dau.getGrowthRate === 'function' ? metrics.dau.getGrowthRate() : 0
  }

  return [
    {
      key: 'dau',
      title: '日活跃用户',
      value: metrics.dau?.value || 0,
      unit: '人',
      growthRate: getDauGrowthRate(),
      trend: (getDauGrowthRate() >= 0 ? 'up' : 'down') as 'up' | 'down' | 'flat',
      icon: h(UserOutlined),
      color: '#1890ff'
    },
    {
      key: 'wau',
      title: '周活跃用户',
      value: metrics.wau?.value || 0,
      unit: '人',
      growthRate: (() => {
        return metrics.wau && typeof metrics.wau.getGrowthRate === 'function' ? metrics.wau.getGrowthRate() : 0
      })(),
      trend: (() => {
        const rate = metrics.wau && typeof metrics.wau.getGrowthRate === 'function' ? metrics.wau.getGrowthRate() : 0
        return (rate >= 0 ? 'up' : 'down') as 'up' | 'down' | 'flat'
      })(),
      icon: h(UserOutlined),
      color: '#52c41a'
    },
    {
      key: 'mau',
      title: '月活跃用户',
      value: metrics.mau?.value || 0,
      unit: '人',
      growthRate: (() => {
        return metrics.mau && typeof metrics.mau.getGrowthRate === 'function' ? metrics.mau.getGrowthRate() : 0
      })(),
      trend: (() => {
        const rate = metrics.mau && typeof metrics.mau.getGrowthRate === 'function' ? metrics.mau.getGrowthRate() : 0
        return (rate >= 0 ? 'up' : 'down') as 'up' | 'down' | 'flat'
      })(),
      icon: h(UserOutlined),
      color: '#fa8c16'
    },
    {
      key: 'stickinessRatio',
      title: '黏性系数',
      value: metrics.stickinessRatio?.value || 0,
      unit: '%',
      growthRate: (() => {
        return metrics.stickinessRatio && typeof metrics.stickinessRatio.getGrowthRate === 'function' ? metrics.stickinessRatio.getGrowthRate() : 0
      })(),
      trend: (() => {
        const rate = metrics.stickinessRatio && typeof metrics.stickinessRatio.getGrowthRate === 'function' ? metrics.stickinessRatio.getGrowthRate() : 0
        return (rate >= 0 ? 'up' : 'down') as 'up' | 'down' | 'flat'
      })(),
      icon: h(RiseOutlined),
      color: '#eb2f96'
    }
  ]
})

const trendChartData = computed(() => {
  return activeUserStore.trendChartData
})

const distributionChartData = computed(() => {
  return activeUserStore.distributionChartData
})

const frequencyChartData = computed(() => {
  const data = activeUserStore.frequencyData
  return data?.frequencyData || null
})

const comparisonChartData = computed(() => {
  return activeUserStore.comparisonData
})

const tableData = computed(() => {
  // 将聚合数据转换为表格数据
  return []
})

// 方法
const handleFilterChange = (params: ActiveUserAnalysisRequest) => {
  activeUserStore.setQueryParams(params)
  loadData()
}

const handleRefresh = () => {
  loadData()
}

const handleExport = async () => {
  try {
    await activeUserStore.exportData('excel')
  } catch (error) {
    message.error('导出失败')
  }
}

const handleTrendTypeChange = () => {
  // 趋势图类型变化时重新加载趋势数据
  activeUserStore.fetchTrends()
}

const handleDistributionTypeChange = () => {
  // 分布类型变化时重新加载分布数据
  activeUserStore.fetchDistribution()
}

const handleProductLineConfirm = (productLineIds: number[]) => {
  selectedProductLines.value = productLineIds
  activeUserStore.fetchComparison(productLineIds)
  showProductLineSelector.value = false
}

const handleTableRefresh = () => {
  loadData()
}

const handleTableExport = () => {
  handleExport()
}

const loadData = async () => {
  try {
    await activeUserStore.refreshAllData()
  } catch (error) {
    message.error('加载数据失败')
  }
}

// 初始化
onMounted(() => {
  // 设置默认时间范围（最近30天）
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(endDate.getDate() - 29)
  
  filterParams.value.startDate = startDate.toISOString().split('T')[0]
  filterParams.value.endDate = endDate.toISOString().split('T')[0]
  
  activeUserStore.setQueryParams(filterParams.value)
  loadData()
})
</script>

<style scoped lang="less">
.active-user-analysis {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      .page-title {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .page-description {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }

    .header-actions {
      flex-shrink: 0;
    }
  }

  .filter-section {
    margin-bottom: 24px;
  }

  .metrics-section {
    margin-bottom: 24px;
  }

  .charts-section {
    margin-bottom: 24px;
  }

  .table-section {
    margin-bottom: 24px;
  }
}

@media (max-width: 768px) {
  .active-user-analysis {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }
  }
}
</style>
