<template>
  <div class="login-behavior-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <LoginOutlined />
          登录行为分析
        </h1>
        <p class="page-description">
          分析用户登录行为模式、频次、时间分布等关键指标，洞察用户活跃度和使用习惯
        </p>
      </div>
      
      <!-- 操作按钮 -->
      <div class="header-actions">
        <a-space>
          <a-button 
            type="primary" 
            :icon="h(ReloadOutlined)" 
            :loading="loading"
            @click="handleRefresh"
          >
            刷新数据
          </a-button>
          <a-button 
            :icon="h(DownloadOutlined)"
            :loading="loading"
            @click="handleExport"
          >
            导出数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <a-card :bordered="false" size="small">
        <div class="filter-content">
          <a-row :gutter="16" align="middle">
            <a-col :span="6">
              <div class="filter-item">
                <label>时间范围：</label>
                <a-range-picker
                  v-model:value="dateRange"
                  @change="handleDateChange"
                  style="width: 100%"
                />
              </div>
            </a-col>
            <a-col :span="6">
              <div class="filter-item">
                <label>产品线：</label>
                <a-select
                  v-model:value="selectedProductLines"
                  mode="multiple"
                  placeholder="选择产品线"
                  style="width: 100%"
                  @change="handleProductLineChange"
                >
                  <a-select-option value="reader">福昕阅读器</a-select-option>
                  <a-select-option value="editor">PDF编辑器</a-select-option>
                  <a-select-option value="cloud">云服务</a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="filter-item">
                <label>时间粒度：</label>
                <a-radio-group v-model:value="granularity" @change="handleGranularityChange">
                  <a-radio-button value="hour">小时</a-radio-button>
                  <a-radio-button value="day">天</a-radio-button>
                  <a-radio-button value="week">周</a-radio-button>
                </a-radio-group>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="filter-item">
                <a-button type="primary" @click="handleSearch">
                  <SearchOutlined />
                  查询
                </a-button>
                <a-button @click="handleReset" style="margin-left: 8px">
                  重置
                </a-button>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-section">
      <a-row :gutter="16">
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card :bordered="false" class="metric-card">
            <a-statistic
              title="总登录次数"
              :value="metrics.totalLogins"
              :loading="loading"
              suffix="次"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <LoginOutlined />
              </template>
            </a-statistic>
            <div class="metric-trend">
              <span class="trend-text">较昨日</span>
              <span :class="['trend-value', metrics.loginsTrend >= 0 ? 'positive' : 'negative']">
                <component :is="metrics.loginsTrend >= 0 ? RiseOutlined : FallOutlined" />
                {{ Math.abs(metrics.loginsTrend) }}%
              </span>
            </div>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card :bordered="false" class="metric-card">
            <a-statistic
              title="独立登录用户"
              :value="metrics.uniqueUsers"
              :loading="loading"
              suffix="人"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-statistic>
            <div class="metric-trend">
              <span class="trend-text">较昨日</span>
              <span :class="['trend-value', metrics.usersTrend >= 0 ? 'positive' : 'negative']">
                <component :is="metrics.usersTrend >= 0 ? RiseOutlined : FallOutlined" />
                {{ Math.abs(metrics.usersTrend) }}%
              </span>
            </div>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card :bordered="false" class="metric-card">
            <a-statistic
              title="平均登录频次"
              :value="metrics.avgFrequency"
              :loading="loading"
              :precision="2"
              suffix="次/人"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <BarChartOutlined />
              </template>
            </a-statistic>
            <div class="metric-trend">
              <span class="trend-text">较昨日</span>
              <span :class="['trend-value', metrics.frequencyTrend >= 0 ? 'positive' : 'negative']">
                <component :is="metrics.frequencyTrend >= 0 ? RiseOutlined : FallOutlined" />
                {{ Math.abs(metrics.frequencyTrend) }}%
              </span>
            </div>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card :bordered="false" class="metric-card">
            <a-statistic
              title="登录成功率"
              :value="metrics.successRate"
              :loading="loading"
              :precision="2"
              suffix="%"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
            <div class="metric-trend">
              <span class="trend-text">较昨日</span>
              <span :class="['trend-value', metrics.successRateTrend >= 0 ? 'positive' : 'negative']">
                <component :is="metrics.successRateTrend >= 0 ? RiseOutlined : FallOutlined" />
                {{ Math.abs(metrics.successRateTrend) }}%
              </span>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 图表分析区域 -->
    <div class="charts-section">
      <a-row :gutter="16">
        <!-- 登录趋势图 -->
        <a-col :xs="24" :lg="12">
          <a-card title="登录趋势分析" :bordered="false">
            <template #extra>
              <a-radio-group 
                v-model:value="trendType" 
                size="small"
                @change="handleTrendTypeChange"
              >
                <a-radio-button value="count">登录次数</a-radio-button>
                <a-radio-button value="users">登录用户</a-radio-button>
              </a-radio-group>
            </template>
            <div class="chart-container">
              <div v-if="loading" class="chart-loading">
                <a-spin size="large" />
              </div>
              <div v-else class="chart-placeholder">
                登录趋势图表区域
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 时间分布图 -->
        <a-col :xs="24" :lg="12">
          <a-card title="登录时间分布" :bordered="false">
            <div class="chart-container">
              <div v-if="loading" class="chart-loading">
                <a-spin size="large" />
              </div>
              <div v-else class="chart-placeholder">
                时间分布热力图区域
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 设备分布图 -->
        <a-col :xs="24" :lg="12">
          <a-card title="登录设备分布" :bordered="false">
            <div class="chart-container">
              <div v-if="loading" class="chart-loading">
                <a-spin size="large" />
              </div>
              <div v-else class="chart-placeholder">
                设备分布饼图区域
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 地域分布图 -->
        <a-col :xs="24" :lg="12">
          <a-card title="登录地域分布" :bordered="false">
            <div class="chart-container">
              <div v-if="loading" class="chart-loading">
                <a-spin size="large" />
              </div>
              <div v-else class="chart-placeholder">
                地域分布地图区域
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 详细数据表格 -->
    <div class="table-section">
      <a-card title="登录详细数据" :bordered="false">
        <template #extra>
          <a-space>
            <a-button size="small" @click="handleTableRefresh">
              <ReloadOutlined />
            </a-button>
            <a-button size="small" @click="handleTableExport">
              <DownloadOutlined />
            </a-button>
          </a-space>
        </template>
        <a-table
          :columns="tableColumns"
          :data-source="tableData"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="record.status === 'success' ? 'green' : 'red'">
                {{ record.status === 'success' ? '成功' : '失败' }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue'
import { message } from 'ant-design-vue'
import { 
  LoginOutlined,
  UserOutlined,
  ReloadOutlined,
  DownloadOutlined,
  SearchOutlined,
  BarChartOutlined,
  CheckCircleOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'

/**
 * 登录行为分析页面
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */

// 响应式数据
const loading = ref(false)
const dateRange = ref<[Dayjs, Dayjs]>([dayjs().subtract(30, 'day'), dayjs()])
const selectedProductLines = ref<string[]>([])
const granularity = ref('day')
const trendType = ref('count')

// 核心指标数据
const metrics = reactive({
  totalLogins: 125680,
  loginsTrend: 12.5,
  uniqueUsers: 45230,
  usersTrend: 8.3,
  avgFrequency: 2.78,
  frequencyTrend: -2.1,
  successRate: 98.5,
  successRateTrend: 0.8
})

// 表格配置
const tableColumns = [
  { title: '时间', dataIndex: 'time', key: 'time' },
  { title: '用户ID', dataIndex: 'userId', key: 'userId' },
  { title: '登录方式', dataIndex: 'method', key: 'method' },
  { title: '设备类型', dataIndex: 'device', key: 'device' },
  { title: 'IP地址', dataIndex: 'ip', key: 'ip' },
  { title: '地域', dataIndex: 'location', key: 'location' },
  { title: '状态', dataIndex: 'status', key: 'status' }
]

const tableData = ref([
  {
    key: '1',
    time: '2025-06-30 14:30:25',
    userId: 'user_12345',
    method: '账号密码',
    device: 'Windows PC',
    ip: '*************',
    location: '北京市',
    status: 'success'
  }
])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 1000,
  showSizeChanger: true,
  showQuickJumper: true
})

// 方法
const handleRefresh = async () => {
  loading.value = true
  try {
    // TODO: 替换为真实API调用
    // await loginApi.refreshData()

    // 临时提示，等待API实现
    console.log('登录数据刷新API待实现')
    message.success('数据刷新成功')
  } catch (error) {
    console.error('刷新数据失败:', error)
    message.error('刷新数据失败')
  } finally {
    loading.value = false
  }
}

const handleExport = () => {
  message.info('导出功能开发中...')
}

const handleDateChange = () => {
  // 处理日期范围变化
}

const handleProductLineChange = () => {
  // 处理产品线选择变化
}

const handleGranularityChange = () => {
  // 处理时间粒度变化
}

const handleSearch = async () => {
  loading.value = true
  try {
    // TODO: 替换为真实API调用
    // await loginApi.searchData(searchParams)

    // 临时提示，等待API实现
    console.log('登录数据查询API待实现')
    message.success('查询完成')
  } catch (error) {
    console.error('查询数据失败:', error)
    message.error('查询数据失败')
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  dateRange.value = [dayjs().subtract(30, 'day'), dayjs()]
  selectedProductLines.value = []
  granularity.value = 'day'
}

const handleTrendTypeChange = () => {
  // 处理趋势类型变化
}

const handleTableRefresh = () => {
  handleRefresh()
}

const handleTableExport = () => {
  handleExport()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

// 初始化
onMounted(() => {
  // 设置默认时间范围（最近7天）
  // 加载初始数据
})
</script>

<style scoped lang="less">
.login-behavior-analysis {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

    .header-content {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #262626;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .page-description {
        font-size: 14px;
        color: #8c8c8c;
        margin: 0;
        line-height: 1.5;
      }
    }
  }

  .filter-section {
    margin-bottom: 24px;

    .filter-content {
      .filter-item {
        label {
          display: block;
          margin-bottom: 4px;
          font-weight: 500;
          color: #262626;
        }
      }
    }
  }

  .metrics-section {
    margin-bottom: 24px;

    .metric-card {
      .metric-trend {
        margin-top: 8px;
        display: flex;
        align-items: center;
        gap: 8px;

        .trend-text {
          font-size: 12px;
          color: #8c8c8c;
        }

        .trend-value {
          font-size: 12px;
          font-weight: 500;
          display: flex;
          align-items: center;
          gap: 2px;

          &.positive {
            color: #52c41a;
          }

          &.negative {
            color: #ff4d4f;
          }
        }
      }
    }
  }

  .charts-section {
    margin-bottom: 24px;

    .chart-container {
      height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;

      .chart-loading {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
      }

      .chart-placeholder {
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .table-section {
    .ant-table {
      background: white;
    }
  }
}
</style>
