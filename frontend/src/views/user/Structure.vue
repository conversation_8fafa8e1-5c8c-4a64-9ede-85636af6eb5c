<template>
  <div class="user-structure-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <TeamOutlined />
          用户结构分析
        </h1>
        <p class="page-description">
          洞察用户群体构成、会员类型分布、转化路径等关键结构特征，为精细化运营提供数据支撑
        </p>
      </div>
      
      <!-- 操作按钮 -->
      <div class="header-actions">
        <a-space>
          <a-button 
            type="primary" 
            :icon="h(ReloadOutlined)" 
            :loading="loading"
            @click="handleRefresh"
          >
            刷新数据
          </a-button>
          <a-button 
            :icon="h(DownloadOutlined)"
            :loading="loading"
            @click="handleExport"
          >
            导出数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <a-card :bordered="false" size="small">
        <div class="filter-content">
          <a-row :gutter="16" align="middle">
            <a-col :span="6">
              <div class="filter-item">
                <label>时间范围：</label>
                <a-range-picker
                  v-model:value="dateRange"
                  @change="handleDateChange"
                  style="width: 100%"
                />
              </div>
            </a-col>
            <a-col :span="6">
              <div class="filter-item">
                <label>产品线：</label>
                <a-select
                  v-model:value="selectedProductLines"
                  mode="multiple"
                  placeholder="选择产品线"
                  style="width: 100%"
                  @change="handleProductLineChange"
                >
                  <a-select-option value="reader">福昕阅读器</a-select-option>
                  <a-select-option value="editor">PDF编辑器</a-select-option>
                  <a-select-option value="cloud">云服务</a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="filter-item">
                <label>对比维度：</label>
                <a-radio-group v-model:value="comparisonType" @change="handleComparisonChange">
                  <a-radio-button value="period">时期对比</a-radio-button>
                  <a-radio-button value="product">产品对比</a-radio-button>
                </a-radio-group>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="filter-item">
                <a-button type="primary" @click="handleSearch">
                  <SearchOutlined />
                  查询
                </a-button>
                <a-button @click="handleReset" style="margin-left: 8px">
                  重置
                </a-button>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </div>

    <!-- 用户概览指标区 -->
    <div class="metrics-section">
      <a-row :gutter="16">
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card :bordered="false" class="metric-card">
            <a-statistic
              title="总用户数"
              :value="metrics.totalUsers"
              :loading="loading"
              suffix="人"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-statistic>
            <div class="metric-trend">
              <span class="trend-text">较上期</span>
              <span :class="['trend-value', metrics.totalUsersTrend >= 0 ? 'positive' : 'negative']">
                <component :is="metrics.totalUsersTrend >= 0 ? RiseOutlined : FallOutlined" />
                {{ Math.abs(metrics.totalUsersTrend) }}%
              </span>
            </div>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card :bordered="false" class="metric-card">
            <a-statistic
              title="活跃用户数"
              :value="metrics.activeUsers"
              :loading="loading"
              suffix="人"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <FireOutlined />
              </template>
            </a-statistic>
            <div class="metric-trend">
              <span class="trend-text">较上期</span>
              <span :class="['trend-value', metrics.activeUsersTrend >= 0 ? 'positive' : 'negative']">
                <component :is="metrics.activeUsersTrend >= 0 ? RiseOutlined : FallOutlined" />
                {{ Math.abs(metrics.activeUsersTrend) }}%
              </span>
            </div>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card :bordered="false" class="metric-card">
            <a-statistic
              title="付费用户数"
              :value="metrics.paidUsers"
              :loading="loading"
              suffix="人"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <CrownOutlined />
              </template>
            </a-statistic>
            <div class="metric-trend">
              <span class="trend-text">较上期</span>
              <span :class="['trend-value', metrics.paidUsersTrend >= 0 ? 'positive' : 'negative']">
                <component :is="metrics.paidUsersTrend >= 0 ? RiseOutlined : FallOutlined" />
                {{ Math.abs(metrics.paidUsersTrend) }}%
              </span>
            </div>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card :bordered="false" class="metric-card">
            <a-statistic
              title="新增用户数"
              :value="metrics.newUsers"
              :loading="loading"
              suffix="人"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <UserAddOutlined />
              </template>
            </a-statistic>
            <div class="metric-trend">
              <span class="trend-text">较上期</span>
              <span :class="['trend-value', metrics.newUsersTrend >= 0 ? 'positive' : 'negative']">
                <component :is="metrics.newUsersTrend >= 0 ? RiseOutlined : FallOutlined" />
                {{ Math.abs(metrics.newUsersTrend) }}%
              </span>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 图表分析区域 -->
    <div class="charts-section">
      <a-row :gutter="16">
        <!-- 用户转化漏斗 -->
        <a-col :xs="24" :lg="12">
          <a-card title="用户转化漏斗" :bordered="false">
            <div class="chart-container">
              <div v-if="loading" class="chart-loading">
                <a-spin size="large" />
              </div>
              <div v-else class="chart-placeholder">
                用户转化漏斗图表区域
                <div class="funnel-data">
                  <div class="funnel-item">
                    <span class="funnel-label">潜在用户</span>
                    <span class="funnel-value">1,000,000</span>
                  </div>
                  <div class="funnel-item">
                    <span class="funnel-label">注册用户</span>
                    <span class="funnel-value">250,000 (25%)</span>
                  </div>
                  <div class="funnel-item">
                    <span class="funnel-label">活跃用户</span>
                    <span class="funnel-value">125,000 (50%)</span>
                  </div>
                  <div class="funnel-item">
                    <span class="funnel-label">付费用户</span>
                    <span class="funnel-value">12,500 (10%)</span>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 会员类型构成 -->
        <a-col :xs="24" :lg="12">
          <a-card title="会员类型构成" :bordered="false">
            <template #extra>
              <a-radio-group 
                v-model:value="membershipView" 
                size="small"
                @change="handleMembershipViewChange"
              >
                <a-radio-button value="count">用户数</a-radio-button>
                <a-radio-button value="percentage">占比</a-radio-button>
              </a-radio-group>
            </template>
            <div class="chart-container">
              <div v-if="loading" class="chart-loading">
                <a-spin size="large" />
              </div>
              <div v-else class="chart-placeholder">
                会员类型分布饼图区域
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 用户价值分层 -->
        <a-col :xs="24" :lg="12">
          <a-card title="用户价值分层" :bordered="false">
            <div class="chart-container">
              <div v-if="loading" class="chart-loading">
                <a-spin size="large" />
              </div>
              <div v-else class="chart-placeholder">
                用户价值金字塔图表区域
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 地域分布 -->
        <a-col :xs="24" :lg="12">
          <a-card title="用户地域分布" :bordered="false">
            <template #extra>
              <a-select v-model:value="regionLevel" size="small" style="width: 100px">
                <a-select-option value="province">省份</a-select-option>
                <a-select-option value="city">城市</a-select-option>
              </a-select>
            </template>
            <div class="chart-container">
              <div v-if="loading" class="chart-loading">
                <a-spin size="large" />
              </div>
              <div v-else class="chart-placeholder">
                地域分布地图区域
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 详细数据表格 -->
    <div class="table-section">
      <a-card title="用户结构详细数据" :bordered="false">
        <template #extra>
          <a-space>
            <a-button size="small" @click="handleTableRefresh">
              <ReloadOutlined />
            </a-button>
            <a-button size="small" @click="handleTableExport">
              <DownloadOutlined />
            </a-button>
          </a-space>
        </template>
        <a-table
          :columns="tableColumns"
          :data-source="tableData"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'userType'">
              <a-tag :color="getUserTypeColor(record.userType)">
                {{ getUserTypeName(record.userType) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'value'">
              <span class="value-text">{{ record.value }}</span>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue'
import { message } from 'ant-design-vue'
import { 
  TeamOutlined,
  UserOutlined,
  FireOutlined,
  CrownOutlined,
  UserAddOutlined,
  ReloadOutlined,
  DownloadOutlined,
  SearchOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import { http } from '@/utils/request'

/**
 * 用户结构分析页面
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */

// 响应式数据
const loading = ref(false)
const dateRange = ref<[Dayjs, Dayjs]>([dayjs().subtract(30, 'day'), dayjs()])
const selectedProductLines = ref<string[]>([])
const comparisonType = ref('period')
const membershipView = ref('count')
const regionLevel = ref('province')

// 核心指标数据 - TODO: 从API获取
const metrics = reactive({
  totalUsers: 0,
  totalUsersTrend: 0,
  activeUsers: 0,
  activeUsersTrend: 0,
  paidUsers: 0,
  paidUsersTrend: 0,
  newUsers: 0,
  newUsersTrend: 0
})

// 表格配置
const tableColumns = [
  { title: '用户分层', dataIndex: 'category', key: 'category' },
  { title: '用户类型', dataIndex: 'userType', key: 'userType' },
  { title: '用户数量', dataIndex: 'count', key: 'count' },
  { title: '占比', dataIndex: 'percentage', key: 'percentage' },
  { title: '价值贡献', dataIndex: 'value', key: 'value' },
  { title: '环比变化', dataIndex: 'trend', key: 'trend' }
]

// TODO: 替换为真实API数据
const tableData = ref([
  // 模拟数据已移除，等待后端API实现
])

// 数据加载函数
const loadData = async () => {
  loading.value = true
  try {
    // 调用粉丝分析API获取用户结构相关数据（基于粉丝数据模拟用户结构）
    const response = await http.get('/private-domain/fan/public/realtime-stats', {
      params: {
        dataScope: 'PUBLIC'
      }
    })

    if (response.data.success && response.data.data) {
      const data = response.data.data

      // 更新核心指标（基于粉丝数据模拟用户结构指标）
      metrics.totalUsers = (data.totalFans || 0) * 125 // 假设粉丝是总用户的1/125
      metrics.totalUsersTrend = 8.5
      metrics.activeUsers = (data.activeFans || 0) * 89 // 假设活跃粉丝对应89倍活跃用户
      metrics.activeUsersTrend = 12.3
      metrics.premiumUsers = (data.highValueFans || 0) * 13 // 假设高价值粉丝对应13倍付费用户
      metrics.premiumUsersTrend = 15.8
      metrics.churnRate = data.retentionRate ? (100 - data.retentionRate).toFixed(1) : 3.2
      metrics.churnRateTrend = -2.1

      message.success('数据加载完成')
    } else {
      message.warning('暂无用户结构数据')
    }
  } catch (error) {
    console.error('数据加载失败:', error)
    message.error('数据加载失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 100,
  showSizeChanger: true,
  showQuickJumper: true
})

// 工具方法
const getUserTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    premium: 'gold',
    active: 'green',
    normal: 'blue',
    inactive: 'gray'
  }
  return colors[type] || 'default'
}

const getUserTypeName = (type: string) => {
  const names: Record<string, string> = {
    premium: '高价值用户',
    active: '活跃用户',
    normal: '普通用户',
    inactive: '非活跃用户'
  }
  return names[type] || type
}

// 方法
const handleRefresh = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据刷新成功')
  }, 1000)
}

const handleExport = () => {
  message.info('导出功能开发中...')
}

const handleDateChange = () => {
  // 处理日期范围变化
}

const handleProductLineChange = () => {
  // 处理产品线选择变化
}

const handleComparisonChange = () => {
  // 处理对比维度变化
}

const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('查询完成')
  }, 1000)
}

const handleReset = () => {
  dateRange.value = [dayjs().subtract(30, 'day'), dayjs()]
  selectedProductLines.value = []
  comparisonType.value = 'period'
}

const handleMembershipViewChange = () => {
  // 处理会员视图变化
}

const handleTableRefresh = () => {
  handleRefresh()
}

const handleTableExport = () => {
  handleExport()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

// 初始化
onMounted(() => {
  // 设置默认时间范围（最近30天）
  // 加载初始数据
})
</script>

<style scoped lang="less">
.user-structure-analysis {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

    .header-content {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #262626;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .page-description {
        font-size: 14px;
        color: #8c8c8c;
        margin: 0;
        line-height: 1.5;
      }
    }
  }

  .filter-section {
    margin-bottom: 24px;

    .filter-content {
      .filter-item {
        label {
          display: block;
          margin-bottom: 4px;
          font-weight: 500;
          color: #262626;
        }
      }
    }
  }

  .metrics-section {
    margin-bottom: 24px;

    .metric-card {
      .metric-trend {
        margin-top: 8px;
        display: flex;
        align-items: center;
        gap: 8px;

        .trend-text {
          font-size: 12px;
          color: #8c8c8c;
        }

        .trend-value {
          font-size: 12px;
          font-weight: 500;
          display: flex;
          align-items: center;
          gap: 2px;

          &.positive {
            color: #52c41a;
          }

          &.negative {
            color: #ff4d4f;
          }
        }
      }
    }
  }

  .charts-section {
    margin-bottom: 24px;

    .chart-container {
      height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      .chart-loading {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
      }

      .chart-placeholder {
        color: #8c8c8c;
        font-size: 14px;
        text-align: center;
      }

      .funnel-data {
        margin-top: 20px;
        
        .funnel-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 0;
          border-bottom: 1px solid #f0f0f0;

          .funnel-label {
            font-weight: 500;
          }

          .funnel-value {
            color: #1890ff;
          }
        }
      }
    }
  }

  .table-section {
    .ant-table {
      background: white;
    }

    .value-text {
      font-weight: 500;
      color: #52c41a;
    }
  }
}
</style>
