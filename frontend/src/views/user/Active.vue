<template>
  <div class="user-active-page">
    <div class="page-header">
      <h1>活跃用户分析</h1>
      <p>分析用户活跃度趋势，了解用户参与情况</p>
    </div>
    
    <div class="content-wrapper">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-card title="活跃用户概览" class="overview-card">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic
                  title="日活跃用户(DAU)"
                  :value="statistics.dau"
                  suffix="人"
                  :value-style="{ color: '#3f8600' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="周活跃用户(WAU)"
                  :value="statistics.wau"
                  suffix="人"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="月活跃用户(MAU)"
                  :value="statistics.mau"
                  suffix="人"
                  :value-style="{ color: '#722ed1' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="活跃率"
                  :value="statistics.activeRate"
                  suffix="%"
                  :precision="2"
                  :value-style="{ color: '#cf1322' }"
                />
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>
      
      <a-row :gutter="16" style="margin-top: 16px;">
        <a-col :span="24">
          <a-card title="活跃用户趋势" class="chart-card">
            <div class="chart-container">
              <v-chart :option="chartOption" style="height: 400px;" />
            </div>
          </a-card>
        </a-col>
      </a-row>
      
      <a-row :gutter="16" style="margin-top: 16px;">
        <a-col :span="12">
          <a-card title="活跃度分布" class="chart-card">
            <div class="chart-container">
              <v-chart :option="pieChartOption" style="height: 300px;" />
            </div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="时段活跃分析" class="chart-card">
            <div class="chart-container">
              <v-chart :option="heatmapOption" style="height: 300px;" />
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import VChart from 'vue-echarts'
import type { EChartsOption } from 'echarts'

// 统计数据
const statistics = ref({
  dau: 12580,
  wau: 45230,
  mau: 156780,
  activeRate: 68.5
})

// 趋势图配置
const chartOption = ref<EChartsOption>({
  title: {
    text: '活跃用户趋势',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['DAU', 'WAU', 'MAU'],
    top: 30
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: 'DAU',
      type: 'line',
      data: [8200, 9100, 10500, 11200, 12000, 12800, 13500, 12900, 11800, 12200, 12800, 12580]
    },
    {
      name: 'WAU',
      type: 'line',
      data: [32000, 35000, 38000, 41000, 43000, 45000, 47000, 46000, 44000, 45000, 46000, 45230]
    },
    {
      name: 'MAU',
      type: 'line',
      data: [120000, 125000, 135000, 142000, 148000, 152000, 158000, 155000, 150000, 154000, 157000, 156780]
    }
  ]
})

// 饼图配置
const pieChartOption = ref<EChartsOption>({
  title: {
    text: '用户活跃度分布',
    left: 'center'
  },
  tooltip: {
    trigger: 'item'
  },
  series: [
    {
      type: 'pie',
      radius: '60%',
      data: [
        { value: 35, name: '高活跃用户' },
        { value: 33, name: '中活跃用户' },
        { value: 22, name: '低活跃用户' },
        { value: 10, name: '沉睡用户' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
})

// 热力图配置
const heatmapOption = ref<EChartsOption>({
  title: {
    text: '24小时活跃热力图',
    left: 'center'
  },
  tooltip: {
    position: 'top'
  },
  xAxis: {
    type: 'category',
    data: Array.from({ length: 24 }, (_, i) => `${i}:00`)
  },
  yAxis: {
    type: 'category',
    data: ['周日', '周六', '周五', '周四', '周三', '周二', '周一']
  },
  visualMap: {
    min: 0,
    max: 100,
    calculable: true,
    orient: 'horizontal',
    left: 'center',
    bottom: '10%'
  },
  series: [
    {
      type: 'heatmap',
      data: generateHeatmapData(),
      label: {
        show: false
      }
    }
  ]
})

// 生成热力图数据 - TODO: 替换为真实API数据
function generateHeatmapData() {
  // 临时使用空数据，等待API实现
  console.log('热力图数据API待实现')
  return []
}

onMounted(() => {
  // 组件挂载后的初始化逻辑
  console.log('活跃用户分析页面已加载')
})
</script>

<style scoped lang="less">
.user-active-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: #262626;
  }
  
  p {
    margin: 0;
    color: #8c8c8c;
    font-size: 14px;
  }
}

.content-wrapper {
  .overview-card {
    .ant-statistic {
      text-align: center;
    }
  }
  
  .chart-card {
    .chart-container {
      padding: 16px 0;
    }
  }
}
</style>
