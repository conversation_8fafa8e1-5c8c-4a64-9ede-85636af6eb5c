<template>
  <div class="settings-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">系统设置</h1>
          <p class="page-description">个性化您的系统使用体验和偏好设置</p>
        </div>
        <div class="header-right">
          <a-space>
            <a-button @click="handleReset">
              <template #icon><ReloadOutlined /></template>
              重置设置
            </a-button>
            <a-button type="primary" @click="handleSaveAll" :loading="saveLoading">
              <template #icon><SaveOutlined /></template>
              保存所有设置
            </a-button>
          </a-space>
        </div>
      </div>
    </div>

    <div class="settings-content">
      <a-row :gutter="24">
        <!-- 左侧设置菜单 -->
        <a-col :span="6">
          <a-card class="settings-menu">
            <a-menu
              v-model:selectedKeys="selectedKeys"
              mode="inline"
              @click="handleMenuClick"
            >
              <a-menu-item key="appearance">
                <template #icon><BgColorsOutlined /></template>
                界面外观
              </a-menu-item>
              <a-menu-item key="language">
                <template #icon><GlobalOutlined /></template>
                语言地区
              </a-menu-item>
              <a-menu-item key="notifications">
                <template #icon><BellOutlined /></template>
                通知设置
              </a-menu-item>
              <a-menu-item key="security">
                <template #icon><SafetyOutlined /></template>
                安全隐私
              </a-menu-item>
              <a-menu-item key="data">
                <template #icon><DatabaseOutlined /></template>
                数据管理
              </a-menu-item>
              <a-menu-item key="advanced">
                <template #icon><SettingOutlined /></template>
                高级设置
              </a-menu-item>
            </a-menu>
          </a-card>
        </a-col>

        <!-- 右侧设置内容 -->
        <a-col :span="18">
          <!-- 界面外观设置 -->
          <a-card v-show="activeSection === 'appearance'" title="界面外观设置">
            <a-form layout="vertical">
              <a-form-item label="主题模式">
                <a-radio-group v-model:value="settings.theme" @change="handleThemeChange">
                  <a-radio value="light">浅色主题</a-radio>
                  <a-radio value="dark">深色主题</a-radio>
                  <a-radio value="auto">跟随系统</a-radio>
                </a-radio-group>
              </a-form-item>

              <a-form-item label="主题色彩">
                <div class="color-picker-group">
                  <div 
                    v-for="color in themeColors" 
                    :key="color.value"
                    class="color-option"
                    :class="{ active: settings.primaryColor === color.value }"
                    @click="settings.primaryColor = color.value"
                  >
                    <div class="color-circle" :style="{ backgroundColor: color.color }"></div>
                    <span>{{ color.name }}</span>
                  </div>
                </div>
              </a-form-item>

              <a-form-item label="侧边栏设置">
                <a-space direction="vertical" style="width: 100%">
                  <a-checkbox v-model:checked="settings.sidebarCollapsed">
                    默认收起侧边栏
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.fixedSidebar">
                    固定侧边栏
                  </a-checkbox>
                </a-space>
              </a-form-item>

              <a-form-item label="页面布局">
                <a-radio-group v-model:value="settings.layout">
                  <a-radio value="side">侧边布局</a-radio>
                  <a-radio value="top">顶部布局</a-radio>
                  <a-radio value="mix">混合布局</a-radio>
                </a-radio-group>
              </a-form-item>

              <a-form-item label="内容区域">
                <a-space direction="vertical" style="width: 100%">
                  <a-checkbox v-model:checked="settings.fixedHeader">
                    固定页头
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.showBreadcrumb">
                    显示面包屑导航
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.showFooter">
                    显示页脚
                  </a-checkbox>
                </a-space>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 语言地区设置 -->
          <a-card v-show="activeSection === 'language'" title="语言地区设置">
            <a-form layout="vertical">
              <a-form-item label="界面语言">
                <a-select v-model:value="settings.language" style="width: 200px">
                  <a-select-option value="zh-CN">简体中文</a-select-option>
                  <a-select-option value="zh-TW">繁體中文</a-select-option>
                  <a-select-option value="en-US">English</a-select-option>
                  <a-select-option value="ja-JP">日本語</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="时区设置">
                <a-select v-model:value="settings.timezone" style="width: 300px">
                  <a-select-option value="Asia/Shanghai">中国标准时间 (UTC+8)</a-select-option>
                  <a-select-option value="Asia/Tokyo">日本标准时间 (UTC+9)</a-select-option>
                  <a-select-option value="America/New_York">美国东部时间 (UTC-5)</a-select-option>
                  <a-select-option value="Europe/London">格林威治时间 (UTC+0)</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="日期格式">
                <a-radio-group v-model:value="settings.dateFormat">
                  <a-radio value="YYYY-MM-DD">2024-06-22</a-radio>
                  <a-radio value="MM/DD/YYYY">06/22/2024</a-radio>
                  <a-radio value="DD/MM/YYYY">22/06/2024</a-radio>
                </a-radio-group>
              </a-form-item>

              <a-form-item label="时间格式">
                <a-radio-group v-model:value="settings.timeFormat">
                  <a-radio value="24">24小时制</a-radio>
                  <a-radio value="12">12小时制</a-radio>
                </a-radio-group>
              </a-form-item>

              <a-form-item label="数字格式">
                <a-select v-model:value="settings.numberFormat" style="width: 200px">
                  <a-select-option value="1,234.56">1,234.56 (英文)</a-select-option>
                  <a-select-option value="1 234,56">1 234,56 (法文)</a-select-option>
                  <a-select-option value="1.234,56">1.234,56 (德文)</a-select-option>
                </a-select>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 通知设置 -->
          <a-card v-show="activeSection === 'notifications'" title="通知设置">
            <a-form layout="vertical">
              <a-form-item label="桌面通知">
                <a-space direction="vertical" style="width: 100%">
                  <a-checkbox v-model:checked="settings.notifications.desktop">
                    启用桌面通知
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.notifications.sound">
                    通知声音
                  </a-checkbox>
                </a-space>
              </a-form-item>

              <a-form-item label="系统通知">
                <a-space direction="vertical" style="width: 100%">
                  <a-checkbox v-model:checked="settings.notifications.system">
                    系统维护通知
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.notifications.security">
                    安全警告通知
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.notifications.updates">
                    系统更新通知
                  </a-checkbox>
                </a-space>
              </a-form-item>

              <a-form-item label="业务通知">
                <a-space direction="vertical" style="width: 100%">
                  <a-checkbox v-model:checked="settings.notifications.dataAlert">
                    数据异常警报
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.notifications.reportReady">
                    报表生成完成
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.notifications.taskComplete">
                    任务完成通知
                  </a-checkbox>
                </a-space>
              </a-form-item>

              <a-form-item label="邮件通知">
                <a-space direction="vertical" style="width: 100%">
                  <a-checkbox v-model:checked="settings.notifications.email">
                    启用邮件通知
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.notifications.weeklyReport">
                    每周数据报告
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.notifications.monthlyReport">
                    每月数据报告
                  </a-checkbox>
                </a-space>
              </a-form-item>

              <a-form-item label="通知时间">
                <a-time-range-picker 
                  v-model:value="settings.notifications.quietHours"
                  format="HH:mm"
                  :placeholder="['开始时间', '结束时间']"
                />
                <div style="margin-top: 8px; color: #8c8c8c; font-size: 12px;">
                  设置免打扰时间段，此时间段内不会收到通知
                </div>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 安全隐私设置 -->
          <a-card v-show="activeSection === 'security'" title="安全隐私设置">
            <a-form layout="vertical">
              <a-form-item label="会话管理">
                <a-space direction="vertical" style="width: 100%">
                  <div class="setting-item">
                    <span>自动登出时间</span>
                    <a-select v-model:value="settings.security.sessionTimeout" style="width: 150px">
                      <a-select-option value="30">30分钟</a-select-option>
                      <a-select-option value="60">1小时</a-select-option>
                      <a-select-option value="120">2小时</a-select-option>
                      <a-select-option value="480">8小时</a-select-option>
                    </a-select>
                  </div>
                  <a-checkbox v-model:checked="settings.security.rememberLogin">
                    记住登录状态
                  </a-checkbox>
                </a-space>
              </a-form-item>

              <a-form-item label="登录安全">
                <a-space direction="vertical" style="width: 100%">
                  <a-checkbox v-model:checked="settings.security.twoFactor">
                    启用双因素认证
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.security.loginNotification">
                    异地登录通知
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.security.deviceBinding">
                    设备绑定验证
                  </a-checkbox>
                </a-space>
              </a-form-item>

              <a-form-item label="数据隐私">
                <a-space direction="vertical" style="width: 100%">
                  <a-checkbox v-model:checked="settings.security.dataEncryption">
                    本地数据加密
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.security.auditLog">
                    操作审计日志
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.security.dataExport">
                    允许数据导出
                  </a-checkbox>
                </a-space>
              </a-form-item>

              <a-form-item label="隐私设置">
                <a-space direction="vertical" style="width: 100%">
                  <a-checkbox v-model:checked="settings.security.analytics">
                    参与使用情况分析
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.security.crashReport">
                    自动发送错误报告
                  </a-checkbox>
                </a-space>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 数据管理设置 -->
          <a-card v-show="activeSection === 'data'" title="数据管理设置">
            <a-form layout="vertical">
              <a-form-item label="数据缓存">
                <a-space direction="vertical" style="width: 100%">
                  <div class="setting-item">
                    <span>缓存大小限制</span>
                    <a-select v-model:value="settings.data.cacheSize" style="width: 150px">
                      <a-select-option value="100">100MB</a-select-option>
                      <a-select-option value="500">500MB</a-select-option>
                      <a-select-option value="1000">1GB</a-select-option>
                      <a-select-option value="2000">2GB</a-select-option>
                    </a-select>
                  </div>
                  <a-checkbox v-model:checked="settings.data.autoCleanCache">
                    自动清理过期缓存
                  </a-checkbox>
                </a-space>
              </a-form-item>

              <a-form-item label="数据同步">
                <a-space direction="vertical" style="width: 100%">
                  <a-checkbox v-model:checked="settings.data.autoSync">
                    自动同步数据
                  </a-checkbox>
                  <div class="setting-item">
                    <span>同步频率</span>
                    <a-select v-model:value="settings.data.syncInterval" style="width: 150px">
                      <a-select-option value="5">5分钟</a-select-option>
                      <a-select-option value="15">15分钟</a-select-option>
                      <a-select-option value="30">30分钟</a-select-option>
                      <a-select-option value="60">1小时</a-select-option>
                    </a-select>
                  </div>
                </a-space>
              </a-form-item>

              <a-form-item label="数据备份">
                <a-space direction="vertical" style="width: 100%">
                  <a-checkbox v-model:checked="settings.data.autoBackup">
                    自动备份数据
                  </a-checkbox>
                  <div class="setting-item">
                    <span>备份保留时间</span>
                    <a-select v-model:value="settings.data.backupRetention" style="width: 150px">
                      <a-select-option value="7">7天</a-select-option>
                      <a-select-option value="30">30天</a-select-option>
                      <a-select-option value="90">90天</a-select-option>
                      <a-select-option value="365">1年</a-select-option>
                    </a-select>
                  </div>
                </a-space>
              </a-form-item>

              <a-form-item label="数据清理">
                <a-space>
                  <a-button @click="handleClearCache">
                    清理缓存
                  </a-button>
                  <a-button @click="handleClearLogs">
                    清理日志
                  </a-button>
                  <a-button @click="handleExportData">
                    导出数据
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 高级设置 -->
          <a-card v-show="activeSection === 'advanced'" title="高级设置">
            <a-form layout="vertical">
              <a-form-item label="开发者选项">
                <a-space direction="vertical" style="width: 100%">
                  <a-checkbox v-model:checked="settings.advanced.debugMode">
                    启用调试模式
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.advanced.showConsole">
                    显示控制台
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.advanced.verboseLogging">
                    详细日志记录
                  </a-checkbox>
                </a-space>
              </a-form-item>

              <a-form-item label="实验性功能">
                <a-space direction="vertical" style="width: 100%">
                  <a-checkbox v-model:checked="settings.advanced.betaFeatures">
                    启用测试版功能
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.advanced.previewFeatures">
                    启用预览功能
                  </a-checkbox>
                </a-space>
              </a-form-item>

              <a-form-item label="性能优化">
                <a-space direction="vertical" style="width: 100%">
                  <a-checkbox v-model:checked="settings.advanced.lazyLoading">
                    启用懒加载
                  </a-checkbox>
                  <a-checkbox v-model:checked="settings.advanced.virtualScrolling">
                    启用虚拟滚动
                  </a-checkbox>
                  <div class="setting-item">
                    <span>页面大小</span>
                    <a-select v-model:value="settings.advanced.pageSize" style="width: 150px">
                      <a-select-option value="10">10条/页</a-select-option>
                      <a-select-option value="20">20条/页</a-select-option>
                      <a-select-option value="50">50条/页</a-select-option>
                      <a-select-option value="100">100条/页</a-select-option>
                    </a-select>
                  </div>
                </a-space>
              </a-form-item>

              <a-form-item label="系统重置">
                <a-space>
                  <a-popconfirm
                    title="确定要重置所有设置吗？此操作不可撤销。"
                    @confirm="handleResetAll"
                  >
                    <a-button danger>
                      重置所有设置
                    </a-button>
                  </a-popconfirm>
                  <a-button @click="handleExportSettings">
                    导出设置
                  </a-button>
                  <a-button @click="handleImportSettings">
                    导入设置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  SaveOutlined,
  BgColorsOutlined,
  GlobalOutlined,
  BellOutlined,
  SafetyOutlined,
  DatabaseOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

// 当前选中的设置菜单
const selectedKeys = ref(['appearance'])
const activeSection = ref('appearance')

// 保存加载状态
const saveLoading = ref(false)

// 主题色彩选项
const themeColors = [
  { name: '拂晓蓝', value: 'daybreak', color: '#1890ff' },
  { name: '薄暮', value: 'dust', color: '#F5222D' },
  { name: '火山', value: 'volcano', color: '#FA541C' },
  { name: '日暮', value: 'sunset', color: '#FAAD14' },
  { name: '明青', value: 'cyan', color: '#13C2C2' },
  { name: '极光绿', value: 'green', color: '#52C41A' },
  { name: '极客蓝', value: 'geekblue', color: '#2F54EB' },
  { name: '酱紫', value: 'purple', color: '#722ED1' }
]

// 系统设置数据
const settings = reactive({
  // 界面外观
  theme: 'light',
  primaryColor: 'daybreak',
  sidebarCollapsed: false,
  fixedSidebar: true,
  layout: 'side',
  fixedHeader: true,
  showBreadcrumb: true,
  showFooter: false,

  // 语言地区
  language: 'zh-CN',
  timezone: 'Asia/Shanghai',
  dateFormat: 'YYYY-MM-DD',
  timeFormat: '24',
  numberFormat: '1,234.56',

  // 通知设置
  notifications: {
    desktop: true,
    sound: true,
    system: true,
    security: true,
    updates: true,
    dataAlert: true,
    reportReady: true,
    taskComplete: false,
    email: true,
    weeklyReport: true,
    monthlyReport: false,
    quietHours: null
  },

  // 安全隐私
  security: {
    sessionTimeout: '60',
    rememberLogin: true,
    twoFactor: false,
    loginNotification: true,
    deviceBinding: false,
    dataEncryption: true,
    auditLog: true,
    dataExport: true,
    analytics: true,
    crashReport: true
  },

  // 数据管理
  data: {
    cacheSize: '500',
    autoCleanCache: true,
    autoSync: true,
    syncInterval: '15',
    autoBackup: true,
    backupRetention: '30'
  },

  // 高级设置
  advanced: {
    debugMode: false,
    showConsole: false,
    verboseLogging: false,
    betaFeatures: false,
    previewFeatures: false,
    lazyLoading: true,
    virtualScrolling: true,
    pageSize: '20'
  }
})

// 方法定义
const handleMenuClick = (info: any) => {
  const key = String(info.key)
  activeSection.value = key
  selectedKeys.value = [key]
}

const handleThemeChange = () => {
  message.info(`主题已切换为${settings.theme === 'light' ? '浅色' : settings.theme === 'dark' ? '深色' : '自动'}模式`)
}

const handleSaveAll = async () => {
  try {
    saveLoading.value = true

    // TODO: 调用真实API保存设置
    // await systemConfigApi.saveUserSettings(settings)

    // 保存到本地存储
    localStorage.setItem('userSettings', JSON.stringify(settings))
    message.success('设置保存成功')
  } catch (error) {
    console.error('保存设置失败:', error)
    message.error('设置保存失败')
  } finally {
    saveLoading.value = false
  }
}

const handleReset = () => {
  // 重置当前分类的设置
  message.info(`已重置${getMenuTitle(activeSection.value)}设置`)
}

const handleResetAll = () => {
  // 重置所有设置为默认值
  Object.assign(settings, getDefaultSettings())
  message.success('所有设置已重置为默认值')
}

const handleClearCache = () => {
  message.success('缓存清理完成')
}

const handleClearLogs = () => {
  message.success('日志清理完成')
}

const handleExportData = () => {
  message.success('数据导出完成')
}

const handleExportSettings = () => {
  const dataStr = JSON.stringify(settings, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = 'settings.json'
  link.click()
  URL.revokeObjectURL(url)
  message.success('设置导出完成')
}

const handleImportSettings = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = (e: any) => {
    const file = e.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e: any) => {
        try {
          const importedSettings = JSON.parse(e.target.result)
          Object.assign(settings, importedSettings)
          message.success('设置导入完成')
        } catch (error) {
          message.error('设置文件格式错误')
        }
      }
      reader.readAsText(file)
    }
  }
  input.click()
}

const getMenuTitle = (key: string) => {
  const titles: Record<string, string> = {
    appearance: '界面外观',
    language: '语言地区',
    notifications: '通知设置',
    security: '安全隐私',
    data: '数据管理',
    advanced: '高级设置'
  }
  return titles[key] || key
}

const getDefaultSettings = () => {
  return {
    theme: 'light',
    primaryColor: 'daybreak',
    sidebarCollapsed: false,
    fixedSidebar: true,
    layout: 'side',
    fixedHeader: true,
    showBreadcrumb: true,
    showFooter: false,
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    dateFormat: 'YYYY-MM-DD',
    timeFormat: '24',
    numberFormat: '1,234.56',
    notifications: {
      desktop: true,
      sound: true,
      system: true,
      security: true,
      updates: true,
      dataAlert: true,
      reportReady: true,
      taskComplete: false,
      email: true,
      weeklyReport: true,
      monthlyReport: false,
      quietHours: null
    },
    security: {
      sessionTimeout: '60',
      rememberLogin: true,
      twoFactor: false,
      loginNotification: true,
      deviceBinding: false,
      dataEncryption: true,
      auditLog: true,
      dataExport: true,
      analytics: true,
      crashReport: true
    },
    data: {
      cacheSize: '500',
      autoCleanCache: true,
      autoSync: true,
      syncInterval: '15',
      autoBackup: true,
      backupRetention: '30'
    },
    advanced: {
      debugMode: false,
      showConsole: false,
      verboseLogging: false,
      betaFeatures: false,
      previewFeatures: false,
      lazyLoading: true,
      virtualScrolling: true,
      pageSize: '20'
    }
  }
}

// 初始化
onMounted(() => {
  // 从本地存储加载设置
  const savedSettings = localStorage.getItem('userSettings')
  if (savedSettings) {
    try {
      const parsed = JSON.parse(savedSettings)
      Object.assign(settings, parsed)
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  }
})
</script>

<style scoped lang="less">
.settings-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;

  .page-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .header-left {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 28px;
          font-weight: 600;
          color: #262626;
        }

        .page-description {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  .settings-content {
    .settings-menu {
      position: sticky;
      top: 24px;
    }

    .color-picker-group {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      .color-option {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        padding: 8px;
        border-radius: 6px;
        transition: all 0.3s;

        &:hover {
          background: #f5f5f5;
        }

        &.active {
          background: #e6f7ff;
          border: 1px solid #1890ff;
        }

        .color-circle {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          margin-bottom: 8px;
          border: 2px solid #fff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        span {
          font-size: 12px;
          color: #666;
        }
      }
    }

    .setting-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      margin-bottom: 8px;
    }
  }
}
</style>
