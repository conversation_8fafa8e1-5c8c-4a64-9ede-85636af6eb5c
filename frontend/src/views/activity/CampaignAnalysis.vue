<template>
  <div class="campaign-analysis">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>活动效果分析</h1>
      <p>分析营销活动的参与度和转化效果，优化活动策略和ROI</p>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <a-card>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="时间范围">
              <a-range-picker
                v-model:value="dateRange"
                :presets="datePresets"
                @change="handleDateChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="活动类型">
              <a-select
                v-model:value="selectedCampaignTypes"
                mode="multiple"
                placeholder="选择活动类型"
                @change="handleCampaignTypeChange"
              >
                <a-select-option value="promotion">促销活动</a-select-option>
                <a-select-option value="brand">品牌推广</a-select-option>
                <a-select-option value="product">产品发布</a-select-option>
                <a-select-option value="user">用户活动</a-select-option>
                <a-select-option value="holiday">节日营销</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="活动状态">
              <a-select
                v-model:value="selectedStatus"
                placeholder="选择状态"
                @change="handleStatusChange"
              >
                <a-select-option value="">全部状态</a-select-option>
                <a-select-option value="planning">策划中</a-select-option>
                <a-select-option value="running">进行中</a-select-option>
                <a-select-option value="ended">已结束</a-select-option>
                <a-select-option value="paused">已暂停</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="投放渠道">
              <a-select
                v-model:value="selectedChannels"
                mode="multiple"
                placeholder="选择渠道"
                @change="handleChannelChange"
              >
                <a-select-option value="wechat">微信</a-select-option>
                <a-select-option value="weibo">微博</a-select-option>
                <a-select-option value="douyin">抖音</a-select-option>
                <a-select-option value="xiaohongshu">小红书</a-select-option>
                <a-select-option value="email">邮件</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 核心指标卡片 -->
    <a-row :gutter="16" class="metrics-row">
      <a-col :span="6">
        <StatCard
          title="活动总数"
          :value="metrics.totalCampaigns"
          suffix="个"
          trend="up"
          :trend-value="12.5"
          color="#E91E63"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="平均参与率"
          :value="metrics.avgParticipationRate"
          suffix="%"
          trend="up"
          :trend-value="5.8"
          color="#52c41a"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="总转化数"
          :value="metrics.totalConversions"
          suffix="次"
          trend="up"
          :trend-value="18.3"
          color="#1890ff"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="平均ROI"
          :value="metrics.avgROI"
          suffix=":1"
          trend="up"
          :trend-value="0.8"
          color="#faad14"
        />
      </a-col>
    </a-row>

    <!-- 图表区域 -->
    <a-row :gutter="16" class="charts-row">
      <!-- 活动效果趋势 -->
      <a-col :span="12">
        <a-card title="活动效果趋势" class="chart-card">
          <div ref="effectTrendChartRef" class="chart-container"></div>
        </a-card>
      </a-col>

      <!-- 活动类型效果对比 -->
      <a-col :span="12">
        <a-card title="活动类型效果对比" class="chart-card">
          <div ref="typeComparisonChartRef" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" class="charts-row">
      <!-- 渠道效果分析 -->
      <a-col :span="12">
        <a-card title="渠道效果分析" class="chart-card">
          <div ref="channelEffectChartRef" class="chart-container"></div>
        </a-card>
      </a-col>

      <!-- 参与转化漏斗 -->
      <a-col :span="12">
        <a-card title="参与转化漏斗" class="chart-card">
          <div ref="conversionFunnelChartRef" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 活动ROI分析 -->
    <a-row :gutter="16" class="charts-row">
      <a-col :span="24">
        <a-card title="活动ROI分析" class="chart-card">
          <div ref="roiAnalysisChartRef" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 活动详情表格 -->
    <a-card title="活动详情" class="table-card">
      <a-table
        :columns="campaignColumns"
        :data-source="campaignList"
        :pagination="pagination"
        :loading="loading"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'campaignType'">
            <a-tag :color="getCampaignTypeColor(record.campaignType)">
              {{ getCampaignTypeText(record.campaignType) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'participationRate'">
            <a-progress
              :percent="record.participationRate"
              size="small"
              :stroke-color="getParticipationColor(record.participationRate)"
            />
          </template>
          <template v-if="column.key === 'roi'">
            <span :class="getRoiClass(record.roi)">
              {{ record.roi }}:1
            </span>
          </template>
          <template v-if="column.key === 'budget'">
            <span class="budget">¥{{ record.budget.toLocaleString() }}</span>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="viewCampaignDetail(record)">
                查看详情
              </a-button>
              <a-button type="link" @click="analyzeCampaign(record)">
                深度分析
              </a-button>
              <a-button type="link" @click="optimizeCampaign(record)">
                优化建议
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import dayjs, { type Dayjs } from 'dayjs'
import * as echarts from 'echarts'
import StatCard from '@/components/StatCard.vue'
import { http } from '@/utils/request'

// 响应式数据
const dateRange = ref<[Dayjs, Dayjs]>([dayjs().subtract(30, 'day'), dayjs()])
const selectedCampaignTypes = ref<string[]>([])
const selectedStatus = ref<string>('')
const selectedChannels = ref<string[]>([])
const loading = ref(false)

// 图表引用
const effectTrendChartRef = ref<HTMLElement>()
const typeComparisonChartRef = ref<HTMLElement>()
const channelEffectChartRef = ref<HTMLElement>()
const conversionFunnelChartRef = ref<HTMLElement>()
const roiAnalysisChartRef = ref<HTMLElement>()

// 核心指标
const metrics = reactive({
  totalCampaigns: 48,
  avgParticipationRate: 15.8,
  totalConversions: 12580,
  avgROI: 3.2
})

// 日期预设
const datePresets = [
  { label: '最近7天', value: [dayjs().subtract(7, 'day'), dayjs()] },
  { label: '最近30天', value: [dayjs().subtract(30, 'day'), dayjs()] },
  { label: '最近90天', value: [dayjs().subtract(90, 'day'), dayjs()] }
]

// 表格配置
const campaignColumns = [
  { title: '活动名称', dataIndex: 'campaignName', key: 'campaignName' },
  { title: '活动类型', dataIndex: 'campaignType', key: 'campaignType' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '预算', dataIndex: 'budget', key: 'budget' },
  { title: '参与率', dataIndex: 'participationRate', key: 'participationRate' },
  { title: '转化数', dataIndex: 'conversions', key: 'conversions' },
  { title: 'ROI', dataIndex: 'roi', key: 'roi' },
  { title: '开始时间', dataIndex: 'startTime', key: 'startTime' },
  { title: '操作', key: 'action' }
]

// TODO: 替换为真实API数据
const campaignList = ref([
  // 模拟数据已移除，等待后端API实现
])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 48,
  showSizeChanger: true,
  showQuickJumper: true
})

// 事件处理
const handleDateChange = (dates: any) => {
  console.log('Date range changed:', dates)
  loadData()
}

const handleCampaignTypeChange = (values: any) => {
  const campaignTypes = Array.isArray(values) ? values : []
  console.log('Campaign types changed:', campaignTypes)
  loadData()
}

const handleStatusChange = (value: any) => {
  const status = String(value || '')
  console.log('Status changed:', status)
  loadData()
}

const handleChannelChange = (values: any) => {
  const channels = Array.isArray(values) ? values : []
  console.log('Channels changed:', channels)
  loadData()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const viewCampaignDetail = (record: any) => {
  message.info(`查看活动详情: ${record.campaignName}`)
}

const analyzeCampaign = (record: any) => {
  message.info(`深度分析: ${record.campaignName}`)
}

const optimizeCampaign = (record: any) => {
  message.info(`优化建议: ${record.campaignName}`)
}

const getCampaignTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    promotion: 'red',
    brand: 'blue',
    product: 'green',
    user: 'orange',
    holiday: 'purple'
  }
  return colors[type] || 'default'
}

const getCampaignTypeText = (type: string) => {
  const texts: Record<string, string> = {
    promotion: '促销活动',
    brand: '品牌推广',
    product: '产品发布',
    user: '用户活动',
    holiday: '节日营销'
  }
  return texts[type] || type
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    planning: 'blue',
    running: 'green',
    ended: 'default',
    paused: 'orange'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    planning: '策划中',
    running: '进行中',
    ended: '已结束',
    paused: '已暂停'
  }
  return texts[status] || status
}

const getParticipationColor = (rate: number) => {
  if (rate >= 20) return '#52c41a'
  if (rate >= 15) return '#faad14'
  if (rate >= 10) return '#ff7a45'
  return '#ff4d4f'
}

const getRoiClass = (roi: number) => {
  if (roi >= 4) return 'excellent-roi'
  if (roi >= 3) return 'good-roi'
  if (roi >= 2) return 'fair-roi'
  return 'poor-roi'
}

// 数据加载
const loadData = async () => {
  loading.value = true
  try {
    // 获取日期范围
    const startDate = dateRange.value?.[0]?.format('YYYY-MM-DD') || '2025-07-01'
    const endDate = dateRange.value?.[1]?.format('YYYY-MM-DD') || '2025-08-04'

    // 调用活动分析API
    const response = await http.get('/activity/campaign/public/overview', {
      params: {
        startDate,
        endDate,
        campaignTypes: selectedCampaignTypes.value.length > 0 ? selectedCampaignTypes.value : undefined,
        statuses: selectedStatus.value ? [selectedStatus.value] : undefined,
        dataScope: 'PUBLIC'
      }
    })

    if (response.data.success && response.data.data) {
      const data = response.data.data

      // 更新核心指标
      if (data.coreMetrics) {
        metrics.totalCampaigns = data.coreMetrics.totalCampaigns?.value || 0
        metrics.totalConversions = data.coreMetrics.totalConversions?.value || 0
        metrics.avgParticipationRate = data.coreMetrics.avgClickRate?.value || 0
        metrics.avgROI = data.coreMetrics.avgConversionRate?.value || 0
      }

      // 更新活动列表
      if (data.campaignList) {
        campaignList.value = data.campaignList.map((campaign: any) => ({
          key: campaign.campaignId,
          campaignName: campaign.campaignName,
          campaignType: campaign.campaignType,
          status: campaign.status,
          budget: campaign.budget,
          actualCost: campaign.actualCost,
          impressions: campaign.impressions,
          clicks: campaign.clicks,
          conversions: campaign.conversions,
          clickRate: campaign.clickRate,
          conversionRate: campaign.conversionRate,
          roi: campaign.roi,
          startTime: campaign.startTime,
          endTime: campaign.endTime
        }))
      }

      message.success('数据加载完成')
    } else {
      message.warning('暂无活动数据')
    }
  } catch (error) {
    console.error('数据加载失败:', error)
    message.error('数据加载失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 活动效果趋势图
    if (effectTrendChartRef.value) {
      const effectTrendChart = echarts.init(effectTrendChartRef.value)
      effectTrendChart.setOption({
        tooltip: { trigger: 'axis' },
        legend: { data: ['参与率', '转化率', 'ROI'] },
        xAxis: { type: 'category', data: ['1月', '2月', '3月', '4月', '5月', '6月'] },
        yAxis: [
          { type: 'value', name: '百分比(%)', position: 'left' },
          { type: 'value', name: 'ROI', position: 'right' }
        ],
        series: [
          {
            name: '参与率',
            type: 'line',
            data: [12.5, 14.2, 16.8, 15.3, 17.1, 15.8],
            smooth: true,
            itemStyle: { color: '#E91E63' }
          },
          {
            name: '转化率',
            type: 'line',
            data: [3.2, 3.8, 4.1, 3.9, 4.5, 4.2],
            smooth: true,
            itemStyle: { color: '#1890ff' }
          },
          {
            name: 'ROI',
            type: 'line',
            yAxisIndex: 1,
            data: [2.8, 3.1, 3.5, 3.2, 3.8, 3.2],
            smooth: true,
            itemStyle: { color: '#faad14' }
          }
        ]
      })
    }

    // 其他图表类似初始化...
  })
}

// 生命周期
onMounted(() => {
  loadData()
  initCharts()
})
</script>

<style scoped>
.campaign-analysis {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
}

.filter-section {
  margin-bottom: 24px;
}

.metrics-row {
  margin-bottom: 24px;
}

.charts-row {
  margin-bottom: 24px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
}

.table-card {
  margin-bottom: 24px;
}

.budget {
  font-weight: 600;
  color: #ff4d4f;
}

.excellent-roi {
  color: #52c41a;
  font-weight: 600;
}

.good-roi {
  color: #1890ff;
  font-weight: 600;
}

.fair-roi {
  color: #faad14;
  font-weight: 600;
}

.poor-roi {
  color: #ff4d4f;
  font-weight: 600;
}
</style>
