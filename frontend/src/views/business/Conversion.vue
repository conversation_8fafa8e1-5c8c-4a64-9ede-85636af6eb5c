<template>
  <div class="conversion-analysis">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>付费转化分析</h1>
      <p>分析用户从访问到付费的转化路径，优化转化漏斗和提升付费率</p>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <a-card>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="时间范围">
              <a-range-picker
                v-model:value="dateRange"
                :presets="datePresets"
                @change="handleDateChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="产品线">
              <a-select
                v-model:value="selectedProductLines"
                mode="multiple"
                placeholder="选择产品线"
                @change="handleProductLineChange"
              >
                <a-select-option
                  v-for="product in productLines"
                  :key="product.id"
                  :value="product.id"
                >
                  {{ product.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="用户类型">
              <a-select
                v-model:value="selectedUserType"
                placeholder="选择用户类型"
                @change="handleUserTypeChange"
              >
                <a-select-option value="">全部用户</a-select-option>
                <a-select-option value="new">新用户</a-select-option>
                <a-select-option value="old">老用户</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="渠道来源">
              <a-select
                v-model:value="selectedChannel"
                placeholder="选择渠道"
                @change="handleChannelChange"
              >
                <a-select-option value="">全部渠道</a-select-option>
                <a-select-option value="organic">自然流量</a-select-option>
                <a-select-option value="paid">付费推广</a-select-option>
                <a-select-option value="social">社交媒体</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 核心指标卡片 -->
    <a-row :gutter="16" class="metrics-row">
      <a-col :span="6">
        <StatCard
          title="整体转化率"
          :value="metrics.overallConversion"
          suffix="%"
          trend="up"
          :trend-value="2.3"
          color="#1890ff"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="新用户转化率"
          :value="metrics.newUserConversion"
          suffix="%"
          trend="up"
          :trend-value="1.8"
          color="#52c41a"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="平均转化时长"
          :value="metrics.avgConversionTime"
          suffix="天"
          trend="down"
          :trend-value="0.5"
          color="#722ed1"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="转化用户数"
          :value="metrics.convertedUsers"
          suffix="人"
          trend="up"
          :trend-value="12.7"
          color="#faad14"
        />
      </a-col>
    </a-row>

    <!-- 转化漏斗图 -->
    <a-row :gutter="16" class="charts-row">
      <a-col :span="24">
        <a-card title="转化漏斗分析" class="chart-card">
          <div ref="funnelChartRef" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 转化趋势和渠道分析 -->
    <a-row :gutter="16" class="charts-row">
      <a-col :span="12">
        <a-card title="转化率趋势" class="chart-card">
          <div ref="trendChartRef" class="chart-container"></div>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="渠道转化对比" class="chart-card">
          <div ref="channelChartRef" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 用户转化路径分析 -->
    <a-card title="用户转化路径" class="path-card">
      <a-table
        :columns="pathColumns"
        :data-source="pathData"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'conversionRate'">
            <a-progress
              :percent="record.conversionRate"
              size="small"
              :stroke-color="getProgressColor(record.conversionRate)"
            />
          </template>
          <template v-if="column.key === 'dropRate'">
            <span :class="{ 'high-drop': record.dropRate > 50 }">
              {{ record.dropRate }}%
            </span>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import dayjs, { type Dayjs } from 'dayjs'
import * as echarts from 'echarts'
import StatCard from '@/components/StatCard.vue'
import { http } from '@/utils/request'

// 响应式数据
const dateRange = ref<[Dayjs, Dayjs]>([dayjs().subtract(30, 'day'), dayjs()])
const selectedProductLines = ref<number[]>([])
const selectedUserType = ref<string>('')
const selectedChannel = ref<string>('')

// 图表引用
const funnelChartRef = ref<HTMLElement>()
const trendChartRef = ref<HTMLElement>()
const channelChartRef = ref<HTMLElement>()

// 核心指标
const metrics = reactive({
  overallConversion: 3.2,
  newUserConversion: 2.8,
  avgConversionTime: 5.2,
  convertedUsers: 1580
})

// 产品线数据
const productLines = ref([
  { id: 1, name: 'PDF Reader' },
  { id: 2, name: 'PDF Editor' },
  { id: 3, name: 'PDF365' },
  { id: 4, name: '智慧打印' }
])

// 日期预设
const datePresets = [
  { label: '最近7天', value: [dayjs().subtract(7, 'day'), dayjs()] },
  { label: '最近30天', value: [dayjs().subtract(30, 'day'), dayjs()] },
  { label: '最近90天', value: [dayjs().subtract(90, 'day'), dayjs()] }
]

// 转化路径表格配置
const pathColumns = [
  { title: '转化步骤', dataIndex: 'step', key: 'step' },
  { title: '用户数', dataIndex: 'userCount', key: 'userCount' },
  { title: '转化率', dataIndex: 'conversionRate', key: 'conversionRate' },
  { title: '流失率', dataIndex: 'dropRate', key: 'dropRate' },
  { title: '平均停留时间', dataIndex: 'avgTime', key: 'avgTime' }
]

const pathData = ref([
  {
    key: '1',
    step: '访问首页',
    userCount: 50000,
    conversionRate: 100,
    dropRate: 0,
    avgTime: '2分30秒'
  },
  {
    key: '2',
    step: '浏览产品',
    userCount: 35000,
    conversionRate: 70,
    dropRate: 30,
    avgTime: '5分15秒'
  },
  {
    key: '3',
    step: '注册账号',
    userCount: 12000,
    conversionRate: 24,
    dropRate: 66,
    avgTime: '3分45秒'
  },
  {
    key: '4',
    step: '试用功能',
    userCount: 8000,
    conversionRate: 16,
    dropRate: 33,
    avgTime: '15分30秒'
  },
  {
    key: '5',
    step: '完成付费',
    userCount: 1600,
    conversionRate: 3.2,
    dropRate: 80,
    avgTime: '2分10秒'
  }
])

// 事件处理
const handleDateChange = (dates: any) => {
  console.log('Date range changed:', dates)
  loadData()
}

const handleProductLineChange = (values: any) => {
  const productLines = Array.isArray(values) ? values : []
  console.log('Product lines changed:', productLines)
  loadData()
}

const handleUserTypeChange = (value: any) => {
  const userType = String(value || '')
  console.log('User type changed:', userType)
  loadData()
}

const handleChannelChange = (value: any) => {
  const channel = String(value || '')
  console.log('Channel changed:', channel)
  loadData()
}

const getProgressColor = (rate: number) => {
  if (rate >= 80) return '#52c41a'
  if (rate >= 60) return '#faad14'
  if (rate >= 40) return '#ff7a45'
  return '#ff4d4f'
}

// 数据加载
const loadData = async () => {
  loading.value = true
  try {
    // 调用粉丝分析API获取转化相关数据（基于粉丝数据模拟转化）
    const response = await http.get('/private-domain/fan/public/realtime-stats', {
      params: {
        dataScope: 'PUBLIC'
      }
    })

    if (response.data.success && response.data.data) {
      const data = response.data.data

      // 更新核心指标（基于粉丝数据模拟转化指标）
      metrics.conversionRate = data.retentionRate || 85.2
      metrics.totalConversions = data.activeFans || 1250
      metrics.avgConversionTime = data.avgActivityLevel ? (data.avgActivityLevel / 10).toFixed(1) : 5.8
      metrics.conversionValue = (data.totalFans || 0) * 128 // 假设每个转化价值128元

      message.success('数据加载完成')
    } else {
      message.warning('暂无转化数据')
    }
  } catch (error) {
    console.error('数据加载失败:', error)
    message.error('数据加载失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 漏斗图
    if (funnelChartRef.value) {
      const funnelChart = echarts.init(funnelChartRef.value)
      funnelChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c}%'
        },
        series: [{
          name: '转化漏斗',
          type: 'funnel',
          left: '10%',
          top: 60,
          bottom: 60,
          width: '80%',
          min: 0,
          max: 100,
          minSize: '0%',
          maxSize: '100%',
          sort: 'descending',
          gap: 2,
          label: {
            show: true,
            position: 'inside'
          },
          data: [
            { value: 100, name: '访问首页' },
            { value: 70, name: '浏览产品' },
            { value: 24, name: '注册账号' },
            { value: 16, name: '试用功能' },
            { value: 3.2, name: '完成付费' }
          ]
        }]
      })
    }

    // 趋势图
    if (trendChartRef.value) {
      const trendChart = echarts.init(trendChartRef.value)
      trendChart.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: { type: 'category', data: ['1月', '2月', '3月', '4月', '5月', '6月'] },
        yAxis: { type: 'value', axisLabel: { formatter: '{value}%' } },
        series: [{
          data: [2.8, 3.1, 2.9, 3.3, 3.0, 3.2],
          type: 'line',
          smooth: true,
          name: '转化率'
        }]
      })
    }

    // 渠道对比图
    if (channelChartRef.value) {
      const channelChart = echarts.init(channelChartRef.value)
      channelChart.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: { type: 'category', data: ['自然流量', '付费推广', '社交媒体', '邮件营销', '直接访问'] },
        yAxis: { type: 'value', axisLabel: { formatter: '{value}%' } },
        series: [{
          data: [3.2, 4.1, 2.8, 3.5, 2.9],
          type: 'bar',
          name: '转化率'
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  loadData()
  initCharts()
})
</script>

<style scoped>
.conversion-analysis {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
}

.filter-section {
  margin-bottom: 24px;
}

.metrics-row {
  margin-bottom: 24px;
}

.charts-row {
  margin-bottom: 24px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
}

.path-card {
  margin-bottom: 24px;
}

.high-drop {
  color: #ff4d4f;
  font-weight: 600;
}
</style>
