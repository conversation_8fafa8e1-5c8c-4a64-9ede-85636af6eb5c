<template>
  <div class="welcome-container">
    <div class="welcome-content">
      <div class="welcome-header">
        <h1>欢迎使用 SCRM-Next</h1>
        <p>企业级数据分析平台</p>
      </div>
      
      <div class="welcome-features">
        <a-row :gutter="[24, 24]">
          <a-col :xs="24" :sm="12" :md="8">
            <a-card class="feature-card">
              <template #cover>
                <div class="feature-icon">
                  <DashboardOutlined />
                </div>
              </template>
              <a-card-meta
                title="数据看板"
                description="实时展示核心业务指标，总览仪表盘和产品线仪表盘"
              />
            </a-card>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8">
            <a-card class="feature-card">
              <template #cover>
                <div class="feature-icon">
                  <UserOutlined />
                </div>
              </template>
              <a-card-meta
                title="用户分析"
                description="活跃度分析、增长分析、留存分析、用户分群"
              />
            </a-card>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8">
            <a-card class="feature-card">
              <template #cover>
                <div class="feature-icon">
                  <BarChartOutlined />
                </div>
              </template>
              <a-card-meta
                title="行为分析"
                description="事件分析、路径分析、漏斗分析、功能使用分析"
              />
            </a-card>
          </a-col>
        </a-row>
      </div>
      
      <div class="welcome-actions">
        <a-space size="large">
          <a-button type="primary" size="large" @click="goToLogin">
            立即登录
          </a-button>
          <a-button size="large" @click="viewDemo">
            查看演示
          </a-button>
        </a-space>
      </div>
      
      <div class="welcome-info">
        <a-alert
          message="系统状态"
          description="前端应用已成功启动，所有组件加载正常"
          type="success"
          show-icon
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import {
  DashboardOutlined,
  UserOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue'

const router = useRouter()

const goToLogin = () => {
  router.push('/login')
}

const viewDemo = () => {
  // 这里可以跳转到演示页面或显示演示内容
  console.log('查看演示')
}
</script>

<style lang="less" scoped>
.welcome-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.welcome-content {
  max-width: 1200px;
  width: 100%;
  text-align: center;
}

.welcome-header {
  margin-bottom: 48px;
  
  h1 {
    font-size: 48px;
    font-weight: 700;
    color: white;
    margin: 0 0 16px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  p {
    font-size: 20px;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
  }
}

.welcome-features {
  margin-bottom: 48px;
  
  .feature-card {
    height: 100%;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }
    
    .feature-icon {
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #1e88e5, #1976d2);
      
      .anticon {
        font-size: 32px;
        color: white;
      }
    }
  }
}

.welcome-actions {
  margin-bottom: 48px;
  
  .ant-btn {
    height: 48px;
    padding: 0 32px;
    font-size: 16px;
    border-radius: 24px;
  }
}

.welcome-info {
  max-width: 600px;
  margin: 0 auto;
}

// 响应式设计
@media (max-width: 768px) {
  .welcome-header {
    h1 {
      font-size: 32px;
    }
    
    p {
      font-size: 16px;
    }
  }
  
  .welcome-actions {
    .ant-space {
      flex-direction: column;
      width: 100%;
      
      .ant-btn {
        width: 100%;
      }
    }
  }
}
</style>
