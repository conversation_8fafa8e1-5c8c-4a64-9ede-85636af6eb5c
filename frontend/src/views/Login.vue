<template>
  <div class="login-container">
    <!-- 左侧品牌区域 -->
    <div class="brand-section">
      <div class="brand-content">
        <div class="brand-logo">
          <div class="logo-icon">
            <BarChartOutlined />
          </div>
          <h1 class="brand-title">SCRM-Next</h1>
        </div>

        <div class="brand-description">
          <h2>智能数据分析平台</h2>
          <p>为企业提供全方位的数据洞察与商业决策支持</p>

          <div class="feature-highlights">
            <div class="feature-item">
              <CheckCircleOutlined />
              <span>实时数据监控</span>
            </div>
            <div class="feature-item">
              <CheckCircleOutlined />
              <span>智能分析报告</span>
            </div>
            <div class="feature-item">
              <CheckCircleOutlined />
              <span>多维度数据可视化</span>
            </div>
          </div>
        </div>

        <!-- 装饰性图形 -->
        <div class="geometric-shapes">
          <div class="shape shape-1"></div>
          <div class="shape shape-2"></div>
          <div class="shape shape-3"></div>
        </div>
      </div>
    </div>

    <!-- 右侧登录区域 -->
    <div class="login-section">
      <div class="login-card">
        <div class="login-header">
          <h3>欢迎回来</h3>
          <p>请登录您的账户以继续</p>
        </div>

        <a-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          layout="vertical"
          class="login-form"
          @finish="handleLogin"
        >
          <a-form-item name="username">
            <label class="form-label">用户名</label>
            <a-input
              v-model:value="formData.username"
              size="large"
              placeholder="输入您的用户名"
              class="modern-input"
            />
          </a-form-item>

          <a-form-item name="password">
            <label class="form-label">密码</label>
            <a-input-password
              v-model:value="formData.password"
              size="large"
              placeholder="输入您的密码"
              class="modern-input"
            />
          </a-form-item>

          <div class="form-options">
            <a-checkbox v-model:checked="formData.rememberMe" class="remember-checkbox">
              记住我
            </a-checkbox>
            <a href="#" class="forgot-link">忘记密码？</a>
          </div>

          <a-button
            type="primary"
            html-type="submit"
            size="large"
            block
            :loading="loading"
            class="login-button"
          >
            <span v-if="!loading">登录</span>
            <span v-else>登录中...</span>
          </a-button>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { h, ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { BarChartOutlined, CheckCircleOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import { useUserStore } from '@/stores/user'
import type { LoginRequest } from '@/types'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)

const formData = reactive<LoginRequest>({
  username: 'admin',
  password: 'admin123',
  rememberMe: false
})

// 表单验证规则
const rules: any = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  try {
    loading.value = true
    
    await userStore.login(formData)
    
    message.success('登录成功')
    
    // 获取重定向地址，默认跳转到总览仪表盘
    const redirect = (route.query.redirect as string) || '/dashboard/overview'
    router.push(redirect)
    
  } catch (error: any) {
    console.error('登录失败:', error)
    message.error(error.message || '登录失败，请检查用户名和密码')
  } finally {
    loading.value = false
  }
}

// 页面标题
onMounted(() => {
  document.title = '用户登录 - SCRM-Next'
})
</script>

<style lang="less" scoped>
.login-container {
  display: flex;
  min-height: 100vh;
  background: #f8fafc;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

// 左侧品牌区域
.brand-section {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  .brand-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 500px;
    padding: 0 40px;
  }

  .brand-logo {
    margin-bottom: 48px;

    .logo-icon {
      width: 80px;
      height: 80px;
      background: rgba(255, 255, 255, 0.15);
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 24px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);

      .anticon {
        font-size: 36px;
        color: white;
      }
    }

    .brand-title {
      font-size: 42px;
      font-weight: 700;
      margin: 0;
      letter-spacing: -0.02em;
    }
  }

  .brand-description {
    h2 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 16px 0;
      opacity: 0.95;
    }

    p {
      font-size: 16px;
      opacity: 0.8;
      line-height: 1.6;
      margin: 0 0 40px 0;
    }
  }

  .feature-highlights {
    text-align: left;

    .feature-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 14px;
      opacity: 0.9;

      .anticon {
        margin-right: 12px;
        color: #4ade80;
        font-size: 16px;
      }
    }
  }

  // 装饰性几何图形
  .geometric-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;

    .shape {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.05);
      animation: float 8s ease-in-out infinite;

      &.shape-1 {
        width: 300px;
        height: 300px;
        top: -150px;
        right: -150px;
        animation-delay: 0s;
      }

      &.shape-2 {
        width: 200px;
        height: 200px;
        bottom: -100px;
        left: -100px;
        animation-delay: 3s;
      }

      &.shape-3 {
        width: 150px;
        height: 150px;
        top: 50%;
        left: -75px;
        animation-delay: 6s;
      }
    }
  }
}

// 右侧登录区域
.login-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: white;

  .login-card {
    width: 100%;
    max-width: 420px;

    .login-header {
      text-align: center;
      margin-bottom: 40px;

      h3 {
        font-size: 28px;
        font-weight: 700;
        color: #1e293b;
        margin: 0 0 8px 0;
        letter-spacing: -0.02em;
      }

      p {
        font-size: 16px;
        color: #64748b;
        margin: 0;
      }
    }

    .login-form {
      .form-label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #374151;
        margin-bottom: 8px;
      }

      .modern-input {
        border-radius: 12px;
        border: 1.5px solid #e2e8f0;
        transition: all 0.2s ease;

        &:hover {
          border-color: #cbd5e1;
        }

        &:focus,
        &.ant-input-focused {
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .ant-input {
          border: none;
          box-shadow: none;
          padding: 12px 16px;
          font-size: 15px;

          &:focus {
            box-shadow: none;
          }
        }
      }

      .ant-form-item {
        margin-bottom: 24px;
      }

      .form-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 32px;

        .remember-checkbox {
          font-size: 14px;
          color: #64748b;

          .ant-checkbox-checked .ant-checkbox-inner {
            background-color: #667eea;
            border-color: #667eea;
          }
        }

        .forgot-link {
          font-size: 14px;
          color: #667eea;
          text-decoration: none;
          font-weight: 500;

          &:hover {
            color: #5a67d8;
            text-decoration: underline;
          }
        }
      }

      .login-button {
        height: 48px;
        border-radius: 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        font-size: 16px;
        font-weight: 600;
        letter-spacing: 0.02em;
        transition: all 0.2s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        &:active {
          transform: translateY(0);
        }

        &.ant-btn-loading {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
      }
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .login-container {
    flex-direction: column;
  }

  .brand-section {
    min-height: 40vh;

    .brand-content {
      padding: 40px 20px;
    }

    .brand-logo .brand-title {
      font-size: 32px;
    }

    .brand-description h2 {
      font-size: 20px;
    }
  }

  .login-section {
    padding: 20px;

    .login-card {
      max-width: 400px;
    }
  }
}

@media (max-width: 768px) {
  .brand-section {
    min-height: 35vh;

    .brand-content {
      padding: 30px 20px;
    }

    .brand-logo {
      margin-bottom: 32px;

      .logo-icon {
        width: 60px;
        height: 60px;

        .anticon {
          font-size: 28px;
        }
      }

      .brand-title {
        font-size: 28px;
      }
    }

    .brand-description {
      h2 {
        font-size: 18px;
      }

      p {
        font-size: 14px;
        margin-bottom: 24px;
      }
    }

    .feature-highlights {
      display: none;
    }
  }

  .login-section {
    padding: 20px 16px;

    .login-card .login-header {
      margin-bottom: 32px;

      h3 {
        font-size: 24px;
      }

      p {
        font-size: 14px;
      }
    }
  }
}
</style>
