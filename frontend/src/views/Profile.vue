<template>
  <div class="profile-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">个人信息</h1>
          <p class="page-description">管理您的个人资料和账户设置</p>
        </div>
      </div>
    </div>

    <div class="profile-content">
      <a-row :gutter="24">
        <!-- 左侧个人信息卡片 -->
        <a-col :span="8">
          <a-card class="profile-card">
            <div class="profile-avatar-section">
              <a-upload
                :show-upload-list="false"
                :before-upload="beforeUpload"
                @change="handleAvatarChange"
                accept="image/*"
              >
                <div class="avatar-upload">
                  <a-avatar :size="120" :src="userInfo?.avatar">
                    {{ userInfo?.realName?.charAt(0) || userInfo?.username?.charAt(0) }}
                  </a-avatar>
                  <div class="upload-overlay">
                    <CameraOutlined />
                    <div>更换头像</div>
                  </div>
                </div>
              </a-upload>
              
              <div class="profile-basic-info">
                <h3>{{ userInfo?.realName || userInfo?.username }}</h3>
                <p class="user-role">{{ getRoleText(userInfo?.roles?.[0]) }}</p>
                <p class="user-email">{{ userInfo?.email }}</p>
              </div>
            </div>

            <a-divider />

            <div class="profile-stats">
              <div class="stat-item">
                <div class="stat-value">{{ profileStats.loginCount }}</div>
                <div class="stat-label">登录次数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ profileStats.lastLogin }}</div>
                <div class="stat-label">最后登录</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ profileStats.accountAge }}</div>
                <div class="stat-label">账户年龄</div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 右侧信息编辑区 -->
        <a-col :span="16">
          <a-tabs v-model:activeKey="activeTab" type="card">
            <!-- 基本信息 -->
            <a-tab-pane key="basic" tab="基本信息">
              <a-card>
                <a-form
                  ref="basicFormRef"
                  :model="basicForm"
                  :rules="basicRules"
                  layout="vertical"
                  @finish="handleBasicSubmit"
                >
                  <a-row :gutter="16">
                    <a-col :span="12">
                      <a-form-item label="用户名" name="username">
                        <a-input 
                          v-model:value="basicForm.username" 
                          disabled
                          placeholder="用户名不可修改"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col :span="12">
                      <a-form-item label="真实姓名" name="realName">
                        <a-input 
                          v-model:value="basicForm.realName" 
                          placeholder="请输入真实姓名"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>

                  <a-row :gutter="16">
                    <a-col :span="12">
                      <a-form-item label="邮箱地址" name="email">
                        <a-input 
                          v-model:value="basicForm.email" 
                          placeholder="请输入邮箱地址"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col :span="12">
                      <a-form-item label="手机号码" name="phone">
                        <a-input 
                          v-model:value="basicForm.phone" 
                          placeholder="请输入手机号码"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>

                  <a-row :gutter="16">
                    <a-col :span="12">
                      <a-form-item label="所属公司" name="company">
                        <a-input 
                          v-model:value="basicForm.company" 
                          placeholder="请输入所属公司"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col :span="12">
                      <a-form-item label="部门" name="department">
                        <a-input 
                          v-model:value="basicForm.department" 
                          placeholder="请输入部门"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>

                  <a-form-item label="个人简介" name="bio">
                    <a-textarea 
                      v-model:value="basicForm.bio" 
                      placeholder="请输入个人简介"
                      :rows="4"
                    />
                  </a-form-item>

                  <a-form-item>
                    <a-space>
                      <a-button type="primary" html-type="submit" :loading="basicLoading">
                        保存修改
                      </a-button>
                      <a-button @click="resetBasicForm">
                        重置
                      </a-button>
                    </a-space>
                  </a-form-item>
                </a-form>
              </a-card>
            </a-tab-pane>

            <!-- 安全设置 -->
            <a-tab-pane key="security" tab="安全设置">
              <a-card title="修改密码">
                <a-form
                  ref="passwordFormRef"
                  :model="passwordForm"
                  :rules="passwordRules"
                  layout="vertical"
                  @finish="handlePasswordSubmit"
                >
                  <a-form-item label="当前密码" name="currentPassword">
                    <a-input-password 
                      v-model:value="passwordForm.currentPassword" 
                      placeholder="请输入当前密码"
                    />
                  </a-form-item>

                  <a-form-item label="新密码" name="newPassword">
                    <a-input-password 
                      v-model:value="passwordForm.newPassword" 
                      placeholder="请输入新密码"
                    />
                  </a-form-item>

                  <a-form-item label="确认新密码" name="confirmPassword">
                    <a-input-password 
                      v-model:value="passwordForm.confirmPassword" 
                      placeholder="请再次输入新密码"
                    />
                  </a-form-item>

                  <a-form-item>
                    <a-space>
                      <a-button type="primary" html-type="submit" :loading="passwordLoading">
                        修改密码
                      </a-button>
                      <a-button @click="resetPasswordForm">
                        重置
                      </a-button>
                    </a-space>
                  </a-form-item>
                </a-form>
              </a-card>

              <a-card title="安全信息" style="margin-top: 24px;">
                <a-descriptions :column="1" bordered>
                  <a-descriptions-item label="账户状态">
                    <a-tag color="green">正常</a-tag>
                  </a-descriptions-item>
                  <a-descriptions-item label="最后登录时间">
                    {{ formatDate(userInfo?.lastLoginTime) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="最后登录IP">
                    {{ userInfo?.lastLoginIp || '-' }}
                  </a-descriptions-item>
                  <a-descriptions-item label="账户创建时间">
                    {{ formatDate(userInfo?.createdAt) }}
                  </a-descriptions-item>
                </a-descriptions>
              </a-card>
            </a-tab-pane>

            <!-- 操作日志 -->
            <a-tab-pane key="logs" tab="操作日志">
              <a-card>
                <a-table
                  :columns="logColumns"
                  :data-source="userLogs"
                  :loading="logLoading"
                  :pagination="logPagination"
                  size="small"
                  @change="handleLogTableChange"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'action'">
                      <a-tag :color="getActionColor(record.action)">
                        {{ getActionText(record.action) }}
                      </a-tag>
                    </template>
                    <template v-else-if="column.key === 'createdAt'">
                      {{ formatDate(record.createdAt) }}
                    </template>
                  </template>
                </a-table>
              </a-card>
            </a-tab-pane>
          </a-tabs>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { CameraOutlined } from '@ant-design/icons-vue'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import type { TableColumnsType, UploadProps } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'

dayjs.extend(relativeTime)

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

// 当前活跃标签
const activeTab = ref('basic')

// 加载状态
const basicLoading = ref(false)
const passwordLoading = ref(false)
const logLoading = ref(false)

// 表单引用
const basicFormRef = ref()
const passwordFormRef = ref()

// 基本信息表单
const basicForm = reactive({
  username: '',
  realName: '',
  email: '',
  phone: '',
  company: '',
  department: '',
  bio: ''
})

// 密码修改表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 个人统计信息
const profileStats = reactive({
  loginCount: 156,
  lastLogin: '2小时前',
  accountAge: '2年3个月'
})

// 用户操作日志
const userLogs = ref([
  {
    id: 1,
    action: 'login',
    description: '用户登录系统',
    ip: '*************',
    createdAt: '2024-06-22T10:30:00Z'
  },
  {
    id: 2,
    action: 'update_profile',
    description: '修改个人信息',
    ip: '*************',
    createdAt: '2024-06-21T16:45:00Z'
  },
  {
    id: 3,
    action: 'change_password',
    description: '修改登录密码',
    ip: '*************',
    createdAt: '2024-06-20T09:15:00Z'
  }
])

// 日志表格列配置
const logColumns: TableColumnsType = [
  {
    title: '操作类型',
    key: 'action',
    width: 120
  },
  {
    title: '操作描述',
    dataIndex: 'description',
    key: 'description'
  },
  {
    title: 'IP地址',
    dataIndex: 'ip',
    key: 'ip',
    width: 150
  },
  {
    title: '操作时间',
    key: 'createdAt',
    width: 180
  }
]

// 日志分页配置
const logPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 50,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表单验证规则
const basicRules: Record<string, Rule[]> = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

const passwordRules: Record<string, Rule[]> = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string) => {
        if (value && value !== passwordForm.newPassword) {
          return Promise.reject('两次输入的密码不一致')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

// 工具方法
const formatDate = (dateStr?: string) => {
  if (!dateStr) return '-'
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss')
}

const getRoleText = (role?: string) => {
  const texts: Record<string, string> = {
    super_admin: '超级管理员',
    admin: '管理员',
    user: '普通用户',
    viewer: '只读用户'
  }
  return texts[role || ''] || '普通用户'
}

const getActionColor = (action: string) => {
  const colors: Record<string, string> = {
    login: 'blue',
    logout: 'gray',
    update_profile: 'green',
    change_password: 'orange'
  }
  return colors[action] || 'default'
}

const getActionText = (action: string) => {
  const texts: Record<string, string> = {
    login: '登录',
    logout: '登出',
    update_profile: '修改信息',
    change_password: '修改密码'
  }
  return texts[action] || action
}

// 头像上传处理
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('只能上传 JPG/PNG 格式的图片!')
    return false
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过 2MB!')
    return false
  }
  return false // 阻止自动上传
}

const handleAvatarChange = (info: any) => {
  if (info.file) {
    // 这里可以处理头像上传逻辑
    message.success('头像上传成功')
  }
}

// 表单提交处理
const handleBasicSubmit = async () => {
  try {
    basicLoading.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 更新用户信息
    userStore.updateUserInfo(basicForm)
    message.success('个人信息更新成功')
  } catch (error) {
    message.error('更新失败，请重试')
  } finally {
    basicLoading.value = false
  }
}

const handlePasswordSubmit = async () => {
  try {
    passwordLoading.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    message.success('密码修改成功')
    resetPasswordForm()
  } catch (error) {
    message.error('密码修改失败，请重试')
  } finally {
    passwordLoading.value = false
  }
}

// 表单重置
const resetBasicForm = () => {
  if (userInfo.value) {
    Object.assign(basicForm, {
      username: userInfo.value.username,
      realName: userInfo.value.realName || '',
      email: userInfo.value.email || '',
      phone: userInfo.value.phone || '',
      company: userInfo.value.company || '',
      department: userInfo.value.department || '',
      bio: userInfo.value.bio || ''
    })
  }
}

const resetPasswordForm = () => {
  Object.assign(passwordForm, {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
}

// 日志表格处理
const handleLogTableChange = (pag: any) => {
  logPagination.current = pag.current
  logPagination.pageSize = pag.pageSize
  // 这里可以加载日志数据
}

// 初始化
onMounted(() => {
  resetBasicForm()
})
</script>

<style scoped lang="less">
.profile-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;

  .page-header {
    margin-bottom: 24px;

    .header-content {
      .header-left {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 28px;
          font-weight: 600;
          color: #262626;
        }

        .page-description {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  .profile-content {
    .profile-card {
      .profile-avatar-section {
        text-align: center;

        .avatar-upload {
          position: relative;
          display: inline-block;
          cursor: pointer;

          .upload-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.3s;

            &:hover {
              opacity: 1;
            }
          }
        }

        .profile-basic-info {
          margin-top: 16px;

          h3 {
            margin: 0 0 8px 0;
            font-size: 20px;
            font-weight: 600;
          }

          .user-role {
            margin: 0 0 4px 0;
            color: #1890ff;
            font-weight: 500;
          }

          .user-email {
            margin: 0;
            color: #8c8c8c;
            font-size: 14px;
          }
        }
      }

      .profile-stats {
        display: flex;
        justify-content: space-around;

        .stat-item {
          text-align: center;

          .stat-value {
            font-size: 20px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 4px;
          }

          .stat-label {
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }
    }
  }
}
</style>
