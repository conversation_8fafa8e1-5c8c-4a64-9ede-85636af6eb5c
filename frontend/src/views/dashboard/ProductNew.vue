<template>
  <div class="product-dashboard page-enter" ref="pageContainer">
    <div class="page-header">
      <h1>产品线仪表盘</h1>
      <p>深入分析各产品线的详细数据和性能指标</p>
    </div>
    
    <div class="dashboard-content">
      <!-- 产品线选择器 -->
      <div class="product-selector">
        <a-space :size="isMobile ? 'small' : 'large'" :direction="isMobile ? 'vertical' : 'horizontal'">
          <div>
            <label>选择产品线：</label>
            <a-select
              v-model:value="selectedProduct"
              :style="{ width: isMobile ? '100%' : '200px' }"
              @change="handleProductChange"
            >
              <a-select-option value="">全部产品线</a-select-option>
              <a-select-option
                v-for="product in productLines"
                :key="product.id"
                :value="product.id"
              >
                {{ product.name }}
              </a-select-option>
            </a-select>
          </div>
          <div>
            <label>时间范围：</label>
            <a-range-picker
              v-model:value="dateRange as any"
              @change="handleDateChange"
              :style="{ width: isMobile ? '100%' : 'auto' }"
            />
          </div>
          <div>
            <label>对比模式：</label>
            <a-switch
              v-model:checked="compareMode"
              checked-children="开启"
              un-checked-children="关闭"
              @change="handleCompareModeChange"
            />
          </div>
        </a-space>
      </div>

      <!-- 产品信息展示 -->
      <div v-if="currentProduct" class="product-info">
        <a-card>
          <div class="product-header">
            <div class="product-icon" :style="{ backgroundColor: currentProduct.color }">
              <AppstoreOutlined />
            </div>
            <div class="product-details">
              <h2>{{ currentProduct.name }}</h2>
              <p>专业的PDF处理服务</p>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 核心指标卡片 -->
      <div class="product-stats">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :lg="6" v-for="metric in coreMetrics" :key="metric.key">
            <StatCard
              :title="metric.title"
              :value="metric.value"
              :unit="metric.unit"
              :trend="metric.change"
              :trend-text="metric.trendText"
              :icon="metric.icon"
              :color="metric.color"
              clickable
              @click="handleMetricClick(metric.key)"
            />
          </a-col>
        </a-row>
      </div>

      <!-- 图表区域 -->
      <a-row :gutter="[16, 16]" class="charts-section">
        <a-col :xs="24" :lg="12">
          <ChartCard title="用户活跃度趋势" :description="`${currentProduct?.name || '全部产品线'} 用户活跃度趋势`">
            <LineChart
              :data="userActivityData"
              :height="isMobile ? 250 : 300"
              :smooth="true"
            />
          </ChartCard>
        </a-col>

        <a-col :xs="24" :lg="12">
          <ChartCard title="功能使用分析" :description="`${currentProduct?.name || '全部产品线'} 核心功能使用率`">
            <PieChart
              :data="featureUsageData"
              :height="isMobile ? 250 : 300"
              :donut="true"
              :legend-position="isMobile ? 'bottom' : 'bottom'"
            />
          </ChartCard>
        </a-col>

        <a-col :xs="24" :lg="12">
          <ChartCard title="收入分析" :description="`${currentProduct?.name || '全部产品线'} 收入趋势和构成`">
            <AreaChart
              :data="revenueAnalysisData"
              :height="isMobile ? 250 : 300"
              :stacked="true"
            />
          </ChartCard>
        </a-col>

        <a-col :xs="24" :lg="12">
          <ChartCard title="用户留存分析" :description="`${currentProduct?.name || '全部产品线'} 用户留存率变化`">
            <BarChart
              :data="retentionData"
              :height="isMobile ? 250 : 300"
              :horizontal="false"
            />
          </ChartCard>
        </a-col>
      </a-row>
      
      <!-- 对比分析区域 -->
      <a-row :gutter="[16, 16]" class="comparison-section" v-if="compareMode">
        <a-col :span="24">
          <ChartCard title="产品线对比分析" description="各产品线核心指标对比">
            <BarChart
              :data="productComparisonData"
              :height="400"
              :horizontal="true"
            />
          </ChartCard>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick, watch } from 'vue'
import { AppstoreOutlined, UserOutlined, TeamOutlined, DollarOutlined, BarChartOutlined } from '@ant-design/icons-vue'
import type { Dayjs } from 'dayjs'
import StatCard from '@/components/StatCard.vue'
import ChartCard from '@/components/ChartCard.vue'
import { LineChart, AreaChart, PieChart, BarChart } from '@/components/charts'
import { useDashboardStore } from '@/stores/dashboard'
import { dashboardApi } from '@/api/dashboard'
import { pageLoadAnimation, animateCounterWithPulse } from '@/utils/animations'
import { useAppStore } from '@/stores/app'

// 产品线数据
const appStore = useAppStore()
const productLines = computed(() => appStore.productLines)
const selectedProduct = ref('')
const dateRange = ref<[Dayjs, Dayjs] | null>(null)
const compareMode = ref(false)

// 当前产品信息
const currentProduct = computed(() => {
  if (!selectedProduct.value) return null
  return productLines.value.find(p => String(p.id) === String(selectedProduct.value))
})

// 使用Dashboard Store
const dashboardStore = useDashboardStore()

// 产品线数据状态
const productMetrics = ref({
  activeUsers: { value: 0, change: 0 },
  newUsers: { value: 0, change: 0 },
  revenue: { value: 0, change: 0 },
  downloads: { value: 0, change: 0 },
  retention: { value: 0, change: 0 }
})
const userActivityDataRaw = ref<{ dates: string[], series: any[] }>({ dates: [], series: [] })
const revenueAnalysisDataRaw = ref<{ dates: string[], series: any[] }>({ dates: [], series: [] })
const featureUsageDataRaw = ref<any[]>([])
const productComparisonDataRaw = ref<any[]>([])
const loading = ref(false)

// 转换为图表格式
const userActivityData = computed(() => ({
  categories: userActivityDataRaw.value.dates,
  series: userActivityDataRaw.value.series.slice(0, 2)
}))

const revenueAnalysisData = computed(() => ({
  categories: revenueAnalysisDataRaw.value.dates,
  series: revenueAnalysisDataRaw.value.series
}))

const featureUsageData = computed(() =>
  featureUsageDataRaw.value.map((item: any) => ({
    name: item.name,
    value: item.value,
    color: item.color
  }))
)

const retentionData = computed(() => ({
  categories: ['1日留存', '3日留存', '7日留存', '14日留存', '30日留存'],
  series: [{
    name: '留存率',
    data: [85, 72, 58, 45, 32],
    color: currentProduct.value?.color || '#1e88e5'
  }]
}))

const productComparisonData = computed(() => ({
  categories: productComparisonDataRaw.value.map((item: any) => item.name),
  series: [{
    name: '用户数',
    data: productComparisonDataRaw.value.map((item: any) => item.users),
    color: '#1e88e5'
  }]
}))

// 核心指标
const coreMetrics = computed(() => [
  {
    key: 'users',
    title: '活跃用户',
    value: productMetrics.value.activeUsers.value,
    unit: '人',
    change: productMetrics.value.activeUsers.change,
    trendText: '环比上月',
    icon: UserOutlined,
    color: currentProduct.value?.color || '#1e88e5'
  },
  {
    key: 'newUsers',
    title: '新增用户',
    value: productMetrics.value.newUsers.value,
    unit: '人',
    change: productMetrics.value.newUsers.change,
    trendText: '环比上月',
    icon: TeamOutlined,
    color: '#43a047'
  },
  {
    key: 'revenue',
    title: '产品收入',
    value: productMetrics.value.revenue.value,
    unit: '元',
    change: productMetrics.value.revenue.change,
    trendText: '环比上月',
    icon: DollarOutlined,
    color: '#fb8c00'
  },
  {
    key: 'retention',
    title: '用户留存',
    value: productMetrics.value.retention.value,
    unit: '%',
    change: productMetrics.value.retention.change,
    trendText: '环比上月',
    icon: BarChartOutlined,
    color: '#8e24aa'
  }
])

const handleProductChange = (value: any) => {
  selectedProduct.value = value
  refreshData()
}

const handleDateChange = (dates: any) => {
  dateRange.value = dates
  refreshData()
}

const handleCompareModeChange = (checked: any) => {
  compareMode.value = Boolean(checked)
}

const handleMetricClick = (key: string) => {
  console.log('点击指标:', key)
}

// 页面容器引用
const pageContainer = ref<HTMLElement>()

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
}

const handleResize = () => {
  checkMobile()
}

const refreshData = async () => {
  try {
    loading.value = true

    // 确保有产品线ID，如果没有选择则使用默认的第一个产品线
    let productLineIds: number[] = []
    if (currentProduct.value && currentProduct.value.id) {
      productLineIds = [Number(currentProduct.value.id)]
    } else if (productLines.value.length > 0) {
      // 如果没有选择产品线，使用第一个可用的产品线
      productLineIds = [Number(productLines.value[0].id)]
      console.info('未选择具体产品线，使用默认产品线:', productLines.value[0].name)
    } else {
      // 如果没有任何产品线，使用默认ID
      productLineIds = [1]
      console.info('没有可用产品线，使用默认ID: 1')
    }

    // 获取仪表盘数据参数
    const params = {
      productLineIds,
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0],
      timeGranularity: 'day' as const,
      includeComparison: true
    }

    console.log('发送仪表盘请求参数:', params)

    // 调用真实API获取数据
    const response = await dashboardApi.getProductLineDashboard(params)

    if (response.code === 200 && response.data) {
      const data = response.data

      // 更新产品指标
      productMetrics.value = {
        activeUsers: {
          value: data.metrics.activeUsers,
          change: data.metrics.userGrowthRate
        },
        newUsers: {
          value: data.metrics.newUsers,
          change: data.metrics.userGrowthRate
        },
        revenue: {
          value: data.metrics.totalRevenue,
          change: data.metrics.revenueGrowthRate
        },
        downloads: {
          value: data.metrics.downloads,
          change: 0
        },
        retention: {
          value: data.metrics.retentionRate || 0,
          change: 0 // 暂时设为0，后续可以从API获取变化率
        }
      }

      // 更新图表数据
      if (data.userGrowthChart) {
        userActivityDataRaw.value = {
          dates: data.userGrowthChart.categories || [],
          series: data.userGrowthChart.series?.map((s: any) => ({
            name: s.name,
            data: s.data,
            color: s.color
          })) || []
        }
      }

      if (data.revenueTrendChart) {
        revenueAnalysisDataRaw.value = {
          dates: data.revenueTrendChart.categories || [],
          series: data.revenueTrendChart.series?.map((s: any) => ({
            name: s.name,
            data: s.data,
            color: s.color
          })) || []
        }
      }

      if (data.userDistributionChart) {
        featureUsageDataRaw.value = data.userDistributionChart.series?.[0]?.data || []
      }

      if (data.comparisons) {
        productComparisonDataRaw.value = data.comparisons.map((comp: any) => ({
          id: comp.productLineId,
          name: comp.productLineName,
          users: comp.metrics?.users || 0,
          revenue: comp.metrics?.revenue || 0,
          downloads: comp.metrics?.downloads || 0,
          growth: comp.metrics?.growth || 0,
          marketShare: comp.metrics?.marketShare || 0,
          color: productLines.value.find(p => Number(p.id) === Number(comp.productLineId))?.color || '#1e88e5'
        }))
      }
    }
  } catch (error) {
    console.error('获取产品线数据失败:', error)
    // 发生错误时使用默认数据
    productMetrics.value = {
      activeUsers: { value: 0, change: 0 },
      newUsers: { value: 0, change: 0 },
      revenue: { value: 0, change: 0 },
      downloads: { value: 0, change: 0 },
      retention: { value: 0, change: 0 }
    }
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  checkMobile()
  window.addEventListener('resize', handleResize)

  // 等待产品线数据加载完成
  await appStore.loadProductLines()

  // 如果没有选择产品线，默认选择第一个
  if (!selectedProduct.value && productLines.value.length > 0) {
    selectedProduct.value = String(productLines.value[0].id)
  }

  refreshData()

  // 等待DOM渲染完成后执行动画
  await nextTick()
  if (pageContainer.value) {
    pageLoadAnimation(pageContainer.value)

    // 为数字添加计数动画
    setTimeout(() => {
      const valueElements = pageContainer.value?.querySelectorAll('.stat-value')
      valueElements?.forEach((element, index) => {
        const value = parseInt(element.textContent || '0')
        if (value > 0) {
          animateCounterWithPulse(element as HTMLElement, 0, value, 1000)
        }
      })
    }, 800)
  }
})

// 监听产品线选择变化
watch(selectedProduct, (newValue) => {
  if (newValue) {
    refreshData()
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.product-dashboard {
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
      color: #333;
    }
    
    p {
      color: #666;
      margin: 0;
    }
  }
  
  .product-selector {
    margin-bottom: 24px;
    
    label {
      margin-right: 8px;
      color: #666;
    }
  }
  
  .product-info {
    margin-bottom: 24px;
    
    .product-header {
      display: flex;
      align-items: center;
      
      .product-icon {
        width: 64px;
        height: 64px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        
        .anticon {
          font-size: 32px;
          color: white;
        }
      }
      
      .product-details {
        h2 {
          font-size: 20px;
          font-weight: 600;
          margin: 0 0 8px 0;
          color: #333;
        }
        
        p {
          color: #666;
          margin: 0;
        }
      }
    }
  }
  
  .product-stats {
    margin-bottom: 24px;
  }
  
  .charts-section {
    margin-bottom: 24px;
  }
  
  .comparison-section {
    margin-bottom: 24px;
  }
}
</style>
