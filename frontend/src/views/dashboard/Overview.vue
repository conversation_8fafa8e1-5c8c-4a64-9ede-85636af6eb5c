<template>
  <div class="overview-container page-enter" ref="pageContainer">
    <div class="page-header">
      <h1>总览仪表盘</h1>
      <p>全面展示各产品线核心业务指标的实时状态和趋势变化</p>
    </div>

    <div class="overview-content">
      <!-- 加载状态 -->
      <a-spin :spinning="dashboardStore.isLoading" tip="加载中...">
        <!-- 核心指标卡片 -->
        <a-row :gutter="[16, 16]" class="stats-cards">
          <a-col
            :xs="24"
            :sm="12"
            :lg="6"
            v-for="metric in coreMetrics"
            :key="metric.key"
          >
            <StatCard
              :title="metric.title"
              :value="metric.value"
              :unit="metric.unit"
              :trend="metric.change"
              :trend-text="metric.trendText"
              :icon="metric.icon"
              :color="metric.color"
              clickable
              @click="handleMetricClick(metric.key)"
            />
          </a-col>
        </a-row>

        <!-- 图表区域 -->
        <a-row :gutter="[16, 16]" class="charts-section">
          <a-col :xs="24" :xl="12">
            <ChartCard title="用户增长趋势" description="最近30天用户增长情况">
              <LineChart
                :data="userGrowthData"
                :height="isMobile ? 250 : 300"
                :smooth="true"
              />
            </ChartCard>
          </a-col>

          <a-col :xs="24" :xl="12">
            <ChartCard title="收入统计" description="最近30天收入变化">
              <AreaChart
                :data="revenueData"
                :height="isMobile ? 250 : 300"
                :stacked="false"
              />
            </ChartCard>
          </a-col>

          <a-col :xs="24" :xl="12">
            <ChartCard title="产品线分布" description="各产品线用户占比">
              <PieChart
                :data="productLineDistribution"
                :height="isMobile ? 250 : 300"
                :donut="true"
                :legend-position="isMobile ? 'bottom' : 'right'"
              />
            </ChartCard>
          </a-col>

          <a-col :xs="24" :xl="12">
            <ChartCard title="用户类型分布" description="不同类型用户占比">
              <BarChart
                :data="userTypeData"
                :height="isMobile ? 250 : 300"
                :horizontal="false"
              />
            </ChartCard>
          </a-col>
        </a-row>

        <!-- 实时数据区域 -->
        <a-row :gutter="[16, 16]" class="realtime-section">
          <a-col :xs="24" :lg="16">
            <ChartCard
              title="实时在线用户"
              description="过去24小时在线用户变化"
            >
              <LineChart
                :data="realTimeData"
                :height="250"
                :smooth="true"
                :show-data-zoom="false"
              />
            </ChartCard>
          </a-col>

          <a-col :xs="24" :lg="8">
            <div class="realtime-stats">
              <StatCard
                title="当前在线"
                :value="currentStats.currentOnline"
                unit="人"
                variant="compact"
                :icon="GlobalOutlined"
                color="#52c41a"
              />
              <StatCard
                title="今日峰值"
                :value="currentStats.peakOnline"
                unit="人"
                variant="compact"
                :icon="RiseOutlined"
                color="#1890ff"
                style="margin-top: 16px"
              />
              <StatCard
                title="今日下载"
                :value="currentStats.totalDownloadsToday"
                unit="次"
                variant="compact"
                :icon="DownloadOutlined"
                color="#fa8c16"
                style="margin-top: 16px"
              />
            </div>
          </a-col>
        </a-row>
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick } from "vue";
import {
  DollarOutlined,
  UserOutlined,
  TeamOutlined,
  UserAddOutlined,
  GlobalOutlined,
  RiseOutlined,
  DownloadOutlined,
} from "@ant-design/icons-vue";
import StatCard from "@/components/StatCard.vue";
import ChartCard from "@/components/ChartCard.vue";
import { LineChart, AreaChart, PieChart, BarChart } from "@/components/charts";
import { pageLoadAnimation, animateCounterWithPulse } from "@/utils/animations";
import { useDashboardStore } from "@/stores/dashboard";
import { message } from "ant-design-vue";

// 使用Dashboard Store
const dashboardStore = useDashboardStore();

// 核心指标数据（从store获取）
const coreMetrics = computed(() => dashboardStore.coreMetrics);

// 图表数据（从store获取）
const userGrowthData = computed(() => {
  const data = dashboardStore.userGrowthData;
  return {
    categories: [...data.categories],
    series: data.series.map(s => ({ ...s, data: [...s.data] }))
  };
});
const revenueData = computed(() => {
  const data = dashboardStore.revenueData;
  return {
    categories: [...data.categories],
    series: data.series.map(s => ({ ...s, data: [...s.data] }))
  };
});

const productLineDistribution = computed(() => {
  const data = dashboardStore.productLineDistribution;
  return [...data];
});
const userTypeData = computed(() => {
  const data = dashboardStore.userTypeData;
  return {
    categories: [...data.categories],
    series: data.series.map(s => ({ ...s, data: [...s.data] }))
  };
});

const realTimeData = computed(() => {
  const data = dashboardStore.realTimeData;
  return {
    categories: [...data.categories],
    series: data.series.map(s => ({ ...s, data: [...s.data] }))
  };
});
const currentStats = computed(() => dashboardStore.currentStats);

// 处理指标点击事件
const handleMetricClick = (key: string) => {
  console.log("点击指标:", key);
  // 这里可以添加跳转到详细页面的逻辑
};

// 页面容器引用
const pageContainer = ref<HTMLElement>();

// 移动端检测
const isMobile = ref(false);

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

const handleResize = () => {
  checkMobile();
};

// 定时刷新数据
const refreshData = async () => {
  try {
    await dashboardStore.fetchOverviewDashboard();
    await dashboardStore.fetchRealTimeStats();
  } catch (error) {
    console.error("刷新数据失败:", error);
  }
};

let timer: NodeJS.Timeout;

onMounted(async () => {
  console.log("总览仪表盘已加载");

  // 检查移动端
  checkMobile();
  window.addEventListener("resize", handleResize);

  // 初始化Dashboard数据
  try {
    await dashboardStore.initialize();
  } catch (error) {
    console.error("初始化Dashboard数据失败:", error);
    message.error("加载数据失败，请刷新页面重试");
  }

  // 等待DOM渲染完成后执行动画
  await nextTick();
  if (pageContainer.value) {
    pageLoadAnimation(pageContainer.value);

    // 为数字添加计数动画
    setTimeout(() => {
      const valueElements =
        pageContainer.value?.querySelectorAll(".stat-value");
      valueElements?.forEach((element, index) => {
        const value = parseInt(element.textContent || "0");
        if (value > 0) {
          animateCounterWithPulse(element as HTMLElement, 0, value, 1000);
        }
      });
    }, 800);
  }

  // 每30秒刷新一次实时数据
  timer = setInterval(refreshData, 30000);
});

onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
  window.removeEventListener("resize", handleResize);
});
</script>

<style lang="less" scoped>
.overview-container {
  .page-header {
    margin-bottom: 24px;

    h1 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
      color: #333;
    }

    p {
      color: #666;
      margin: 0;
    }
  }

  .stats-cards {
    margin-bottom: 24px;
  }

  .charts-section {
    margin-bottom: 24px;
  }

  .realtime-section {
    .realtime-stats {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
  }
}
</style>
