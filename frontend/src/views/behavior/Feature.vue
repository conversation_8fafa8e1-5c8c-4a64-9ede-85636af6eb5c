<template>
  <div class="feature-analysis">
    <div class="page-header">
      <h1>功能使用分析</h1>
      <p>分析用户对各功能模块的使用情况，了解功能受欢迎程度和使用趋势</p>
    </div>

    <!-- 筛选条件 -->
    <a-card class="filter-card" :bordered="false">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item label="产品线">
            <a-select
              v-model:value="filters.productLine"
              placeholder="请选择产品线"
              allow-clear
              @change="handleFilterChange"
            >
              <a-select-option value="all">全部产品线</a-select-option>
              <a-select-option value="pdf-reader">PDF Reader</a-select-option>
              <a-select-option value="pdf-editor">PDF Editor</a-select-option>
              <a-select-option value="phantom-pdf">PhantomPDF</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="时间范围">
            <a-range-picker
              v-model:value="filters.dateRange"
              format="YYYY-MM-DD"
              @change="handleFilterChange"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="版本">
            <a-select
              v-model:value="filters.version"
              placeholder="请选择版本"
              allow-clear
              @change="handleFilterChange"
            >
              <a-select-option value="all">全部版本</a-select-option>
              <a-select-option value="v12.0">v12.0</a-select-option>
              <a-select-option value="v11.2">v11.2</a-select-option>
              <a-select-option value="v11.1">v11.1</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="用户群体">
            <a-select
              v-model:value="filters.userGroup"
              placeholder="请选择用户群体"
              allow-clear
              @change="handleFilterChange"
            >
              <a-select-option value="all">全部用户</a-select-option>
              <a-select-option value="new">新用户</a-select-option>
              <a-select-option value="active">活跃用户</a-select-option>
              <a-select-option value="premium">付费用户</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-card>

    <!-- 核心指标卡片 -->
    <a-row :gutter="16" class="metrics-row">
      <a-col :span="6">
        <StatCard
          title="总功能数"
          :value="metrics.totalFeatures"
          suffix="个"
          trend="up"
          :trend-value="5.2"
          color="#1890ff"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="活跃功能数"
          :value="metrics.activeFeatures"
          suffix="个"
          trend="up"
          :trend-value="3.8"
          color="#52c41a"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="平均使用率"
          :value="metrics.avgUsageRate"
          suffix="%"
          trend="down"
          :trend-value="1.2"
          color="#faad14"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="功能覆盖率"
          :value="metrics.featureCoverage"
          suffix="%"
          trend="up"
          :trend-value="2.1"
          color="#722ed1"
        />
      </a-col>
    </a-row>

    <!-- 功能使用排行 -->
    <a-row :gutter="16">
      <a-col :span="12">
        <a-card title="功能使用排行榜" :bordered="false">
          <div ref="featureRankingChart" style="height: 400px;"></div>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="功能使用趋势" :bordered="false">
          <div ref="featureTrendChart" style="height: 400px;"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 功能详细列表 -->
    <a-card title="功能使用详情" :bordered="false" class="feature-table-card">
      <a-table
        :columns="columns"
        :data-source="featureList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'usageRate'">
            <a-progress
              :percent="record.usageRate"
              size="small"
              :stroke-color="getProgressColor(record.usageRate)"
            />
          </template>
          <template v-else-if="column.key === 'trend'">
            <span :class="['trend', record.trend > 0 ? 'trend-up' : 'trend-down']">
              <CaretUpOutlined v-if="record.trend > 0" />
              <CaretDownOutlined v-else />
              {{ Math.abs(record.trend) }}%
            </span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-button type="link" size="small" @click="viewFeatureDetail(record)">
              查看详情
            </a-button>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import * as echarts from 'echarts'
import StatCard from '@/components/StatCard.vue'
import { useFeatureUsageStore } from '@/stores/behavior/feature'
import { useAppStore } from '@/stores/app'
import { http } from '@/utils/request'

// 使用stores
const featureUsageStore = useFeatureUsageStore()
const appStore = useAppStore()

// 筛选条件
const filters = reactive({
  productLine: 'all',
  dateRange: [dayjs().subtract(30, 'day'), dayjs()] as [Dayjs, Dayjs],
  version: 'all',
  userGroup: 'all'
})

// 核心指标
const metrics = reactive({
  totalFeatures: 156,
  activeFeatures: 128,
  avgUsageRate: 67.8,
  featureCoverage: 82.1
})

// 表格相关
const loading = ref(false)
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true
})

const columns = [
  {
    title: '功能名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '功能模块',
    dataIndex: 'module',
    key: 'module',
    width: 120
  },
  {
    title: '使用次数',
    dataIndex: 'usageCount',
    key: 'usageCount',
    width: 100,
    sorter: true
  },
  {
    title: '使用率',
    dataIndex: 'usageRate',
    key: 'usageRate',
    width: 120,
    sorter: true
  },
  {
    title: '趋势',
    dataIndex: 'trend',
    key: 'trend',
    width: 80
  },
  {
    title: '最后使用时间',
    dataIndex: 'lastUsedTime',
    key: 'lastUsedTime',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 100
  }
]

const featureList = ref<any[]>([])

// 图表引用
const featureRankingChart = ref()
const featureTrendChart = ref()

// 处理筛选条件变化
const handleFilterChange = () => {
  loadFeatureData()
}

// 处理表格变化
const handleTableChange = (pag: any, _filters: any, _sorter: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadFeatureData()
}

// 获取进度条颜色
const getProgressColor = (rate: number) => {
  if (rate >= 80) return '#52c41a'
  if (rate >= 60) return '#faad14'
  return '#ff4d4f'
}

// 查看功能详情
const viewFeatureDetail = (record: any) => {
  console.log('查看功能详情:', record)
}

// 加载功能数据
const loadFeatureData = async () => {
  loading.value = true
  try {
    // 获取产品线ID列表
    const productLineIds = filters.productLine !== 'all' ? [parseInt(filters.productLine)] :
                          appStore.productLines.map(p => p.id)

    // 首先获取功能列表（使用公共API）
    const featureListResponse = await http.get('/behavior/feature/public/list', {
      params: { productLineIds }
    })

    if (featureListResponse.code === 200 && featureListResponse.data) {
      const featureIds = featureListResponse.data.map((f: any) => f.featureId)

      // 获取功能使用统计（使用公共API）
      const statsResponse = await http.get('/behavior/feature/public/usage-statistics', {
        params: {
          startDate: filters.dateRange[0].format('YYYY-MM-DD'),
          endDate: filters.dateRange[1].format('YYYY-MM-DD'),
          featureIds,
          productLineIds,
          includeComparison: true
        }
      })

      // 获取实时统计（使用公共API）
      const realTimeResponse = await http.get('/behavior/feature/public/realtime-stats', {
        params: { featureIds, productLineIds }
      })

      // 更新表格数据
      updateTableData(featureListResponse.data, statsResponse.data, realTimeResponse.data)

      // 更新核心指标
      if (realTimeResponse.code === 200 && realTimeResponse.data) {
        metrics.totalFeatures = realTimeResponse.data.totalFeatures || 0
        metrics.activeFeatures = realTimeResponse.data.activeFeatures || 0
        metrics.avgUsageRate = realTimeResponse.data.avgUsageRate || 0
        metrics.featureCoverage = realTimeResponse.data.featureCoverage || 0
      }
    }

  } catch (error) {
    console.error('加载数据失败:', error)
    // 使用默认数据
    featureList.value = [
      {
        id: 1,
        name: 'PDF阅读',
        module: '核心功能',
        usageCount: 125680,
        usageRate: 95.2,
        trend: 2.3,
        lastUsedTime: '2024-01-15 14:30:25'
      },
      {
        id: 2,
        name: '文档注释',
        module: '编辑功能',
        usageCount: 89456,
        usageRate: 78.6,
        trend: 5.1,
        lastUsedTime: '2024-01-15 13:45:12'
      },
      {
        id: 3,
        name: '页面缩放',
        module: '视图功能',
        usageCount: 76543,
        usageRate: 72.3,
        trend: -1.2,
        lastUsedTime: '2024-01-15 12:20:08'
      }
    ]
    pagination.total = featureList.value.length
  } finally {
    loading.value = false
  }
}

// 更新表格数据
const updateTableData = (features: any[], statsData: any, _realTimeData: any) => {
  if (features && statsData) {
    const usageStats = statsData.usageStats || {}

    featureList.value = features.map((feature, index) => {
      const stats = usageStats[feature.featureId] || {}
      return {
        id: index + 1,
        name: feature.featureName,
        module: feature.featureCategory || '未分类',
        usageCount: stats.totalUsage || 0,
        usageRate: stats.usageRate || 0,
        trend: stats.growthRate || 0,
        lastUsedTime: stats.lastUsedTime ? new Date(stats.lastUsedTime).toLocaleString() : '-'
      }
    })

    pagination.total = featureList.value.length
  }
}

// 初始化图表
const initCharts = () => {
  // 功能使用排行榜
  const rankingChart = echarts.init(featureRankingChart.value)
  const rankingOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: ['PDF阅读', '文档注释', '页面缩放', '文本搜索', '书签管理', '打印功能', '导出功能', '签名功能']
    },
    series: [
      {
        name: '使用次数',
        type: 'bar',
        data: [125680, 89456, 76543, 65432, 54321, 43210, 32109, 21098],
        itemStyle: {
          color: '#1890ff'
        }
      }
    ]
  }
  rankingChart.setOption(rankingOption)

  // 功能使用趋势
  const trendChart = echarts.init(featureTrendChart.value)
  const trendOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['PDF阅读', '文档注释', '页面缩放']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: 'PDF阅读',
        type: 'line',
        data: [120, 132, 101, 134, 90, 230, 210],
        smooth: true
      },
      {
        name: '文档注释',
        type: 'line',
        data: [220, 182, 191, 234, 290, 330, 310],
        smooth: true
      },
      {
        name: '页面缩放',
        type: 'line',
        data: [150, 232, 201, 154, 190, 330, 410],
        smooth: true
      }
    ]
  }
  trendChart.setOption(trendOption)
}

onMounted(() => {
  loadFeatureData()
  initCharts()
})
</script>

<style scoped lang="less">
.feature-analysis {
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #262626;
    }
    
    p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .filter-card {
    margin-bottom: 24px;
  }

  .metrics-row {
    margin-bottom: 24px;
  }

  .feature-table-card {
    margin-top: 24px;
  }

  .trend {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    
    &.trend-up {
      color: #52c41a;
    }
    
    &.trend-down {
      color: #ff4d4f;
    }
  }
}
</style>
