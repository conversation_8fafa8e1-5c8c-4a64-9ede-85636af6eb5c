<template>
  <div class="event-comparison-chart">
    <div class="chart-header">
      <h3 class="chart-title">事件对比分析</h3>
      <div class="chart-controls">
        <a-select
          v-model:value="comparisonType"
          style="width: 120px"
          @change="handleComparisonTypeChange"
        >
          <a-select-option value="count">事件次数</a-select-option>
          <a-select-option value="users">用户数</a-select-option>
          <a-select-option value="rate">转化率</a-select-option>
        </a-select>
        <a-button type="text" @click="refreshChart">
          <template #icon>
            <ReloadOutlined />
          </template>
        </a-button>
      </div>
    </div>
    
    <div class="chart-content">
      <div ref="chartRef" class="chart-container"></div>
      
      <div v-if="loading" class="chart-loading">
        <a-spin size="large" />
      </div>
      
      <div v-if="!loading && !hasData" class="chart-empty">
        <a-empty description="暂无数据" />
      </div>
    </div>
    
    <div v-if="hasData && !loading" class="comparison-summary">
      <div class="summary-item">
        <span class="summary-label">对比事件数:</span>
        <span class="summary-value">{{ eventCount }}</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">最高值:</span>
        <span class="summary-value">{{ maxValue }}</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">最低值:</span>
        <span class="summary-value">{{ minValue }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'

interface ComparisonData {
  eventName: string
  count: number
  users: number
  rate: number
  period: string
}

interface Props {
  data?: ComparisonData[]
  loading?: boolean
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  loading: false,
  height: 400
})

const emit = defineEmits<{
  refresh: []
  comparisonTypeChange: [type: string]
}>()

// 响应式数据
const chartRef = ref<HTMLElement>()
const chartInstance = ref<echarts.ECharts>()
const comparisonType = ref<'count' | 'users' | 'rate'>('count')

// 计算属性
const hasData = computed(() => props.data && props.data.length > 0)

const eventCount = computed(() => {
  if (!hasData.value) return 0
  return [...new Set(props.data.map(item => item.eventName))].length
})

const maxValue = computed(() => {
  if (!hasData.value) return '0'
  const values = props.data.map(item => item[comparisonType.value])
  const max = Math.max(...values)
  return formatValue(max)
})

const minValue = computed(() => {
  if (!hasData.value) return '0'
  const values = props.data.map(item => item[comparisonType.value])
  const min = Math.min(...values)
  return formatValue(min)
})

// 格式化数值
const formatValue = (value: number): string => {
  if (comparisonType.value === 'rate') {
    return `${value.toFixed(1)}%`
  } else if (value >= 10000) {
    return `${(value / 10000).toFixed(1)}万`
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}k`
  }
  return value.toString()
}

// 图表配置
const getChartOption = (): EChartsOption => {
  if (!hasData.value) return {}

  // 获取所有事件名称和时间段
  const eventNames = [...new Set(props.data.map(item => item.eventName))]
  const periods = [...new Set(props.data.map(item => item.period))].sort()

  // 构建系列数据
  const series = eventNames.map((eventName, index) => {
    const eventData = periods.map(period => {
      const item = props.data.find(d => d.eventName === eventName && d.period === period)
      return item ? item[comparisonType.value] : 0
    })

    return {
      name: eventName,
      type: 'bar' as const,
      data: eventData,
      itemStyle: {
        color: `hsl(${(index * 60) % 360}, 70%, 50%)`
      },
      emphasis: {
        focus: 'series' as const
      },
      animationDelay: index * 100
    }
  })

  return {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        let result = `<div style="margin-bottom: 4px; font-weight: bold;">${params[0].axisValue}</div>`
        params.forEach((param: any) => {
          const value = formatValue(param.value)
          result += `
            <div style="display: flex; align-items: center; margin-bottom: 2px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; margin-right: 8px;"></span>
              <span style="margin-right: 8px;">${param.seriesName}:</span>
              <span style="font-weight: bold;">${value}</span>
            </div>
          `
        })
        return result
      }
    },
    legend: {
      data: eventNames,
      top: 10,
      type: 'scroll'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: periods,
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => formatValue(value)
      }
    },
    series,
    animationEasing: 'elasticOut',
    animationDelayUpdate: (idx: number) => idx * 5
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance.value = echarts.init(chartRef.value)
  updateChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance.value) return
  
  const option = getChartOption()
  chartInstance.value.setOption(option, true)
}

// 处理窗口大小变化
const handleResize = () => {
  chartInstance.value?.resize()
}

// 处理对比类型变化
const handleComparisonTypeChange = (value: any) => {
  const type = String(value)
  comparisonType.value = type as 'count' | 'users' | 'rate'
  updateChart()
  emit('comparisonTypeChange', type)
}

// 刷新图表
const refreshChart = () => {
  emit('refresh')
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

watch(() => props.loading, (loading) => {
  if (!loading) {
    updateChart()
  }
})

// 生命周期
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance.value?.dispose()
})
</script>

<style scoped>
.event-comparison-chart {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-content {
  position: relative;
}

.chart-container {
  width: 100%;
  height: v-bind(props.height + 'px');
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.chart-empty {
  height: v-bind(props.height + 'px');
  display: flex;
  align-items: center;
  justify-content: center;
}

.comparison-summary {
  display: flex;
  justify-content: space-around;
  margin-top: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;
}

.summary-item {
  text-align: center;
}

.summary-label {
  display: block;
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.summary-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}
</style>
