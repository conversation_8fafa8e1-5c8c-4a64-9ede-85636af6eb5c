<template>
  <div class="event-trend-chart">
    <div class="chart-header">
      <h3 class="chart-title">事件趋势分析</h3>
      <div class="chart-controls">
        <a-select
          v-model:value="chartType"
          style="width: 120px"
          @change="handleChartTypeChange"
        >
          <a-select-option value="line">折线图</a-select-option>
          <a-select-option value="area">面积图</a-select-option>
          <a-select-option value="bar">柱状图</a-select-option>
        </a-select>
        <a-button type="text" @click="refreshChart">
          <template #icon>
            <ReloadOutlined />
          </template>
        </a-button>
      </div>
    </div>
    
    <div class="chart-content">
      <div ref="chartRef" class="chart-container"></div>
      
      <div v-if="loading" class="chart-loading">
        <a-spin size="large" />
      </div>
      
      <div v-if="!loading && !hasData" class="chart-empty">
        <a-empty description="暂无数据" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'

interface TrendData {
  date: string
  value: number
  eventName: string
}

interface Props {
  data?: TrendData[]
  loading?: boolean
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  loading: false,
  height: 400
})

const emit = defineEmits<{
  refresh: []
  chartTypeChange: [type: string]
}>()

// 响应式数据
const chartRef = ref<HTMLElement>()
const chartInstance = ref<echarts.ECharts>()
const chartType = ref<'line' | 'area' | 'bar'>('line')

// 计算属性
const hasData = computed(() => props.data && props.data.length > 0)

// 图表配置
const getChartOption = (): EChartsOption => {
  if (!hasData.value) return {}

  // 处理数据
  const dates = [...new Set(props.data.map(item => item.date))].sort()
  const eventNames = [...new Set(props.data.map(item => item.eventName))]
  
  const series = eventNames.map(eventName => {
    const eventData = dates.map(date => {
      const item = props.data.find(d => d.date === date && d.eventName === eventName)
      return item ? item.value : 0
    })

    const baseConfig = {
      name: eventName,
      data: eventData,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 2
      }
    }

    switch (chartType.value) {
      case 'area':
        return {
          ...baseConfig,
          type: 'line' as const,
          areaStyle: {
            opacity: 0.3
          }
        }
      case 'bar':
        return {
          ...baseConfig,
          type: 'bar' as const,
          barWidth: '60%'
        }
      default:
        return {
          ...baseConfig,
          type: 'line' as const
        }
    }
  })

  return {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: (params: any) => {
        let result = `<div style="margin-bottom: 4px;">${params[0].axisValue}</div>`
        params.forEach((param: any) => {
          result += `
            <div style="display: flex; align-items: center; margin-bottom: 2px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="margin-right: 8px;">${param.seriesName}:</span>
              <span style="font-weight: bold;">${param.value.toLocaleString()}</span>
            </div>
          `
        })
        return result
      }
    },
    legend: {
      data: eventNames,
      top: 10,
      type: 'scroll'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: chartType.value === 'bar',
      data: dates,
      axisLabel: {
        formatter: (value: string) => {
          const date = new Date(value)
          return `${date.getMonth() + 1}/${date.getDate()}`
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => {
          if (value >= 10000) {
            return `${(value / 10000).toFixed(1)}万`
          } else if (value >= 1000) {
            return `${(value / 1000).toFixed(1)}k`
          }
          return value.toString()
        }
      }
    },
    series,
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 100,
        height: 20,
        bottom: 10
      }
    ]
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance.value = echarts.init(chartRef.value)
  updateChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance.value) return
  
  const option = getChartOption()
  chartInstance.value.setOption(option, true)
}

// 处理窗口大小变化
const handleResize = () => {
  chartInstance.value?.resize()
}

// 处理图表类型变化
const handleChartTypeChange = (value: any) => {
  const type = String(value)
  chartType.value = type as 'line' | 'area' | 'bar'
  updateChart()
  emit('chartTypeChange', type)
}

// 刷新图表
const refreshChart = () => {
  emit('refresh')
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

watch(() => props.loading, (loading) => {
  if (!loading) {
    updateChart()
  }
})

// 生命周期
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance.value?.dispose()
})
</script>

<style scoped>
.event-trend-chart {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-content {
  position: relative;
}

.chart-container {
  width: 100%;
  height: v-bind(props.height + 'px');
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.chart-empty {
  height: v-bind(props.height + 'px');
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
