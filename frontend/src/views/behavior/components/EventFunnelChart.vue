<template>
  <div class="event-funnel-chart">
    <div class="chart-header">
      <h3 class="chart-title">事件漏斗分析</h3>
      <div class="chart-controls">
        <a-select
          v-model:value="displayMode"
          style="width: 120px"
          @change="handleDisplayModeChange"
        >
          <a-select-option value="count">用户数</a-select-option>
          <a-select-option value="rate">转化率</a-select-option>
        </a-select>
        <a-button type="text" @click="refreshChart">
          <template #icon>
            <ReloadOutlined />
          </template>
        </a-button>
      </div>
    </div>
    
    <div class="chart-content">
      <div ref="chartRef" class="chart-container"></div>
      
      <div v-if="loading" class="chart-loading">
        <a-spin size="large" />
      </div>
      
      <div v-if="!loading && !hasData" class="chart-empty">
        <a-empty description="暂无数据" />
      </div>
    </div>
    
    <div v-if="hasData && !loading" class="funnel-summary">
      <div class="summary-item">
        <span class="summary-label">总转化率:</span>
        <span class="summary-value">{{ overallConversionRate }}%</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">最大流失步骤:</span>
        <span class="summary-value">{{ maxDropoffStep }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'

interface FunnelStep {
  stepName: string
  userCount: number
  conversionRate: number
  dropoffRate: number
}

interface Props {
  data?: FunnelStep[]
  loading?: boolean
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  loading: false,
  height: 400
})

const emit = defineEmits<{
  refresh: []
  displayModeChange: [mode: string]
}>()

// 响应式数据
const chartRef = ref<HTMLElement>()
const chartInstance = ref<echarts.ECharts>()
const displayMode = ref<'count' | 'rate'>('count')

// 计算属性
const hasData = computed(() => props.data && props.data.length > 0)

const overallConversionRate = computed(() => {
  if (!hasData.value) return '0.0'
  const firstStep = props.data[0]
  const lastStep = props.data[props.data.length - 1]
  const rate = (lastStep.userCount / firstStep.userCount) * 100
  return rate.toFixed(1)
})

const maxDropoffStep = computed(() => {
  if (!hasData.value) return '-'
  let maxDropoff = 0
  let maxDropoffStepName = ''
  
  props.data.forEach(step => {
    if (step.dropoffRate > maxDropoff) {
      maxDropoff = step.dropoffRate
      maxDropoffStepName = step.stepName
    }
  })
  
  return maxDropoffStepName
})

// 图表配置
const getChartOption = (): EChartsOption => {
  if (!hasData.value) return {}

  const funnelData = props.data.map((step, index) => ({
    name: step.stepName,
    value: displayMode.value === 'count' ? step.userCount : step.conversionRate,
    itemStyle: {
      color: `hsl(${210 + index * 30}, 70%, ${60 - index * 5}%)`
    }
  }))

  return {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const stepIndex = props.data.findIndex(step => step.stepName === params.name)
        const step = props.data[stepIndex]
        
        return `
          <div style="margin-bottom: 4px; font-weight: bold;">${step.stepName}</div>
          <div>用户数: ${step.userCount.toLocaleString()}</div>
          <div>转化率: ${step.conversionRate.toFixed(1)}%</div>
          <div>流失率: ${step.dropoffRate.toFixed(1)}%</div>
        `
      }
    },
    series: [
      {
        name: '漏斗分析',
        type: 'funnel',
        left: '10%',
        top: '10%',
        width: '80%',
        height: '80%',
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside',
          formatter: (params: any) => {
            const stepIndex = props.data.findIndex(step => step.stepName === params.name)
            const step = props.data[stepIndex]
            
            if (displayMode.value === 'count') {
              return `${step.stepName}\n${step.userCount.toLocaleString()}`
            } else {
              return `${step.stepName}\n${step.conversionRate.toFixed(1)}%`
            }
          },
          fontSize: 12,
          color: '#fff',
          fontWeight: 'bold'
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 14
          }
        },
        data: funnelData
      }
    ]
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance.value = echarts.init(chartRef.value)
  updateChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance.value) return
  
  const option = getChartOption()
  chartInstance.value.setOption(option, true)
}

// 处理窗口大小变化
const handleResize = () => {
  chartInstance.value?.resize()
}

// 处理显示模式变化
const handleDisplayModeChange = (value: any) => {
  const mode = String(value)
  displayMode.value = mode as 'count' | 'rate'
  updateChart()
  emit('displayModeChange', mode)
}

// 刷新图表
const refreshChart = () => {
  emit('refresh')
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

watch(() => props.loading, (loading) => {
  if (!loading) {
    updateChart()
  }
})

// 生命周期
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance.value?.dispose()
})
</script>

<style scoped>
.event-funnel-chart {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-content {
  position: relative;
}

.chart-container {
  width: 100%;
  height: v-bind(props.height + 'px');
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.chart-empty {
  height: v-bind(props.height + 'px');
  display: flex;
  align-items: center;
  justify-content: center;
}

.funnel-summary {
  display: flex;
  justify-content: space-around;
  margin-top: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;
}

.summary-item {
  text-align: center;
}

.summary-label {
  display: block;
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.summary-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}
</style>
