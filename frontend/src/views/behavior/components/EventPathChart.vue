<template>
  <div class="event-path-chart">
    <div class="chart-header">
      <h3 class="chart-title">事件路径分析</h3>
      <div class="chart-controls">
        <a-select
          v-model:value="layoutType"
          style="width: 120px"
          @change="handleLayoutChange"
        >
          <a-select-option value="force">力导向图</a-select-option>
          <a-select-option value="circular">环形布局</a-select-option>
        </a-select>
        <a-button type="text" @click="refreshChart">
          <template #icon>
            <ReloadOutlined />
          </template>
        </a-button>
      </div>
    </div>
    
    <div class="chart-content">
      <div ref="chartRef" class="chart-container"></div>
      
      <div v-if="loading" class="chart-loading">
        <a-spin size="large" />
      </div>
      
      <div v-if="!loading && !hasData" class="chart-empty">
        <a-empty description="暂无数据" />
      </div>
    </div>
    
    <div v-if="hasData && !loading" class="path-legend">
      <div class="legend-item">
        <span class="legend-color" style="background: #1890ff"></span>
        <span class="legend-text">起始事件</span>
      </div>
      <div class="legend-item">
        <span class="legend-color" style="background: #52c41a"></span>
        <span class="legend-text">中间事件</span>
      </div>
      <div class="legend-item">
        <span class="legend-color" style="background: #fa541c"></span>
        <span class="legend-text">结束事件</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'

interface PathNode {
  id: string
  name: string
  value: number
  category: 'start' | 'middle' | 'end'
}

interface PathLink {
  source: string
  target: string
  value: number
}

interface Props {
  nodes?: PathNode[]
  links?: PathLink[]
  loading?: boolean
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  nodes: () => [],
  links: () => [],
  loading: false,
  height: 500
})

const emit = defineEmits<{
  refresh: []
  layoutChange: [layout: string]
}>()

// 响应式数据
const chartRef = ref<HTMLElement>()
const chartInstance = ref<echarts.ECharts>()
const layoutType = ref<'force' | 'circular'>('force')

// 计算属性
const hasData = computed(() => props.nodes && props.nodes.length > 0)

// 图表配置
const getChartOption = (): EChartsOption => {
  if (!hasData.value) return {}

  const categories = [
    { name: '起始事件', itemStyle: { color: '#1890ff' } },
    { name: '中间事件', itemStyle: { color: '#52c41a' } },
    { name: '结束事件', itemStyle: { color: '#fa541c' } }
  ]

  const nodes = props.nodes.map(node => ({
    id: node.id,
    name: node.name,
    value: node.value,
    category: node.category === 'start' ? 0 : node.category === 'middle' ? 1 : 2,
    symbolSize: Math.max(20, Math.min(60, node.value / 100)),
    label: {
      show: true,
      fontSize: 12,
      fontWeight: 'bold'
    },
    itemStyle: {
      borderWidth: 2,
      borderColor: '#fff'
    }
  }))

  const links = props.links.map(link => ({
    source: link.source,
    target: link.target,
    value: link.value,
    lineStyle: {
      width: Math.max(1, Math.min(8, link.value / 500)),
      opacity: 0.6,
      curveness: 0.2
    },
    label: {
      show: false
    }
  }))

  const baseConfig = {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'item' as const,
      formatter: (params: any) => {
        if (params.dataType === 'node') {
          return `
            <div style="margin-bottom: 4px; font-weight: bold;">${params.data.name}</div>
            <div>事件次数: ${params.data.value.toLocaleString()}</div>
            <div>类型: ${categories[params.data.category].name}</div>
          `
        } else if (params.dataType === 'edge') {
          return `
            <div style="margin-bottom: 4px; font-weight: bold;">路径转换</div>
            <div>从: ${params.data.source}</div>
            <div>到: ${params.data.target}</div>
            <div>转换次数: ${params.data.value.toLocaleString()}</div>
          `
        }
        return ''
      }
    },
    legend: {
      data: categories.map(cat => cat.name),
      top: 10,
      left: 'center'
    },
    series: [
      {
        name: '事件路径',
        type: 'graph' as const,
        layout: layoutType.value,
        data: nodes,
        links: links,
        categories: categories,
        roam: true,
        focusNodeAdjacency: true,
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1,
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        },
        label: {
          position: 'inside',
          fontSize: 10
        },
        lineStyle: {
          color: 'source',
          curveness: 0.3
        },
        emphasis: {
          focus: 'adjacency',
          lineStyle: {
            width: 10
          }
        }
      }
    ]
  }

  // 根据布局类型添加特定配置
  if (layoutType.value === 'force') {
    (baseConfig.series[0] as any).force = {
      repulsion: 1000,
      gravity: 0.1,
      edgeLength: [50, 200],
      layoutAnimation: true
    }
  } else if (layoutType.value === 'circular') {
    (baseConfig.series[0] as any).circular = {
      rotateLabel: true
    }
  }

  return baseConfig as any
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance.value = echarts.init(chartRef.value)
  updateChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance.value) return
  
  const option = getChartOption()
  chartInstance.value.setOption(option, true)
}

// 处理窗口大小变化
const handleResize = () => {
  chartInstance.value?.resize()
}

// 处理布局变化
const handleLayoutChange = (value: any) => {
  const layout = String(value)
  layoutType.value = layout as 'force' | 'circular'
  updateChart()
  emit('layoutChange', layout)
}

// 刷新图表
const refreshChart = () => {
  emit('refresh')
}

// 监听数据变化
watch(() => [props.nodes, props.links], () => {
  updateChart()
}, { deep: true })

watch(() => props.loading, (loading) => {
  if (!loading) {
    updateChart()
  }
})

// 生命周期
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance.value?.dispose()
})
</script>

<style scoped>
.event-path-chart {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-content {
  position: relative;
}

.chart-container {
  width: 100%;
  height: v-bind(props.height + 'px');
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.chart-empty {
  height: v-bind(props.height + 'px');
  display: flex;
  align-items: center;
  justify-content: center;
}

.path-legend {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-text {
  font-size: 12px;
  color: #595959;
}
</style>
