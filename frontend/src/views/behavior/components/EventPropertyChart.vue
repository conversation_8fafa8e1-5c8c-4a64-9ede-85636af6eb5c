<template>
  <div class="event-property-chart">
    <div class="chart-header">
      <h3 class="chart-title">事件属性分析</h3>
      <div class="chart-controls">
        <a-select
          v-model:value="chartType"
          style="width: 120px"
          @change="handleChartTypeChange"
        >
          <a-select-option value="pie">饼图</a-select-option>
          <a-select-option value="bar">柱状图</a-select-option>
          <a-select-option value="treemap">矩形树图</a-select-option>
        </a-select>
        <a-button type="text" @click="refreshChart">
          <template #icon>
            <ReloadOutlined />
          </template>
        </a-button>
      </div>
    </div>
    
    <div class="chart-content">
      <div ref="chartRef" class="chart-container"></div>
      
      <div v-if="loading" class="chart-loading">
        <a-spin size="large" />
      </div>
      
      <div v-if="!loading && !hasData" class="chart-empty">
        <a-empty description="暂无数据" />
      </div>
    </div>
    
    <div v-if="hasData && !loading" class="property-summary">
      <div class="summary-item">
        <span class="summary-label">属性总数:</span>
        <span class="summary-value">{{ propertyCount }}</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">最高占比:</span>
        <span class="summary-value">{{ maxPercentage }}%</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'

interface PropertyData {
  propertyName: string
  propertyValue: string
  count: number
  percentage: number
}

interface Props {
  data?: PropertyData[]
  loading?: boolean
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  loading: false,
  height: 400
})

const emit = defineEmits<{
  refresh: []
  chartTypeChange: [type: string]
}>()

// 响应式数据
const chartRef = ref<HTMLElement>()
const chartInstance = ref<echarts.ECharts>()
const chartType = ref<'pie' | 'bar' | 'treemap'>('pie')

// 计算属性
const hasData = computed(() => props.data && props.data.length > 0)

const propertyCount = computed(() => {
  if (!hasData.value) return 0
  return props.data.length
})

const maxPercentage = computed(() => {
  if (!hasData.value) return '0.0'
  const max = Math.max(...props.data.map(item => item.percentage))
  return max.toFixed(1)
})

// 图表配置
const getChartOption = (): EChartsOption => {
  if (!hasData.value) return {}

  const chartData = props.data.map((item, index) => ({
    name: `${item.propertyName}: ${item.propertyValue}`,
    value: item.count,
    percentage: item.percentage,
    itemStyle: {
      color: `hsl(${(index * 30) % 360}, 70%, ${60 - (index % 3) * 10}%)`
    }
  }))

  switch (chartType.value) {
    case 'pie':
      return {
        title: {
          show: false
        },
        tooltip: {
          trigger: 'item',
          formatter: (params: any) => {
            return `
              <div style="margin-bottom: 4px; font-weight: bold;">${params.name}</div>
              <div>数量: ${params.value.toLocaleString()}</div>
              <div>占比: ${params.data.percentage.toFixed(1)}%</div>
            `
          }
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          right: 10,
          top: 20,
          bottom: 20,
          data: chartData.map(item => item.name)
        },
        series: [
          {
            name: '事件属性',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['40%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: chartData
          }
        ]
      }

    case 'bar':
      return {
        title: {
          show: false
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params: any) => {
            const param = params[0]
            return `
              <div style="margin-bottom: 4px; font-weight: bold;">${param.name}</div>
              <div>数量: ${param.value.toLocaleString()}</div>
              <div>占比: ${param.data.percentage.toFixed(1)}%</div>
            `
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: chartData.map(item => item.name),
          axisLabel: {
            interval: 0,
            rotate: 45,
            fontSize: 10
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: (value: number) => {
              if (value >= 10000) {
                return `${(value / 10000).toFixed(1)}万`
              } else if (value >= 1000) {
                return `${(value / 1000).toFixed(1)}k`
              }
              return value.toString()
            }
          }
        },
        series: [
          {
            name: '数量',
            type: 'bar',
            data: chartData,
            itemStyle: {
              borderRadius: [4, 4, 0, 0]
            },
            emphasis: {
              focus: 'series'
            }
          }
        ]
      }

    case 'treemap':
      return {
        title: {
          show: false
        },
        tooltip: {
          trigger: 'item',
          formatter: (params: any) => {
            return `
              <div style="margin-bottom: 4px; font-weight: bold;">${params.name}</div>
              <div>数量: ${params.value.toLocaleString()}</div>
              <div>占比: ${params.data.percentage.toFixed(1)}%</div>
            `
          }
        },
        series: [
          {
            name: '事件属性',
            type: 'treemap',
            data: chartData,
            roam: false,
            nodeClick: false,
            breadcrumb: {
              show: false
            },
            label: {
              show: true,
              formatter: (params: any) => {
                return `${params.name}\n${params.value.toLocaleString()}`
              },
              fontSize: 12
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 2
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 20,
                shadowColor: 'rgba(0,0,0,0.8)'
              }
            }
          }
        ]
      }

    default:
      return {}
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance.value = echarts.init(chartRef.value)
  updateChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance.value) return
  
  const option = getChartOption()
  chartInstance.value.setOption(option, true)
}

// 处理窗口大小变化
const handleResize = () => {
  chartInstance.value?.resize()
}

// 处理图表类型变化
const handleChartTypeChange = (value: any) => {
  const type = String(value)
  chartType.value = type as 'pie' | 'bar' | 'treemap'
  updateChart()
  emit('chartTypeChange', type)
}

// 刷新图表
const refreshChart = () => {
  emit('refresh')
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

watch(() => props.loading, (loading) => {
  if (!loading) {
    updateChart()
  }
})

// 生命周期
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance.value?.dispose()
})
</script>

<style scoped>
.event-property-chart {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-content {
  position: relative;
}

.chart-container {
  width: 100%;
  height: v-bind(props.height + 'px');
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.chart-empty {
  height: v-bind(props.height + 'px');
  display: flex;
  align-items: center;
  justify-content: center;
}

.property-summary {
  display: flex;
  justify-content: space-around;
  margin-top: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;
}

.summary-item {
  text-align: center;
}

.summary-label {
  display: block;
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.summary-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}
</style>
