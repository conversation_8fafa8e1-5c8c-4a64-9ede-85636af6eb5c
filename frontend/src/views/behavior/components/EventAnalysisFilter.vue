<template>
  <div class="event-analysis-filter">
    <a-row :gutter="16" align="middle">
      <!-- 时间范围选择 -->
      <a-col :span="6">
        <div class="filter-item">
          <label class="filter-label">时间范围</label>
          <a-range-picker
            v-model:value="dateRange"
            :placeholder="['开始日期', '结束日期']"
            format="YYYY-MM-DD"
            style="width: 100%"
            @change="handleDateRangeChange"
          />
        </div>
      </a-col>

      <!-- 时间粒度选择 -->
      <a-col :span="3">
        <div class="filter-item">
          <label class="filter-label">时间粒度</label>
          <a-select
            v-model:value="granularity"
            style="width: 100%"
            @change="handleGranularityChange"
          >
            <a-select-option value="HOUR">小时</a-select-option>
            <a-select-option value="DAY">天</a-select-option>
            <a-select-option value="WEEK">周</a-select-option>
            <a-select-option value="MONTH">月</a-select-option>
          </a-select>
        </div>
      </a-col>

      <!-- 产品线选择 -->
      <a-col :span="4">
        <div class="filter-item">
          <label class="filter-label">产品线</label>
          <a-select
            v-model:value="selectedProductLines"
            mode="multiple"
            :max-tag-count="1"
            placeholder="选择产品线"
            style="width: 100%"
            @change="handleProductLineChange"
          >
            <a-select-option
              v-for="product in productLineOptions"
              :key="product.id"
              :value="product.id"
            >
              {{ product.name }}
            </a-select-option>
          </a-select>
        </div>
      </a-col>

      <!-- 事件选择 -->
      <a-col :span="6">
        <div class="filter-item">
          <label class="filter-label">事件</label>
          <a-select
            v-model:value="selectedEvents"
            mode="multiple"
            :max-tag-count="2"
            placeholder="选择事件"
            style="width: 100%"
            show-search
            :filter-option="filterEventOption"
            @change="handleEventChange"
          >
            <a-select-option
              v-for="event in eventList"
              :key="event.eventId"
              :value="event.eventId"
              :label="event.eventName"
            >
              <div class="event-option">
                <div class="event-name">{{ event.eventName }}</div>
                <div class="event-category">{{ event.eventCategory }}</div>
              </div>
            </a-select-option>
          </a-select>
        </div>
      </a-col>

      <!-- 高级筛选 -->
      <a-col :span="3">
        <div class="filter-item">
          <a-space>
            <a-button @click="showAdvancedFilter = !showAdvancedFilter">
              <FilterOutlined />
              高级筛选
            </a-button>
            <a-button type="primary" @click="handleApplyFilter">
              <SearchOutlined />
              应用
            </a-button>
          </a-space>
        </div>
      </a-col>

      <!-- 快捷时间选择 -->
      <a-col :span="2">
        <div class="filter-item">
          <a-dropdown>
            <a-button>
              <ClockCircleOutlined />
              快捷选择
            </a-button>
            <template #overlay>
              <a-menu @click="handleQuickTimeSelect">
                <a-menu-item key="today">今天</a-menu-item>
                <a-menu-item key="yesterday">昨天</a-menu-item>
                <a-menu-item key="last7days">最近7天</a-menu-item>
                <a-menu-item key="last30days">最近30天</a-menu-item>
                <a-menu-item key="thisMonth">本月</a-menu-item>
                <a-menu-item key="lastMonth">上月</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-col>
    </a-row>

    <!-- 高级筛选面板 -->
    <div v-if="showAdvancedFilter" class="advanced-filter-panel">
      <a-divider />
      <a-row :gutter="16">
        <!-- 事件分类筛选 -->
        <a-col :span="6">
          <div class="filter-item">
            <label class="filter-label">事件分类</label>
            <a-select
              v-model:value="advancedFilters.eventCategories"
              mode="multiple"
              placeholder="选择事件分类"
              style="width: 100%"
            >
              <a-select-option value="用户行为">用户行为</a-select-option>
              <a-select-option value="系统事件">系统事件</a-select-option>
              <a-select-option value="业务操作">业务操作</a-select-option>
              <a-select-option value="错误事件">错误事件</a-select-option>
              <a-select-option value="性能事件">性能事件</a-select-option>
            </a-select>
          </div>
        </a-col>

        <!-- 设备类型筛选 -->
        <a-col :span="6">
          <div class="filter-item">
            <label class="filter-label">设备类型</label>
            <a-select
              v-model:value="advancedFilters.deviceTypes"
              mode="multiple"
              placeholder="选择设备类型"
              style="width: 100%"
            >
              <a-select-option value="Desktop">桌面端</a-select-option>
              <a-select-option value="Mobile">移动端</a-select-option>
              <a-select-option value="Tablet">平板</a-select-option>
              <a-select-option value="Web">网页端</a-select-option>
            </a-select>
          </div>
        </a-col>

        <!-- 操作系统筛选 -->
        <a-col :span="6">
          <div class="filter-item">
            <label class="filter-label">操作系统</label>
            <a-select
              v-model:value="advancedFilters.operatingSystems"
              mode="multiple"
              placeholder="选择操作系统"
              style="width: 100%"
            >
              <a-select-option value="Windows">Windows</a-select-option>
              <a-select-option value="macOS">macOS</a-select-option>
              <a-select-option value="Linux">Linux</a-select-option>
              <a-select-option value="iOS">iOS</a-select-option>
              <a-select-option value="Android">Android</a-select-option>
            </a-select>
          </div>
        </a-col>

        <!-- 应用版本筛选 -->
        <a-col :span="6">
          <div class="filter-item">
            <label class="filter-label">应用版本</label>
            <a-select
              v-model:value="advancedFilters.appVersions"
              mode="multiple"
              placeholder="选择应用版本"
              style="width: 100%"
            >
              <a-select-option value="v1.0">v1.0</a-select-option>
              <a-select-option value="v1.1">v1.1</a-select-option>
              <a-select-option value="v1.2">v1.2</a-select-option>
              <a-select-option value="v2.0">v2.0</a-select-option>
            </a-select>
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-top: 16px">
        <!-- 事件次数范围 -->
        <a-col :span="6">
          <div class="filter-item">
            <label class="filter-label">事件次数范围</label>
            <a-input-group compact>
              <a-input-number
                v-model:value="advancedFilters.minEventCount"
                placeholder="最小值"
                style="width: 50%"
                :min="0"
              />
              <a-input-number
                v-model:value="advancedFilters.maxEventCount"
                placeholder="最大值"
                style="width: 50%"
                :min="0"
              />
            </a-input-group>
          </div>
        </a-col>

        <!-- 用户数范围 -->
        <a-col :span="6">
          <div class="filter-item">
            <label class="filter-label">用户数范围</label>
            <a-input-group compact>
              <a-input-number
                v-model:value="advancedFilters.minUserCount"
                placeholder="最小值"
                style="width: 50%"
                :min="0"
              />
              <a-input-number
                v-model:value="advancedFilters.maxUserCount"
                placeholder="最大值"
                style="width: 50%"
                :min="0"
              />
            </a-input-group>
          </div>
        </a-col>

        <!-- 地域筛选 -->
        <a-col :span="6">
          <div class="filter-item">
            <label class="filter-label">地域</label>
            <a-select
              v-model:value="advancedFilters.regions"
              mode="multiple"
              placeholder="选择地域"
              style="width: 100%"
            >
              <a-select-option value="北京">北京</a-select-option>
              <a-select-option value="上海">上海</a-select-option>
              <a-select-option value="广州">广州</a-select-option>
              <a-select-option value="深圳">深圳</a-select-option>
              <a-select-option value="杭州">杭州</a-select-option>
            </a-select>
          </div>
        </a-col>

        <!-- 渠道筛选 -->
        <a-col :span="6">
          <div class="filter-item">
            <label class="filter-label">渠道</label>
            <a-select
              v-model:value="advancedFilters.channels"
              mode="multiple"
              placeholder="选择渠道"
              style="width: 100%"
            >
              <a-select-option value="官网">官网</a-select-option>
              <a-select-option value="应用商店">应用商店</a-select-option>
              <a-select-option value="推广链接">推广链接</a-select-option>
              <a-select-option value="合作伙伴">合作伙伴</a-select-option>
            </a-select>
          </div>
        </a-col>
      </a-row>

      <!-- 高级筛选操作按钮 -->
      <div class="advanced-filter-actions">
        <a-space>
          <a-button @click="handleResetAdvancedFilter">重置</a-button>
          <a-button type="primary" @click="handleApplyAdvancedFilter">应用高级筛选</a-button>
        </a-space>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import type { MenuInfo } from 'ant-design-vue/es/menu/src/interface'
import {
  FilterOutlined,
  SearchOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'
import type { EventAnalysisRequest, EventInfo, EventFilterCondition } from '@/api/behavior/event'

import dayjs, { type Dayjs } from 'dayjs'

/**
 * 事件分析筛选器组件
 *
 * <AUTHOR>
 * @since 2025-07-01
 */

// Props
interface Props {
  params: EventAnalysisRequest
  eventList: EventInfo[]
}

const props = withDefaults(defineProps<Props>(), {
  params: () => ({
    startDate: '',
    endDate: '',
    granularity: 'DAY',
    productLineIds: [],
    eventIds: [],
    includeComparison: false,
    includeDetails: false
  }),
  eventList: () => []
})

// Emits
const emit = defineEmits<{
  change: [params: EventAnalysisRequest]
  'update:params': [params: EventAnalysisRequest]
}>()

// 响应式数据
const dateRange = ref<[Dayjs, Dayjs]>([dayjs().subtract(7, 'day'), dayjs()])
const granularity = ref<string>('DAY')
const selectedProductLines = ref<number[]>([])
const selectedEvents = ref<string[]>([])
const showAdvancedFilter = ref<boolean>(false)

// 产品线选项数据
const productLineOptions = ref([
  { id: 1, name: '福昕阅读器GA版' },
  { id: 2, name: '福昕阅读器PLUS版' },
  { id: 3, name: 'PDF编辑器个人版' },
  { id: 4, name: 'PDF编辑器专业版' },
  { id: 5, name: 'PDF365在线服务' },
  { id: 6, name: 'PDF转换器' },
  { id: 7, name: '卸载器' },
  { id: 8, name: '福昕移动阅读器' },
  { id: 9, name: 'PDF SDK工具包' },
  { id: 10, name: '内容平台' }
])

// 高级筛选条件
const advancedFilters = ref<EventFilterCondition>({
  eventCategories: [],
  deviceTypes: [],
  operatingSystems: [],
  appVersions: [],
  regions: [],
  channels: [],
  minEventCount: undefined,
  maxEventCount: undefined,
  minUserCount: undefined,
  maxUserCount: undefined,
  propertyFilters: []
})

// 计算属性
const currentParams = computed<EventAnalysisRequest>(() => ({
  startDate: dateRange.value?.[0]?.format('YYYY-MM-DD') || '',
  endDate: dateRange.value?.[1]?.format('YYYY-MM-DD') || '',
  granularity: granularity.value as any,
  productLineIds: selectedProductLines.value,
  eventIds: selectedEvents.value,
  includeComparison: false,
  includeDetails: false,
  filterCondition: showAdvancedFilter.value ? advancedFilters.value : undefined
}))

// 事件选项过滤
const filterEventOption = (input: string, option: any) => {
  const event = props.eventList.find(e => e.eventId === option.value)
  if (!event) return false
  
  return event.eventName.toLowerCase().includes(input.toLowerCase()) ||
         event.eventCategory.toLowerCase().includes(input.toLowerCase()) ||
         event.eventId.toLowerCase().includes(input.toLowerCase())
}

// 处理日期范围变化
const handleDateRangeChange = (dates: [string, string] | [Dayjs, Dayjs] | null, dateStrings?: [string, string]) => {
  if (dates && Array.isArray(dates)) {
    // 如果是字符串数组，转换为Dayjs对象
    if (typeof dates[0] === 'string') {
      dateRange.value = [dayjs(dates[0]), dayjs(dates[1])]
    } else {
      dateRange.value = dates as [Dayjs, Dayjs]
    }
  } else {
    dateRange.value = [dayjs().subtract(7, 'day'), dayjs()]
  }
  emitChange()
}

// 处理时间粒度变化
const handleGranularityChange = () => {
  emitChange()
}

// 处理产品线变化
const handleProductLineChange = () => {
  emitChange()
}

// 处理事件变化
const handleEventChange = () => {
  emitChange()
}

// 处理应用筛选
const handleApplyFilter = () => {
  if (!dateRange.value || !dateRange.value[0] || !dateRange.value[1]) {
    message.warning('请选择时间范围')
    return
  }
  
  if (selectedEvents.value.length === 0) {
    message.warning('请至少选择一个事件')
    return
  }
  
  emitChange()
}

// 处理快捷时间选择
const handleQuickTimeSelect = (info: MenuInfo) => {
  const key = info.key as string
  const today = dayjs()
  let startDate: Dayjs
  let endDate: Dayjs = today
  
  switch (key) {
    case 'today':
      startDate = today
      break
    case 'yesterday':
      startDate = today.subtract(1, 'day')
      endDate = today.subtract(1, 'day')
      break
    case 'last7days':
      startDate = today.subtract(6, 'day')
      break
    case 'last30days':
      startDate = today.subtract(29, 'day')
      break
    case 'thisMonth':
      startDate = today.startOf('month')
      break
    case 'lastMonth':
      startDate = today.subtract(1, 'month').startOf('month')
      endDate = today.subtract(1, 'month').endOf('month')
      break
    default:
      return
  }
  
  dateRange.value = [startDate, endDate]
  emitChange()
}

// 处理重置高级筛选
const handleResetAdvancedFilter = () => {
  advancedFilters.value = {
    eventCategories: [],
    deviceTypes: [],
    operatingSystems: [],
    appVersions: [],
    regions: [],
    channels: [],
    minEventCount: undefined,
    maxEventCount: undefined,
    minUserCount: undefined,
    maxUserCount: undefined,
    propertyFilters: []
  }
}

// 处理应用高级筛选
const handleApplyAdvancedFilter = () => {
  emitChange()
  message.success('高级筛选已应用')
}

// 发出变化事件
const emitChange = () => {
  const params = currentParams.value
  emit('update:params', params)
  emit('change', params)
}

// 监听props变化
watch(() => props.params, (newParams) => {
  if (newParams.startDate && newParams.endDate) {
    dateRange.value = [dayjs(newParams.startDate), dayjs(newParams.endDate)]
  }
  granularity.value = newParams.granularity || 'DAY'
  selectedProductLines.value = newParams.productLineIds || []
  selectedEvents.value = newParams.eventIds || []
}, { immediate: true })
</script>

<style scoped>
.event-analysis-filter {
  padding: 16px;
}

.filter-item {
  margin-bottom: 8px;
}

.filter-label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.event-option {
  display: flex;
  flex-direction: column;
}

.event-name {
  font-size: 14px;
  color: #1f2937;
}

.event-category {
  font-size: 12px;
  color: #6b7280;
}

.advanced-filter-panel {
  margin-top: 16px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
}

.advanced-filter-actions {
  margin-top: 16px;
  text-align: right;
}
</style>
