<template>
  <div class="event-analysis-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <ThunderboltOutlined />
          事件分析
        </h1>
        <p class="page-description">
          深入分析用户在不同产品线中的行为事件，识别关键用户行为模式和使用路径
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="header-actions">
        <a-space>
          <a-button
            type="primary"
            :icon="h(ReloadOutlined)"
            :loading="loading"
            @click="handleRefresh"
          >
            刷新数据
          </a-button>
          <a-button
            :icon="h(DownloadOutlined)"
            :loading="loading"
            @click="handleExport"
          >
            导出数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <a-card :bordered="false" size="small">
        <EventAnalysisFilter
          v-model:params="filterParams"
          :event-list="eventList"
          @change="handleFilterChange"
        />
      </a-card>
    </div>

    <!-- 分析类型选择 -->
    <div class="analysis-type-section">
      <a-card :bordered="false" size="small">
        <a-radio-group v-model:value="analysisType" size="large" @change="handleAnalysisTypeChange">
          <a-radio-button value="trend">
            <LineChartOutlined />
            趋势分析
          </a-radio-button>
          <a-radio-button value="funnel">
            <FunnelPlotOutlined />
            漏斗分析
          </a-radio-button>
          <a-radio-button value="path">
            <NodeIndexOutlined />
            路径分析
          </a-radio-button>
          <a-radio-button value="comparison">
            <BarChartOutlined />
            对比分析
          </a-radio-button>
          <a-radio-button value="property">
            <PieChartOutlined />
            属性分析
          </a-radio-button>
        </a-radio-group>
      </a-card>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-section" v-if="coreMetricsList.length > 0">
      <a-row :gutter="16">
        <a-col :span="6" v-for="metric in coreMetricsList" :key="metric.name">
          <MetricCard
            :title="metric.name"
            :value="metric.value"
            :previous-value="metric.previousValue"
            :change-rate="metric.changeRate"
            :unit="metric.unit"
            :format="metric.format"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 分析内容区域 -->
    <div class="content-wrapper">
      <!-- 事件趋势分析 -->
      <div v-if="analysisType === 'trend'" class="analysis-content">
        <a-card title="事件趋势分析" class="chart-card" :loading="loading">
          <template #extra>
            <a-space>
              <a-select v-model:value="trendChartType" size="small" style="width: 120px">
                <a-select-option value="line">折线图</a-select-option>
                <a-select-option value="bar">柱状图</a-select-option>
                <a-select-option value="area">面积图</a-select-option>
              </a-select>
              <a-switch v-model:checked="showUserCount" size="small">
                <template #checkedChildren>用户数</template>
                <template #unCheckedChildren>事件数</template>
              </a-switch>
            </a-space>
          </template>
          <div class="chart-container">
            <EventTrendChart
              :data="trendData as any"
              :chart-type="trendChartType"
              :show-user-count="showUserCount"
              :loading="loading"
              style="height: 400px;"
            />
          </div>
        </a-card>
      </div>

      <!-- 事件漏斗分析 -->
      <div v-if="analysisType === 'funnel'" class="analysis-content">
        <a-card title="事件漏斗分析" class="chart-card" :loading="loading">
          <template #extra>
            <a-space>
              <a-button size="small" @click="handleFunnelSettings">
                <SettingOutlined />
                漏斗设置
              </a-button>
            </a-space>
          </template>
          <div class="chart-container">
            <EventFunnelChart
              :data="funnelData as any"
              :loading="loading"
              style="height: 500px;"
              @step-click="handleFunnelStepClick"
            />
          </div>
        </a-card>
      </div>

      <!-- 事件路径分析 -->
      <div v-if="analysisType === 'path'" class="analysis-content">
        <a-card title="事件路径分析" class="chart-card" :loading="loading">
          <template #extra>
            <a-space>
              <a-select v-model:value="pathViewType" size="small" style="width: 120px">
                <a-select-option value="sankey">桑基图</a-select-option>
                <a-select-option value="tree">树状图</a-select-option>
                <a-select-option value="graph">关系图</a-select-option>
              </a-select>
            </a-space>
          </template>
          <div class="chart-container">
            <EventPathChart
              :data="pathData"
              :view-type="pathViewType"
              :loading="loading"
              style="height: 600px;"
              @node-click="handlePathNodeClick"
            />
          </div>
        </a-card>
      </div>

      <!-- 事件对比分析 -->
      <div v-if="analysisType === 'comparison'" class="analysis-content">
        <a-card title="事件对比分析" class="chart-card" :loading="loading">
          <div class="chart-container">
            <EventComparisonChart
              :data="comparisonData as any"
              :loading="loading"
              style="height: 400px;"
            />
          </div>
        </a-card>
      </div>

      <!-- 事件属性分析 -->
      <div v-if="analysisType === 'property'" class="analysis-content">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card title="属性值分布" class="chart-card" :loading="loading">
              <EventPropertyChart
                :data="propertyData as any"
                chart-type="pie"
                :loading="loading"
                style="height: 350px;"
              />
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="属性趋势" class="chart-card" :loading="loading">
              <EventPropertyChart
                :data="propertyData as any"
                chart-type="bar"
                :loading="loading"
                style="height: 350px;"
              />
            </a-card>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 实时统计侧边栏 -->
    <div class="realtime-stats" v-if="realTimeStats">
      <a-card title="实时统计" size="small">
        <div v-if="realTimeStats">
          <div class="stats-item">
            <div class="stats-label">总事件数</div>
            <div class="stats-value">{{ realTimeStats.totalEvents?.toLocaleString() || '0' }}</div>
          </div>
          <div class="stats-item">
            <div class="stats-label">独立用户</div>
            <div class="stats-value">{{ realTimeStats.uniqueUsers?.toLocaleString() || '0' }}</div>
          </div>
          <div class="stats-item">
            <div class="stats-label">平均事件数/用户</div>
            <div class="stats-value">{{ realTimeStats.avgEventsPerUser?.toFixed(1) || '0.0' }}</div>
          </div>
          <a-divider />
          <div class="top-events">
            <div class="section-title">热门事件</div>
            <div v-for="event in realTimeStats.topEvents || []" :key="event.eventId" class="event-item">
              <div class="event-name">{{ event.eventName }}</div>
              <div class="event-count">{{ event.eventCount?.toLocaleString() || '0' }}</div>
            </div>
          </div>
        </div>
        <div v-else class="no-data">
          <a-empty description="暂无实时统计数据" />
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, h } from 'vue'
import { message } from 'ant-design-vue'
import {
  ThunderboltOutlined,
  ReloadOutlined,
  DownloadOutlined,
  LineChartOutlined,
  FunnelPlotOutlined,
  NodeIndexOutlined,
  BarChartOutlined,
  PieChartOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'
import { useEventAnalysisStore } from '@/stores/behavior/event'
import type { EventAnalysisRequest } from '@/api/behavior/event'

// 组件导入
import EventAnalysisFilter from './components/EventAnalysisFilter.vue'
import MetricCard from '@/components/charts/MetricCard.vue'
import EventTrendChart from './components/EventTrendChart.vue'
import EventFunnelChart from './components/EventFunnelChart.vue'
import EventPathChart from './components/EventPathChart.vue'
import EventComparisonChart from './components/EventComparisonChart.vue'
import EventPropertyChart from './components/EventPropertyChart.vue'

/**
 * 事件分析页面
 *
 * <AUTHOR>
 * @since 2025-07-01
 */

// Store
const eventAnalysisStore = useEventAnalysisStore()

// 响应式数据
const loading = computed(() => eventAnalysisStore.loading)
const trendData = computed(() => eventAnalysisStore.trendData || null)
const funnelData = computed(() => eventAnalysisStore.funnelData || null)
const pathData = computed(() => eventAnalysisStore.pathData || null)
const comparisonData = computed(() => eventAnalysisStore.comparisonData || null)
const propertyData = computed(() => eventAnalysisStore.propertyData || null)
const eventList = computed(() => eventAnalysisStore.eventList || [])
const realTimeStats = computed(() => eventAnalysisStore.realTimeStats || null)
const coreMetrics = computed(() => eventAnalysisStore.coreMetrics)

// 本地状态
const analysisType = ref<string>('trend')
const trendChartType = ref<string>('line')
const showUserCount = ref<boolean>(false)
const pathViewType = ref<string>('sankey')

// 筛选参数
const filterParams = ref<EventAnalysisRequest>({
  startDate: '',
  endDate: '',
  granularity: 'DAY',
  productLineIds: [],
  eventIds: [],
  includeComparison: false,
  includeDetails: false
})

// 核心指标列表
const coreMetricsList = computed(() => {
  const metrics = coreMetrics.value
  return Object.keys(metrics).map(key => ({
    name: metrics[key].name,
    value: metrics[key].value,
    previousValue: metrics[key].previousValue,
    changeRate: metrics[key].changeRate,
    unit: metrics[key].unit,
    format: metrics[key].format
  }))
})

// 处理筛选器变化
const handleFilterChange = (params: EventAnalysisRequest) => {
  filterParams.value = { ...params }
  loadAnalysisData()
}

// 处理分析类型变化
const handleAnalysisTypeChange = () => {
  loadAnalysisData()
}

// 加载分析数据
const loadAnalysisData = async () => {
  if (!filterParams.value.startDate || !filterParams.value.endDate) {
    return
  }

  const params = {
    startDate: filterParams.value.startDate,
    endDate: filterParams.value.endDate,
    granularity: filterParams.value.granularity,
    productLineIds: filterParams.value.productLineIds,
    eventIds: filterParams.value.eventIds || [],
    includeComparison: filterParams.value.includeComparison
  }

  try {
    switch (analysisType.value) {
      case 'trend':
        if (params.eventIds.length > 0) {
          await eventAnalysisStore.fetchEventTrends(params)
        }
        break
      case 'funnel':
        if (params.eventIds.length > 1) {
          await eventAnalysisStore.fetchEventFunnel(params)
        }
        break
      case 'path':
        if (params.eventIds.length > 0) {
          await eventAnalysisStore.fetchEventPath({
            ...params,
            startEventIds: params.eventIds.slice(0, 1),
            endEventIds: params.eventIds.slice(-1)
          })
        }
        break
      case 'comparison':
        if (params.eventIds.length > 1) {
          await eventAnalysisStore.fetchEventComparison(params)
        }
        break
      case 'property':
        if (params.eventIds.length === 1) {
          await eventAnalysisStore.fetchEventProperty({
            ...params,
            eventId: params.eventIds[0],
            propertyNames: ['platform', 'version', 'user_type']
          })
        }
        break
    }
  } catch (error) {
    console.error('加载分析数据失败:', error)
  }
}

// 刷新数据
const handleRefresh = () => {
  loadAnalysisData()
  eventAnalysisStore.fetchRealTimeStats({
    eventIds: filterParams.value.eventIds,
    productLineIds: filterParams.value.productLineIds
  })
}

// 导出数据
const handleExport = async () => {
  try {
    await eventAnalysisStore.exportData(filterParams.value)
  } catch (error) {
    console.error('导出数据失败:', error)
  }
}

// 漏斗设置
const handleFunnelSettings = () => {
  message.info('漏斗设置功能开发中...')
}

// 漏斗步骤点击
const handleFunnelStepClick = (step: any) => {
  console.log('漏斗步骤点击:', step)
}

// 路径节点点击
const handlePathNodeClick = (node: any) => {
  console.log('路径节点点击:', node)
}

// 监听分析类型变化
watch(analysisType, () => {
  loadAnalysisData()
})

// 组件挂载
onMounted(async () => {
  // 获取事件列表
  await eventAnalysisStore.fetchEventList()
  
  // 获取实时统计
  await eventAnalysisStore.fetchRealTimeStats()
  
  // 设置默认时间范围
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(endDate.getDate() - 30)
  
  filterParams.value.startDate = startDate.toISOString().split('T')[0]
  filterParams.value.endDate = endDate.toISOString().split('T')[0]
})
</script>

<style scoped>
.event-analysis-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
  position: relative;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.header-content h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  margin: 8px 0 0 0;
  color: #6b7280;
  font-size: 14px;
}

.filter-section,
.analysis-type-section,
.metrics-section {
  margin-bottom: 16px;
}

.content-wrapper {
  margin-right: 320px;
}

.chart-card {
  margin-bottom: 16px;
}

.chart-container {
  min-height: 300px;
}

.realtime-stats {
  position: fixed;
  top: 120px;
  right: 24px;
  width: 280px;
  z-index: 100;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.stats-label {
  color: #6b7280;
  font-size: 14px;
}

.stats-value {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
}

.event-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px;
  background: #f9fafb;
  border-radius: 4px;
}

.event-name {
  font-size: 12px;
  color: #6b7280;
}

.event-count {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}
</style>
