<template>
  <div class="search-analysis">
    <div class="page-header">
      <h1>搜索行为分析</h1>
      <p>分析用户搜索行为模式，了解用户需求和搜索效果</p>
    </div>

    <!-- 筛选条件 -->
    <a-card class="filter-card" :bordered="false">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item label="产品线">
            <a-select
              v-model:value="filters.productLine"
              placeholder="请选择产品线"
              allow-clear
              @change="handleFilterChange"
            >
              <a-select-option value="all">全部产品线</a-select-option>
              <a-select-option value="pdf-reader">PDF Reader</a-select-option>
              <a-select-option value="pdf-editor">PDF Editor</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="时间范围">
            <a-range-picker
              v-model:value="filters.dateRange"
              format="YYYY-MM-DD"
              @change="handleFilterChange"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="搜索类型">
            <a-select
              v-model:value="filters.searchType"
              placeholder="请选择搜索类型"
              allow-clear
              @change="handleFilterChange"
            >
              <a-select-option value="all">全部类型</a-select-option>
              <a-select-option value="text">文本搜索</a-select-option>
              <a-select-option value="advanced">高级搜索</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="结果状态">
            <a-select
              v-model:value="filters.resultStatus"
              placeholder="请选择结果状态"
              allow-clear
              @change="handleFilterChange"
            >
              <a-select-option value="all">全部状态</a-select-option>
              <a-select-option value="success">有结果</a-select-option>
              <a-select-option value="empty">无结果</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-card>

    <!-- 核心指标卡片 -->
    <a-row :gutter="16" class="metrics-row">
      <a-col :span="6">
        <StatCard
          title="总搜索次数"
          :value="metrics.totalSearches"
          suffix="次"
          trend="up"
          :trend-value="8.5"
          color="#1890ff"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="搜索成功率"
          :value="metrics.successRate"
          suffix="%"
          trend="up"
          :trend-value="2.3"
          color="#52c41a"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="平均搜索时长"
          :value="metrics.avgSearchTime"
          suffix="秒"
          trend="down"
          :trend-value="0.8"
          color="#faad14"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="热门关键词数"
          :value="metrics.hotKeywords"
          suffix="个"
          trend="up"
          :trend-value="12.1"
          color="#722ed1"
        />
      </a-col>
    </a-row>

    <!-- 搜索趋势和热词分析 -->
    <a-row :gutter="16">
      <a-col :span="12">
        <a-card title="搜索趋势分析" :bordered="false">
          <div ref="searchTrendChart" style="height: 400px;"></div>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="热门搜索词云" :bordered="false">
          <div ref="keywordCloudChart" style="height: 400px;"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 搜索关键词排行 -->
    <a-card title="搜索关键词排行" :bordered="false" class="keyword-table-card">
      <a-table
        :columns="columns"
        :data-source="keywordList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'successRate'">
            <a-progress
              :percent="record.successRate"
              size="small"
              :stroke-color="getProgressColor(record.successRate)"
            />
          </template>
          <template v-else-if="column.key === 'trend'">
            <span :class="['trend', record.trend > 0 ? 'trend-up' : 'trend-down']">
              <CaretUpOutlined v-if="record.trend > 0" />
              <CaretDownOutlined v-else />
              {{ Math.abs(record.trend) }}%
            </span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-button type="link" size="small" @click="viewKeywordDetail(record)">
              查看详情
            </a-button>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import * as echarts from 'echarts'
import StatCard from '@/components/StatCard.vue'
import { http } from '@/utils/request'

// 筛选条件
const filters = reactive({
  productLine: 'all',
  dateRange: [dayjs().subtract(30, 'day'), dayjs()] as [Dayjs, Dayjs],
  searchType: 'all',
  resultStatus: 'all'
})

// 核心指标 - 从API获取
const metrics = reactive({
  totalSearches: 0,
  successRate: 0,
  avgSearchTime: 0,
  hotKeywords: 0
})

// 表格相关
const loading = ref(false)
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true
})

const columns = [
  {
    title: '搜索关键词',
    dataIndex: 'keyword',
    key: 'keyword',
    width: 200
  },
  {
    title: '搜索次数',
    dataIndex: 'searchCount',
    key: 'searchCount',
    width: 120,
    sorter: true
  },
  {
    title: '成功率',
    dataIndex: 'successRate',
    key: 'successRate',
    width: 120,
    sorter: true
  },
  {
    title: '平均结果数',
    dataIndex: 'avgResults',
    key: 'avgResults',
    width: 120
  },
  {
    title: '趋势',
    dataIndex: 'trend',
    key: 'trend',
    width: 80
  },
  {
    title: '最后搜索时间',
    dataIndex: 'lastSearchTime',
    key: 'lastSearchTime',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 100
  }
]

// 关键词列表 - 从API获取
const keywordList = ref([])

// 图表引用
const searchTrendChart = ref()
const keywordCloudChart = ref()

// 处理筛选条件变化
const handleFilterChange = () => {
  loadSearchData()
}

// 处理表格变化
const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadSearchData()
}

// 获取进度条颜色
const getProgressColor = (rate: number) => {
  if (rate >= 90) return '#52c41a'
  if (rate >= 70) return '#faad14'
  return '#ff4d4f'
}

// 查看关键词详情
const viewKeywordDetail = (record: any) => {
  console.log('查看关键词详情:', record)
}

// 加载搜索数据
const loadSearchData = async () => {
  loading.value = true
  try {
    // 调用搜索关键词API
    const response = await http.get('/behavior/search/keywords', {
      params: {
        startDate: filters.dateRange[0].format('YYYY-MM-DD'),
        endDate: filters.dateRange[1].format('YYYY-MM-DD'),
        page: pagination.current,
        pageSize: pagination.pageSize
      }
    })

    if (response.data.success) {
      keywordList.value = response.data.data.list
      pagination.total = response.data.data.total
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    keywordList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  // 搜索趋势分析
  const trendChart = echarts.init(searchTrendChart.value)
  const trendOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['搜索次数', '成功次数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '搜索次数',
        type: 'line',
        data: [4200, 4800, 4100, 4600, 4300, 5200, 4900],
        smooth: true,
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '成功次数',
        type: 'line',
        data: [3600, 4200, 3500, 4000, 3800, 4500, 4300],
        smooth: true,
        itemStyle: { color: '#52c41a' }
      }
    ]
  }
  trendChart.setOption(trendOption)

  // 关键词云图（简化版）
  const cloudChart = echarts.init(keywordCloudChart.value)
  const cloudOption = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 1256, name: 'PDF合并' },
          { value: 987, name: '文档转换' },
          { value: 765, name: '页面提取' },
          { value: 543, name: '文本搜索' },
          { value: 432, name: '注释添加' },
          { value: 321, name: '签名功能' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  cloudChart.setOption(cloudOption)
}

onMounted(() => {
  loadSearchData()
  initCharts()
})
</script>

<style scoped lang="less">
.search-analysis {
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #262626;
    }
    
    p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .filter-card {
    margin-bottom: 24px;
  }

  .metrics-row {
    margin-bottom: 24px;
  }

  .keyword-table-card {
    margin-top: 24px;
  }

  .trend {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    
    &.trend-up {
      color: #52c41a;
    }
    
    &.trend-down {
      color: #ff4d4f;
    }
  }
}
</style>
