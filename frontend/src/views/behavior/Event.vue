<template>
  <div class="behavior-event-page">
    <div class="page-header">
      <h1>事件分析</h1>
      <p>分析用户行为事件，了解用户操作习惯</p>
    </div>
    
    <div class="content-wrapper">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-card title="事件概览" class="overview-card">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic
                  title="总事件数"
                  :value="statistics.totalEvents"
                  suffix="次"
                  :value-style="{ color: '#3f8600' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="独立用户数"
                  :value="statistics.uniqueUsers"
                  suffix="人"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="平均事件数/用户"
                  :value="statistics.avgEventsPerUser"
                  :precision="1"
                  :value-style="{ color: '#722ed1' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="事件转化率"
                  :value="statistics.conversionRate"
                  suffix="%"
                  :precision="2"
                  :value-style="{ color: '#cf1322' }"
                />
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>
      
      <a-row :gutter="16" style="margin-top: 16px;">
        <a-col :span="24">
          <a-card title="事件趋势分析" class="chart-card">
            <div class="chart-container">
              <v-chart :option="eventTrendOption" style="height: 400px;" />
            </div>
          </a-card>
        </a-col>
      </a-row>
      
      <a-row :gutter="16" style="margin-top: 16px;">
        <a-col :span="12">
          <a-card title="热门事件排行" class="chart-card">
            <div class="chart-container">
              <v-chart :option="topEventsOption" style="height: 300px;" />
            </div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="事件分布" class="chart-card">
            <div class="chart-container">
              <v-chart :option="eventDistributionOption" style="height: 300px;" />
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import VChart from 'vue-echarts'
import type { EChartsOption } from 'echarts'

// 统计数据
const statistics = ref({
  totalEvents: 125680,
  uniqueUsers: 12580,
  avgEventsPerUser: 9.8,
  conversionRate: 15.6
})

// 事件趋势图配置
const eventTrendOption = ref<EChartsOption>({
  title: {
    text: '事件趋势分析',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['点击事件', '页面浏览', '搜索事件', '下载事件'],
    top: 30
  },
  xAxis: {
    type: 'category',
    data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '点击事件',
      type: 'line',
      data: [820, 932, 1290, 1340, 1320, 1200]
    },
    {
      name: '页面浏览',
      type: 'line',
      data: [1200, 1320, 1890, 2340, 2100, 1800]
    },
    {
      name: '搜索事件',
      type: 'line',
      data: [450, 520, 780, 890, 850, 720]
    },
    {
      name: '下载事件',
      type: 'line',
      data: [320, 380, 450, 520, 480, 420]
    }
  ]
})

// 热门事件排行图配置
const topEventsOption = ref<EChartsOption>({
  title: {
    text: '热门事件TOP10',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  xAxis: {
    type: 'value'
  },
  yAxis: {
    type: 'category',
    data: ['登录', '搜索', '下载', '分享', '收藏', '评论', '点赞', '浏览', '注册', '购买']
  },
  series: [
    {
      type: 'bar',
      data: [12580, 11200, 9800, 8500, 7200, 6800, 6200, 5800, 4500, 3200],
      itemStyle: {
        color: '#1890ff'
      }
    }
  ]
})

// 事件分布图配置
const eventDistributionOption = ref<EChartsOption>({
  title: {
    text: '事件类型分布',
    left: 'center'
  },
  tooltip: {
    trigger: 'item'
  },
  series: [
    {
      type: 'pie',
      radius: '60%',
      data: [
        { value: 35, name: '用户行为' },
        { value: 28, name: '页面交互' },
        { value: 20, name: '功能使用' },
        { value: 12, name: '系统事件' },
        { value: 5, name: '其他' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
})

onMounted(() => {
  // 组件挂载后的初始化逻辑
  console.log('事件分析页面已加载')
})
</script>

<style scoped lang="less">
.behavior-event-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: #262626;
  }
  
  p {
    margin: 0;
    color: #8c8c8c;
    font-size: 14px;
  }
}

.content-wrapper {
  .overview-card {
    .ant-statistic {
      text-align: center;
    }
  }
  
  .chart-card {
    .chart-container {
      padding: 16px 0;
    }
  }
}
</style>
