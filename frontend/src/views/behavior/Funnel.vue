<template>
  <div class="funnel-analysis">
    <div class="page-header">
      <h1>漏斗分析</h1>
      <p>分析用户在关键业务流程中的转化情况，识别流失节点和优化机会</p>
    </div>

    <!-- 筛选条件 -->
    <a-card class="filter-card" :bordered="false">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item label="漏斗类型">
            <a-select
              v-model:value="filters.funnelType"
              placeholder="请选择漏斗类型"
              @change="handleFilterChange"
            >
              <a-select-option value="registration">用户注册漏斗</a-select-option>
              <a-select-option value="purchase">购买转化漏斗</a-select-option>
              <a-select-option value="feature">功能使用漏斗</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="时间范围">
            <a-range-picker
              v-model:value="filters.dateRange"
              format="YYYY-MM-DD"
              @change="handleFilterChange"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="产品线">
            <a-select
              v-model:value="filters.productLine"
              placeholder="请选择产品线"
              allow-clear
              @change="handleFilterChange"
            >
              <a-select-option value="all">全部产品线</a-select-option>
              <a-select-option value="pdf-reader">PDF Reader</a-select-option>
              <a-select-option value="pdf-editor">PDF Editor</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="用户群体">
            <a-select
              v-model:value="filters.userGroup"
              placeholder="请选择用户群体"
              allow-clear
              @change="handleFilterChange"
            >
              <a-select-option value="all">全部用户</a-select-option>
              <a-select-option value="new">新用户</a-select-option>
              <a-select-option value="returning">回访用户</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-card>

    <!-- 核心指标卡片 -->
    <a-row :gutter="16" class="metrics-row">
      <a-col :span="6">
        <StatCard
          title="总体转化率"
          :value="metrics.overallConversion"
          suffix="%"
          trend="up"
          :trend-value="2.8"
          color="#1890ff"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="最大流失节点"
          :value="metrics.maxLossStep"
          suffix=""
          trend="down"
          :trend-value="5.2"
          color="#ff4d4f"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="平均转化时长"
          :value="metrics.avgConversionTime"
          suffix="天"
          trend="down"
          :trend-value="0.5"
          color="#52c41a"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="漏斗完成用户"
          :value="metrics.completedUsers"
          suffix="人"
          trend="up"
          :trend-value="15.3"
          color="#722ed1"
        />
      </a-col>
    </a-row>

    <!-- 漏斗图表 -->
    <a-row :gutter="16">
      <a-col :span="16">
        <a-card title="转化漏斗图" :bordered="false">
          <div ref="funnelChart" style="height: 500px;"></div>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card title="转化步骤详情" :bordered="false">
          <div class="funnel-steps">
            <div v-for="(step, index) in funnelSteps" :key="index" class="step-item">
              <div class="step-header">
                <span class="step-number">{{ index + 1 }}</span>
                <span class="step-name">{{ step.name }}</span>
              </div>
              <div class="step-metrics">
                <div class="metric-item">
                  <span class="metric-label">用户数:</span>
                  <span class="metric-value">{{ step.users.toLocaleString() }}</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">转化率:</span>
                  <span class="metric-value" :class="getConversionClass(step.conversion)">
                    {{ step.conversion }}%
                  </span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">流失率:</span>
                  <span class="metric-value loss-rate">{{ step.lossRate }}%</span>
                </div>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 转化趋势分析 -->
    <a-card title="转化趋势分析" :bordered="false" class="trend-card">
      <div ref="conversionTrendChart" style="height: 400px;"></div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import dayjs, { type Dayjs } from 'dayjs'
import * as echarts from 'echarts'
import StatCard from '@/components/StatCard.vue'
import { http } from '@/utils/request'

// 筛选条件
const filters = reactive({
  funnelType: 'registration',
  dateRange: [dayjs().subtract(30, 'day'), dayjs()] as [Dayjs, Dayjs],
  productLine: 'all',
  userGroup: 'all'
})

// 核心指标
const metrics = reactive({
  overallConversion: 23.5,
  maxLossStep: '第2步',
  avgConversionTime: 3.2,
  completedUsers: 2456
})

// 漏斗步骤数据
const funnelSteps = ref([
  {
    name: '访问官网',
    users: 50000,
    conversion: 100,
    lossRate: 0
  },
  {
    name: '下载软件',
    users: 25000,
    conversion: 50.0,
    lossRate: 50.0
  },
  {
    name: '安装软件',
    users: 20000,
    conversion: 80.0,
    lossRate: 20.0
  },
  {
    name: '首次启动',
    users: 18000,
    conversion: 90.0,
    lossRate: 10.0
  },
  {
    name: '完成注册',
    users: 12000,
    conversion: 66.7,
    lossRate: 33.3
  },
  {
    name: '首次使用',
    users: 10000,
    conversion: 83.3,
    lossRate: 16.7
  }
])

// 图表引用
const funnelChart = ref()
const conversionTrendChart = ref()

// 处理筛选条件变化
const handleFilterChange = () => {
  loadFunnelData()
  updateCharts()
}

// 获取转化率样式类
const getConversionClass = (rate: number) => {
  if (rate >= 80) return 'high-conversion'
  if (rate >= 60) return 'medium-conversion'
  return 'low-conversion'
}

// 加载漏斗数据
const loadFunnelData = async () => {
  try {
    // 调用公共漏斗分析API
    const response = await http.get('/behavior/funnel/public/conversion-analysis', {
      params: {
        startDate: filterForm.startDate,
        endDate: filterForm.endDate,
        funnelSteps: filterForm.funnelSteps || ['访问', '注册', '激活', '付费'],
        productLineIds: filterForm.productLineIds
      }
    })

    if (response.data.success) {
      // 更新漏斗数据状态
      updateCharts()
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

// 更新图表
const updateCharts = () => {
  initFunnelChart()
  initTrendChart()
}

// 初始化漏斗图
const initFunnelChart = () => {
  const chart = echarts.init(funnelChart.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c}人 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: funnelSteps.value.map(step => step.name)
    },
    series: [
      {
        name: '转化漏斗',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: 50000,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside',
          formatter: '{b}: {c}人\n转化率: {d}%'
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 20
          }
        },
        data: funnelSteps.value.map(step => ({
          value: step.users,
          name: step.name
        }))
      }
    ]
  }
  chart.setOption(option)
}

// 初始化趋势图
const initTrendChart = () => {
  const chart = echarts.init(conversionTrendChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['整体转化率', '第1步转化率', '第2步转化率', '第3步转化率']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '整体转化率',
        type: 'line',
        data: [20.5, 21.2, 22.8, 23.1, 22.9, 23.5, 24.2],
        smooth: true,
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '第1步转化率',
        type: 'line',
        data: [48.2, 49.1, 50.5, 49.8, 50.2, 50.0, 51.2],
        smooth: true,
        itemStyle: { color: '#52c41a' }
      },
      {
        name: '第2步转化率',
        type: 'line',
        data: [78.5, 79.2, 80.1, 79.8, 80.5, 80.0, 81.2],
        smooth: true,
        itemStyle: { color: '#faad14' }
      },
      {
        name: '第3步转化率',
        type: 'line',
        data: [88.2, 89.1, 90.5, 89.8, 90.2, 90.0, 91.2],
        smooth: true,
        itemStyle: { color: '#722ed1' }
      }
    ]
  }
  chart.setOption(option)
}

onMounted(() => {
  loadFunnelData()
  updateCharts()
})
</script>

<style scoped lang="less">
.funnel-analysis {
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #262626;
    }
    
    p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .filter-card {
    margin-bottom: 24px;
  }

  .metrics-row {
    margin-bottom: 24px;
  }

  .trend-card {
    margin-top: 24px;
  }

  .funnel-steps {
    .step-item {
      padding: 16px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .step-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        
        .step-number {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: #1890ff;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          margin-right: 8px;
        }
        
        .step-name {
          font-weight: 500;
          color: #262626;
        }
      }
      
      .step-metrics {
        .metric-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 4px;
          font-size: 12px;
          
          .metric-label {
            color: #8c8c8c;
          }
          
          .metric-value {
            font-weight: 500;
            
            &.high-conversion {
              color: #52c41a;
            }
            
            &.medium-conversion {
              color: #faad14;
            }
            
            &.low-conversion {
              color: #ff4d4f;
            }
            
            &.loss-rate {
              color: #ff4d4f;
            }
          }
        }
      }
    }
  }
}
</style>
