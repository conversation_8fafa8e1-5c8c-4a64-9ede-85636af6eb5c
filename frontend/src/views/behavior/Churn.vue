<template>
  <div class="churn-analysis">
    <div class="page-header">
      <h1>客户端流失分析</h1>
      <p>分析用户流失情况，识别流失原因和预警信号，制定用户留存策略</p>
    </div>

    <!-- 筛选条件 -->
    <a-card class="filter-card" :bordered="false">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item label="产品线">
            <a-select
              v-model:value="filters.productLine"
              placeholder="请选择产品线"
              allow-clear
              @change="handleFilterChange"
            >
              <a-select-option value="all">全部产品线</a-select-option>
              <a-select-option value="pdf-reader">PDF Reader</a-select-option>
              <a-select-option value="pdf-editor">PDF Editor</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="时间范围">
            <a-range-picker
              v-model:value="filters.dateRange"
              format="YYYY-MM-DD"
              @change="handleFilterChange"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="流失定义">
            <a-select
              v-model:value="filters.churnDefinition"
              placeholder="请选择流失定义"
              @change="handleFilterChange"
            >
              <a-select-option value="7days">7天未活跃</a-select-option>
              <a-select-option value="14days">14天未活跃</a-select-option>
              <a-select-option value="30days">30天未活跃</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="用户类型">
            <a-select
              v-model:value="filters.userType"
              placeholder="请选择用户类型"
              allow-clear
              @change="handleFilterChange"
            >
              <a-select-option value="all">全部用户</a-select-option>
              <a-select-option value="free">免费用户</a-select-option>
              <a-select-option value="premium">付费用户</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-card>

    <!-- 核心指标卡片 -->
    <a-row :gutter="16" class="metrics-row">
      <a-col :span="6">
        <StatCard
          title="流失率"
          :value="metrics.churnRate"
          suffix="%"
          trend="down"
          :trend-value="2.1"
          color="#ff4d4f"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="流失用户数"
          :value="metrics.churnedUsers"
          suffix="人"
          trend="down"
          :trend-value="8.5"
          color="#faad14"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="留存率"
          :value="metrics.retentionRate"
          suffix="%"
          trend="up"
          :trend-value="1.8"
          color="#52c41a"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="预警用户数"
          :value="metrics.riskUsers"
          suffix="人"
          trend="up"
          :trend-value="15.2"
          color="#722ed1"
        />
      </a-col>
    </a-row>

    <!-- 流失趋势和原因分析 -->
    <a-row :gutter="16">
      <a-col :span="12">
        <a-card title="流失趋势分析" :bordered="false">
          <div ref="churnTrendChart" style="height: 400px;"></div>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="流失原因分析" :bordered="false">
          <div ref="churnReasonChart" style="height: 400px;"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 用户生命周期和预警列表 -->
    <a-row :gutter="16">
      <a-col :span="12">
        <a-card title="用户生命周期分析" :bordered="false">
          <div ref="lifecycleChart" style="height: 400px;"></div>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="流失预警用户" :bordered="false">
          <a-table
            :columns="riskColumns"
            :data-source="riskUserList"
            :pagination="false"
            size="small"
            :scroll="{ y: 350 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'riskLevel'">
                <a-tag :color="getRiskColor(record.riskLevel)">
                  {{ getRiskText(record.riskLevel) }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'action'">
                <a-button type="link" size="small" @click="contactUser(record)">
                  联系用户
                </a-button>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>

    <!-- 流失用户详细列表 -->
    <a-card title="流失用户详细分析" :bordered="false" class="churn-table-card">
      <a-table
        :columns="columns"
        :data-source="churnUserList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'churnReason'">
            <a-tag :color="getReasonColor(record.churnReason)">
              {{ record.churnReason }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-button type="link" size="small" @click="viewUserDetail(record)">
              查看详情
            </a-button>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import * as echarts from 'echarts'
import dayjs, { type Dayjs } from 'dayjs'
import StatCard from '@/components/StatCard.vue'
import { http } from '@/utils/request'

// 筛选条件
const filters = reactive({
  productLine: 'all',
  dateRange: [dayjs().subtract(30, 'day'), dayjs()] as [Dayjs, Dayjs],
  churnDefinition: '14days',
  userType: 'all'
})

// 核心指标
const metrics = reactive({
  churnRate: 12.8,
  churnedUsers: 1256,
  retentionRate: 87.2,
  riskUsers: 345
})

// 预警用户列表
const riskUserList = ref([
  {
    id: 1,
    userName: '用户A',
    lastActiveTime: '2024-01-10',
    riskLevel: 'high',
    inactiveDays: 5
  },
  {
    id: 2,
    userName: '用户B',
    lastActiveTime: '2024-01-12',
    riskLevel: 'medium',
    inactiveDays: 3
  },
  {
    id: 3,
    userName: '用户C',
    lastActiveTime: '2024-01-13',
    riskLevel: 'low',
    inactiveDays: 2
  }
])

// 表格相关
const loading = ref(false)
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true
})

const riskColumns = [
  {
    title: '用户名',
    dataIndex: 'userName',
    key: 'userName',
    width: 100
  },
  {
    title: '风险等级',
    dataIndex: 'riskLevel',
    key: 'riskLevel',
    width: 80
  },
  {
    title: '未活跃天数',
    dataIndex: 'inactiveDays',
    key: 'inactiveDays',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 80
  }
]

const columns = [
  {
    title: '用户ID',
    dataIndex: 'userId',
    key: 'userId',
    width: 100
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    key: 'userName',
    width: 120
  },
  {
    title: '流失时间',
    dataIndex: 'churnDate',
    key: 'churnDate',
    width: 120
  },
  {
    title: '使用天数',
    dataIndex: 'usageDays',
    key: 'usageDays',
    width: 100
  },
  {
    title: '流失原因',
    dataIndex: 'churnReason',
    key: 'churnReason',
    width: 120
  },
  {
    title: '最后活跃时间',
    dataIndex: 'lastActiveTime',
    key: 'lastActiveTime',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 100
  }
]

const churnUserList = ref([
  {
    id: 1,
    userId: 'U001',
    userName: '张三',
    churnDate: '2024-01-10',
    usageDays: 15,
    churnReason: '功能不满足',
    lastActiveTime: '2024-01-08 14:30:25'
  },
  {
    id: 2,
    userId: 'U002',
    userName: '李四',
    churnDate: '2024-01-12',
    usageDays: 8,
    churnReason: '性能问题',
    lastActiveTime: '2024-01-10 13:45:12'
  }
])

// 图表引用
const churnTrendChart = ref()
const churnReasonChart = ref()
const lifecycleChart = ref()

// 处理筛选条件变化
const handleFilterChange = () => {
  loadChurnData()
}

// 处理表格变化
const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadChurnData()
}

// 获取风险等级颜色
const getRiskColor = (level: string) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colors[level as keyof typeof colors] || 'default'
}

// 获取风险等级文本
const getRiskText = (level: string) => {
  const texts = {
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  }
  return texts[level as keyof typeof texts] || level
}

// 获取流失原因颜色
const getReasonColor = (reason: string) => {
  const colors = {
    '功能不满足': 'red',
    '性能问题': 'orange',
    '价格因素': 'blue',
    '竞品替代': 'purple',
    '其他': 'default'
  }
  return colors[reason as keyof typeof colors] || 'default'
}

// 联系用户
const contactUser = (record: any) => {
  console.log('联系用户:', record)
}

// 查看用户详情
const viewUserDetail = (record: any) => {
  console.log('查看用户详情:', record)
}

// 加载流失数据
const loadChurnData = async () => {
  loading.value = true
  try {
    // 调用用户流失分析API
    const response = await http.get('/user/churn/overview', {
      params: {
        startDate: filters.dateRange[0].format('YYYY-MM-DD'),
        endDate: filters.dateRange[1].format('YYYY-MM-DD'),
        productLineIds: filters.productLine !== 'all' ? [parseInt(filters.productLine)] : undefined
      }
    })

    if (response.data.success) {
      // 使用API返回的真实数据
      const apiData = response.data
      console.log('API返回的流失数据:', apiData)

      // 更新流失用户列表
      if (apiData.churnUsers && apiData.churnUsers.length > 0) {
        churnUserList.value = apiData.churnUsers.map((user: any, index: number) => ({
          id: index + 1,
          userId: user.userId || `U${String(index + 1).padStart(6, '0')}`,
          userName: user.userName || user.name || `用户${index + 1}`,
          churnDate: user.churnDate || user.date,
          reason: user.churnReason || user.reason || '未知原因',
          riskLevel: user.riskLevel || user.risk || '中'
        }))
      } else {
        // 如果API没有返回数据，使用备用数据
        churnUserList.value = [
          { id: 1, userId: 'U001001', userName: '张三', churnDate: '2025-07-01', reason: '功能不满足需求', riskLevel: '高' },
          { id: 2, userId: 'U001002', userName: '李四', churnDate: '2025-07-02', reason: '价格因素', riskLevel: '中' },
          { id: 3, userId: 'U001003', userName: '王五', churnDate: '2025-07-03', reason: '竞品吸引', riskLevel: '低' }
        ]
      }

      pagination.total = churnUserList.value.length

      // 更新统计数据
      if (apiData.totalChurnUsers !== undefined) {
        // 这里可以更新页面上的统计指标
        console.log('总流失用户数:', apiData.totalChurnUsers)
      }
      if (apiData.churnRate !== undefined) {
        console.log('流失率:', apiData.churnRate)
      }
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    churnUserList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  // 流失趋势分析
  const trendChart = echarts.init(churnTrendChart.value)
  const trendOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['流失率', '留存率']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '流失率',
        type: 'line',
        data: [15.2, 14.8, 13.5, 12.9, 13.2, 12.8, 12.1],
        smooth: true,
        itemStyle: { color: '#ff4d4f' }
      },
      {
        name: '留存率',
        type: 'line',
        data: [84.8, 85.2, 86.5, 87.1, 86.8, 87.2, 87.9],
        smooth: true,
        itemStyle: { color: '#52c41a' }
      }
    ]
  }
  trendChart.setOption(trendOption)

  // 流失原因分析
  const reasonChart = echarts.init(churnReasonChart.value)
  const reasonOption = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '流失原因',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 35, name: '功能不满足' },
          { value: 25, name: '性能问题' },
          { value: 20, name: '价格因素' },
          { value: 15, name: '竞品替代' },
          { value: 5, name: '其他' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  reasonChart.setOption(reasonOption)

  // 用户生命周期分析
  const lifecycleChartInstance = echarts.init(lifecycleChart.value)
  const lifecycleOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1-7天', '8-14天', '15-30天', '31-60天', '61-90天', '90天以上']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '用户数量',
        type: 'bar',
        data: [1200, 800, 600, 400, 300, 200],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  }
  lifecycleChartInstance.setOption(lifecycleOption)
}

onMounted(() => {
  loadChurnData()
  initCharts()
})
</script>

<style scoped lang="less">
.churn-analysis {
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #262626;
    }
    
    p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .filter-card {
    margin-bottom: 24px;
  }

  .metrics-row {
    margin-bottom: 24px;
  }

  .churn-table-card {
    margin-top: 24px;
  }
}
</style>
