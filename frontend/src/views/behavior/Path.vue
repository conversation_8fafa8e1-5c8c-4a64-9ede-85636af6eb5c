<template>
  <div class="path-analysis">
    <div class="page-header">
      <h1>用户行为路径</h1>
      <p>分析用户在产品中的行为路径，了解用户使用习惯和流程优化点</p>
    </div>

    <!-- 筛选条件 -->
    <a-card class="filter-card" :bordered="false">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item label="产品线">
            <a-select
              v-model:value="filters.productLine"
              placeholder="请选择产品线"
              allow-clear
              @change="handleFilterChange"
            >
              <a-select-option value="all">全部产品线</a-select-option>
              <a-select-option value="pdf-reader">PDF Reader</a-select-option>
              <a-select-option value="pdf-editor">PDF Editor</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="时间范围">
            <a-range-picker
              v-model:value="filters.dateRange"
              format="YYYY-MM-DD"
              @change="handleFilterChange"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="路径类型">
            <a-select
              v-model:value="filters.pathType"
              placeholder="请选择路径类型"
              allow-clear
              @change="handleFilterChange"
            >
              <a-select-option value="all">全部路径</a-select-option>
              <a-select-option value="common">常用路径</a-select-option>
              <a-select-option value="conversion">转化路径</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="用户群体">
            <a-select
              v-model:value="filters.userGroup"
              placeholder="请选择用户群体"
              allow-clear
              @change="handleFilterChange"
            >
              <a-select-option value="all">全部用户</a-select-option>
              <a-select-option value="new">新用户</a-select-option>
              <a-select-option value="active">活跃用户</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-card>

    <!-- 核心指标卡片 -->
    <a-row :gutter="16" class="metrics-row">
      <a-col :span="6">
        <StatCard
          title="总路径数"
          :value="metrics.totalPaths"
          suffix="条"
          trend="up"
          :trend-value="15.2"
          color="#1890ff"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="平均路径长度"
          :value="metrics.avgPathLength"
          suffix="步"
          trend="down"
          :trend-value="0.8"
          color="#52c41a"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="路径完成率"
          :value="metrics.completionRate"
          suffix="%"
          trend="up"
          :trend-value="3.5"
          color="#faad14"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="平均停留时长"
          :value="metrics.avgDuration"
          suffix="分钟"
          trend="up"
          :trend-value="2.1"
          color="#722ed1"
        />
      </a-col>
    </a-row>

    <!-- 路径流向图和热门路径 -->
    <a-row :gutter="16">
      <a-col :span="16">
        <a-card title="用户行为流向图" :bordered="false">
          <div ref="pathFlowChart" style="height: 500px;"></div>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card title="热门路径排行" :bordered="false">
          <div class="path-ranking">
            <div v-for="(path, index) in topPaths" :key="index" class="path-item">
              <div class="path-rank">{{ index + 1 }}</div>
              <div class="path-content">
                <div class="path-name">{{ path.name }}</div>
                <div class="path-stats">
                  <span class="path-count">{{ path.count }}次</span>
                  <span class="path-rate">{{ path.rate }}%</span>
                </div>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 路径详细列表 -->
    <a-card title="路径详细分析" :bordered="false" class="path-table-card">
      <a-table
        :columns="columns"
        :data-source="pathList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'completionRate'">
            <a-progress
              :percent="record.completionRate"
              size="small"
              :stroke-color="getProgressColor(record.completionRate)"
            />
          </template>
          <template v-else-if="column.key === 'trend'">
            <span :class="['trend', record.trend > 0 ? 'trend-up' : 'trend-down']">
              <CaretUpOutlined v-if="record.trend > 0" />
              <CaretDownOutlined v-else />
              {{ Math.abs(record.trend) }}%
            </span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-button type="link" size="small" @click="viewPathDetail(record)">
              查看详情
            </a-button>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import * as echarts from 'echarts'
import StatCard from '@/components/StatCard.vue'
import { http } from '@/utils/request'

// 筛选条件
const filters = reactive({
  productLine: 'all',
  dateRange: [dayjs().subtract(30, 'day'), dayjs()] as [Dayjs, Dayjs],
  pathType: 'all',
  userGroup: 'all'
})

// 核心指标
const metrics = reactive({
  totalPaths: 1256,
  avgPathLength: 4.8,
  completionRate: 78.5,
  avgDuration: 12.3
})

// 热门路径数据
const topPaths = ref([
  { name: '启动 → 打开文档 → 阅读', count: 8956, rate: 35.2 },
  { name: '启动 → 打开文档 → 注释 → 保存', count: 6543, rate: 25.7 },
  { name: '启动 → 新建文档 → 编辑', count: 4321, rate: 17.0 },
  { name: '启动 → 打开文档 → 打印', count: 3210, rate: 12.6 },
  { name: '启动 → 设置 → 偏好设置', count: 2456, rate: 9.5 }
])

// 表格相关
const loading = ref(false)
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true
})

const columns = [
  {
    title: '路径名称',
    dataIndex: 'pathName',
    key: 'pathName',
    width: 300
  },
  {
    title: '使用次数',
    dataIndex: 'usageCount',
    key: 'usageCount',
    width: 120,
    sorter: true
  },
  {
    title: '完成率',
    dataIndex: 'completionRate',
    key: 'completionRate',
    width: 120,
    sorter: true
  },
  {
    title: '平均时长',
    dataIndex: 'avgDuration',
    key: 'avgDuration',
    width: 120
  },
  {
    title: '趋势',
    dataIndex: 'trend',
    key: 'trend',
    width: 80
  },
  {
    title: '操作',
    key: 'action',
    width: 100
  }
]

const pathList = ref([
  {
    id: 1,
    pathName: '启动 → 打开文档 → 阅读 → 关闭',
    usageCount: 8956,
    completionRate: 92.5,
    avgDuration: '8.5分钟',
    trend: 12.3
  },
  {
    id: 2,
    pathName: '启动 → 打开文档 → 注释 → 保存 → 关闭',
    usageCount: 6543,
    completionRate: 85.6,
    avgDuration: '15.2分钟',
    trend: 5.1
  },
  {
    id: 3,
    pathName: '启动 → 新建文档 → 编辑 → 保存',
    usageCount: 4321,
    completionRate: 78.3,
    avgDuration: '22.8分钟',
    trend: -2.1
  }
])

// 图表引用
const pathFlowChart = ref()

// 处理筛选条件变化
const handleFilterChange = () => {
  loadPathData()
}

// 处理表格变化
const handleTableChange = (pag: any, _filters: any, _sorter: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadPathData()
}

// 获取进度条颜色
const getProgressColor = (rate: number) => {
  if (rate >= 80) return '#52c41a'
  if (rate >= 60) return '#faad14'
  return '#ff4d4f'
}

// 查看路径详情
const viewPathDetail = (record: any) => {
  console.log('查看路径详情:', record)
}

// 加载路径数据
const loadPathData = async () => {
  loading.value = true
  try {
    // 调用公共用户路径分析API
    const response = await http.get('/behavior/path/public/flow-analysis', {
      params: {
        startDate: filters.dateRange[0].format('YYYY-MM-DD'),
        endDate: filters.dateRange[1].format('YYYY-MM-DD'),
        startNodes: ['首页'],
        endNodes: [],
        productLineIds: filters.productLine !== 'all' ? [filters.productLine] : []
      }
    })

    if (response.data.success) {
      // 使用API返回的真实数据
      const apiData = response.data
      console.log('API返回的路径数据:', apiData)

      // 转换API数据为前端需要的格式
      if (apiData.topPaths && apiData.topPaths.length > 0) {
        pathList.value = apiData.topPaths.map((path: any, index: number) => ({
          id: index + 1,
          path: path.pathName || path.pathId,
          userCount: path.userCount || 0,
          conversionRate: path.conversionRate || 0,
          avgTime: path.avgTime || 0
        }))
      } else {
        // 如果API没有返回数据，使用备用数据
        pathList.value = [
          { id: 1, path: '首页 → 功能页 → 编辑页', userCount: 1250, conversionRate: 78.5, avgTime: 5.2 },
          { id: 2, path: '首页 → 帮助页 → 功能页', userCount: 890, conversionRate: 65.3, avgTime: 8.1 },
          { id: 3, path: '首页 → 设置页', userCount: 650, conversionRate: 45.2, avgTime: 3.5 }
        ]
      }

      pagination.total = pathList.value.length

      // 更新统计数据
      if (apiData.totalPaths !== undefined) {
        // 这里可以更新页面上的统计指标
        console.log('总路径数:', apiData.totalPaths)
        console.log('独立用户数:', apiData.uniqueUsers)
        console.log('平均路径长度:', apiData.avgPathLength)
        console.log('平均路径时间:', apiData.avgPathTime)
      }
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  // 用户行为流向图（桑基图）
  const flowChart = echarts.init(pathFlowChart.value)
  const flowOption = {
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove'
    },
    series: [
      {
        type: 'sankey',
        data: [
          { name: '启动应用' },
          { name: '打开文档' },
          { name: '新建文档' },
          { name: '阅读文档' },
          { name: '编辑文档' },
          { name: '添加注释' },
          { name: '保存文档' },
          { name: '打印文档' },
          { name: '关闭应用' }
        ],
        links: [
          { source: '启动应用', target: '打开文档', value: 15000 },
          { source: '启动应用', target: '新建文档', value: 5000 },
          { source: '打开文档', target: '阅读文档', value: 8000 },
          { source: '打开文档', target: '编辑文档', value: 4000 },
          { source: '打开文档', target: '添加注释', value: 3000 },
          { source: '阅读文档', target: '关闭应用', value: 6000 },
          { source: '阅读文档', target: '打印文档', value: 2000 },
          { source: '编辑文档', target: '保存文档', value: 3500 },
          { source: '添加注释', target: '保存文档', value: 2800 },
          { source: '保存文档', target: '关闭应用', value: 6300 },
          { source: '打印文档', target: '关闭应用', value: 2000 },
          { source: '新建文档', target: '编辑文档', value: 4500 },
          { source: '新建文档', target: '保存文档', value: 500 }
        ],
        emphasis: {
          focus: 'adjacency'
        },
        lineStyle: {
          color: 'gradient',
          curveness: 0.5
        }
      }
    ]
  }
  flowChart.setOption(flowOption)
}

onMounted(() => {
  loadPathData()
  initCharts()
})
</script>

<style scoped lang="less">
.path-analysis {
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #262626;
    }
    
    p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .filter-card {
    margin-bottom: 24px;
  }

  .metrics-row {
    margin-bottom: 24px;
  }

  .path-table-card {
    margin-top: 24px;
  }

  .path-ranking {
    .path-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .path-rank {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin-right: 12px;
        
        &:nth-child(1) {
          background: #ff4d4f;
          color: white;
        }
        
        &:nth-child(2) {
          background: #faad14;
          color: white;
        }
        
        &:nth-child(3) {
          background: #52c41a;
          color: white;
        }
      }
      
      .path-content {
        flex: 1;
        
        .path-name {
          font-size: 14px;
          color: #262626;
          margin-bottom: 4px;
        }
        
        .path-stats {
          font-size: 12px;
          color: #8c8c8c;
          
          .path-count {
            margin-right: 12px;
          }
          
          .path-rate {
            color: #1890ff;
          }
        }
      }
    }
  }

  .trend {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    
    &.trend-up {
      color: #52c41a;
    }
    
    &.trend-down {
      color: #ff4d4f;
    }
  }
}
</style>
