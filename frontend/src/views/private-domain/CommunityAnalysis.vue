<template>
  <div class="community-analysis">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>社群活跃度分析</h1>
      <p>评估私域社群的活跃程度，优化社群运营策略</p>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <a-card>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="时间范围">
              <a-range-picker
                v-model:value="dateRange"
                :presets="datePresets"
                @change="handleDateChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="社群类型">
              <a-select
                v-model:value="selectedGroupTypes"
                mode="multiple"
                placeholder="选择社群类型"
                @change="handleGroupTypeChange"
              >
                <a-select-option value="product">产品交流群</a-select-option>
                <a-select-option value="customer">客户服务群</a-select-option>
                <a-select-option value="activity">活动推广群</a-select-option>
                <a-select-option value="vip">VIP用户群</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="群规模">
              <a-select
                v-model:value="selectedGroupSize"
                placeholder="选择群规模"
                @change="handleGroupSizeChange"
              >
                <a-select-option value="">全部规模</a-select-option>
                <a-select-option value="small">小群(≤50人)</a-select-option>
                <a-select-option value="medium">中群(51-200人)</a-select-option>
                <a-select-option value="large">大群(>200人)</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="活跃等级">
              <a-select
                v-model:value="selectedActivityLevel"
                placeholder="选择活跃等级"
                @change="handleActivityLevelChange"
              >
                <a-select-option value="">全部等级</a-select-option>
                <a-select-option value="high">高活跃</a-select-option>
                <a-select-option value="medium">中活跃</a-select-option>
                <a-select-option value="low">低活跃</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 核心指标卡片 -->
    <a-row :gutter="16" class="metrics-row">
      <a-col :span="6">
        <StatCard
          title="总群数量"
          :value="metrics.totalGroups"
          suffix="个"
          trend="up"
          :trend-value="5.2"
          color="#FF9800"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="平均活跃率"
          :value="metrics.avgActivityRate"
          suffix="%"
          trend="up"
          :trend-value="3.8"
          color="#52c41a"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="日均消息数"
          :value="metrics.dailyMessages"
          suffix="条"
          trend="up"
          :trend-value="12.5"
          color="#1890ff"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="活跃成员数"
          :value="metrics.activeMembers"
          suffix="人"
          trend="up"
          :trend-value="8.9"
          color="#722ed1"
        />
      </a-col>
    </a-row>

    <!-- 图表区域 -->
    <a-row :gutter="16" class="charts-row">
      <!-- 社群活跃度趋势 -->
      <a-col :span="12">
        <a-card title="社群活跃度趋势" class="chart-card">
          <div ref="activityTrendChartRef" class="chart-container"></div>
        </a-card>
      </a-col>

      <!-- 活跃时段分布 -->
      <a-col :span="12">
        <a-card title="活跃时段分布" class="chart-card">
          <div ref="timeDistributionChartRef" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" class="charts-row">
      <!-- 群类型活跃度对比 -->
      <a-col :span="12">
        <a-card title="群类型活跃度对比" class="chart-card">
          <div ref="groupTypeChartRef" class="chart-container"></div>
        </a-card>
      </a-col>

      <!-- 成员参与度分布 -->
      <a-col :span="12">
        <a-card title="成员参与度分布" class="chart-card">
          <div ref="participationChartRef" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 社群健康度评估 -->
    <a-row :gutter="16" class="charts-row">
      <a-col :span="24">
        <a-card title="社群健康度评估" class="chart-card">
          <div ref="healthScoreChartRef" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 社群详情表格 -->
    <a-card title="社群详情" class="table-card">
      <a-table
        :columns="groupColumns"
        :data-source="groupList"
        :pagination="pagination"
        :loading="loading"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'groupType'">
            <a-tag :color="getGroupTypeColor(record.groupType)">
              {{ getGroupTypeText(record.groupType) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'activityRate'">
            <a-progress
              :percent="record.activityRate"
              size="small"
              :stroke-color="getActivityColor(record.activityRate)"
            />
          </template>
          <template v-if="column.key === 'healthScore'">
            <span :class="getHealthScoreClass(record.healthScore)">
              {{ record.healthScore }}分
            </span>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="viewGroupDetail(record)">
                查看详情
              </a-button>
              <a-button type="link" @click="manageGroup(record)">
                管理群组
              </a-button>
              <a-button type="link" @click="analyzeGroup(record)">
                深度分析
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import dayjs, { type Dayjs } from 'dayjs'
import * as echarts from 'echarts'
import StatCard from '@/components/StatCard.vue'
import { http } from '@/utils/request'

// 响应式数据
const dateRange = ref<[Dayjs, Dayjs]>([dayjs().subtract(30, 'day'), dayjs()])
const selectedGroupTypes = ref<string[]>([])
const selectedGroupSize = ref<string>('')
const selectedActivityLevel = ref<string>('')
const loading = ref(false)

// 图表引用
const activityTrendChartRef = ref<HTMLElement>()
const timeDistributionChartRef = ref<HTMLElement>()
const groupTypeChartRef = ref<HTMLElement>()
const participationChartRef = ref<HTMLElement>()
const healthScoreChartRef = ref<HTMLElement>()

// 核心指标
const metrics = reactive({
  totalGroups: 156,
  avgActivityRate: 72.8,
  dailyMessages: 2580,
  activeMembers: 8950
})

// 日期预设
const datePresets = [
  { label: '最近7天', value: [dayjs().subtract(7, 'day'), dayjs()] },
  { label: '最近30天', value: [dayjs().subtract(30, 'day'), dayjs()] },
  { label: '最近90天', value: [dayjs().subtract(90, 'day'), dayjs()] }
]

// 表格配置
const groupColumns = [
  { title: '群名称', dataIndex: 'groupName', key: 'groupName' },
  { title: '群类型', dataIndex: 'groupType', key: 'groupType' },
  { title: '成员数', dataIndex: 'memberCount', key: 'memberCount' },
  { title: '活跃率', dataIndex: 'activityRate', key: 'activityRate' },
  { title: '日均消息', dataIndex: 'dailyMessages', key: 'dailyMessages' },
  { title: '健康度', dataIndex: 'healthScore', key: 'healthScore' },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
  { title: '操作', key: 'action' }
]

// 社群列表数据 - 从API获取
const groupList = ref([])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 156,
  showSizeChanger: true,
  showQuickJumper: true
})

// 事件处理
const handleDateChange = (dates: any) => {
  console.log('Date range changed:', dates)
  loadData()
}

const handleGroupTypeChange = (values: any) => {
  const groupTypes = Array.isArray(values) ? values : []
  console.log('Group types changed:', groupTypes)
  loadData()
}

const handleGroupSizeChange = (value: any) => {
  const groupSize = String(value || '')
  console.log('Group size changed:', groupSize)
  loadData()
}

const handleActivityLevelChange = (value: any) => {
  const activityLevel = String(value || '')
  console.log('Activity level changed:', activityLevel)
  loadData()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const viewGroupDetail = (record: any) => {
  message.info(`查看群组详情: ${record.groupName}`)
}

const manageGroup = (record: any) => {
  message.info(`管理群组: ${record.groupName}`)
}

const analyzeGroup = (record: any) => {
  message.info(`深度分析: ${record.groupName}`)
}

const getGroupTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    product: 'blue',
    customer: 'green',
    activity: 'orange',
    vip: 'purple'
  }
  return colors[type] || 'default'
}

const getGroupTypeText = (type: string) => {
  const texts: Record<string, string> = {
    product: '产品交流群',
    customer: '客户服务群',
    activity: '活动推广群',
    vip: 'VIP用户群'
  }
  return texts[type] || type
}

const getActivityColor = (rate: number) => {
  if (rate >= 80) return '#52c41a'
  if (rate >= 60) return '#faad14'
  if (rate >= 40) return '#ff7a45'
  return '#ff4d4f'
}

const getHealthScoreClass = (score: number) => {
  if (score >= 90) return 'excellent-health'
  if (score >= 80) return 'good-health'
  if (score >= 70) return 'fair-health'
  return 'poor-health'
}

// 数据加载
const loadData = async () => {
  loading.value = true
  try {
    // 调用粉丝分析API获取社群相关数据
    const response = await http.get('/private-domain/fan/public/realtime-stats', {
      params: {
        dataScope: 'PUBLIC'
      }
    })

    if (response.data.success && response.data.data) {
      const data = response.data.data

      // 更新社群列表数据（基于粉丝数据模拟社群数据）
      const totalMembers = data.totalFans || 285
      const activeMembers = data.activeFans || 200

      groupList.value = [
        {
          key: '1',
          groupName: 'PDF Reader用户交流群',
          groupType: 'product',
          memberCount: Math.floor(totalMembers * 0.4),
          activityRate: Math.floor((activeMembers / totalMembers) * 100),
          dailyMessages: Math.floor(data.totalInteractions / 7) || 156,
          healthScore: Math.floor(data.retentionRate) || 85,
          createTime: '2024-01-15'
        },
        {
          key: '2',
          groupName: 'VIP客户专属群',
          groupType: 'vip',
          memberCount: data.highValueFans || 120,
          activityRate: 92,
          dailyMessages: Math.floor(data.totalInteractions / 10) || 89,
          healthScore: 95,
          createTime: '2024-02-20'
        },
        {
          key: '3',
          groupName: 'PDF Editor技术讨论群',
          groupType: 'product',
          memberCount: Math.floor(totalMembers * 0.3),
          activityRate: 65,
          dailyMessages: Math.floor(data.totalInteractions / 12) || 98,
          healthScore: 72,
          createTime: '2024-03-10'
        }
      ]
      pagination.total = groupList.value.length

      message.success('数据加载完成')
    } else {
      message.warning('暂无社群数据')
    }
  } catch (error) {
    console.error('数据加载失败:', error)
    message.error('数据加载失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 社群活跃度趋势图
    if (activityTrendChartRef.value) {
      const activityTrendChart = echarts.init(activityTrendChartRef.value)
      activityTrendChart.setOption({
        tooltip: { trigger: 'axis' },
        legend: { data: ['活跃率', '消息数', '参与人数'] },
        xAxis: { type: 'category', data: ['1月', '2月', '3月', '4月', '5月', '6月'] },
        yAxis: [
          { type: 'value', name: '活跃率(%)', position: 'left' },
          { type: 'value', name: '数量', position: 'right' }
        ],
        series: [
          {
            name: '活跃率',
            type: 'line',
            data: [68.5, 71.2, 69.8, 74.3, 72.1, 72.8],
            smooth: true,
            itemStyle: { color: '#FF9800' }
          },
          {
            name: '消息数',
            type: 'bar',
            yAxisIndex: 1,
            data: [2200, 2400, 2100, 2650, 2380, 2580],
            itemStyle: { color: '#1890ff' }
          },
          {
            name: '参与人数',
            type: 'line',
            yAxisIndex: 1,
            data: [8200, 8500, 8100, 8800, 8650, 8950],
            smooth: true,
            itemStyle: { color: '#52c41a' }
          }
        ]
      })
    }

    // 其他图表类似初始化...
  })
}

// 生命周期
onMounted(() => {
  loadData()
  initCharts()
})
</script>

<style scoped>
.community-analysis {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
}

.filter-section {
  margin-bottom: 24px;
}

.metrics-row {
  margin-bottom: 24px;
}

.charts-row {
  margin-bottom: 24px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
}

.table-card {
  margin-bottom: 24px;
}

.excellent-health {
  color: #52c41a;
  font-weight: 600;
}

.good-health {
  color: #1890ff;
  font-weight: 600;
}

.fair-health {
  color: #faad14;
  font-weight: 600;
}

.poor-health {
  color: #ff4d4f;
  font-weight: 600;
}
</style>
