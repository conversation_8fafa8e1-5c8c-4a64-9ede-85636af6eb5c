<template>
  <div class="fan-analysis">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>粉丝数据分析</h1>
      <p>监控私域平台（企业微信、公众号）的粉丝增长和流失情况，评估私域运营效果</p>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <a-card>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="时间范围">
              <a-range-picker
                v-model:value="dateRange"
                :presets="datePresets"
                @change="handleDateChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="平台类型">
              <a-select
                v-model:value="selectedPlatforms"
                mode="multiple"
                placeholder="选择平台"
                @change="handlePlatformChange"
              >
                <a-select-option value="wechat">企业微信</a-select-option>
                <a-select-option value="official">公众号</a-select-option>
                <a-select-option value="weibo">微博</a-select-option>
                <a-select-option value="douyin">抖音</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="粉丝来源">
              <a-select
                v-model:value="selectedSource"
                placeholder="选择来源"
                @change="handleSourceChange"
              >
                <a-select-option value="">全部来源</a-select-option>
                <a-select-option value="organic">自然增长</a-select-option>
                <a-select-option value="campaign">活动推广</a-select-option>
                <a-select-option value="referral">推荐关注</a-select-option>
                <a-select-option value="paid">付费推广</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="粉丝类型">
              <a-select
                v-model:value="selectedFanType"
                placeholder="选择类型"
                @change="handleFanTypeChange"
              >
                <a-select-option value="">全部粉丝</a-select-option>
                <a-select-option value="active">活跃粉丝</a-select-option>
                <a-select-option value="silent">沉默粉丝</a-select-option>
                <a-select-option value="high_value">高价值粉丝</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 核心指标卡片 -->
    <a-row :gutter="16" class="metrics-row">
      <a-col :span="6">
        <StatCard
          title="累计粉丝数"
          :value="metrics.totalFans"
          suffix="人"
          trend="up"
          :trend-value="8.5"
          color="#9C27B0"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="新增粉丝数"
          :value="metrics.newFans"
          suffix="人"
          trend="up"
          :trend-value="12.3"
          color="#52c41a"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="粉丝活跃率"
          :value="metrics.activeRate"
          suffix="%"
          trend="up"
          :trend-value="2.1"
          color="#1890ff"
        />
      </a-col>
      <a-col :span="6">
        <StatCard
          title="粉丝净增量"
          :value="metrics.netGrowth"
          suffix="人"
          trend="up"
          :trend-value="15.7"
          color="#faad14"
        />
      </a-col>
    </a-row>

    <!-- 图表区域 -->
    <a-row :gutter="16" class="charts-row">
      <!-- 粉丝增长趋势 -->
      <a-col :span="12">
        <a-card title="粉丝增长趋势" class="chart-card">
          <div ref="growthChartRef" class="chart-container"></div>
        </a-card>
      </a-col>

      <!-- 粉丝来源分布 -->
      <a-col :span="12">
        <a-card title="粉丝来源分布" class="chart-card">
          <div ref="sourceChartRef" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" class="charts-row">
      <!-- 平台粉丝对比 -->
      <a-col :span="12">
        <a-card title="平台粉丝对比" class="chart-card">
          <div ref="platformChartRef" class="chart-container"></div>
        </a-card>
      </a-col>

      <!-- 粉丝活跃度分布 -->
      <a-col :span="12">
        <a-card title="粉丝活跃度分布" class="chart-card">
          <div ref="activityChartRef" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 粉丝地域分布 -->
    <a-row :gutter="16" class="charts-row">
      <a-col :span="24">
        <a-card title="粉丝地域分布" class="chart-card">
          <div ref="geoChartRef" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 粉丝详情表格 -->
    <a-card title="粉丝详情" class="table-card">
      <a-table
        :columns="fanColumns"
        :data-source="fanList"
        :pagination="pagination"
        :loading="loading"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'platform'">
            <a-tag :color="getPlatformColor(record.platform)">
              {{ getPlatformText(record.platform) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'activityLevel'">
            <a-progress
              :percent="record.activityLevel"
              size="small"
              :stroke-color="getActivityColor(record.activityLevel)"
            />
          </template>
          <template v-if="column.key === 'fanValue'">
            <span :class="getValueClass(record.fanValue)">
              {{ record.fanValue }}
            </span>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="viewFanDetail(record)">
                查看详情
              </a-button>
              <a-button type="link" @click="manageFan(record)">
                管理
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import dayjs, { type Dayjs } from 'dayjs'
import * as echarts from 'echarts'
import StatCard from '@/components/StatCard.vue'
import { http } from '@/utils/request'

// 响应式数据
const dateRange = ref<[Dayjs, Dayjs]>([dayjs().subtract(30, 'day'), dayjs()])
const selectedPlatforms = ref<string[]>([])
const selectedSource = ref<string>('')
const selectedFanType = ref<string>('')
const loading = ref(false)

// 图表引用
const growthChartRef = ref<HTMLElement>()
const sourceChartRef = ref<HTMLElement>()
const platformChartRef = ref<HTMLElement>()
const activityChartRef = ref<HTMLElement>()
const geoChartRef = ref<HTMLElement>()

// 核心指标
const metrics = reactive({
  totalFans: 125800,
  newFans: 3250,
  activeRate: 68.5,
  netGrowth: 2890
})

// 日期预设
const datePresets = [
  { label: '最近7天', value: [dayjs().subtract(7, 'day'), dayjs()] },
  { label: '最近30天', value: [dayjs().subtract(30, 'day'), dayjs()] },
  { label: '最近90天', value: [dayjs().subtract(90, 'day'), dayjs()] }
]

// 表格配置
const fanColumns = [
  { title: '粉丝ID', dataIndex: 'fanId', key: 'fanId' },
  { title: '昵称', dataIndex: 'nickname', key: 'nickname' },
  { title: '平台', dataIndex: 'platform', key: 'platform' },
  { title: '关注时间', dataIndex: 'followTime', key: 'followTime' },
  { title: '活跃度', dataIndex: 'activityLevel', key: 'activityLevel' },
  { title: '粉丝价值', dataIndex: 'fanValue', key: 'fanValue' },
  { title: '地区', dataIndex: 'region', key: 'region' },
  { title: '操作', key: 'action' }
]

// 粉丝列表数据 - 从API获取
const fanList = ref([])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 100,
  showSizeChanger: true,
  showQuickJumper: true
})

// 事件处理
const handleDateChange = (dates: any) => {
  console.log('Date range changed:', dates)
  loadData()
}

const handlePlatformChange = (values: any) => {
  const platforms = Array.isArray(values) ? values : []
  console.log('Platforms changed:', platforms)
  loadData()
}

const handleSourceChange = (value: any) => {
  const source = String(value || '')
  console.log('Source changed:', source)
  loadData()
}

const handleFanTypeChange = (value: any) => {
  const fanType = String(value || '')
  console.log('Fan type changed:', fanType)
  loadData()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const viewFanDetail = (record: any) => {
  message.info(`查看粉丝详情: ${record.nickname}`)
}

const manageFan = (record: any) => {
  message.info(`管理粉丝: ${record.nickname}`)
}

const getPlatformColor = (platform: string) => {
  const colors: Record<string, string> = {
    wechat: 'green',
    official: 'blue',
    weibo: 'orange',
    douyin: 'purple'
  }
  return colors[platform] || 'default'
}

const getPlatformText = (platform: string) => {
  const texts: Record<string, string> = {
    wechat: '企业微信',
    official: '公众号',
    weibo: '微博',
    douyin: '抖音'
  }
  return texts[platform] || platform
}

const getActivityColor = (level: number) => {
  if (level >= 80) return '#52c41a'
  if (level >= 60) return '#faad14'
  if (level >= 40) return '#ff7a45'
  return '#ff4d4f'
}

const getValueClass = (value: string) => {
  if (value === '高价值') return 'high-value'
  if (value === '中等价值') return 'medium-value'
  return 'low-value'
}

// 数据加载
const loadData = async () => {
  loading.value = true
  try {
    // 获取日期范围 - 不传时间范围，查询所有数据
    const startDate = dateRange.value?.[0]?.format('YYYY-MM-DD')
    const endDate = dateRange.value?.[1]?.format('YYYY-MM-DD')

    // 调用粉丝分析API
    const response = await http.get('/private-domain/fan/public/list', {
      params: {
        startDate,
        endDate,
        platforms: selectedPlatforms.value.length > 0 ? selectedPlatforms.value : undefined,
        source: selectedSource.value || undefined,
        fanType: selectedFanType.value || undefined,
        page: pagination.current,
        pageSize: pagination.pageSize,
        dataScope: 'PUBLIC'
      }
    })

    if (response.data.success && response.data.data) {
      const data = response.data.data

      // 更新核心指标
      if (data.coreMetrics) {
        metrics.totalFans = data.coreMetrics.totalFans?.value || 0
        metrics.activeFans = data.coreMetrics.activeFans?.value || 0
        metrics.highValueFans = data.coreMetrics.highValueFans?.value || 0
        metrics.avgActivityLevel = data.coreMetrics.avgActivityLevel?.value || 0
      }

      // 更新粉丝列表
      if (data.fanList) {
        fanList.value = data.fanList.map((fan: any) => ({
          key: fan.fanId,
          fanId: fan.fanCode,
          nickname: fan.nickname,
          platform: fan.platform,
          followTime: fan.followTime,
          activityLevel: fan.activityLevel,
          fanValue: fan.fanValue === 'high' ? '高价值' : fan.fanValue === 'medium' ? '中等价值' : '低价值',
          region: fan.region
        }))
      }

      // 更新分页信息
      if (data.pageInfo) {
        pagination.total = data.pageInfo.total
        pagination.current = data.pageInfo.page
        pagination.pageSize = data.pageInfo.pageSize
      }

      message.success('数据加载完成')
    } else {
      message.warning('暂无粉丝数据')
    }
  } catch (error) {
    console.error('数据加载失败:', error)
    message.error('数据加载失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 粉丝增长趋势图
    if (growthChartRef.value) {
      const growthChart = echarts.init(growthChartRef.value)
      growthChart.setOption({
        tooltip: { trigger: 'axis' },
        legend: { data: ['新增粉丝', '取关粉丝', '净增长'] },
        xAxis: { type: 'category', data: ['1月', '2月', '3月', '4月', '5月', '6月'] },
        yAxis: { type: 'value' },
        series: [
          {
            name: '新增粉丝',
            type: 'line',
            data: [3200, 3500, 3100, 3800, 3300, 3250],
            smooth: true,
            itemStyle: { color: '#9C27B0' }
          },
          {
            name: '取关粉丝',
            type: 'line',
            data: [280, 320, 250, 380, 290, 360],
            smooth: true,
            itemStyle: { color: '#ff4d4f' }
          },
          {
            name: '净增长',
            type: 'line',
            data: [2920, 3180, 2850, 3420, 3010, 2890],
            smooth: true,
            itemStyle: { color: '#52c41a' }
          }
        ]
      })
    }

    // 其他图表类似初始化...
  })
}

// 生命周期
onMounted(() => {
  loadData()
  initCharts()
})
</script>

<style scoped>
.fan-analysis {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
}

.filter-section {
  margin-bottom: 24px;
}

.metrics-row {
  margin-bottom: 24px;
}

.charts-row {
  margin-bottom: 24px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
}

.table-card {
  margin-bottom: 24px;
}

.high-value {
  color: #52c41a;
  font-weight: 600;
}

.medium-value {
  color: #faad14;
  font-weight: 600;
}

.low-value {
  color: #ff4d4f;
  font-weight: 600;
}
</style>
