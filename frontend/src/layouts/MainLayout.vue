<template>
  <a-layout class="main-layout">
    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      class="sidebar"
      width="256"
      :collapsed-width="isMobile ? 0 : 80"
      :breakpoint="isMobile ? 'lg' : undefined"
      theme="light"
      :class="{ 'mobile-sidebar': isMobile }"
    >
      <!-- Logo -->
      <div class="logo">
        <img src="/logo.svg" alt="SCRM-Next" />
        <span v-show="!collapsed" class="logo-text">SCRM-Next</span>
      </div>
      
      <!-- 视角切换 -->
      <div v-show="!collapsed" class="view-switch">
        <a-radio-group v-model:value="currentView" button-style="solid" size="small">
          <a-radio-button value="function">功能视角</a-radio-button>
          <a-radio-button value="product">产品线视角</a-radio-button>
        </a-radio-group>
      </div>
      
      <!-- 菜单 -->
      <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        mode="inline"
        theme="light"
        :items="menuItems as any"
        @click="handleMenuClick"
      />
    </a-layout-sider>
    
    <!-- 主内容区 -->
    <a-layout>
      <!-- 顶部导航 -->
      <a-layout-header class="header">
        <div class="header-left">
          <a-button
            type="text"
            :icon="collapsed ? h(MenuUnfoldOutlined) : h(MenuFoldOutlined)"
            @click="toggleCollapsed"
          />
          
          <!-- 面包屑导航 -->
          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item v-for="item in breadcrumbItems" :key="item.path">
              <router-link v-if="item.path" :to="item.path">{{ item.title }}</router-link>
              <span v-else>{{ item.title }}</span>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        
        <div class="header-right">
          <!-- 产品线筛选器 -->
          <a-select
            v-model:value="selectedProductLines"
            mode="multiple"
            placeholder="选择产品线"
            style="width: 200px; margin-right: 16px"
            :options="productLineOptions"
            @change="handleProductLineChange"
          />
          
          <!-- 用户菜单 -->
          <a-dropdown>
            <a-space class="user-info">
              <a-avatar :src="userInfo?.avatar" :size="32">
                {{ userInfo?.realName?.charAt(0) || userInfo?.username?.charAt(0) }}
              </a-avatar>
              <span>{{ userInfo?.realName || userInfo?.username }}</span>
              <DownOutlined />
            </a-space>
            
            <template #overlay>
              <a-menu @click="handleUserMenuClick">
                <a-menu-item key="profile">
                  <UserOutlined />
                  个人信息
                </a-menu-item>
                <a-menu-item key="settings">
                  <SettingOutlined />
                  系统设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>
      
      <!-- 内容区域 -->
      <a-layout-content class="content">
        <router-view />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { h, ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DownOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  DashboardOutlined,
  TeamOutlined,
  DollarOutlined,
  AppstoreOutlined,
  BarChartOutlined,
  LineChartOutlined,
  PieChartOutlined,
  GlobalOutlined,
  ThunderboltOutlined
} from '@ant-design/icons-vue'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import { storeToRefs } from 'pinia'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const appStore = useAppStore()

// 响应式数据
const { userInfo } = storeToRefs(userStore)
const { 
  sidebarCollapsed: collapsed, 
  selectedProductLines, 
  currentView,
  functionMenuList,
  productMenuList 
} = storeToRefs(appStore)

// 菜单相关
const selectedKeys = ref<string[]>([])
const openKeys = ref<string[]>([])

// 移动端检测
const isMobile = ref(false)

// 图标映射
const iconMap: Record<string, any> = {
  DashboardOutlined,
  UserOutlined,
  TeamOutlined,
  DollarOutlined,
  AppstoreOutlined,
  BarChartOutlined,
  LineChartOutlined,
  PieChartOutlined,
  GlobalOutlined,
  SettingOutlined,
  ThunderboltOutlined
}

// 转换菜单项，将字符串图标转换为组件
const transformMenuItems = (items: any[]): any[] => {
  return items.map(item => ({
    ...item,
    icon: item.icon && typeof item.icon === 'string' ? h(iconMap[item.icon]) : item.icon,
    children: item.children ? transformMenuItems(item.children) : undefined
  }))
}

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
  if (isMobile.value) {
    appStore.setSidebarCollapsed(true)
  }
}

const handleResize = () => {
  checkMobile()
}

// 计算属性
const menuItems = computed(() => {
  const rawItems = currentView.value === 'function' ? functionMenuList.value : productMenuList.value
  return transformMenuItems(rawItems)
})

const productLineOptions = computed(() => {
  return appStore.productLines.map(item => ({
    label: item.name,
    value: item.code
  }))
})

const breadcrumbItems = computed(() => {
  const items: any[] = []
  const matched = route.matched.filter(item => item.meta?.title)

  matched.forEach(item => {
    items.push({
      title: item.meta?.title,
      path: item.path === route.path ? '' : item.path
    })
  })

  return items
})

// 方法
const toggleCollapsed = () => {
  appStore.toggleSidebar()
}

const handleMenuClick = ({ key }: { key: string | number }) => {
  // 根据菜单key导航到对应页面
  const findMenuItem = (items: any[], targetKey: string): any => {
    for (const item of items) {
      if (item.key === targetKey) return item
      if (item.children) {
        const found = findMenuItem(item.children, targetKey)
        if (found) return found
      }
    }
    return null
  }
  
  const menuItem = findMenuItem(menuItems.value, String(key))
  if (menuItem?.path) {
    router.push(menuItem.path)
  }
}

const handleProductLineChange = (values: any) => {
  appStore.setSelectedProductLines(Array.isArray(values) ? values : [])
}

const handleUserMenuClick = (info: any) => {
  const key = String(info.key)
  switch (key) {
    case 'profile':
      handleProfile()
      break
    case 'settings':
      handleSettings()
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleProfile = () => {
  router.push('/profile')
}

const handleSettings = () => {
  router.push('/settings')
}

const handleLogout = async () => {
  try {
    await userStore.logout()
    // 使用 window.location.href 强制刷新页面并跳转到登录页
    window.location.href = '/login'
  } catch (error) {
    console.error('退出登录失败:', error)
    // 即使登出失败，也强制跳转到登录页
    window.location.href = '/login'
  }
}

// 监听路由变化，更新选中的菜单
watch(
  () => route.path,
  (newPath) => {
    // 根据当前路径设置选中的菜单项
    const pathSegments = newPath.split('/').filter(Boolean)
    if (pathSegments.length >= 2) {
      selectedKeys.value = [pathSegments[1]]
      openKeys.value = [pathSegments[0]]
    }
  },
  { immediate: true }
)

// 初始化
onMounted(async () => {
  await appStore.initAppState()
  checkMobile()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.main-layout {
  height: 100vh;
}

.sidebar {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  z-index: 100;

  &.mobile-sidebar {
    position: fixed !important;
    height: 100vh;
    left: 0;
    top: 0;
  }

  &.mobile-sidebar {
    position: fixed !important;
    height: 100vh;
    left: 0;
    top: 0;
  }
  
  .logo {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
    border-bottom: 1px solid #f0f0f0;
    
    img {
      height: 32px;
      width: 32px;
    }
    
    .logo-text {
      margin-left: 12px;
      font-size: 18px;
      font-weight: 600;
      color: #1e88e5;
    }
  }
  
  .view-switch {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
  }
}

.header {
  background: #fff;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 99;
  
  .header-left {
    display: flex;
    align-items: center;
    
    .breadcrumb {
      margin-left: 16px;
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    
    .user-info {
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 6px;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: #f5f5f5;
      }
    }
  }
}

.content {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  overflow: auto;
  max-height: calc(100vh - 120px); /* 限制最大高度，确保滚动容器明确 */
  scroll-behavior: smooth; /* 平滑滚动 */
}

// 移动端适配
@media (max-width: 768px) {
  .main-layout {
    .header {
      padding: 0 16px;

      .header-left {
        .breadcrumb {
          display: none;
        }
      }

      .header-right {
        .product-selector {
          display: none;
        }
      }
    }

    .content {
      margin: 16px;
      padding: 16px;
      border-radius: 4px;
    }
  }
}

@media (max-width: 576px) {
  .main-layout {
    .header {
      padding: 0 12px;
    }

    .content {
      margin: 12px;
      padding: 12px;
    }
  }
}
</style>
