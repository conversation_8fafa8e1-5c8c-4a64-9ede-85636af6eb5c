import { defineStore } from 'pinia'
import type { UserInfo, LoginRequest, LoginResponse } from '@/types'
import { http } from '@/utils/request'

interface UserState {
  token: string | null
  userInfo: UserInfo | null
  isLoggedIn: boolean
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: localStorage.getItem('token'),
    userInfo: null,
    isLoggedIn: false
  }),

  getters: {
    // 是否为管理员
    isAdmin: (state): boolean => {
      return state.userInfo?.userType === 1
    },
    
    // 用户显示名称
    displayName: (state): string => {
      return state.userInfo?.realName || state.userInfo?.username || '未知用户'
    }
  },

  actions: {
    // 登录
    async login(loginData: LoginRequest): Promise<void> {
      try {
        const response = await http.post<LoginResponse>('/auth/login', loginData)
        const { accessToken, userInfo } = response.data
        
        // 保存token和用户信息
        this.token = accessToken
        this.userInfo = userInfo
        this.isLoggedIn = true
        
        // 持久化存储
        localStorage.setItem('token', accessToken)
        localStorage.setItem('userInfo', JSON.stringify(userInfo))
      } catch (error) {
        console.error('登录失败:', error)
        throw error
      }
    },

    // 登出
    async logout(skipApiCall = false): Promise<void> {
      // 如果不跳过API调用且有token，则调用后端登出接口
      if (!skipApiCall && this.token) {
        try {
          await http.post('/auth/logout')
        } catch (error) {
          console.error('登出请求失败:', error)
          // 即使后端登出失败，也要清除本地数据
        }
      }

      // 清除本地数据
      this.token = null
      this.userInfo = null
      this.isLoggedIn = false

      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
    },

    // 获取当前用户信息
    async getCurrentUser(): Promise<void> {
      try {
        const response = await http.get<UserInfo>('/auth/user/info')
        this.userInfo = response.data
        this.isLoggedIn = true
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 如果获取用户信息失败，清除本地数据（跳过API调用避免循环）
        this.logout(true)
        throw error
      }
    },

    // 初始化用户状态
    async initUserState(): Promise<void> {
      const token = localStorage.getItem('token')
      const userInfoStr = localStorage.getItem('userInfo')
      
      if (token && userInfoStr) {
        try {
          this.token = token
          this.userInfo = JSON.parse(userInfoStr)
          this.isLoggedIn = true
          
          // 验证token有效性
          await this.getCurrentUser()
        } catch (error) {
          console.error('初始化用户状态失败:', error)
          this.logout(true) // 跳过API调用
        }
      }
    },

    // 更新用户信息
    updateUserInfo(userInfo: Partial<UserInfo>): void {
      if (this.userInfo) {
        this.userInfo = { ...this.userInfo, ...userInfo }
        localStorage.setItem('userInfo', JSON.stringify(this.userInfo))
      }
    }
  }
})
