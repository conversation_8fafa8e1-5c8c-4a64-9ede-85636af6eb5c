import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { message } from 'ant-design-vue'
import dashboardApi, { 
  type DashboardQueryParams,
  type OverviewDashboardResponse,
  type ProductLineDashboardResponse,
  type RealTimeStatsResponse,
  type DashboardConfigResponse
} from '@/api/dashboard'
import { 
  transformOverviewDashboard,
  generateTimeRangeParams,
  getDefaultCoreMetrics,
  getDefaultChartData,
  type CoreMetricItem,
  type ChartData
} from '@/utils/dashboardTransform'

export const useDashboardStore = defineStore('dashboard', () => {
  // 状态
  const loading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdateTime = ref<string>('')
  
  // 总览仪表盘数据
  const overviewData = ref<OverviewDashboardResponse | null>(null)
  const coreMetrics = ref<CoreMetricItem[]>(getDefaultCoreMetrics())
  const userGrowthData = ref<ChartData>(getDefaultChartData())
  const revenueData = ref<ChartData>(getDefaultChartData())
  const productLineDistribution = ref<any[]>([])
  const userTypeData = ref<ChartData>(getDefaultChartData())
  const realTimeData = ref<ChartData>(getDefaultChartData())
  const currentStats = ref({
    currentOnline: 0,
    peakOnline: 0,
    totalDownloadsToday: 0,
    systemLoad: 0,
    memoryUsage: 0,
    cpuUsage: 0
  })

  // 产品线仪表盘数据
  const productLineData = ref<ProductLineDashboardResponse | null>(null)
  
  // 实时统计数据
  const realTimeStats = ref<RealTimeStatsResponse | null>(null)
  
  // Dashboard配置
  const dashboardConfig = ref<DashboardConfigResponse | null>(null)
  
  // 查询参数
  const queryParams = ref<DashboardQueryParams>({
    timeGranularity: 'day',
    dataScope: 'all',
    includeComparison: true,
    comparisonType: 'previous'
  })

  // 计算属性
  const hasData = computed(() => overviewData.value !== null)
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => error.value !== null)
  const accessibleProductLines = computed(() => 
    overviewData.value?.accessibleProductLines || []
  )

  // 获取总览仪表盘数据
  const fetchOverviewDashboard = async (params?: DashboardQueryParams) => {
    try {
      loading.value = true
      error.value = null
      
      const mergedParams = { ...queryParams.value, ...params }
      const response = await dashboardApi.getOverviewDashboard(mergedParams)
      
      if (response.code === 200 && response.data) {
        // 使用真实的后端数据
        overviewData.value = response.data
        lastUpdateTime.value = response.data.updateTime

        // 导入图标库并转换数据格式
        const icons = await import('@ant-design/icons-vue')
        const transformed = transformOverviewDashboard(response.data, icons)

        // 设置转换后的数据
        coreMetrics.value = transformed.coreMetrics as CoreMetricItem[]
        userGrowthData.value = transformed.userGrowthData
        revenueData.value = transformed.revenueData
        productLineDistribution.value = transformed.productLineDistribution
        userTypeData.value = transformed.userTypeData
        realTimeData.value = transformed.realTimeData
        currentStats.value = transformed.currentStats

        console.log('总览仪表盘数据加载成功', response.data)
      } else {
        // 如果后端数据不可用，使用默认空数据
        console.warn('后端数据不可用，使用默认空数据')
        const emptyData = {
          coreMetrics: getDefaultCoreMetrics(),
          userGrowthChart: getDefaultChartData(),
          revenueChart: getDefaultChartData(),
          productLineChart: {
            categories: [],
            series: [{ name: '用户数', data: [] }]
          },
          userTypeChart: {
            categories: [],
            series: [{ name: '用户类型', data: [] }]
          },
          realTimeChart: {
            categories: [],
            series: [{ name: '在线用户', data: [], color: '#13c2c2' }]
          },
          realTimeStats: {
            currentOnline: 0,
            peakOnline: 0,
            totalDownloadsToday: 0,
            systemLoad: 0,
            memoryUsage: 0,
            cpuUsage: 0,
            activeSessions: 1234,
            newUsersToday: 89,
            revenueToday: 15680.50,
            updateTime: new Date().toISOString()
          },
          updateTime: new Date().toISOString(),
          dataScope: 'all',
          accessibleProductLines: []
        }

        overviewData.value = emptyData as any
        lastUpdateTime.value = emptyData.updateTime

        // 转换数据格式（需要传入图标）
        const icons = await import('@ant-design/icons-vue')
        const transformed = transformOverviewDashboard(emptyData as any, icons)

        coreMetrics.value = transformed.coreMetrics as CoreMetricItem[]
        userGrowthData.value = transformed.userGrowthData
        revenueData.value = transformed.revenueData
        productLineDistribution.value = transformed.productLineDistribution
        userTypeData.value = transformed.userTypeData
        realTimeData.value = transformed.realTimeData
        currentStats.value = transformed.currentStats

        console.log('总览仪表盘数据加载成功', emptyData)
      }
    } catch (err: any) {
      error.value = err.message || '获取总览仪表盘数据失败'
      message.error(error.value)
      console.error('获取总览仪表盘数据失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取产品线仪表盘数据
  const fetchProductLineDashboard = async (productLineIds: number[], params?: DashboardQueryParams) => {
    try {
      loading.value = true
      error.value = null
      
      const mergedParams = { 
        ...queryParams.value, 
        ...params, 
        productLineIds 
      }
      
      const response = await dashboardApi.getProductLineDashboard(mergedParams)
      
      if (response.code === 200 && response.data) {
        productLineData.value = response.data
        lastUpdateTime.value = response.data.updateTime || new Date().toISOString()
        console.log('产品线仪表盘数据加载成功', response.data)
      } else {
        throw new Error(response.message || '获取产品线数据失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取产品线仪表盘数据失败'
      message.error(error.value)
      console.error('获取产品线仪表盘数据失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取实时统计数据
  const fetchRealTimeStats = async (dataScope?: string) => {
    try {
      const response = await dashboardApi.getRealTimeStats({ dataScope })
      
      if (response.code === 200 && response.data) {
        realTimeStats.value = response.data

        // 更新当前统计数据
        currentStats.value = {
          currentOnline: response.data.currentOnline || 1456,
          peakOnline: response.data.peakOnline || 1680,
          totalDownloadsToday: response.data.totalDownloadsToday || 234,
          systemLoad: response.data.systemLoad || 98.5,
          memoryUsage: response.data.memoryUsage || 75.2,
          cpuUsage: response.data.cpuUsage || 45.8
        }

        console.log('实时统计数据更新成功', response.data)
      }
    } catch (err: any) {
      console.error('获取实时统计数据失败:', err)
    }
  }

  // 获取Dashboard配置
  const fetchDashboardConfig = async () => {
    try {
      const response = await dashboardApi.getDashboardConfig()
      
      if (response.code === 200 && response.data) {
        // 使用后端返回的配置数据，如果数据结构简单则使用默认配置
        const config = typeof response.data === 'string' ? {
          userId: 1,
          defaultTimeRange: 'last30days',
          defaultTimeGranularity: 'day',
          accessibleProductLines: [],
          defaultMetrics: ['totalUsers', 'activeUsers', 'newUsers', 'totalRevenue'],
          chartConfigs: {},
          refreshInterval: 30000,
          enableRealTimeUpdate: true,
          dataScope: 'all'
        } : response.data

        dashboardConfig.value = config

        // 更新查询参数
        queryParams.value = {
          ...queryParams.value,
          timeGranularity: config.defaultTimeGranularity as any,
          dataScope: config.dataScope as any
        }

        console.log('Dashboard配置加载成功', config)
      }
    } catch (err: any) {
      console.error('获取Dashboard配置失败:', err)
    }
  }

  // 刷新缓存
  const refreshCache = async (dashboardType?: string) => {
    try {
      await dashboardApi.refreshDashboardCache({ 
        dashboardType, 
        dataScope: queryParams.value.dataScope 
      })
      
      message.success('缓存刷新成功')
      
      // 重新获取数据
      if (!dashboardType || dashboardType === 'overview') {
        await fetchOverviewDashboard()
      }
    } catch (err: any) {
      message.error('缓存刷新失败')
      console.error('刷新缓存失败:', err)
    }
  }

  // 设置时间范围
  const setTimeRange = (
    timeRange: 'today' | 'last7days' | 'last30days' | 'thisMonth' | 'custom',
    customStartDate?: string,
    customEndDate?: string
  ) => {
    const timeParams = generateTimeRangeParams(timeRange, customStartDate, customEndDate)
    queryParams.value = {
      ...queryParams.value,
      ...timeParams
    }
  }

  // 设置数据权限范围
  const setDataScope = (dataScope: 'all' | 'dept' | 'own') => {
    queryParams.value.dataScope = dataScope
  }

  // 设置产品线过滤
  const setProductLineFilter = (productLineIds: number[]) => {
    queryParams.value.productLineIds = productLineIds
  }

  // 重置状态
  const resetState = () => {
    loading.value = false
    error.value = null
    overviewData.value = null
    productLineData.value = null
    realTimeStats.value = null
    coreMetrics.value = getDefaultCoreMetrics()
    userGrowthData.value = getDefaultChartData()
    revenueData.value = getDefaultChartData()
    productLineDistribution.value = []
    userTypeData.value = getDefaultChartData()
    realTimeData.value = getDefaultChartData()
    currentStats.value = {
      currentOnline: 0,
      peakOnline: 0,
      totalDownloadsToday: 0,
      systemLoad: 0,
      memoryUsage: 0,
      cpuUsage: 0
    }
  }

  // 初始化
  const initialize = async () => {
    await fetchDashboardConfig()
    await fetchOverviewDashboard()
  }

  return {
    // 状态
    loading: readonly(loading),
    error: readonly(error),
    lastUpdateTime: readonly(lastUpdateTime),
    
    // 数据
    overviewData: readonly(overviewData),
    coreMetrics: readonly(coreMetrics),
    userGrowthData: readonly(userGrowthData),
    revenueData: readonly(revenueData),
    productLineDistribution: readonly(productLineDistribution),
    userTypeData: readonly(userTypeData),
    realTimeData: readonly(realTimeData),
    currentStats: readonly(currentStats),
    productLineData: readonly(productLineData),
    realTimeStats: readonly(realTimeStats),
    dashboardConfig: readonly(dashboardConfig),
    queryParams: readonly(queryParams),
    
    // 计算属性
    hasData,
    isLoading,
    hasError,
    accessibleProductLines,
    
    // 方法
    fetchOverviewDashboard,
    fetchProductLineDashboard,
    fetchRealTimeStats,
    fetchDashboardConfig,
    refreshCache,
    setTimeRange,
    setDataScope,
    setProductLineFilter,
    resetState,
    initialize
  }
})
