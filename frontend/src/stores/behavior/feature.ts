import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { featureUsageApi } from '@/api/behavior/feature'
import type { 
  FeatureUsageRequest, 
  FeatureUsageResponse,
  FeatureInfo,
  FeatureRealTimeStats,
  FeatureUsageAnalysisSummary,
  FeatureUsageTrendComparison,
  TimeGranularity 
} from '@/api/behavior/feature'

/**
 * 功能使用分析Store
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */
export const useFeatureUsageStore = defineStore('featureUsage', () => {
  // 状态
  const loading = ref(false)
  const error = ref('')
  
  // 数据
  const usageStatisticsData = ref<FeatureUsageResponse | null>(null)
  const heatAnalysisData = ref<FeatureUsageResponse | null>(null)
  const pathAnalysisData = ref<FeatureUsageResponse | null>(null)
  const valueContributionData = ref<FeatureUsageResponse | null>(null)
  const satisfactionEvaluationData = ref<FeatureUsageResponse | null>(null)
  const featureList = ref<FeatureInfo[]>([])
  const realTimeStats = ref<FeatureRealTimeStats | null>(null)
  const summaryData = ref<FeatureUsageAnalysisSummary | null>(null)
  const trendComparisonData = ref<FeatureUsageTrendComparison | null>(null)
  
  // 查询参数
  const queryParams = ref<FeatureUsageRequest>({
    startDate: '',
    endDate: '',
    granularity: 'DAY',
    productLineIds: [],
    featureIds: [],
    includeComparison: false,
    includeDetails: false
  })
  
  // 最后更新时间
  const lastUpdateTime = ref<string>('')

  // 计算属性
  const coreMetrics = computed(() => {
    return usageStatisticsData.value?.coreMetrics || 
           heatAnalysisData.value?.coreMetrics || 
           pathAnalysisData.value?.coreMetrics || 
           valueContributionData.value?.coreMetrics || 
           satisfactionEvaluationData.value?.coreMetrics || 
           {}
  })

  const hasData = computed(() => {
    return usageStatisticsData.value || heatAnalysisData.value || pathAnalysisData.value || 
           valueContributionData.value || satisfactionEvaluationData.value
  })

  const isLoading = computed(() => loading.value)

  const errorMessage = computed(() => error.value)

  // 获取功能使用统计
  const fetchFeatureUsageStatistics = async (params: {
    startDate: string
    endDate: string
    granularity?: TimeGranularity
    featureIds: string[]
    productLineIds?: number[]
    includeComparison?: boolean
  }) => {
    loading.value = true
    error.value = ''
    
    try {
      const response = await featureUsageApi.getFeatureUsageStatistics(params)

      usageStatisticsData.value = response.data
      lastUpdateTime.value = new Date().toISOString()
      message.success('功能使用统计数据获取成功')
    } catch (err: any) {
      error.value = err.message || '获取功能使用统计失败'
      message.error(error.value)
      console.error('获取功能使用统计失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取功能热度分析
  const fetchFeatureHeatAnalysis = async (params: {
    startDate: string
    endDate: string
    featureIds?: string[]
    productLineIds?: number[]
  }) => {
    loading.value = true
    error.value = ''
    
    try {
      const response = await featureUsageApi.getFeatureHeatAnalysis(params)

      heatAnalysisData.value = response.data
      lastUpdateTime.value = new Date().toISOString()
      message.success('功能热度分析数据获取成功')
    } catch (err: any) {
      error.value = err.message || '获取功能热度分析失败'
      message.error(error.value)
      console.error('获取功能热度分析失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取功能路径分析
  const fetchFeaturePathAnalysis = async (params: {
    startDate: string
    endDate: string
    startFeatureIds: string[]
    endFeatureIds?: string[]
    productLineIds?: number[]
  }) => {
    loading.value = true
    error.value = ''
    
    try {
      const response = await featureUsageApi.getFeaturePathAnalysis(params)

      pathAnalysisData.value = response.data
      lastUpdateTime.value = new Date().toISOString()
      message.success('功能路径分析数据获取成功')
    } catch (err: any) {
      error.value = err.message || '获取功能路径分析失败'
      message.error(error.value)
      console.error('获取功能路径分析失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取功能价值贡献分析
  const fetchFeatureValueContribution = async (params: {
    startDate: string
    endDate: string
    featureIds: string[]
    productLineIds?: number[]
  }) => {
    loading.value = true
    error.value = ''
    
    try {
      const response = await featureUsageApi.getFeatureValueContribution(params)
      
      if (response.code === 200) {
        valueContributionData.value = response.data
        lastUpdateTime.value = new Date().toISOString()
        message.success('功能价值贡献分析数据获取成功')
      } else {
        error.value = response.message || '获取功能价值贡献分析失败'
        message.error(error.value)
      }
    } catch (err: any) {
      error.value = err.message || '获取功能价值贡献分析失败'
      message.error(error.value)
      console.error('获取功能价值贡献分析失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取功能满意度评估
  const fetchFeatureSatisfactionEvaluation = async (params: {
    startDate: string
    endDate: string
    featureIds: string[]
    productLineIds?: number[]
  }) => {
    loading.value = true
    error.value = ''
    
    try {
      const response = await featureUsageApi.getFeatureSatisfactionEvaluation(params)
      
      if (response.code === 200) {
        satisfactionEvaluationData.value = response.data
        lastUpdateTime.value = new Date().toISOString()
        message.success('功能满意度评估数据获取成功')
      } else {
        error.value = response.message || '获取功能满意度评估失败'
        message.error(error.value)
      }
    } catch (err: any) {
      error.value = err.message || '获取功能满意度评估失败'
      message.error(error.value)
      console.error('获取功能满意度评估失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取功能列表
  const fetchFeatureList = async (params?: {
    productLineIds?: number[]
  }) => {
    try {
      const response = await featureUsageApi.getFeatureList(params)
      
      if (response.code === 200) {
        featureList.value = response.data
      } else {
        message.error(response.message || '获取功能列表失败')
      }
    } catch (err: any) {
      message.error('获取功能列表失败')
      console.error('获取功能列表失败:', err)
    }
  }

  // 获取实时功能使用统计
  const fetchRealTimeStats = async (params?: {
    featureIds?: string[]
    productLineIds?: number[]
  }) => {
    try {
      const response = await featureUsageApi.getRealTimeStats(params)
      
      if (response.code === 200) {
        realTimeStats.value = response.data
      } else {
        message.error(response.message || '获取实时功能使用统计失败')
      }
    } catch (err: any) {
      message.error('获取实时功能使用统计失败')
      console.error('获取实时功能使用统计失败:', err)
    }
  }

  // 获取功能使用分析统计摘要
  const fetchSummary = async (params: {
    startDate: string
    endDate: string
  }) => {
    try {
      const response = await featureUsageApi.getSummary(params)
      
      if (response.code === 200) {
        summaryData.value = response.data
      } else {
        message.error(response.message || '获取功能使用分析统计摘要失败')
      }
    } catch (err: any) {
      message.error('获取功能使用分析统计摘要失败')
      console.error('获取功能使用分析统计摘要失败:', err)
    }
  }

  // 获取功能使用趋势对比
  const fetchTrendComparison = async (params: {
    startDate: string
    endDate: string
    featureIds?: string[]
    productLineIds?: number[]
  }) => {
    try {
      const response = await featureUsageApi.getTrendComparison(params)
      
      if (response.code === 200) {
        trendComparisonData.value = response.data
      } else {
        message.error(response.message || '获取功能使用趋势对比失败')
      }
    } catch (err: any) {
      message.error('获取功能使用趋势对比失败')
      console.error('获取功能使用趋势对比失败:', err)
    }
  }

  // 导出数据
  const exportData = async (data: FeatureUsageRequest) => {
    try {
      const response = await featureUsageApi.exportData(data)
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `功能使用分析数据_${new Date().toISOString().slice(0, 10)}.xlsx`)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      message.success('数据导出成功')
    } catch (err: any) {
      message.error('数据导出失败')
      console.error('数据导出失败:', err)
    }
  }

  // 清空数据
  const clearData = () => {
    usageStatisticsData.value = null
    heatAnalysisData.value = null
    pathAnalysisData.value = null
    valueContributionData.value = null
    satisfactionEvaluationData.value = null
    realTimeStats.value = null
    summaryData.value = null
    trendComparisonData.value = null
    error.value = ''
    lastUpdateTime.value = ''
  }

  // 重置状态
  const reset = () => {
    clearData()
    queryParams.value = {
      startDate: '',
      endDate: '',
      granularity: 'DAY',
      productLineIds: [],
      featureIds: [],
      includeComparison: false,
      includeDetails: false
    }
  }

  return {
    // 状态
    loading: isLoading,
    error: errorMessage,
    
    // 数据
    usageStatisticsData,
    heatAnalysisData,
    pathAnalysisData,
    valueContributionData,
    satisfactionEvaluationData,
    featureList,
    realTimeStats,
    summaryData,
    trendComparisonData,
    queryParams,
    lastUpdateTime,
    
    // 计算属性
    coreMetrics,
    hasData,
    
    // 方法
    fetchFeatureUsageStatistics,
    fetchFeatureHeatAnalysis,
    fetchFeaturePathAnalysis,
    fetchFeatureValueContribution,
    fetchFeatureSatisfactionEvaluation,
    fetchFeatureList,
    fetchRealTimeStats,
    fetchSummary,
    fetchTrendComparison,
    exportData,
    clearData,
    reset
  }
})
