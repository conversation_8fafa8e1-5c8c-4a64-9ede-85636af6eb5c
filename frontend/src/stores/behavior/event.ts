import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { eventAnalysisApi } from '@/api/behavior/event'
import type { 
  EventAnalysisRequest, 
  EventAnalysisResponse,
  EventInfo,
  EventRealTimeStats,
  EventAnalysisSummary,
  TimeGranularity 
} from '@/api/behavior/event'

/**
 * 事件分析Store
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */
export const useEventAnalysisStore = defineStore('eventAnalysis', () => {
  // 状态
  const loading = ref(false)
  const error = ref('')
  
  // 数据
  const trendData = ref<EventAnalysisResponse | null>(null)
  const funnelData = ref<EventAnalysisResponse | null>(null)
  const pathData = ref<EventAnalysisResponse | null>(null)
  const comparisonData = ref<EventAnalysisResponse | null>(null)
  const propertyData = ref<EventAnalysisResponse | null>(null)
  const eventList = ref<EventInfo[]>([])
  const realTimeStats = ref<EventRealTimeStats | null>(null)
  const summaryData = ref<EventAnalysisSummary | null>(null)
  
  // 查询参数
  const queryParams = ref<EventAnalysisRequest>({
    startDate: '',
    endDate: '',
    granularity: 'DAY',
    productLineIds: [],
    eventIds: [],
    includeComparison: false,
    includeDetails: false
  })
  
  // 最后更新时间
  const lastUpdateTime = ref<string>('')

  // 计算属性
  const coreMetrics = computed(() => {
    return trendData.value?.coreMetrics || {}
  })

  const hasData = computed(() => {
    return trendData.value || funnelData.value || pathData.value || 
           comparisonData.value || propertyData.value
  })

  const isLoading = computed(() => loading.value)

  const errorMessage = computed(() => error.value)

  // 获取事件趋势分析
  const fetchEventTrends = async (params: {
    startDate: string
    endDate: string
    granularity?: TimeGranularity
    eventIds: string[]
    productLineIds?: number[]
    includeComparison?: boolean
  }) => {
    loading.value = true
    error.value = ''
    
    try {
      const response = await eventAnalysisApi.getEventTrends(params)

      trendData.value = response.data
      lastUpdateTime.value = new Date().toISOString()
      message.success('事件趋势分析数据获取成功')
    } catch (err: any) {
      error.value = err.message || '获取事件趋势分析失败'
      message.error(error.value)
      console.error('获取事件趋势分析失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取事件漏斗分析
  const fetchEventFunnel = async (params: {
    startDate: string
    endDate: string
    eventIds: string[]
    productLineIds?: number[]
  }) => {
    loading.value = true
    error.value = ''
    
    try {
      const response = await eventAnalysisApi.getEventFunnel(params)

      funnelData.value = response.data
      lastUpdateTime.value = new Date().toISOString()
      message.success('事件漏斗分析数据获取成功')
    } catch (err: any) {
      error.value = err.message || '获取事件漏斗分析失败'
      message.error(error.value)
      console.error('获取事件漏斗分析失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取事件路径分析
  const fetchEventPath = async (params: {
    startDate: string
    endDate: string
    startEventIds: string[]
    endEventIds?: string[]
    productLineIds?: number[]
  }) => {
    loading.value = true
    error.value = ''
    
    try {
      const response = await eventAnalysisApi.getEventPath(params)

      pathData.value = response.data
      lastUpdateTime.value = new Date().toISOString()
      message.success('事件路径分析数据获取成功')
    } catch (err: any) {
      error.value = err.message || '获取事件路径分析失败'
      message.error(error.value)
      console.error('获取事件路径分析失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取事件对比分析
  const fetchEventComparison = async (params: {
    startDate: string
    endDate: string
    granularity?: TimeGranularity
    eventIds: string[]
    productLineIds?: number[]
  }) => {
    loading.value = true
    error.value = ''
    
    try {
      const response = await eventAnalysisApi.getEventComparison(params)

      comparisonData.value = response.data
      lastUpdateTime.value = new Date().toISOString()
      message.success('事件对比分析数据获取成功')
    } catch (err: any) {
      error.value = err.message || '获取事件对比分析失败'
      message.error(error.value)
      console.error('获取事件对比分析失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取事件属性分析
  const fetchEventProperty = async (params: {
    startDate: string
    endDate: string
    eventId: string
    propertyNames: string[]
    productLineIds?: number[]
  }) => {
    loading.value = true
    error.value = ''
    
    try {
      const response = await eventAnalysisApi.getEventProperty(params)

      propertyData.value = response.data
      lastUpdateTime.value = new Date().toISOString()
      message.success('事件属性分析数据获取成功')
    } catch (err: any) {
      error.value = err.message || '获取事件属性分析失败'
      message.error(error.value)
      console.error('获取事件属性分析失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取事件列表
  const fetchEventList = async (params?: {
    productLineIds?: number[]
  }) => {
    try {
      const response = await eventAnalysisApi.getEventList(params)
      
      if (response.code === 200) {
        eventList.value = response.data
      } else {
        message.error(response.message || '获取事件列表失败')
      }
    } catch (err: any) {
      message.error('获取事件列表失败')
      console.error('获取事件列表失败:', err)
    }
  }

  // 获取实时事件统计
  const fetchRealTimeStats = async (params?: {
    eventIds?: string[]
    productLineIds?: number[]
  }) => {
    try {
      const response = await eventAnalysisApi.getRealTimeStats(params)
      
      if (response.code === 200) {
        realTimeStats.value = response.data
      } else {
        message.error(response.message || '获取实时事件统计失败')
      }
    } catch (err: any) {
      message.error('获取实时事件统计失败')
      console.error('获取实时事件统计失败:', err)
    }
  }

  // 获取事件分析统计摘要
  const fetchSummary = async (params: {
    startDate: string
    endDate: string
  }) => {
    try {
      const response = await eventAnalysisApi.getSummary(params)
      
      if (response.code === 200) {
        summaryData.value = response.data
      } else {
        message.error(response.message || '获取事件分析统计摘要失败')
      }
    } catch (err: any) {
      message.error('获取事件分析统计摘要失败')
      console.error('获取事件分析统计摘要失败:', err)
    }
  }

  // 导出数据
  const exportData = async (data: EventAnalysisRequest) => {
    try {
      const response = await eventAnalysisApi.exportData(data)
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `事件分析数据_${new Date().toISOString().slice(0, 10)}.xlsx`)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      message.success('数据导出成功')
    } catch (err: any) {
      message.error('数据导出失败')
      console.error('数据导出失败:', err)
    }
  }

  // 清空数据
  const clearData = () => {
    trendData.value = null
    funnelData.value = null
    pathData.value = null
    comparisonData.value = null
    propertyData.value = null
    realTimeStats.value = null
    summaryData.value = null
    error.value = ''
    lastUpdateTime.value = ''
  }

  // 重置状态
  const reset = () => {
    clearData()
    queryParams.value = {
      startDate: '',
      endDate: '',
      granularity: 'DAY',
      productLineIds: [],
      eventIds: [],
      includeComparison: false,
      includeDetails: false
    }
  }

  return {
    // 状态
    loading: isLoading,
    error: errorMessage,
    
    // 数据
    trendData,
    funnelData,
    pathData,
    comparisonData,
    propertyData,
    eventList,
    realTimeStats,
    summaryData,
    queryParams,
    lastUpdateTime,
    
    // 计算属性
    coreMetrics,
    hasData,
    
    // 方法
    fetchEventTrends,
    fetchEventFunnel,
    fetchEventPath,
    fetchEventComparison,
    fetchEventProperty,
    fetchEventList,
    fetchRealTimeStats,
    fetchSummary,
    exportData,
    clearData,
    reset
  }
})
