import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { userGrowthApi } from '@/api/user/growth'
import type { 
  UserGrowthAnalysisRequest, 
  UserGrowthAnalysisResponse,
  UserGrowthSummary,
  TimeGranularity 
} from '@/api/user/growth'

/**
 * 用户增长分析Store
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */
export const useUserGrowthStore = defineStore('userGrowth', () => {
  // 状态
  const loading = ref(false)
  const error = ref('')
  
  // 数据
  const overviewData = ref<UserGrowthAnalysisResponse | null>(null)
  const newUserData = ref<UserGrowthAnalysisResponse | null>(null)
  const retentionData = ref<UserGrowthAnalysisResponse | null>(null)
  const trendData = ref<UserGrowthAnalysisResponse | null>(null)
  const sourceData = ref<UserGrowthAnalysisResponse | null>(null)
  const cohortData = ref<UserGrowthAnalysisResponse | null>(null)
  const comparisonData = ref<UserGrowthAnalysisResponse[]>([])
  const summaryData = ref<UserGrowthSummary | null>(null)
  
  // 查询参数
  const queryParams = ref<UserGrowthAnalysisRequest>({
    startDate: '',
    endDate: '',
    granularity: 'DAY',
    productLineIds: [],
    includeComparison: true,
    includeDetails: false
  })
  
  // 最后更新时间
  const lastUpdateTime = ref<string>('')

  // 计算属性
  const coreMetrics = computed(() => {
    return overviewData.value?.coreMetrics || {}
  })

  const hasData = computed(() => {
    return overviewData.value !== null
  })

  const isLoading = computed(() => loading.value)

  // 参数验证
  const validateParams = (params: Partial<UserGrowthAnalysisRequest>) => {
    if (!params.startDate || !params.endDate) {
      throw new Error('开始日期和结束日期不能为空')
    }
    
    const startDate = new Date(params.startDate)
    const endDate = new Date(params.endDate)
    
    if (startDate > endDate) {
      throw new Error('开始日期不能大于结束日期')
    }
    
    // 时间范围不能超过1年
    const oneYearAgo = new Date()
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1)
    
    if (startDate < oneYearAgo) {
      throw new Error('查询时间范围不能超过1年')
    }
  }

  // 获取用户增长总览
  const fetchOverview = async (params?: Partial<UserGrowthAnalysisRequest>) => {
    try {
      loading.value = true
      error.value = ''

      const requestParams = { ...queryParams.value, ...params }
      validateParams(requestParams)
      
      const response = await userGrowthApi.getOverview({
        startDate: requestParams.startDate,
        endDate: requestParams.endDate,
        granularity: requestParams.granularity,
        productLineIds: requestParams.productLineIds,
        includeComparison: requestParams.includeComparison
      })

      if (response.code === 200) {
        overviewData.value = response.data
        lastUpdateTime.value = new Date().toISOString()
      } else {
        throw new Error(response.message || '获取数据失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取用户增长总览失败'
      message.error(error.value)
      console.error('获取用户增长总览失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取新增用户分析
  const fetchNewUserAnalysis = async (params?: Partial<UserGrowthAnalysisRequest>) => {
    try {
      loading.value = true
      error.value = ''
      
      const requestParams = { ...queryParams.value, ...params }
      const response = await userGrowthApi.getNewUserAnalysis({
        startDate: requestParams.startDate,
        endDate: requestParams.endDate,
        granularity: requestParams.granularity,
        productLineIds: requestParams.productLineIds
      })

      if (response.code === 200) {
        newUserData.value = response.data
        lastUpdateTime.value = new Date().toISOString()
      } else {
        throw new Error(response.message || '获取数据失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取新增用户分析失败'
      message.error(error.value)
      console.error('获取新增用户分析失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取用户留存分析
  const fetchRetentionAnalysis = async (params?: Partial<UserGrowthAnalysisRequest>) => {
    try {
      loading.value = true
      error.value = ''
      
      const requestParams = { ...queryParams.value, ...params }
      const response = await userGrowthApi.getRetentionAnalysis({
        startDate: requestParams.startDate,
        endDate: requestParams.endDate,
        productLineIds: requestParams.productLineIds,
        retentionPeriods: requestParams.retentionPeriods
      })

      if (response.code === 200) {
        retentionData.value = response.data
        lastUpdateTime.value = new Date().toISOString()
      } else {
        throw new Error(response.message || '获取数据失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取用户留存分析失败'
      message.error(error.value)
      console.error('获取用户留存分析失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取增长趋势分析
  const fetchGrowthTrends = async (params?: Partial<UserGrowthAnalysisRequest>) => {
    try {
      loading.value = true
      error.value = ''
      
      const requestParams = { ...queryParams.value, ...params }
      const response = await userGrowthApi.getGrowthTrends({
        startDate: requestParams.startDate,
        endDate: requestParams.endDate,
        granularity: requestParams.granularity,
        productLineIds: requestParams.productLineIds
      })

      if (response.code === 200) {
        trendData.value = response.data
        lastUpdateTime.value = new Date().toISOString()
      } else {
        throw new Error(response.message || '获取数据失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取增长趋势分析失败'
      message.error(error.value)
      console.error('获取增长趋势分析失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取用户来源分析
  const fetchUserSourceAnalysis = async (params?: Partial<UserGrowthAnalysisRequest>) => {
    try {
      loading.value = true
      error.value = ''
      
      const requestParams = { ...queryParams.value, ...params }
      const response = await userGrowthApi.getUserSourceAnalysis({
        startDate: requestParams.startDate,
        endDate: requestParams.endDate,
        productLineIds: requestParams.productLineIds,
        sourceTypes: requestParams.sourceTypes
      })

      if (response.code === 200) {
        sourceData.value = response.data
        lastUpdateTime.value = new Date().toISOString()
      } else {
        throw new Error(response.message || '获取数据失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取用户来源分析失败'
      message.error(error.value)
      console.error('获取用户来源分析失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取队列留存分析
  const fetchCohortRetentionAnalysis = async (params?: Partial<UserGrowthAnalysisRequest>) => {
    try {
      loading.value = true
      error.value = ''
      
      const requestParams = { ...queryParams.value, ...params }
      const response = await userGrowthApi.getCohortRetentionAnalysis({
        startDate: requestParams.startDate,
        endDate: requestParams.endDate,
        productLineIds: requestParams.productLineIds
      })

      if (response.code === 200) {
        cohortData.value = response.data
        lastUpdateTime.value = new Date().toISOString()
      } else {
        throw new Error(response.message || '获取数据失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取队列留存分析失败'
      message.error(error.value)
      console.error('获取队列留存分析失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取产品线对比分析
  const fetchProductLineComparison = async (params?: Partial<UserGrowthAnalysisRequest>) => {
    try {
      loading.value = true
      error.value = ''
      
      const requestParams = { ...queryParams.value, ...params }
      if (!requestParams.productLineIds || requestParams.productLineIds.length === 0) {
        throw new Error('产品线ID列表不能为空')
      }
      
      const response = await userGrowthApi.getProductLineComparison({
        startDate: requestParams.startDate,
        endDate: requestParams.endDate,
        productLineIds: requestParams.productLineIds
      })

      if (response.code === 200) {
        comparisonData.value = response.data
        lastUpdateTime.value = new Date().toISOString()
      } else {
        throw new Error(response.message || '获取数据失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取产品线对比分析失败'
      message.error(error.value)
      console.error('获取产品线对比分析失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取用户增长统计摘要
  const fetchGrowthSummary = async (params?: Partial<UserGrowthAnalysisRequest>) => {
    try {
      loading.value = true
      error.value = ''
      
      const requestParams = { ...queryParams.value, ...params }
      const response = await userGrowthApi.getGrowthSummary({
        startDate: requestParams.startDate,
        endDate: requestParams.endDate
      })

      if (response.code === 200) {
        summaryData.value = response.data
        lastUpdateTime.value = new Date().toISOString()
      } else {
        throw new Error(response.message || '获取数据失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取用户增长统计摘要失败'
      message.error(error.value)
      console.error('获取用户增长统计摘要失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 设置查询参数
  const setQueryParams = (params: Partial<UserGrowthAnalysisRequest>) => {
    queryParams.value = { ...queryParams.value, ...params }
  }

  // 清空数据
  const clearData = () => {
    overviewData.value = null
    newUserData.value = null
    retentionData.value = null
    trendData.value = null
    sourceData.value = null
    cohortData.value = null
    comparisonData.value = []
    summaryData.value = null
    error.value = ''
    lastUpdateTime.value = ''
  }

  // 刷新所有数据
  const refreshAllData = async () => {
    await Promise.all([
      fetchOverview(),
      fetchNewUserAnalysis(),
      fetchRetentionAnalysis(),
      fetchGrowthTrends(),
      fetchUserSourceAnalysis()
    ])
  }

  return {
    // 状态
    loading: isLoading,
    error: computed(() => error.value),
    
    // 数据
    overviewData: computed(() => overviewData.value),
    newUserData: computed(() => newUserData.value),
    retentionData: computed(() => retentionData.value),
    trendData: computed(() => trendData.value),
    sourceData: computed(() => sourceData.value),
    cohortData: computed(() => cohortData.value),
    comparisonData: computed(() => comparisonData.value),
    summaryData: computed(() => summaryData.value),
    
    // 计算属性
    coreMetrics,
    hasData,
    lastUpdateTime: computed(() => lastUpdateTime.value),
    queryParams: computed(() => queryParams.value),
    
    // 方法
    fetchOverview,
    fetchNewUserAnalysis,
    fetchRetentionAnalysis,
    fetchGrowthTrends,
    fetchUserSourceAnalysis,
    fetchCohortRetentionAnalysis,
    fetchProductLineComparison,
    fetchGrowthSummary,
    setQueryParams,
    clearData,
    refreshAllData
  }
})
