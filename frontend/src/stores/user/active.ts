import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { activeUserApi } from '@/api/user/active'
import type { 
  ActiveUserAnalysisRequest, 
  ActiveUserAnalysisResponse, 
  ActiveUserAggregate,
  TimeGranularity 
} from '@/api/user/active'

/**
 * 活跃用户分析Store
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */
export const useActiveUserStore = defineStore('activeUser', () => {
  // 状态
  const loading = ref(false)
  const overviewData = ref<ActiveUserAggregate | null>(null)
  const trendsData = ref<ActiveUserAggregate | null>(null)
  const distributionData = ref<ActiveUserAggregate | null>(null)
  const frequencyData = ref<ActiveUserAggregate | null>(null)
  const comparisonData = ref<ActiveUserAggregate | null>(null)
  const retentionData = ref<ActiveUserAggregate | null>(null)
  const cohortData = ref<ActiveUserAggregate | null>(null)
  const lastUpdateTime = ref<string>('')
  const error = ref<string>('')

  // 查询参数
  const queryParams = ref<ActiveUserAnalysisRequest>({
    startDate: '',
    endDate: '',
    granularity: 'DAY',
    productLineIds: [],
    includeComparison: true,
    includeDetails: false
  })

  // 计算属性
  const hasData = computed(() => {
    return overviewData.value !== null || 
           trendsData.value !== null || 
           distributionData.value !== null
  })

  const isDataStale = computed(() => {
    if (!lastUpdateTime.value) return true
    const updateTime = new Date(lastUpdateTime.value)
    const now = new Date()
    const diffMinutes = (now.getTime() - updateTime.getTime()) / (1000 * 60)
    return diffMinutes > 30 // 30分钟后认为数据过期
  })

  // 核心指标计算
  const coreMetrics = computed(() => {
    if (!overviewData.value?.coreMetrics) return null
    
    const metrics = overviewData.value.coreMetrics
    return {
      dau: metrics.dau,
      wau: metrics.wau,
      mau: metrics.mau,
      stickinessRatio: metrics.stickinessRatio,
      activeUsers: metrics.activeUsers
    }
  })

  // 趋势图表数据
  const trendChartData = computed(() => {
    if (!trendsData.value?.trendData) return null
    
    const trends = trendsData.value.trendData
    return {
      dau: trends.dau,
      wau: trends.wau,
      mau: trends.mau
    }
  })

  // 分布图表数据
  const distributionChartData = computed(() => {
    if (!distributionData.value?.distributionData) return null
    
    return distributionData.value.distributionData
  })

  // 参数验证
  const validateParams = (params: Partial<ActiveUserAnalysisRequest>) => {
    if (!params.startDate || !params.endDate) {
      throw new Error('开始日期和结束日期不能为空')
    }
    return true
  }

  // Actions
  const setQueryParams = (params: Partial<ActiveUserAnalysisRequest>) => {
    queryParams.value = { ...queryParams.value, ...params }
  }

  const setDateRange = (startDate: string, endDate: string) => {
    queryParams.value.startDate = startDate
    queryParams.value.endDate = endDate
  }

  const setGranularity = (granularity: TimeGranularity) => {
    queryParams.value.granularity = granularity
  }

  const setProductLines = (productLineIds: number[]) => {
    queryParams.value.productLineIds = productLineIds
  }

  // 获取活跃用户总览
  const fetchOverview = async (params?: Partial<ActiveUserAnalysisRequest>) => {
    try {
      loading.value = true
      error.value = ''

      const requestParams = { ...queryParams.value, ...params }
      validateParams(requestParams)
      const response = await activeUserApi.getOverview({
        startDate: requestParams.startDate,
        endDate: requestParams.endDate,
        granularity: requestParams.granularity,
        productLineIds: requestParams.productLineIds,
        includeComparison: requestParams.includeComparison
      })

      overviewData.value = response.data as any
      lastUpdateTime.value = new Date().toISOString()
    } catch (err: any) {
      error.value = err.message || '获取活跃用户总览失败'
      message.error(error.value)
      console.error('获取活跃用户总览失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取活跃用户趋势
  const fetchTrends = async (params?: Partial<ActiveUserAnalysisRequest>) => {
    try {
      loading.value = true
      error.value = ''
      
      const requestParams = { ...queryParams.value, ...params }
      const response = await activeUserApi.getTrends({
        startDate: requestParams.startDate,
        endDate: requestParams.endDate,
        granularity: requestParams.granularity,
        productLineIds: requestParams.productLineIds
      })

      trendsData.value = response.data as any
      lastUpdateTime.value = new Date().toISOString()
    } catch (err: any) {
      error.value = err.message || '获取活跃用户趋势失败'
      message.error(error.value)
      console.error('获取活跃用户趋势失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取活跃用户分布
  const fetchDistribution = async (params?: Partial<ActiveUserAnalysisRequest>) => {
    try {
      loading.value = true
      error.value = ''
      
      const requestParams = { ...queryParams.value, ...params }
      const response = await activeUserApi.getDistribution({
        startDate: requestParams.startDate,
        endDate: requestParams.endDate,
        productLineIds: requestParams.productLineIds
      })

      distributionData.value = response.data as any
      lastUpdateTime.value = new Date().toISOString()
    } catch (err: any) {
      error.value = err.message || '获取活跃用户分布失败'
      message.error(error.value)
      console.error('获取活跃用户分布失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取活跃频次分析
  const fetchFrequency = async (params?: Partial<ActiveUserAnalysisRequest>) => {
    try {
      loading.value = true
      error.value = ''

      const requestParams = { ...queryParams.value, ...params }
      validateParams(requestParams)
      const response = await activeUserApi.getFrequency({
        startDate: requestParams.startDate,
        endDate: requestParams.endDate,
        productLineIds: requestParams.productLineIds
      })

      frequencyData.value = response.data as any
      lastUpdateTime.value = new Date().toISOString()
    } catch (err: any) {
      error.value = err.message || '获取活跃频次分析失败'
      message.error(error.value)
      console.error('获取活跃频次分析失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取产品线对比
  const fetchComparison = async (productLineIds: number[], params?: Partial<ActiveUserAnalysisRequest>) => {
    try {
      loading.value = true
      error.value = ''
      
      const requestParams = { ...queryParams.value, ...params }
      const response = await activeUserApi.getComparison({
        startDate: requestParams.startDate,
        endDate: requestParams.endDate,
        productLineIds
      })

      comparisonData.value = response.data as any
      lastUpdateTime.value = new Date().toISOString()
    } catch (err: any) {
      error.value = err.message || '获取产品线对比失败'
      message.error(error.value)
      console.error('获取产品线对比失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取活跃用户留存
  const fetchRetention = async (params?: Partial<ActiveUserAnalysisRequest>) => {
    try {
      loading.value = true
      error.value = ''
      
      const requestParams = { ...queryParams.value, ...params }
      const response = await activeUserApi.getRetention({
        startDate: requestParams.startDate,
        endDate: requestParams.endDate,
        productLineIds: requestParams.productLineIds
      })

      retentionData.value = response.data as any
      lastUpdateTime.value = new Date().toISOString()
    } catch (err: any) {
      error.value = err.message || '获取活跃用户留存失败'
      message.error(error.value)
      console.error('获取活跃用户留存失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取队列分析
  const fetchCohort = async (params?: Partial<ActiveUserAnalysisRequest>) => {
    try {
      loading.value = true
      error.value = ''
      
      const requestParams = { ...queryParams.value, ...params }
      const response = await activeUserApi.getCohort({
        startDate: requestParams.startDate,
        endDate: requestParams.endDate,
        productLineIds: requestParams.productLineIds
      })

      cohortData.value = response.data as any
      lastUpdateTime.value = new Date().toISOString()
    } catch (err: any) {
      error.value = err.message || '获取队列分析失败'
      message.error(error.value)
      console.error('获取队列分析失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 导出数据
  const exportData = async (format: string = 'excel') => {
    try {
      loading.value = true
      
      const requestData: ActiveUserAnalysisRequest = {
        ...queryParams.value,
        exportFormat: format
      }
      
      const response = await activeUserApi.exportData(requestData)
      
      // 创建下载链接
      const blob = new Blob([response.data], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `active_user_analysis_${queryParams.value.startDate}_${queryParams.value.endDate}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      message.success('数据导出成功')
    } catch (err: any) {
      error.value = err.message || '导出数据失败'
      message.error(error.value)
      console.error('导出数据失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 刷新所有数据
  const refreshAllData = async () => {
    await Promise.all([
      fetchOverview(),
      fetchTrends(),
      fetchDistribution(),
      fetchFrequency()
    ])
  }

  // 清空数据
  const clearData = () => {
    overviewData.value = null
    trendsData.value = null
    distributionData.value = null
    frequencyData.value = null
    comparisonData.value = null
    retentionData.value = null
    cohortData.value = null
    lastUpdateTime.value = ''
    error.value = ''
  }

  return {
    // 状态
    loading,
    overviewData,
    trendsData,
    distributionData,
    frequencyData,
    comparisonData,
    retentionData,
    cohortData,
    lastUpdateTime,
    error,
    queryParams,
    
    // 计算属性
    hasData,
    isDataStale,
    coreMetrics,
    trendChartData,
    distributionChartData,
    
    // Actions
    setQueryParams,
    setDateRange,
    setGranularity,
    setProductLines,
    fetchOverview,
    fetchTrends,
    fetchDistribution,
    fetchFrequency,
    fetchComparison,
    fetchRetention,
    fetchCohort,
    exportData,
    refreshAllData,
    clearData
  }
})
