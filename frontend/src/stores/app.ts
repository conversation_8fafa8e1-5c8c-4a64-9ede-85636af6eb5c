import { defineStore } from 'pinia'
import type { ProductLine, MenuItem } from '@/types'
import { http } from '@/utils/request'

interface AppState {
  // 侧边栏是否折叠
  sidebarCollapsed: boolean
  // 当前选中的产品线
  selectedProductLines: string[]
  // 产品线列表
  productLines: ProductLine[]
  // 菜单列表
  menuList: MenuItem[]
  // 当前视角：function(功能模块) | product(产品线)
  currentView: 'function' | 'product'
  // 全局加载状态
  globalLoading: boolean
  // 页面标题
  pageTitle: string
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    sidebarCollapsed: false,
    selectedProductLines: [],
    productLines: [],
    menuList: [],
    currentView: 'function',
    globalLoading: false,
    pageTitle: 'SCRM-Next 数据分析平台'
  }),

  getters: {
    // 获取选中的产品线信息
    selectedProductLineInfo: (state): ProductLine[] => {
      return state.productLines.filter(item => 
        state.selectedProductLines.includes(item.code)
      )
    },

    // 获取功能模块视角菜单
    functionMenuList: (): MenuItem[] => {
      return [
        {
          key: 'dashboard',
          label: '数据看板',
          icon: 'DashboardOutlined',
          children: [
            { key: 'overview', label: '总览仪表盘', path: '/dashboard/overview' },
            { key: 'product', label: '产品线仪表盘', path: '/dashboard/product' }
          ]
        },
        {
          key: 'user',
          label: '用户分析',
          icon: 'UserOutlined',
          children: [
            { key: 'active', label: '活跃用户分析', path: '/user/active' },
            { key: 'growth', label: '用户增长分析', path: '/user/growth' },
            { key: 'login', label: '登录行为分析', path: '/user/login' },
            { key: 'structure', label: '用户结构分析', path: '/user/structure' },
            { key: 'segmentation', label: '用户分群', path: '/user/segmentation' }
          ]
        },
        {
          key: 'behavior',
          label: '行为分析',
          icon: 'BarChartOutlined',
          children: [
            { key: 'event', label: '事件分析', path: '/behavior/event' },
            { key: 'feature', label: '功能使用分析', path: '/behavior/feature' },
            { key: 'search', label: '搜索行为分析', path: '/behavior/search' },
            { key: 'path', label: '用户行为路径', path: '/behavior/path' },
            { key: 'funnel', label: '漏斗分析', path: '/behavior/funnel' },
            { key: 'churn', label: '客户端流失分析', path: '/behavior/churn' }
          ]
        },
        {
          key: 'business',
          label: '商业变现',
          icon: 'DollarOutlined',
          children: [
            { key: 'revenue', label: '订单与收入分析', path: '/business/revenue' },
            { key: 'conversion', label: '付费转化分析', path: '/business/conversion' },
            { key: 'advertising', label: '广告效果分析', path: '/business/advertising' }
          ]
        },
        {
          key: 'private-domain',
          label: '私域运营',
          icon: 'TeamOutlined',
          children: [
            { key: 'fan-analysis', label: '粉丝数据分析', path: '/private-domain/fan-analysis' },
            { key: 'community-analysis', label: '社群活跃度分析', path: '/private-domain/community-analysis' }
          ]
        },
        {
          key: 'activity',
          label: '活动运营',
          icon: 'ThunderboltOutlined',
          children: [
            { key: 'campaign-analysis', label: '活动效果分析', path: '/activity/campaign-analysis' }
          ]
        },
        {
          key: 'system',
          label: '系统管理',
          icon: 'SettingOutlined',
          children: [
            { key: 'user-manage', label: '用户账户与权限管理', path: '/system/user' },
            { key: 'role', label: '角色与权限配置', path: '/system/role' },
            { key: 'log', label: '系统操作日志审计', path: '/system/log' },
            { key: 'product-manage', label: '产品与版本管理', path: '/system/product' },
            { key: 'config', label: '基础配置管理', path: '/system/config' },
            { key: 'report', label: '统一报表与分析配置', path: '/system/report' }
          ]
        }
      ]
    },

    // 获取产品线视角菜单
    productMenuList: (state): MenuItem[] => {
      return state.productLines.map(product => ({
        key: product.code,
        label: product.name,
        icon: 'AppstoreOutlined',
        children: [
          { key: `${product.code}-active`, label: '活跃用户分析', path: `/product/${product.code}/active` },
          { key: `${product.code}-feature`, label: '功能使用分析', path: `/product/${product.code}/feature` },
          { key: `${product.code}-event`, label: '事件分析', path: `/product/${product.code}/event` },
          { key: `${product.code}-revenue`, label: '订单收入分析', path: `/product/${product.code}/revenue` }
        ]
      }))
    }
  },

  actions: {
    // 切换侧边栏折叠状态
    toggleSidebar(): void {
      this.sidebarCollapsed = !this.sidebarCollapsed
    },

    // 设置侧边栏折叠状态
    setSidebarCollapsed(collapsed: boolean): void {
      this.sidebarCollapsed = collapsed
    },

    // 设置选中的产品线
    setSelectedProductLines(productLines: string[]): void {
      this.selectedProductLines = productLines
    },

    // 设置产品线列表
    setProductLines(productLines: ProductLine[]): void {
      this.productLines = productLines
    },

    // 切换视角
    switchView(view: 'function' | 'product'): void {
      this.currentView = view
    },

    // 设置全局加载状态
    setGlobalLoading(loading: boolean): void {
      this.globalLoading = loading
    },

    // 设置页面标题
    setPageTitle(title: string): void {
      this.pageTitle = title
      document.title = title
    },

    // 加载产品线数据
    async loadProductLines(): Promise<void> {
      try {
        const response = await http.get('/system/product-lines/enabled')

        if (response.code === 200 && response.data) {
          // 处理响应数据
          const items = Array.isArray(response.data) ? response.data : []
          this.productLines = items.map((item: any) => ({
            id: item.id,
            name: item.name,
            code: item.code || `product_${item.id}`,
            type: item.type || 1,
            color: item.color || '#1E88E5',
            description: item.description || ''
          }))
          console.log('产品线数据加载成功:', this.productLines)
          return
        }
      } catch (error) {
        console.error('获取产品线数据失败:', error)
        // 如果是权限问题，静默处理，使用默认数据
      }

      // 使用默认数据作为备用
      this.productLines = [
        { id: 1, name: '福昕阅读器GA版', code: 'reader_ga', type: 1, color: '#1E88E5', description: '免费版PDF阅读器' },
        { id: 2, name: '福昕阅读器PLUS版', code: 'reader_plus', type: 1, color: '#43A047', description: '增强版PDF阅读器' },
        { id: 3, name: 'PDF编辑器个人版', code: 'editor_personal', type: 2, color: '#FB8C00', description: '个人版PDF编辑器' },
        { id: 4, name: 'PDF编辑器专业版', code: 'editor_pro', type: 2, color: '#E53935', description: '专业版PDF编辑器' },
        { id: 5, name: 'PDF365在线服务', code: 'pdf365', type: 3, color: '#8E24AA', description: '在线PDF处理服务' },
        { id: 6, name: 'PDF转换器', code: 'converter', type: 4, color: '#00ACC1', description: 'PDF格式转换工具' },
        { id: 7, name: '卸载器', code: 'uninstaller', type: 4, color: '#5E35B1', description: '软件卸载工具' },
        { id: 8, name: '福昕移动阅读器', code: 'mobile_reader', type: 1, color: '#FF5722', description: '移动端PDF阅读器' },
        { id: 9, name: 'PDF SDK工具包', code: 'sdk', type: 4, color: '#795548', description: 'PDF开发工具包' },
        { id: 10, name: '内容平台', code: 'content_platform', type: 5, color: '#607D8B', description: '内容管理平台' }
      ]
      console.log('使用默认产品线数据:', this.productLines)
    },

    // 初始化应用状态
    async initAppState(): Promise<void> {
      // 从localStorage恢复状态
      const savedState = localStorage.getItem('appState')
      if (savedState) {
        try {
          const state = JSON.parse(savedState)
          this.sidebarCollapsed = state.sidebarCollapsed || false
          this.selectedProductLines = state.selectedProductLines || []
          this.currentView = state.currentView || 'function'
        } catch (error) {
          console.error('恢复应用状态失败:', error)
        }
      }

      // 加载产品线数据
      await this.loadProductLines()
    },

    // 保存应用状态
    saveAppState(): void {
      const state = {
        sidebarCollapsed: this.sidebarCollapsed,
        selectedProductLines: this.selectedProductLines,
        currentView: this.currentView
      }
      localStorage.setItem('appState', JSON.stringify(state))
    }
  }
})
