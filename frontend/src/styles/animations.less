// 动画样式定义

// 关键帧动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-15px);
  }
  70% {
    transform: translateY(-7px);
  }
  90% {
    transform: translateY(-3px);
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-180deg) scale(0.8);
  }
  to {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes flipInX {
  from {
    opacity: 0;
    transform: perspective(400px) rotateX(90deg);
  }
  40% {
    transform: perspective(400px) rotateX(-20deg);
  }
  60% {
    transform: perspective(400px) rotateX(10deg);
  }
  80% {
    transform: perspective(400px) rotateX(-5deg);
  }
  to {
    opacity: 1;
    transform: perspective(400px) rotateX(0deg);
  }
}

@keyframes flipInY {
  from {
    opacity: 0;
    transform: perspective(400px) rotateY(90deg);
  }
  40% {
    transform: perspective(400px) rotateY(-20deg);
  }
  60% {
    transform: perspective(400px) rotateY(10deg);
  }
  80% {
    transform: perspective(400px) rotateY(-5deg);
  }
  to {
    opacity: 1;
    transform: perspective(400px) rotateY(0deg);
  }
}

// 动画类
.fade-in-up {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.fade-in-down {
  animation: fadeInDown 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.fade-in-left {
  animation: fadeInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.fade-in-right {
  animation: fadeInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.scale-in {
  animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.pulse {
  animation: pulse 1s ease-in-out;
}

.shake {
  animation: shake 0.5s ease-in-out;
}

.bounce {
  animation: bounce 0.6s ease-in-out;
}

.rotate-in {
  animation: rotateIn 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.slide-in-up {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.slide-in-down {
  animation: slideInDown 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.zoom-in {
  animation: zoomIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.flip-in-x {
  animation: flipInX 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.flip-in-y {
  animation: flipInY 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

// 延迟动画类
.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-400 {
  animation-delay: 0.4s;
}

.delay-500 {
  animation-delay: 0.5s;
}

// 持续时间类
.duration-300 {
  animation-duration: 0.3s;
}

.duration-500 {
  animation-duration: 0.5s;
}

.duration-700 {
  animation-duration: 0.7s;
}

.duration-1000 {
  animation-duration: 1s;
}

// 页面进入动画
.page-enter {
  .page-header {
    opacity: 0;
    animation: fadeInDown 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.1s forwards;
  }
  
  .stats-cards .ant-col {
    opacity: 0;
    
    &:nth-child(1) { animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.2s forwards; }
    &:nth-child(2) { animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.3s forwards; }
    &:nth-child(3) { animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.4s forwards; }
    &:nth-child(4) { animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.5s forwards; }
  }
  
  .charts-section .ant-col {
    opacity: 0;
    
    &:nth-child(1) { animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.6s forwards; }
    &:nth-child(2) { animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.7s forwards; }
    &:nth-child(3) { animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.8s forwards; }
    &:nth-child(4) { animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.9s forwards; }
  }
}

// 悬停效果增强
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

.hover-scale {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: scale(1.02);
  }
}

.hover-glow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    box-shadow: 0 0 20px rgba(24, 144, 255, 0.3);
  }
}
