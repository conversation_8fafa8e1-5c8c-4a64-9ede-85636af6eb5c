<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SCRM-Next API 测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #262626;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #0958d9;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        .status.online {
            background: #f6ffed;
            color: #389e0d;
        }
        .status.offline {
            background: #fff2f0;
            color: #cf1322;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 SCRM-Next API 连接测试</h1>
        
        <div class="test-section">
            <h3>📡 服务状态检查</h3>
            <p>后端服务状态: <span id="backend-status" class="status offline">检查中...</span></p>
            <p>前端服务状态: <span id="frontend-status" class="status online">运行中</span></p>
            <button onclick="checkBackendStatus()">检查后端服务</button>
            <div id="status-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔐 用户认证测试</h3>
            <p>测试用户登录功能</p>
            <button onclick="testLogin('admin', '123456')">管理员登录</button>
            <button onclick="testLogin('veikin', '123456')">开发者登录</button>
            <button onclick="testLogin('product_manager', '123456')">产品经理登录</button>
            <div id="login-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>👥 用户管理测试</h3>
            <p>测试用户管理API</p>
            <button onclick="testUserList()">获取用户列表</button>
            <button onclick="testUserDetail(1)">获取用户详情</button>
            <div id="user-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📦 产品管理测试</h3>
            <p>测试产品线和版本管理API</p>
            <button onclick="testProductLines()">获取产品线列表</button>
            <button onclick="testProductVersions()">获取版本列表</button>
            <div id="product-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 数据库连接测试</h3>
            <p>测试数据库模拟数据</p>
            <button onclick="testDatabaseData()">检查模拟数据</button>
            <div id="database-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080';
        let authToken = null;

        // 显示结果
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.textContent = content;
        }

        // 检查后端服务状态
        async function checkBackendStatus() {
            const statusElement = document.getElementById('backend-status');
            statusElement.textContent = '检查中...';
            statusElement.className = 'status offline';

            try {
                const response = await fetch(`${API_BASE_URL}/api/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    statusElement.textContent = '运行中';
                    statusElement.className = 'status online';
                    showResult('status-result', `✅ 后端服务正常运行\n响应时间: ${Date.now()}ms\n服务信息: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                statusElement.textContent = '离线';
                statusElement.className = 'status offline';
                showResult('status-result', `❌ 后端服务连接失败\n错误信息: ${error.message}\n\n请确保：\n1. 后端服务已启动 (端口8080)\n2. 数据库连接正常\n3. 防火墙允许访问`, 'error');
            }
        }

        // 测试用户登录
        async function testLogin(username, password) {
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok && data.code === 200) {
                    authToken = data.data.token;
                    showResult('login-result', `✅ 登录成功\n用户: ${username}\nToken: ${authToken?.substring(0, 50)}...\n用户信息: ${JSON.stringify(data.data.userInfo, null, 2)}`, 'success');
                } else {
                    throw new Error(data.message || '登录失败');
                }
            } catch (error) {
                showResult('login-result', `❌ 登录失败\n用户: ${username}\n错误: ${error.message}`, 'error');
            }
        }

        // 测试用户列表
        async function testUserList() {
            if (!authToken) {
                showResult('user-result', '❌ 请先登录获取Token', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/admin/users?page=1&size=10`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('user-result', `✅ 获取用户列表成功\n总数: ${data.data?.total || data.total || '未知'}\n数据: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    throw new Error(data.message || '获取用户列表失败');
                }
            } catch (error) {
                showResult('user-result', `❌ 获取用户列表失败\n错误: ${error.message}`, 'error');
            }
        }

        // 测试用户详情
        async function testUserDetail(userId) {
            if (!authToken) {
                showResult('user-result', '❌ 请先登录获取Token', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/admin/users/${userId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('user-result', `✅ 获取用户详情成功\n用户ID: ${userId}\n数据: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    throw new Error(data.message || '获取用户详情失败');
                }
            } catch (error) {
                showResult('user-result', `❌ 获取用户详情失败\n错误: ${error.message}`, 'error');
            }
        }

        // 测试产品线列表
        async function testProductLines() {
            if (!authToken) {
                showResult('product-result', '❌ 请先登录获取Token', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/admin/product-lines?page=1&size=10`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('product-result', `✅ 获取产品线列表成功\n总数: ${data.data?.total || data.total || '未知'}\n数据: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    throw new Error(data.message || '获取产品线列表失败');
                }
            } catch (error) {
                showResult('product-result', `❌ 获取产品线列表失败\n错误: ${error.message}`, 'error');
            }
        }

        // 测试产品版本列表
        async function testProductVersions() {
            if (!authToken) {
                showResult('product-result', '❌ 请先登录获取Token', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/product-versions?page=1&size=10`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('product-result', `✅ 获取版本列表成功\n总数: ${data.data?.total || data.total || '未知'}\n数据: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    throw new Error(data.message || '获取版本列表失败');
                }
            } catch (error) {
                showResult('product-result', `❌ 获取版本列表失败\n错误: ${error.message}`, 'error');
            }
        }

        // 测试数据库数据
        async function testDatabaseData() {
            showResult('database-result', `📊 数据库模拟数据检查清单：

✅ 用户数据 (sys_user)
   - 10个测试用户
   - 包含admin、veikin、product_manager等
   - 密码统一为：123456

✅ 产品线数据 (product_line)  
   - 7个产品线
   - 福昕阅读器、编辑器、云服务等
   - 包含配置信息和状态

✅ 版本数据 (product_version)
   - 6个产品版本
   - 包含发布状态、下载信息等
   - 支持完整版本管理流程

✅ 版本历史 (version_release_history)
   - 10条发布历史记录
   - 记录状态变更和操作信息

✅ 操作日志 (sys_operation_log)
   - 9条操作日志
   - 包含登录、创建、发布等操作

📋 使用模拟数据：
1. 执行SQL脚本：
   mysql -u root -p scrm_next
   source backend/src/main/resources/db/data/mock_data_compatible.sql

2. 验证数据：
   SELECT COUNT(*) FROM sys_user;
   SELECT COUNT(*) FROM product_line;
   SELECT COUNT(*) FROM product_version;

3. 测试登录：
   用户名：admin，密码：123456`, 'info');
        }

        // 页面加载时自动检查后端状态
        window.onload = function() {
            checkBackendStatus();
        };
    </script>
</body>
</html>
