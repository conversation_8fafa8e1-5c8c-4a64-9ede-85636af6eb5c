# SCRM-Next 前端应用

> 基于 Vue 3 + TypeScript + Ant Design Vue 的现代化数据分析平台前端

## 📋 项目概述

SCRM-Next 前端是一个现代化的企业级数据分析平台前端应用，采用 Vue 3 生态系统构建，为福昕公司 15+产品线提供统一的数据可视化和业务洞察界面。

## ✨ 最新更新 (v3.0.0 - 2025年7月7日)

### 🎉 功能完善与优化

#### 🎯 行为分析模块完成

- **事件分析页面**：完整的事件分析界面和图表展示
- **功能使用分析**：功能使用统计和热力图分析
- **漏斗分析页面**：转化漏斗可视化和优化建议
- **用户路径分析**：用户行为路径追踪和流向图

#### 🔧 系统优化

- **滚动重置功能**：路由切换自动滚动到顶部
- **API 集成优化**：完善后端接口对接
- **响应式设计**：优化移动端适配
- **性能优化**：图表渲染和数据加载优化

#### 📊 数据可视化增强

- **ECharts 图表库**：丰富的图表类型支持
- **实时数据更新**：支持数据实时刷新
- **交互式图表**：支持图表联动和钻取
- **自定义主题**：统一的视觉设计风格

## 🛠 技术栈

### 核心框架

- **Vue 3.4.x** - 渐进式 JavaScript 框架
- **TypeScript 5.x** - JavaScript 的超集，提供静态类型检查
- **Vite 5.x** - 下一代前端构建工具

### UI 组件库

- **Ant Design Vue 4.x** - 企业级 UI 设计语言和组件库
- **@ant-design/icons-vue** - Ant Design 图标库

### 状态管理

- **Pinia 2.x** - Vue 的状态管理库

### 路由管理

- **Vue Router 4.x** - Vue.js 官方路由管理器

### HTTP 客户端

- **Axios** - 基于 Promise 的 HTTP 库

### 图表可视化

- **ECharts 5.x** - 强大的数据可视化库
- **vue-echarts** - ECharts 的 Vue 3 组件封装

### 工具库

- **dayjs** - 轻量级日期处理库
- **lodash-es** - 现代化的 JavaScript 工具库

### 开发工具

- **ESLint** - 代码质量检查工具
- **Prettier** - 代码格式化工具
- **TypeScript ESLint** - TypeScript 代码检查
- **Vue ESLint** - Vue 代码检查

## 📁 项目结构

```
frontend/
├── public/                     # 静态资源
├── src/
│   ├── api/                   # API 接口定义
│   ├── components/            # 通用组件
│   │   ├── common/           # 基础通用组件
│   │   ├── charts/           # 图表组件
│   │   └── forms/            # 表单组件
│   ├── layouts/              # 布局组件
│   │   ├── MainLayout.vue    # 主布局
│   │   └── AuthLayout.vue    # 认证布局
│   ├── views/                # 页面组件
│   │   ├── dashboard/        # 数据看板页面
│   │   ├── user/            # 用户分析页面
│   │   ├── behavior/        # 行为分析页面
│   │   ├── system/          # 系统管理页面
│   │   ├── Login.vue        # 登录页面
│   │   └── NotFound.vue     # 404页面
│   ├── stores/              # 状态管理
│   │   ├── user.ts          # 用户状态
│   │   ├── app.ts           # 应用状态
│   │   └── index.ts         # 状态入口
│   ├── router/              # 路由配置
│   │   └── index.ts         # 路由定义
│   ├── styles/              # 样式文件
│   │   ├── index.less       # 全局样式
│   │   ├── variables.less   # 样式变量
│   │   └── mixins.less      # 样式混入
│   ├── utils/               # 工具函数
│   │   ├── request.ts       # HTTP 请求封装
│   │   ├── auth.ts          # 认证工具
│   │   ├── storage.ts       # 存储工具
│   │   ├── format.ts        # 格式化工具
│   │   └── scrollUtils.ts   # 滚动工具函数
│   ├── types/               # TypeScript 类型定义
│   │   ├── api.ts           # API 类型
│   │   ├── user.ts          # 用户类型
│   │   └── common.ts        # 通用类型
│   ├── App.vue              # 根组件
│   └── main.ts              # 应用入口
├── index.html               # HTML 模板
├── package.json             # 项目配置
├── tsconfig.json           # TypeScript 配置
├── vite.config.ts          # Vite 配置
└── README.md               # 项目说明
```

## ⚡ 快速开始

### 🔧 环境要求

- **Node.js** >= 18.0.0 (推荐 18.17.0+)
- **npm** >= 8.0.0 或 **yarn** >= 1.22.0

### 📦 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

### 🚀 启动开发服务器

```bash
# 使用 npm
npm run dev

# 或使用 yarn
yarn dev
```

启动成功后，访问 [http://localhost:3000](http://localhost:3000)

### 🏗️ 构建生产版本

```bash
# 使用 npm
npm run build

# 或使用 yarn
yarn build
```

### 🔍 代码检查

```bash
# ESLint 检查
npm run lint

# TypeScript 类型检查
npm run type-check
```

### 📦 预览构建结果

```bash
npm run preview
```

## ⚙️ 配置说明

### 环境变量

创建 `.env.local` 文件配置本地环境变量：

```bash
# API 基础地址
VITE_API_BASE_URL=http://localhost:9090/api

# WebSocket 地址
VITE_WS_BASE_URL=ws://localhost:9090/ws

# 应用标题
VITE_APP_TITLE=SCRM-Next 数据分析平台

# 开发模式
VITE_NODE_ENV=development
```

### Vite 配置

主要配置项在 `vite.config.ts` 中：

```typescript
export default defineConfig({
  plugins: [
    vue(),
    // 自动导入 Vue API
    AutoImport({
      imports: ["vue", "vue-router", "pinia"],
    }),
    // 自动导入组件
    Components({
      resolvers: [AntDesignVueResolver()],
    }),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
  },
  server: {
    port: 3000,
    proxy: {
      "/api": {
        target: "http://localhost:9090",
        changeOrigin: true,
      },
    },
  },
});
```

## 🎨 开发规范

### 代码规范

- **组件命名**：使用 PascalCase，如 `UserList.vue`
- **文件命名**：使用 camelCase，如 `userApi.ts`
- **变量命名**：使用 camelCase，语义明确
- **常量命名**：使用 UPPER_SNAKE_CASE

### 组件开发规范

```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 导入依赖
import { ref, reactive, onMounted } from "vue";

// 类型定义
interface Props {
  title: string;
}

// Props 定义
const props = defineProps<Props>();

// 响应式数据
const loading = ref(false);

// 生命周期
onMounted(() => {
  // 初始化逻辑
});
</script>

<style scoped lang="less">
// 组件样式
</style>
```

### API 调用规范

```typescript
// api/user.ts
import request from "@/utils/request";

export interface User {
  id: number;
  username: string;
  email: string;
}

export const getUserList = () => {
  return request.get<User[]>("/users");
};

export const createUser = (data: Omit<User, "id">) => {
  return request.post<User>("/users", data);
};
```

## 🧪 测试

### 单元测试

```bash
# 运行测试
npm run test

# 测试覆盖率
npm run test:coverage
```

### E2E 测试

```bash
# 运行 E2E 测试
npm run test:e2e
```

## 🎯 功能特性

### 核心功能模块

- **用户管理**：完整的用户生命周期管理
- **权限控制**：基于角色的细粒度权限控制
- **数据看板**：实时数据监控和可视化
- **用户分析**：用户行为和增长分析
- **行为分析**：事件追踪和路径分析
- **漏斗分析**：转化漏斗可视化和优化
- **用户路径**：用户行为路径追踪和流向图

### 技术特性

- **滚动重置**：路由切换自动滚动到顶部
- **响应式设计**：支持桌面端和移动端
- **实时数据**：支持数据实时刷新和更新
- **交互式图表**：支持图表联动和钻取分析
- **主题定制**：统一的视觉设计风格
- **国际化**：多语言支持（预留）

### 用户体验

- **快速加载**：优化的资源加载策略
- **流畅交互**：防抖节流优化用户操作
- **错误处理**：友好的错误提示和降级处理
- **无障碍访问**：符合 WCAG 标准的可访问性设计

## 📊 性能优化

### 构建优化

- **代码分割**：路由级别的代码分割
- **Tree Shaking**：移除未使用的代码
- **资源压缩**：CSS/JS 文件压缩
- **图片优化**：图片格式优化和懒加载

### 运行时优化

- **虚拟滚动**：大列表性能优化
- **组件懒加载**：按需加载组件
- **缓存策略**：合理使用缓存
- **防抖节流**：优化用户交互

## 🚀 部署

### Docker 部署

```dockerfile
FROM node:18-alpine as builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 静态部署

构建后的 `dist` 目录可以部署到任何静态文件服务器：

- **Nginx**
- **Apache**
- **CDN**
- **对象存储**

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情。

## 📞 支持

- **技术支持**：<EMAIL>
- **问题反馈**：[GitHub Issues](https://github.com/your-org/scrm-next/issues)
- **文档**：[项目文档](../docs/)

---

> 📖 **说明**：本文档会随着项目开发进度持续更新，请关注版本变化。
>
> **最后更新**：2025 年 7 月 7 日 | **版本**：v3.0.0
>
> **v3.0.0 更新内容**：
>
> - ✅ 完成行为分析模块前端界面
> - ✅ 新增滚动重置功能
> - ✅ 优化 API 集成和错误处理
> - ✅ 完善图表组件和数据可视化
> - 🔧 修正配置文件中的端口号
> - 📊 更新功能特性和技术亮点
