/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AAvatar: typeof import('ant-design-vue/es')['Avatar']
    ABadge: typeof import('ant-design-vue/es')['Badge']
    ABreadcrumb: typeof import('ant-design-vue/es')['Breadcrumb']
    ABreadcrumbItem: typeof import('ant-design-vue/es')['BreadcrumbItem']
    AButton: typeof import('ant-design-vue/es')['Button']
    AButtonGroup: typeof import('ant-design-vue/es')['ButtonGroup']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACardMeta: typeof import('ant-design-vue/es')['CardMeta']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACheckboxGroup: typeof import('ant-design-vue/es')['CheckboxGroup']
    ACol: typeof import('ant-design-vue/es')['Col']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    ADescriptions: typeof import('ant-design-vue/es')['Descriptions']
    ADescriptionsItem: typeof import('ant-design-vue/es')['DescriptionsItem']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputGroup: typeof import('ant-design-vue/es')['InputGroup']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuDivider: typeof import('ant-design-vue/es')['MenuDivider']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    APopconfirm: typeof import('ant-design-vue/es')['Popconfirm']
    AProgress: typeof import('ant-design-vue/es')['Progress']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker']
    AreaChart: typeof import('./src/components/charts/AreaChart.vue')['default']
    AResult: typeof import('ant-design-vue/es')['Result']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    AStatistic: typeof import('ant-design-vue/es')['Statistic']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATimeline: typeof import('ant-design-vue/es')['Timeline']
    ATimelineItem: typeof import('ant-design-vue/es')['TimelineItem']
    ATimeRangePicker: typeof import('ant-design-vue/es')['TimeRangePicker']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATree: typeof import('ant-design-vue/es')['Tree']
    AUpload: typeof import('ant-design-vue/es')['Upload']
    BarChart: typeof import('./src/components/charts/BarChart.vue')['default']
    ChartCard: typeof import('./src/components/ChartCard.vue')['default']
    FeatureList: typeof import('./src/components/system/FeatureList.vue')['default']
    FilterPanel: typeof import('./src/components/FilterPanel.vue')['default']
    LineChart: typeof import('./src/components/charts/LineChart.vue')['default']
    MetricCard: typeof import('./src/components/charts/MetricCard.vue')['default']
    PageHeader: typeof import('./src/components/PageHeader.vue')['default']
    PieChart: typeof import('./src/components/charts/PieChart.vue')['default']
    ProductFormModal: typeof import('./src/components/ProductFormModal.vue')['default']
    ProductLineSelector: typeof import('./src/components/common/ProductLineSelector.vue')['default']
    ProductVersionModal: typeof import('./src/components/ProductVersionModal.vue')['default']
    RoleFormModal: typeof import('./src/components/RoleFormModal.vue')['default']
    RolePermissionModal: typeof import('./src/components/RolePermissionModal.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StatCard: typeof import('./src/components/StatCard.vue')['default']
    VersionFormModal: typeof import('./src/components/system/VersionFormModal.vue')['default']
    VersionHistoryModal: typeof import('./src/components/system/VersionHistoryModal.vue')['default']
  }
}
